########################
# Server configuration #
########################
server:
  port: 8080
  servlet:
    context-path: /be

########################
# Management endpoints #
########################
management:
  endpoints:
    jmx:
      domain: nzp.be
      exposure:
        include: '*'
    web:
      exposure:
        include: health,info,configprops,caches,metrics,loggers,threadpool,scheduledtasks,schedulers,threaddump
      base-path: '/endpoints'
  endpoint:
    health:
      show-details: always
  health:
    circuitbreakers:
      enabled: true


########################
# Spring configuration #
########################

spring:

  # Security
  security:
    filter:
      order: 1
    cors:
      path: '/**'
      allowedMethods: '*'
      allowedOrigins: '*'
      allowedHeaders: '*'
    oauth2:
      resourceserver:
        jwt:
          #jwk-set-uri: http://meru-nzp-dev.isdd.sk/prihlasenie/.well-known/jwks.json

  # Expose metrics through JMX
  jmx:
    enabled: true

  # Data source
  datasource:
    driver-class-name: org.postgresql.Driver
    #url: **********************************************
    #username: postgres
    #password: postgres

  # LDAP configuration
  ldap:
    #urls: ldap://ldap:389
    #username: cn=admin,dc=meru-ldap,dc=isdd,dc=sk
    #password: admin

  # Spring Data configuration
  data:
    rest:
      detection-strategy: annotated

  # JPA COnfiguration
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQL10Dialect
    hibernate:
      ddl-auto: none
    open-in-view: false
    properties:
      hibernate:
        # For @BatchSize define way to handle selects
        batch_fetch_style: DYNAMIC
        default_batch_fetch_size: 100
        id:
          new_generator_mappings: true
        # Second level cache
        cache:
          #use_second_level_cache: false
          #use_query_cache: false
          #hazelcast:
            #instance_name: nzp-hazelcast-node
          #region:
            #factory_class: sk.spp.nzp.hazelcast.HazelcastCacheRegionFactory
      # For some case is necessary use FlushMode on COMMIT, when entities are not ready at persist time
      org.hibernate.flushMode: COMMIT

  mail:
    #from: <EMAIL>
    #password: <EMAIL>
    #host: mail.isdd.sk
    #port: 587
    #username: user.dev
    #password: H3slo.123*@
    #properties:
      #mail:
        #smtp:
          #auth: true
          #starttls:
            #enable: true
            #required: false

  # Cache configuration
  cache:
    #type: hazelcast

  # Jackson configuration
  jackson:
    default-property-inclusion: non_null

  # Flyway disabled
  flyway:
    enabled: false
    placeholder-prefix: "$${"
    placeholder-suffix: "}"

  # Config file uploads
  servlet:
    multipart:
      # max-file-size: "500KB"
      # max-request-size: "500KB"


#############
# Hazelcast #
#############
#hazelcast:
#  configuration:
#    file: classpath:hazelcast.xml


#########################
# OpenAPI configuration #
#########################
openapi:
  title: SPP NZP REST API
  description: REST API for SPP New Customer Portal (NZP)
  version: dev
  tos_url:
  contact:
    name: Peter Vavra
    url: https://www.isdd.sk
    email: <EMAIL>
  license:
    name:
    url:


######################
# HTTP configuration #
######################
http:
  resttemplate:
    connectionTimeout: 5000
  proxy:
    host:
    port: -1


##########################
# Security configuration #
##########################
security:
  jwt:
    validation:
      #issuer: https://meru-nzp-dev.isdd.sk/prihlasenie
      #audience: nzp,enzp,ecnzp
  #admin:
    #nzpGroupCode: nzp_admin


###########################
# Templates configuration #
###########################
templates:
  path:
    email: /templates/mail


#################################
# Customer access component
#################################
password:
  policy:
    length:
      min: 8
    uppercase:
      min: 1
      chars: AÁÄBCČDĎEÉFGHCIÍJKLĹĽMNŇOÓÔPQRŔSŠTŤUÚVWXYÝZŽ
    lowercase:
      min: 1
      chars: aáäbcčdďeéfghciíjklĺľmnňoóôpqrŕsštťuúvwxyýzž
    digit:
      min: 1
    special:
      min: 1
    groups:
      min: 3
    loginSubstring:
      length: 5
      caseSensitive: true
    history:
      checked: 12
    generator:
      illegal: Il1oO0


####################################
# Validation regexes configuration #
####################################
name:
  regex: "^([a-zA-Z\\u00C0-\\u024F][a-zA-Z.,' \\-\\u00C0-\\u024F\\u1E00-\\u1EFF]*)$"
vatidnumber:
  regex: ^[A-Z]{2}[a-zA-Z0-9 +*-]{5,20}$
  delete: ZMAZAT
taxidnumber:
  regex: ^\d{10}$
companyregnumber:
  regex: ^\d{8}$
#  computecheck:
#    enabled: false
xss:
  regex: .*<[^>]+>.*
bpnumber:
  regex: ^51\d{8}$
contractnumber:
  regex: ^9\d{9}$
contractid:
  regex: ^\d{10}$|^.{27}$
dpnumber:
  regex: ^(4\d{9}|24Z\w{13}|SKSPPDIS\d{12})$
invoicenumber:
  regex: ^\d{10}$
variablesymbol:
  regex: ^\d{10}$
siponumber:
  regex: ^\d{10}$
eic:
  regex: ^(?i)24Z[a-zA-Z0-9]{13}$
pod:
  regex: ^[a-zA-Z]{8}[0-9]{12}$


#####################################
# Customer components configuration #
#####################################
customer:
  default-locale: sk
  password:
    encoder: bcrypt
  registration:
    challenge-code:
      validity: 1800000
    sap:
      challenge-code:
        validity: 86400000
  recovery:
    challenge-code:
      validity: 900000
  phone:
    change:
      challenge-code:
        maxRetry: 3
        retryDelay: 600000
        validity: 90000
  login-phone:
    challenge-code:
      validity: 900000
  login:
    challenge-code:
      validity: 900000
  pairing:
    sms:
      challenge-code:
        validity: 1800000
  default:
    challenge-code:
      validity: 1800000
  suspicious-activity:
    email: "@spp.sk"
  requests:
    attachment:
      count: 100
      white-list-extensions: >
        pdf,
        jpg,
        jpeg,
        png,
        tiff,
        bmp,
        txt,
        docx,
        doc,
        xlsx,
        xls,
        csv
    confirmation:
      path: /confirm-customer-request?customerRequestId={customerRequestId}&confirmationCode={confirmationCode}
    completion:
      path: /complete-customer-request?customerRequestId={customerRequestId}
#  email:
#    disposableEmailDomains:
#      additional: "domena1.sk,domena2.pl"


#################################
# Invoice component
#################################
invoice:
  customer:
    transaction:
      visibleMaxInDay: 7
  dueAt:
    afterDay: 0
  payment:
    qrcode:
      beneficiaryName: MojeSPP
      single:
        refererNote: Uhrada z MojeSPP PayBySquare
      multiple:
        refererNote: Uhrada z MojeSPP PayBySquare

#################################
# Consumption component
#################################
consumption:
  deliveryPointFact:
    ee:
      ignoreBefore: 2018-12-01
    zp:
      ignoreBefore: 2015-01-01

###########################
# Meter reading compoment #
###########################
meter-readings:
  reading-cycles:
    tolerance:
      before: 0
      after: 0

##################################
# Google recaptcha configuration #
##################################
google:
  recaptcha:
    ignoreEmpty: false
    #secretkey: 6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe
    #hostname: testkey.google.com
    verification-url: https://www.google.com/recaptcha/api/siteverify
    allowInCaseOfUnavailableGoogleService: true
    paths: >
      /customers/registration,
      /customers/registration-social,
      /customers/check-email,
      /customers/anonymous/customer-requests/*,
      /customers/registration/resend,
      /customers/password/recovery,
      /customers/password/recovery/resend,
      /customer-requests,
      /customers/suspicious-activity,
      /customers/re-captcha-token
    token:
      validity: 15M
      allowedUses:
        ANONYMOUS_CUSTOMER_REQUEST:
          - "/customers/check-email"
          - "/customers/anonymous/customer-requests/**"

####################################
# Employee component configuration #
####################################
#employee:
  #locale: SK
  #account:
    #ldap:
      #accountObjectClass: sppAccount
      #branches:
        #- ou=people,dc=meru-ldap,dc=isdd,dc=sk
      #attrGroup: sppMemberOf
      #attrAccountExpires: sppAccountExpires
      #attrUserAccountControl: sppUserAccountControl
      #parseAuthenticationErrorReason: true


##############################
# resilience4j configuration #
##############################
resilience4j:
  circuitbreaker:
    configs:
      default:
        registerHealthIndicator: true
        allowHealthIndicatorToFail: false
        slidingWindowType: count_based
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        failureRateThreshold: 50
        waitDurationInOpenState: 10000
        permittedNumberOfCallsInHalfOpenState: 1
        ignoreExceptions:
          - org.springframework.web.client.HttpClientErrorException
        writableStackTraceEnabled: false
    instances:
      ReCaptchaV2ServiceImplVerifyRecaptchaResponse:
        baseConfig: default
      EpayServiceRESTImplInitTransaction:
        baseConfig: default
  thread-pool-bulkhead:
    configs:
      default:
        coreThreadPoolSize: 30
        maxThreadPoolSize: 30
        queueCapacity: 1
        contextPropagators:
          - sk.spp.nzp.commons.propagator.NzpRequestContextPropagator
          - sk.spp.nzp.commons.propagator.NzpSecurityContextPropagator
          - sk.spp.nzp.commons.propagator.MdcContextPropagator
        writableStackTraceEnabled: false
    instances:
      ReCaptchaV2ServiceImplVerifyRecaptchaResponse:
        baseConfig: default
      EpayServiceRESTImplInitTransaction:
        baseConfig: default
  timelimiter:
    configs:
      default:
        timeoutDuration: 10s
    instances:
      ReCaptchaV2ServiceImplVerifyRecaptchaResponse:
        timeoutDuration: 10s
        baseConfig: default
      EpayServiceRESTImplInitTransaction:
        timeoutDuration: 10s
        baseConfig: default
  scheduled:
    executor:
      contextPropagators:
        - sk.spp.nzp.commons.propagator.NzpRequestContextPropagator
        - sk.spp.nzp.commons.propagator.NzpSecurityContextPropagator
        - sk.spp.nzp.commons.propagator.MdcContextPropagator
      corePoolSize: 30


#############################
# Development configuration #
#############################
development:
  exception:
    show-details: false
  invoice:
    #mockFilePath:
    #overrideDocumentId: 123456789012345678901234567890
  pairing: 
    #status: false


#######################
# Reporting component #
#######################
reporting:
  jdbc:
    template:
      fetch-size: 5000
      max-rows: 500000
  datasource:
    hikari:
      auto-commit: false
      read-only: true
    driver-class-name: org.postgresql.Driver
    #url: ************************************
    #username: nzp
    #password: nzp
  format:
    timestamp: "yyyy-MM-dd HH:mm:ss"
    number: "###.###"
  export:
    filename: report
    sheetName: report
    # Max number of xlsx rows available in memory
    randomAccessWindowSize: 1000
    batch: 100
  customerAccountTable: customer_account


####################
# Commons settings #
####################
commons:
  logging:
    JsonLogConverter:
      serialization:
        inclusion-NON_NULL: true


#######################
# MsSQL configuration #
#######################
mssql:
  datasource:
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    #url: ***************************************************
    #username: sa
    #password: sa

########################
# IBM CM configuration #
########################
ibmcm:
  datastore:
    enabled: false
    #userName: nzcadmin
    #password: 20200929Admin
    #serverName: ICMNLSDB
    #serverConfigPath: src\main\resources\ibmcm\cmbicmsrvs.ini
    #serverEnvironmentConfigPath: src\main\resources\ibmcm\cmbicmenv.ini
    pool:
      maxTotal: 40   # default: 8
      maxIdle: 20  # default: 8
      minIdle: 20   # default: 0
#      lifo:   # default: true
      fairness: true   # default: false
      maxWaitMillis: 60000   # default: -1
#      minEvictableIdleTimeMillis:   # default: 1000 * 60 * 30
#      evictorShutdownTimeoutMillis:   # default: -1
#      softMinEvictableIdleTimeMillis:   # default: 10000
#      numTestsPerEvictionRun:   # default: 3
#      evictionPolicyClassName:   # default: org.apache.commons.pool2.impl.DefaultEvictionPolicy
      testOnCreate: true   # default: false   ***must be true since validation connects datastore***
      testOnBorrow: true   # default: false
#      testOnReturn:   # default: false
      testWhileIdle: true   # default: false
      timeBetweenEvictionRunsMillis: 900000   # default: -1
#      blockWhenExhausted:   # default: true
#      jmxEnabled:   # default: true
#      jmxNamePrefix:   # default: pool
#      jmxNameBase:   # default: null

############################
# Spring DOC configuration #
############################
# Need to generate fully-qualifieed-name for model classes
springdoc:
  use-fqn: true


##########################
# Services configuration #
##########################
service:
  pairing:
    individual:
      verify-required: true


########################
# Paging configuration #
########################
paging:
  size:
    default: 10
    max: 5000


############################
# Challenges configuration #
############################
challenges:
  maxResendCount: 5


#############################
# Application configuration #
#############################
application:
  #externalUrl: http://testnzp.spp.sk
  default:
    locale: sk


#############################
# SMS configuration #
#############################
sms:
  mitto:
    url: https://rest.mittoapi.net/sms
    sender: SPP INFO
    mittoApiKey: 0TqWIQjWK4KRo5adDuHnQ31qTMHAvLsY

#############################
# EPAY configuration #
#############################
epay:
  #initUrl: http://meru-nzp-dev.isdd.sk/epay/init

##################################
#    Restriction policy (REST)   #
# !!!NEEDS HAZELCAST ENABLED!!!  #
##################################
#restriction:
#  policy:
#    enabled: false
#    entries:
#      - { url: /customers/*/phone/change/challange, keyType: URL, action: DENIED, periodMinutes: 15, count: 10}
#      - { url: /customers/*/email/change/challange, keyType: URL, action: GOOGLE_RECAPTCHA, periodMinutes: 30, count: 10}
#      - { url: /customers/codelist, keyType: IP, action: DENIED, periodMinutes: 15, count: 10}
#      - { url: /customers/registration/challenge, keyType: IP, action: GOOGLE_RECAPTCHA, periodMinutes: 15, count: 3}
#      - { url: /customers/password/recovery/challenge, keyType: IP, action: GOOGLE_RECAPTCHA, periodMinutes: 15, count: 3}
#      - { url: /customers/verifyPassword, keyType: IP, action: GOOGLE_RECAPTCHA, periodMinutes: 15, count: 3}


# Freemarker
freemarker:
  cache:
    ttl: 60000

tariff:
  mapping:
    ee:
      "D1": DD1
      "D2": DD2
      "D3": DD3
      "D4": DD4
      "D5": DD5
      "D6": DD6
      "D7": DD7
      "D8": DD8

#Delay to request synchronized
customerRequest:
  sync:
    time: '06:00 AM'

# ========================
# SAP Authneitifcation
# =========================
sap:
  security:
    basicauth:
#      username:
#      password:

# ========================
# SSO WS endpoints configuration
# ========================
sso:
  webservice:
    issuer: nzp
    autoLoginAuthentication:
      # endpoint: http://sso:8080/service/autoLoginAuthenticationService
    oneTimeTokenAuthentication:
      # endpoint: http://sso:8080/service/oneTimeTokenAuthenticationService
