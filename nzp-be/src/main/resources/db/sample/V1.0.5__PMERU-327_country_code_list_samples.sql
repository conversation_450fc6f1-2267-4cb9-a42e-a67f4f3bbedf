INSERT INTO generic_code_list (
    uuid,
    created_at,
    updated_at,
    version,
    code,
    type,
    valid_from,
    valid_to,
    parent_uuid
)
VALUES
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'SK',
    'COUNTRY',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'CZ',
    'COUNTRY',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'AT',
    'COUNTRY',
    null,
    null,
    null
);

INSERT INTO generic_code_list_i18n (
    uuid,
    created_at,
    updated_at,
    version,
    locale,
    name,
    description,
    code_list_uuid
)
VALUES
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Slovenská republika',
    null,
    (select uuid from generic_code_list where code like 'SK')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'en_US',
    'Slovakia',
    null,
    (select uuid from generic_code_list where code like 'SK')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Česká republika',
    null,
    (select uuid from generic_code_list where code like 'C<PERSON>')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'en_US',
    'Czech Republic',
    null,
    (select uuid from generic_code_list where code like 'CZ')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Rakúsko',
    null,
    (select uuid from generic_code_list where code like 'AT')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'en_US',
    'Austria',
    null,
    (select uuid from generic_code_list where code like 'AT')
);