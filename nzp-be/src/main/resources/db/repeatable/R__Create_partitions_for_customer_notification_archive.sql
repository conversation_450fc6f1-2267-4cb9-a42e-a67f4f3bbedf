-- PMERU-1989
CREATE OR REPLACE PROCEDURE create_customer_notification_archive_partitions(start_year INTEGER DEFAULT EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER, num_years INTEGER DEFAULT 2)
    LANGUAGE plpgsql
AS $$
DECLARE
    year_end INTEGER := start_year + num_years;
    partition_name TEXT;
    partition_range_start TEXT;
    partition_range_end TEXT;
BEGIN
    FOR year IN start_year .. year_end
        LOOP
            partition_name := 'customer_notification_archive_' || year;
            partition_range_start := quote_literal(year || '-01-01 00:00:00.000000 +00:00');
            partition_range_end := quote_literal((year + 1) || '-01-01 00:00:00.000000 +00:00');
            EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF customer_notification_archive FOR VALUES FROM (%s) TO (%s)',
                           partition_name, partition_range_start, partition_range_end);
        <PERSON><PERSON> LOOP;
END;
$$;
