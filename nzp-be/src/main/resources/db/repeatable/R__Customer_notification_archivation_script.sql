-- PMERU-1984
CREATE OR REPLACE PROCEDURE archive_customer_notification(batch_size int)
    LANGUAGE plpgsql AS
$$
DECLARE
    row_count   int := 1;
    total_count int := 0;
BEGIN
    RAISE NOTICE 'Archiving process starts';
    WHILE row_count > 0
        LOOP
            WITH archived_rows AS (
                INSERT INTO customer_notification_archive
                    SELECT *
                    FROM customer_notification
                    WHERE (header is not null and updated_at < now() - INTERVAL '6 months')
                       OR (header is null and updated_at < now() - INTERVAL '1 months')
                    LIMIT batch_size
                    RETURNING uuid
            ),
                 delete_send_status AS (
                     DELETE FROM customer_notification_send_status
                         WHERE customer_notification_id IN (SELECT uuid FROM archived_rows)
                 ),
                 delete_render_status AS (
                     DELETE FROM customer_notification_render_status
                         WHERE customer_notification_id IN (SELECT uuid FROM archived_rows)
                 )
            DELETE
            FROM customer_notification
            WHERE uuid IN (SELECT uuid FROM archived_rows);

            GET DIAGNOSTICS row_count = ROW_COUNT;

            COMMIT;

            total_count := total_count + row_count;
            IF row_count > 0 THEN
                RAISE NOTICE 'Archived % rows in this batch, total archived rows: %', row_count, total_count;
            END IF;
        END LOOP;
    RAISE NOTICE 'Archiving process completed, total archived rows: %', total_count;
END
$$;
