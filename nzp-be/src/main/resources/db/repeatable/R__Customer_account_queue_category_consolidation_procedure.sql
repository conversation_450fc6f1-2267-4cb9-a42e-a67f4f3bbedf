CREATE OR REPLACE PROCEDURE fix_customer_account_queue_categories(dryrun boolean DEFAULT false)
    LANGUAGE plpgsql AS
$$
DECLARE
    processed_count INT;
    main_select_index RECORD;
BEGIN

    CREATE TEMPORARY TABLE temp_fixed_queues AS
    SELECT * FROM (
                      select ca.uuid, ca.email, array_agg(distinct bp.queue) as bp_queues, count(distinct bp.queue) as bp_queues_count, ca.queue_category as current_caqueue,
                             (case when ca.queue_category = '0' then 'COLLECTIVE' when ca.queue_category = '1' then 'INDIVIDUAL' when ca.queue_category = '2' then 'ALL' end) as current_caqueue_text,
                             (case when count(distinct bp.queue) > 1 then 'ALL' else (array_agg(distinct bp.queue))[1] end) as new_caqueue_text,
                             (case when count(distinct bp.queue) > 1 then '2' when ((array_agg(distinct bp.queue))[1]) = 'INDIVIDUAL' then '1' else '0' end) as new_caqueue
                      from customer_account ca
                               left outer join business_partner_ownership bpo on bpo.grantor_customer_account_uuid = ca.uuid and bpo."type" = 'OWNER'
                               join business_partner bp on bp.id = bpo.business_partner_id and bp.queue is not null
                      where ca.queue_category is not null or bp.queue is not null
                      group by ca.uuid, ca.email
                  ) AS complex_select;

    RAISE NOTICE '%s : Temporary table temp_fixed_queues created', now();

    processed_count := 0;

    FOR main_select_index IN SELECT * FROM temp_fixed_queues where current_caqueue_text != new_caqueue_text LOOP

            if dryrun then
                RAISE NOTICE 'DRY RUN: Change queue category for account % (%), % (%) -> % (%)', main_select_index.uuid, main_select_index.email, main_select_index.current_caqueue_text, main_select_index.current_caqueue, main_select_index.new_caqueue_text, main_select_index.new_caqueue;
            else
                update customer_account set queue_category = main_select_index.new_caqueue, updated_at = now(), version = version + 1 where uuid = main_select_index.uuid;
                RAISE NOTICE 'Change queue category for account % (%), % (%) -> % (%)', main_select_index.uuid, main_select_index.email, main_select_index.current_caqueue_text, main_select_index.current_caqueue, main_select_index.new_caqueue_text, main_select_index.new_caqueue;
            end if;

            processed_count := processed_count + 1;
            if(processed_count % 500 = 0) then
                commit;
                RAISE NOTICE '%s : Processed records: %', now(), processed_count;
            end if;

        END LOOP;

    RAISE NOTICE '%s : Final count of processed records: %', now(), processed_count;

    RAISE NOTICE '%s : Dropping temporary table temp_fixed_queues', now();
    DROP TABLE temp_fixed_queues;
    RAISE NOTICE '%s : Temporary table temp_fixed_queues dropped', now();
END
$$;
