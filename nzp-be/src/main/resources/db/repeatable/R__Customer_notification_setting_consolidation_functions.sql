CREATE OR REPLACE PROCEDURE fix_notification_settings_checkboxes(batch_size int)
    LANGUAGE plpgsql AS
$$
DECLARE
    main_select_index RECORD;
    template_group_member_index uuid;
    sharing_group_count INT;
    sharing_group_ids uuid[];
    customer_request_group_count INT;
    customer_request_group_ids uuid[];
    invoice_group_count INT;
    invoice_group_ids uuid[];
    advance_invoice_group_count INT;
    advance_invoice_group_ids uuid[];
    limit_overflow_group_count INT;
    limit_overflow_group_ids uuid[];
    current_group_ids uuid[];
    processed_count INT;
    missing_template_ids uuid[];
BEGIN

    DELETE from customer_notification_setting cns2
    where cns2.uuid in (
        select cns.uuid
        from customer_notification_setting cns
                 join notification_template nt on cns.notification_template_id = nt.uuid and nt.template_group is null
    );
    RAISE NOTICE 'Deleted customer_notification_settings for notification_templates without template_group';

    SELECT array_agg(uuid) INTO sharing_group_ids
    FROM notification_template
    WHERE template_group = 'SHARING';

    RAISE NOTICE 'Ids of notification templates for template_group SHARING: %', sharing_group_ids;

    SELECT count(*) INTO sharing_group_count
    FROM notification_template
    WHERE template_group = 'SHARING';

    RAISE NOTICE 'Number of notification templates for template_group SHARING: %', sharing_group_count;

    SELECT array_agg(uuid) INTO invoice_group_ids
    FROM notification_template
    WHERE template_group = 'INVOICE';

    RAISE NOTICE 'Ids of notification templates for template_group INVOICE: %', invoice_group_ids;

    SELECT count(*) INTO invoice_group_count
    FROM notification_template
    WHERE template_group = 'INVOICE';

    RAISE NOTICE 'Number of notification templates for template_group INVOICE: %', invoice_group_count;

    SELECT array_agg(uuid) INTO advance_invoice_group_ids
    FROM notification_template
    WHERE template_group = 'ADVANCE_INVOICE';

    RAISE NOTICE 'Ids of notification templates for template_group ADVANCE_INVOICE: %', advance_invoice_group_ids;

    SELECT count(*) INTO advance_invoice_group_count
    FROM notification_template
    WHERE template_group = 'ADVANCE_INVOICE';

    RAISE NOTICE 'Number of notification templates for template_group ADVANCE_INVOICE: %', advance_invoice_group_count;

    SELECT array_agg(uuid) INTO customer_request_group_ids
    FROM notification_template
    WHERE template_group = 'CUSTOMER_REQUEST';

    RAISE NOTICE 'Ids of notification templates for template_group CUSTOMER_REQUEST: %', customer_request_group_ids;

    SELECT count(*) INTO customer_request_group_count
    FROM notification_template
    WHERE template_group = 'CUSTOMER_REQUEST';

    RAISE NOTICE 'Number of notification templates for template_group CUSTOMER_REQUEST: %', customer_request_group_count;

    SELECT array_agg(uuid) INTO limit_overflow_group_ids
    FROM notification_template
    WHERE template_group = 'LIMIT_OVERFLOW';

    RAISE NOTICE 'Ids of notification templates for template_group LIMIT_OVERFLOW: %', limit_overflow_group_ids;

    SELECT count(*) INTO limit_overflow_group_count
    FROM notification_template
    WHERE template_group = 'LIMIT_OVERFLOW';

    RAISE NOTICE 'Number of notification templates for template_group LIMIT_OVERFLOW: %', limit_overflow_group_count;

    RAISE NOTICE '%s : Creating temporary table temp_record', now();

    -- Step 1: Create a temporary table to store results of the complex select
    CREATE TEMPORARY TABLE temp_records AS
    SELECT * FROM (
                      select nastavenia_stats.*,
                             (case when nastavenia_stats.sms_null = nastavenia_stats.pocet then null else case when nastavenia_stats.sms_enabled > (nastavenia_stats.pocet / 2.0) then true else false end end) as sms_new_value,
                             (case when nastavenia_stats.email_null = nastavenia_stats.pocet then null else case when nastavenia_stats.email_enabled >= (nastavenia_stats.pocet / 2.0) then true else false end end) as email_new_value,
                             (case when nastavenia_stats.push_null = nastavenia_stats.pocet then null else case when nastavenia_stats.push_enabled >= (nastavenia_stats.pocet / 2.0) then true else false end end) as push_new_value
                      from (
                               SELECT cns.customer_account_id,
                                      cns.united_delivery_point_id,
                                      nt.template_group,
                                      (case
                                           when nt.template_group = 'CUSTOMER_REQUEST' then customer_request_group_count
                                           when nt.template_group = 'INVOICE' then invoice_group_count
                                           when nt.template_group = 'ADVANCE_INVOICE' then advance_invoice_group_count
                                           when nt.template_group = 'SHARING' then sharing_group_count
                                           when nt.template_group = 'LIMIT_OVERFLOW' then limit_overflow_group_count
                                           else -1
                                          end) as expected_pocet,
                                      array_agg(nt.uuid) existing_notif_settings_template_uids,
                                      count(*) as pocet,
                                      (select count(*) from customer_notification_setting cns2 join notification_template nt2 on nt2.uuid = cns2.notification_template_id and cns.customer_account_id = cns2.customer_account_id and cns.united_delivery_point_id = cns2.united_delivery_point_id and nt.template_group = nt2.template_group and sms is true) sms_enabled,
                                      (select count(*) from customer_notification_setting cns2 join notification_template nt2 on nt2.uuid = cns2.notification_template_id and cns.customer_account_id = cns2.customer_account_id and cns.united_delivery_point_id = cns2.united_delivery_point_id and nt.template_group = nt2.template_group and sms is null) sms_null,
                                      (select count(*) from customer_notification_setting cns4 join notification_template nt4 on nt4.uuid = cns4.notification_template_id and cns.customer_account_id = cns4.customer_account_id and cns.united_delivery_point_id = cns4.united_delivery_point_id and nt.template_group = nt4.template_group and email is true) email_enabled,
                                      (select count(*) from customer_notification_setting cns4 join notification_template nt4 on nt4.uuid = cns4.notification_template_id and cns.customer_account_id = cns4.customer_account_id and cns.united_delivery_point_id = cns4.united_delivery_point_id and nt.template_group = nt4.template_group and email is null) email_null,
                                      (select count(*) from customer_notification_setting cns6 join notification_template nt6 on nt6.uuid = cns6.notification_template_id and cns.customer_account_id = cns6.customer_account_id and cns.united_delivery_point_id = cns6.united_delivery_point_id and nt.template_group = nt6.template_group and push is true) push_enabled,
                                      (select count(*) from customer_notification_setting cns6 join notification_template nt6 on nt6.uuid = cns6.notification_template_id and cns.customer_account_id = cns6.customer_account_id and cns.united_delivery_point_id = cns6.united_delivery_point_id and nt.template_group = nt6.template_group and push is null) push_null
                               from customer_notification_setting cns
                                        join notification_template nt on cns.notification_template_id = nt.uuid and template_group is not null
                               group by cns.customer_account_id, cns.united_delivery_point_id, nt.template_group
                               order by cns.customer_account_id, cns.united_delivery_point_id, nt.template_group) nastavenia_stats
                      where nastavenia_stats.pocet != nastavenia_stats.expected_pocet
                         or (nastavenia_stats.sms_enabled != 0 and nastavenia_stats.sms_enabled != nastavenia_stats.pocet)
                         or (nastavenia_stats.email_enabled != 0 and nastavenia_stats.email_enabled != nastavenia_stats.pocet)
                         or (nastavenia_stats.push_enabled != 0 and nastavenia_stats.push_enabled != nastavenia_stats.pocet)
                  ) AS complex_select;

    RAISE NOTICE '%s : Temporary table temp_record created', now();

    processed_count := 0;

    FOR main_select_index IN SELECT * FROM temp_records LOOP

            case main_select_index.template_group
                when 'SHARING' then current_group_ids := sharing_group_ids;
                when 'INVOICE' then current_group_ids := invoice_group_ids;
                when 'ADVANCE_INVOICE' then current_group_ids := advance_invoice_group_ids;
                when 'CUSTOMER_REQUEST' then current_group_ids := customer_request_group_ids;
                when 'LIMIT_OVERFLOW' then current_group_ids := limit_overflow_group_ids;
                else RAISE NOTICE '!!! WARNING: UNEXPECTED template_group %. Templates for this class will be skipped!!! Please modify logic of this script to handle this template_group!!!', main_select_index.template_group;
                end case;

--                 RAISE NOTICE 'TODO updateovat existujuce (%): email: %, sms: %, push: %, customerId: %, udpId: %, templateIds: %', main_select_index.template_group, main_select_index.email_new_value, main_select_index.sms_new_value, main_select_index.push_new_value, main_select_index.customer_account_id, main_select_index.united_delivery_point_id, current_group_ids;
            update customer_notification_setting cns set updated_at = NOW(),
                                                         version = cns.version + 1,
                                                         email = main_select_index.email_new_value,
                                                         sms = main_select_index.sms_new_value,
                                                         push = main_select_index.push_new_value
            where cns.customer_account_id = main_select_index.customer_account_id
              and cns.united_delivery_point_id = main_select_index.united_delivery_point_id
              and cns.notification_template_id = ANY(current_group_ids);
            missing_template_ids := ARRAY(SELECT UNNEST(current_group_ids) EXCEPT SELECT UNNEST(main_select_index.existing_notif_settings_template_uids));
            if array_length(missing_template_ids, 1) is not null then
--                     RAISE NOTICE 'budu sa vkladat hodnoty: email: %, sms: %, push: %, customerId: %, udpId: %, templateIds: %', main_select_index.email_new_value, main_select_index.sms_new_value, main_select_index.push_new_value, main_select_index.customer_account_id, main_select_index.united_delivery_point_id, missing_template_ids;
                FOREACH template_group_member_index IN ARRAY missing_template_ids
                    LOOP
                        --                             RAISE NOTICE 'insertujeme (%) email: %, sms: %, push: %, customerId: %, udpId: %, templateId: %', main_select_index.template_group, main_select_index.email_new_value, main_select_index.sms_new_value, main_select_index.push_new_value, main_select_index.customer_account_id, main_select_index.united_delivery_point_id, template_group_member_index;
                        insert into customer_notification_setting (uuid, created_at, updated_at, version, email, sms,
                                                                   customer_account_id, notification_template_id,
                                                                   united_delivery_point_id, push)
                        values (
                                   uuid_generate_v4(), NOW(), NOW(), 1, main_select_index.email_new_value, main_select_index.sms_new_value,
                                   main_select_index.customer_account_id, template_group_member_index, main_select_index.united_delivery_point_id, main_select_index.push_new_value
                               )
                        ON CONFLICT (customer_account_id, notification_template_id, united_delivery_point_id) DO UPDATE
                            SET updated_at = NOW(),
                                version = customer_notification_setting.version + 1,
                                email = EXCLUDED.email,
                                sms = EXCLUDED.sms,
                                push = EXCLUDED.push;
                    END LOOP;
            end if;

            processed_count := processed_count + 1;
            if(processed_count % batch_size = 0) then
                commit;
                RAISE NOTICE '%s : Processed records: %', now(), processed_count;
            end if;

        END LOOP;

    RAISE NOTICE '%s : Final count of processed records: %', now(), processed_count;

    RAISE NOTICE '%s : Dropping temporary table temp_record', now();
    DROP TABLE temp_records;
    RAISE NOTICE '%s : Temporary table temp_record dropped', now();
END
$$;


CREATE OR REPLACE PROCEDURE fix_notification_settings_checkboxes_for_template_group(template_group_name text, batch_size int)
    LANGUAGE plpgsql AS
$$
DECLARE
    main_select_index RECORD;
    template_group_member_index uuid;
    group_count INT;
    group_ids uuid[];
    processed_count INT;
    missing_template_ids uuid[];
BEGIN

    DELETE from customer_notification_setting cns2
    where cns2.uuid in (select cns.uuid
                        from customer_notification_setting cns
                                 join notification_template nt
                                      on cns.notification_template_id = nt.uuid and nt.template_group is null
    );
    RAISE NOTICE 'Deleted customer_notification_settings for notification_templates without template_group';

    SELECT array_agg(uuid) INTO group_ids
    FROM notification_template
    WHERE template_group = template_group_name;

    RAISE NOTICE 'Ids of notification templates for template_group %: %', template_group_name, group_ids;

    SELECT count(*) INTO group_count
    FROM notification_template
    WHERE template_group = template_group_name;

    RAISE NOTICE 'Number of notification templates for template_group %: %', template_group_name, group_count;

    RAISE NOTICE '%s : Creating temporary table temp_record', now();

    -- Step 1: Create a temporary table to store results of the complex select
    CREATE TEMPORARY TABLE temp_records AS
    SELECT * FROM (
                      select nastavenia_stats.*,
                             (case when nastavenia_stats.sms_null = nastavenia_stats.pocet then null else case when nastavenia_stats.sms_enabled > (nastavenia_stats.pocet / 2.0) then true else false end end) as sms_new_value,
                             (case when nastavenia_stats.email_null = nastavenia_stats.pocet then null else case when nastavenia_stats.email_enabled >= (nastavenia_stats.pocet / 2.0) then true else false end end) as email_new_value,
                             (case when nastavenia_stats.push_null = nastavenia_stats.pocet then null else case when nastavenia_stats.push_enabled >= (nastavenia_stats.pocet / 2.0) then true else false end end) as push_new_value
                      from (
                               SELECT cns.customer_account_id,
                                      cns.united_delivery_point_id,
                                      nt.template_group,
                                      group_count as expected_pocet,
                                      array_agg(nt.uuid) existing_notif_settings_template_uids,
                                      count(*) as pocet,
                                      (select count(*) from customer_notification_setting cns2 join notification_template nt2 on nt2.uuid = cns2.notification_template_id and cns.customer_account_id = cns2.customer_account_id and cns.united_delivery_point_id = cns2.united_delivery_point_id and nt.template_group = nt2.template_group and sms is true) sms_enabled,
                                      (select count(*) from customer_notification_setting cns2 join notification_template nt2 on nt2.uuid = cns2.notification_template_id and cns.customer_account_id = cns2.customer_account_id and cns.united_delivery_point_id = cns2.united_delivery_point_id and nt.template_group = nt2.template_group and sms is null) sms_null,
                                      (select count(*) from customer_notification_setting cns4 join notification_template nt4 on nt4.uuid = cns4.notification_template_id and cns.customer_account_id = cns4.customer_account_id and cns.united_delivery_point_id = cns4.united_delivery_point_id and nt.template_group = nt4.template_group and email is true) email_enabled,
                                      (select count(*) from customer_notification_setting cns4 join notification_template nt4 on nt4.uuid = cns4.notification_template_id and cns.customer_account_id = cns4.customer_account_id and cns.united_delivery_point_id = cns4.united_delivery_point_id and nt.template_group = nt4.template_group and email is null) email_null,
                                      (select count(*) from customer_notification_setting cns6 join notification_template nt6 on nt6.uuid = cns6.notification_template_id and cns.customer_account_id = cns6.customer_account_id and cns.united_delivery_point_id = cns6.united_delivery_point_id and nt.template_group = nt6.template_group and push is true) push_enabled,
                                      (select count(*) from customer_notification_setting cns6 join notification_template nt6 on nt6.uuid = cns6.notification_template_id and cns.customer_account_id = cns6.customer_account_id and cns.united_delivery_point_id = cns6.united_delivery_point_id and nt.template_group = nt6.template_group and push is null) push_null
                               from customer_notification_setting cns
                                        join notification_template nt on cns.notification_template_id = nt.uuid and template_group = template_group_name
                               group by cns.customer_account_id, cns.united_delivery_point_id, nt.template_group
                               order by cns.customer_account_id, cns.united_delivery_point_id, nt.template_group) nastavenia_stats
                      where nastavenia_stats.pocet != nastavenia_stats.expected_pocet
                         or (nastavenia_stats.sms_enabled != 0 and nastavenia_stats.sms_enabled != nastavenia_stats.pocet)
                         or (nastavenia_stats.email_enabled != 0 and nastavenia_stats.email_enabled != nastavenia_stats.pocet)
                         or (nastavenia_stats.push_enabled != 0 and nastavenia_stats.push_enabled != nastavenia_stats.pocet)
                  ) AS complex_select;

    RAISE NOTICE '%s : Temporary table temp_record created', now();

    processed_count := 0;

    FOR main_select_index IN SELECT * FROM temp_records LOOP

            --                 RAISE NOTICE 'TODO updateovat existujuce (%): email: %, sms: %, push: %, customerId: %, udpId: %, templateIds: %', main_select_index.template_group, main_select_index.email_new_value, main_select_index.sms_new_value, main_select_index.push_new_value, main_select_index.customer_account_id, main_select_index.united_delivery_point_id, current_group_ids;
            update customer_notification_setting cns set updated_at = NOW(),
                                                         version = cns.version + 1,
                                                         email = main_select_index.email_new_value,
                                                         sms = main_select_index.sms_new_value,
                                                         push = main_select_index.push_new_value
            where cns.customer_account_id = main_select_index.customer_account_id
              and cns.united_delivery_point_id = main_select_index.united_delivery_point_id
              and cns.notification_template_id = ANY(group_ids);
            missing_template_ids := ARRAY(SELECT UNNEST(group_ids) EXCEPT SELECT UNNEST(main_select_index.existing_notif_settings_template_uids));
            if array_length(missing_template_ids, 1) is not null then
--                     RAISE NOTICE 'budu sa vkladat hodnoty: email: %, sms: %, push: %, customerId: %, udpId: %, templateIds: %', main_select_index.email_new_value, main_select_index.sms_new_value, main_select_index.push_new_value, main_select_index.customer_account_id, main_select_index.united_delivery_point_id, missing_template_ids;
                FOREACH template_group_member_index IN ARRAY missing_template_ids
                    LOOP
                        --                             RAISE NOTICE 'insertujeme (%) email: %, sms: %, push: %, customerId: %, udpId: %, templateId: %', main_select_index.template_group, main_select_index.email_new_value, main_select_index.sms_new_value, main_select_index.push_new_value, main_select_index.customer_account_id, main_select_index.united_delivery_point_id, template_group_member_index;
                        insert into customer_notification_setting (uuid, created_at, updated_at, version, email, sms,
                                                                   customer_account_id, notification_template_id,
                                                                   united_delivery_point_id, push)
                        values (
                                   uuid_generate_v4(), NOW(), NOW(), 1, main_select_index.email_new_value, main_select_index.sms_new_value,
                                   main_select_index.customer_account_id, template_group_member_index, main_select_index.united_delivery_point_id, main_select_index.push_new_value
                               )
                        ON CONFLICT (customer_account_id, notification_template_id, united_delivery_point_id) DO UPDATE
                            SET updated_at = NOW(),
                                version = customer_notification_setting.version + 1,
                                email = EXCLUDED.email,
                                sms = EXCLUDED.sms,
                                push = EXCLUDED.push;
                    END LOOP;
            end if;

            processed_count := processed_count + 1;
            if(processed_count % batch_size = 0) then
                commit;
                RAISE NOTICE '%s : Processed records: %', now(), processed_count;
            end if;

        END LOOP;

    RAISE NOTICE '%s : Final count of processed records: %', now(), processed_count;

    RAISE NOTICE '%s : Dropping temporary table temp_record', now();
    DROP TABLE temp_records;
    RAISE NOTICE '%s : Temporary table temp_record dropped', now();
END
$$;
