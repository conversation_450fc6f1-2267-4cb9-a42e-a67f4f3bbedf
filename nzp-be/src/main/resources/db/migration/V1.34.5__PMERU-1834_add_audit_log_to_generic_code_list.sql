INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'BUSINESS_PARTNER_CONSENT_ACCEPT','AUDIT_LOG_CODE', null, null, null);


INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'BUSINESS_PARTNER_CONSENT_REJECT','AUDIT_LOG_CODE', null, null, null);


INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Udelenie marketingového súhlasu', 'Udelenie marketingového súhlasu',
(select uuid from generic_code_list where code = 'BUSINESS_PARTNER_CONSENT_ACCEPT' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Granting marketing consent', 'Granting marketing consent',
(select uuid from generic_code_list where code = 'BUSINESS_PARTNER_CONSENT_ACCEPT' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Odobratie marketingového súhlasu', 'Odobratie marketingového súhlasu',
(select uuid from generic_code_list where code = 'BUSINESS_PARTNER_CONSENT_REJECT' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Rejecting marketing consent', 'Rejecting marketing consent',
(select uuid from generic_code_list where code = 'BUSINESS_PARTNER_CONSENT_REJECT' and type = 'AUDIT_LOG_CODE'));