update notification_template_i18n nti
set sms_body = null
where nti.sms_body is not null and length(nti.sms_body) = 0;

insert into notification_template_i18n (select uuid_generate_v4(), now(), now(), 1, nti.header, '[EN] ' || nti.email_body, '[EN] ' || nti.email_subject, nti.sms_body, nti.notification_template_id, 'EN', nti.status from notification_template_i18n nti where nti.locale in ('sk', 'SK') and nti.email_body is not null and nti.sms_body is null);
insert into notification_template_i18n (select uuid_generate_v4(), now(), now(), 1, nti.header, nti.email_body, nti.email_subject, '[EN] ' || nti.sms_body, nti.notification_template_id, 'EN', nti.status from notification_template_i18n nti where nti.locale in ('sk', 'SK') and nti.email_body is null and nti.sms_body is not null);

insert into notification_template_i18n (select uuid_generate_v4(), now(), now(), 1, '<#switch attributes.notificationType!"">
  <#case "SMS">
     <#assign forma="formou SMS správy">
     <#break>
  <#case "EMAIL">
     <#assign forma="formou e-mailu">
     <#break>
  <#case "LIST">
     <#assign forma="formou listu">
     <#break>
  <#default>
     <#assign forma="">
</#switch>
[EN] Dňa ${attributes.notificationSend} Vám bola zaslaná upomienka ${forma}', nti.email_body, nti.email_subject, nti.sms_body, nti.notification_template_id, 'EN', nti.status from notification_template_i18n nti where nti.locale in ('sk', 'SK') and nti.email_body is null and nti.sms_body is null);