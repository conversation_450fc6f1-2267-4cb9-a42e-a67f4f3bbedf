-- RENAME & DEACTIVATE CURRENT TEMPLATE
UPDATE customer_request_template SET
    code = 'ZOM_ZODP_PDF',
    status = 'INACTIVE',
    updated_at = now(),
    "version" = "version" + 1
WHERE code = 'ZOM_ZODP';

-- CREATE NEW TEMPLATE
INSERT INTO customer_request_template(
	uuid, created_at, updated_at, version, status, code, price, type, link)
VALUES (
    uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZODP', null, 'DIGITAL', null);

-- CREATE NEW TEMPLATE I18Ns
INSERT INTO customer_request_template_i18n (
    uuid, created_at, updated_at, version, locale, name, description, customer_request_template_uuid)
VALUES (
    uuid_generate_v4(), now(), now(), 1, 'SK', (
        SELECT name FROM customer_request_template_i18n
        WHERE customer_request_template_uuid = (
            SELECT uuid FROM customer_request_template
            WHERE code = 'ZOM_ZODP_PDF')
        AND LOWER(locale) = 'sk'), (
        SELECT description FROM customer_request_template_i18n
        WHERE customer_request_template_uuid = (
            SELECT uuid FROM customer_request_template
            WHERE code = 'ZOM_ZODP_PDF')
        AND LOWER(locale) = 'sk'), (
        SELECT uuid FROM customer_request_template
        WHERE code = 'ZOM_ZODP'));

INSERT INTO customer_request_template_i18n (
    uuid, created_at, updated_at, version, locale, name, description, customer_request_template_uuid)
VALUES (
    uuid_generate_v4(), now(), now(), 1, 'EN', (
        SELECT name FROM customer_request_template_i18n
        WHERE customer_request_template_uuid = (
            SELECT uuid FROM customer_request_template
            WHERE code = 'ZOM_ZODP_PDF')
        AND LOWER(locale) = 'en'), (
        SELECT description FROM customer_request_template_i18n
        WHERE customer_request_template_uuid = (
            SELECT uuid FROM customer_request_template
            WHERE code = 'ZOM_ZODP_PDF')
        AND LOWER(locale) = 'en'), (
        SELECT uuid FROM customer_request_template crt
        WHERE crt.code = 'ZOM_ZODP'));

-- UPDATE OLD TEMPLATE I18Ns
UPDATE customer_request_template_i18n SET
    "name" = CONCAT(name, ' (PDF)'),
    description = CONCAT(description, ' (PDF)'),
    updated_at = now(),
    "version" = "version" + 1
WHERE customer_request_template_uuid = (
    SELECT uuid FROM customer_request_template
    WHERE code = 'ZOM_ZODP_PDF');

-- UPDATE EXISTING REQUEST TYPES
UPDATE customer_request
SET "content" = jsonb_set("content"::jsonb, '{type}', '"ZOM_ZODP_PDF"')
WHERE customer_request_template_id = (
     SELECT uuid FROM customer_request_template
     WHERE code = 'ZOM_ZODP_PDF');