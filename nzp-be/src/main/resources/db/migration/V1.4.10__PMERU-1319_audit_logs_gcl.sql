
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Registrácia už registrovaného zákazníka', 'Registrácia už registrovaného zákazníka. Zákazník bude informovaný', (select uuid from generic_code_list where code like 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Customer account already exists', '[EN] Customer account already exists. Customer will be informed.', (select uuid from generic_code_list where code like 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and type = 'AUDIT_LOG_CODE'));
