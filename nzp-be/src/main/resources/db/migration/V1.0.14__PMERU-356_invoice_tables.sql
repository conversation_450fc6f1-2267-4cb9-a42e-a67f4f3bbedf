----------
-- invoice
----------

CREATE TABLE invoice
(
    uuid                         uuid                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      integer                   NOT NULL,
    
    external_id                  CHARACTER VARYING(50)     NOT NULL,
    status                       CHARACTER VARYING(20)     NOT NULL,
    type                         CHARACTER VARYING(50)     NOT NULL,
    amount                       NUMERIC(19,2)             NOT NULL,
    currency                     CHARACTER VARYING(50)     NOT NULL,
    due_at                       DATE                      NOT NULL,
    issue_at                     DATE                      NOT NULL,
    execute_at                   DATE                      NOT NULL,
    vs                           CHARACTER VARYING(50),
    
    delivery_point_id            uuid,

    CONSTRAINT pk_invoice PRIMARY KEY (uuid),
    CONSTRAINT fk_invoice_delivery_point FOREIGN KEY (delivery_point_id)
        REFERENCES delivery_point (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_invoice_external_id on invoice(external_id);
CREATE INDEX idx_invoice_delivery_point_id on invoice(delivery_point_id);