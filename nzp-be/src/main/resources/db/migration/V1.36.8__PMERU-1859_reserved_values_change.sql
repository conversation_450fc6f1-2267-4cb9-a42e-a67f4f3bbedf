-- CREATE TEMPLATE
INSERT INTO notification_template (
    uuid, created_at, updated_at, version, code, status, type, execution_type, name, priority, description, attributes, template_group, default_email, default_sms, enable_email, enable_sms, enable_portal, max_process_time)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REQUEST_RESERVED_VALUES', 'ACTIVE', 'CUSTOMER_SYSTEM', 'AUTOMATIC', 'Žiadosť o zmenu rezervovaných kapacít', 'LOW', 'Notifikácia ohľadom žiadosti o zmenu rezervovaných kapacít', null, null, true, false, true, false, false, 432000000);

-- CREATE i18ns
INSERT INTO notification_template_i18n (
    uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status, header_url)
VALUES (
    uuid_generate_v4(), now(), now(), 1, NULL,
    '<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník ${businessPartner.name} priradený ku Vám požiadal o zmenu rezervovanej kapacity typu ${attributes.reservedValueType} na odbernom mieste ${deliveryPoint.street} ${deliveryPoint.streetNumber}</@spp.notification_email_template>',
    'Žiadosť o zmenu rezervovaných kapacít',
    NULL,
    (SELECT uuid FROM notification_template
        WHERE code = 'CUSTOMER_REQUEST_RESERVED_VALUES'),
    'SK', 'ACTIVE', NULL), (

    uuid_generate_v4(), now(), now(), 1, NULL,
    '<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník ${businessPartner.name} priradený ku Vám požiadal o zmenu rezervovanej kapacity typu ${attributes.reservedValueType} na odbernom mieste ${deliveryPoint.street} ${deliveryPoint.streetNumber}</@spp.notification_email_template>',
    '[EN] Žiadosť o zmenu rezervovaných kapacít',
    NULL,
    (SELECT uuid FROM notification_template
        WHERE code = 'CUSTOMER_REQUEST_RESERVED_VALUES'),
    'EN', 'ACTIVE', NULL);
    
-- CREATE GENERIC TEMPLATE VARIABLES
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.email', 'Email zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.firstName', 'Meno zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'logged.customer.email', 'Email zalogovaného používateľa', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'logged.customer.firstName', 'Meno zalogovaného používateľa', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'logged.customer.lastName', 'Priezvisko zalogovaného používateľa', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'logged.customer.phone', 'Telefónne číslo zalogovaného používateľa', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'notificationTemplateCode', 'Kód notifikácie', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácie cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', 'Týka sa iba SAP entít.', 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'locale', 'Lokalizácia', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.name', 'Názov obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.firstName', 'Meno obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.lastName', 'Priezvisko obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.email', 'Email obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.phone', 'Telefonné číslo obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.street', 'Ulica obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.streetNumber', 'Číslo domu obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.city', 'Obec obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.zipCode', 'PSČ obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.country', 'Štát obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amFirstName', 'Obchodný partner - meno manažéra predaja', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amLastName', 'Obchodný partner - priezvisko manažéra predaja', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amEmail', 'Obchodný partner - email manažéra predaja', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amPhone', 'Obchodný partner - telefónne číslo manažéra predaja', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amName', 'Obchodný partner - meno pobočky', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.externalId', 'SAP ID obchodného partnera', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.kindCode', 'Kód druhu obchodného partnera', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.externalId', 'SAP ID žiadosti', 'Dostupné iba ak bola žiadosť zaevidovaná v SAPe', 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.name', 'Názov žiadosti', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.status.code', 'Kód stavu žiadosti', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.status.name', 'Stav žiadosti', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.registeredAt', 'Dátum registrácie v SAPe', 'Dátum odoslania žiadosti do SAPu', 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.externalId', 'SAP ID žiadosti', 'Dostupné iba ak bola žiadosť zaevidovaná v SAPe', 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.confirmedAt', 'Dátum a čas potvrdenia žiadosti', 'Dostupné iba ak bola žiadosť potvrdená zákazníkom', 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');

-- CREATE ADITIONAL TEMPLATE VARIABLES
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contract.billCycle', 'Zmluva - fakturačný cyklus', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contract.eeTariff', 'Zmluva - eeTariff', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contract.effectiveFrom', 'Zmluva - účinnosť od', null, 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contract.effectiveTo', 'Zmluva - účinnosť do', null, 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contract.product', 'Produkt zmluvy', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.city', 'Zmluvný účet - obec', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.country', 'Zmluvný účet - štát', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.email', 'Email zmluvného účtu', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.firstName', 'Zmluvný účet - meno', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.lastName', 'Zmluvný účet - priezvisko', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.name', 'Zmluvný účet - názov organizácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.postCity', 'Zmluvný účet - korešpondenčná adresa -  mesto', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.postCountry', 'Zmluvný účet - korešpondenčná adresa - štát', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.postStreet', 'Zmluvný účet - korešpondenčná adresa -  ulica', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.postStreetNumber', 'Zmluvný účet - korešpondenčná adresa - popisné číslo', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.postZipCode', 'Zmluvný účet - korešpondenčná adresa -  PSČ', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.street', 'Zmluvný účet - ulica', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.streetNumber', 'Zmluvný účet - číslo', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'contractAccount.zipCode', 'Zmluvný účet - PSČ', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'deliveryPoint.category', 'Kategória odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'deliveryPoint.city', 'Obec odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'deliveryPoint.country', 'Štát odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'deliveryPoint.eic', 'Odberné miesto - eic', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'deliveryPoint.pod', 'Odberné miesto - pod', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'deliveryPoint.street', 'Ulica odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'deliveryPoint.streetNumber', 'Číslo odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'deliveryPoint.tariffRate', 'Tarifa/sadzba na odbernom mieste', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'deliveryPoint.type', 'Typ odberného miesta (skratka)', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'deliveryPoint.typeName.name', 'Typ odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'deliveryPoint.zipCode', 'PSČ odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'attributes.reservedValueType', 'Typ rezervovanej kapacity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_RESERVED_VALUES');

-- ADD RECEIVER CONFIG PARARM
INSERT INTO config_parameter
    (created_at, updated_at, version, id, value, target, type)
VALUES
    (now(), now(), 1, 'customer.request.reserved_values.email', '<EMAIL>', 'BE', 'STRING');