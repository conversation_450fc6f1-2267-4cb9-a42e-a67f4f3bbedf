-----------------
-- united_delivery_point
-----------------

CREATE TABLE united_delivery_point
(
    uuid                         uuid                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      integer                   NOT NULL,
    
    udp_key                        CHARACTER VARYING(128)  NOT NULL,

    street                       CHARACTER VARYING(64),
    street_number                CHARACTER VARYING(32),
    city                         CHARACTER VARYING(64),
    zip_code                     CHARACTER VARYING(32),

    status                       CHARACTER VARYING(50)     NOT NULL,
    hidden                       boolean,

    business_partner_external_id    CHARACTER VARYING(50),

    CONSTRAINT pk_united_delivery_point PRIMARY KEY (uuid)
);

-- indexes
CREATE INDEX idx_united_delivery_point_external_id on united_delivery_point(business_partner_external_id);
CREATE UNIQUE INDEX idx_united_delivery_udp_key on united_delivery_point(udp_key);



-----------------
-- delivery_point
-----------------
alter table delivery_point add column united_delivery_point_id          uuid;
alter table delivery_point add column business_partner_external_id      CHARACTER VARYING(50)    NOT NULL;

alter table delivery_point add constraint fk_delivery_point_united_delivery_point
FOREIGN KEY (united_delivery_point_id) REFERENCES united_delivery_point (uuid);