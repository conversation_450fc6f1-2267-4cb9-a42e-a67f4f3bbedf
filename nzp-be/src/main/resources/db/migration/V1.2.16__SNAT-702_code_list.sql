delete from audit_log where code in ('CUSTOMER_NOTIFICATION_PUBLISHED');

INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'NOTIFICATION_TEMPLATE_CREATE', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk', 'Vytvorenie šablóny notifikácie', null, (select uuid from generic_code_list where code like 'NOTIFICATION_TEMPLATE_CREATE' and type = 'AUDIT_LOG_CODE'));