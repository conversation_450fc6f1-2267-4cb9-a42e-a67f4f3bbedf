
ALTER TABLE customer_request ADD COLUMN registered boolean;
UPDATE customer_request SET status = 'SAP_FINISHED' where status = 'FINISHED';
UPDATE customer_request SET status = 'SAP_CANCELLED' where status = 'CANCELLED';
UPDATE customer_request SET status = 'CREATED' where status = 'CREATED_AS_PAPER';
UPDATE customer_request SET status = 'SAP_CANCELLED_BY_USER' where status = 'CANCELLATION';
UPDATE customer_request SET status = 'CANCELLED_BY_USER' where status = 'CANCELLED_BY_CUSTOMER';
