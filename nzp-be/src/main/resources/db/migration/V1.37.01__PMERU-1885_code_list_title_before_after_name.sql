INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, '01', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '02', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '03', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '04', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '05', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '06', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '07', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '08', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '09', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '10', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '11', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '12', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '13', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '14', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '15', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '16', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '17', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '18', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '19', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '20', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '21', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '22', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '23', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '24', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '25', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '26', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '27', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '28', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '29', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '30', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '31', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '32', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '33', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '34', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '35', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '36', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '37', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '38', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '39', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '40', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '41', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '42', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '43', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '44', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '45', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '46', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '47', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '48', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '49', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '50', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '51', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '52', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '53', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '54', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '55', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '56', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '57', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '58', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '59', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '60', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '61', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '62', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '63', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '64', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '65', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '66', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '67', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '68', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '69', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '70', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '71', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '72', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '73', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '74', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '75', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '76', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '77', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '78', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '79', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '80', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '81', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '82', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '83', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '84', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '85', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '86', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '87', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '88', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '89', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '90', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '91', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '92', 'TITLE_FRONT', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '93', 'TITLE_FRONT', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Ing.', 'inžinier', (select uuid from generic_code_list where code = '01' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Ing.', '[EN] inžinier', (select uuid from generic_code_list where code = '01' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Ing. arch.', 'inžinier architekt', (select uuid from generic_code_list where code = '02' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Ing. arch.', '[EN] inžinier architekt', (select uuid from generic_code_list where code = '02' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'JUDr.', 'doktor práv', (select uuid from generic_code_list where code = '03' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'JUDr.', '[EN] doktor práv', (select uuid from generic_code_list where code = '03' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'MUDr.', 'doktor všeobecného lekárstva', (select uuid from generic_code_list where code = '04' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'MUDr.', '[EN] doktor všeobecného lekárstva', (select uuid from generic_code_list where code = '04' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'MVDr.', 'doktor veterinárskeho lekárstva', (select uuid from generic_code_list where code = '05' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'MVDr.', '[EN] doktor veterinárskeho lekárstva', (select uuid from generic_code_list where code = '05' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'RNDr.', 'doktor prírodných vied', (select uuid from generic_code_list where code = '06' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'RNDr.', '[EN] doktor prírodných vied', (select uuid from generic_code_list where code = '06' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'RSDr.', 'doktor sociálno-politických vied', (select uuid from generic_code_list where code = '07' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'RSDr.', '[EN] doktor sociálno-politických vied', (select uuid from generic_code_list where code = '07' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'PhDr.', 'doktor filozofie', (select uuid from generic_code_list where code = '08' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'PhDr.', '[EN] doktor filozofie', (select uuid from generic_code_list where code = '08' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'PhMr.', 'magister farmácie', (select uuid from generic_code_list where code = '09' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'PhMr.', '[EN] magister farmácie', (select uuid from generic_code_list where code = '09' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'ThDr.', 'doktor teológie', (select uuid from generic_code_list where code = '10' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'ThDr.', '[EN] doktor teológie', (select uuid from generic_code_list where code = '10' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'akad. arch.', 'akademický architekt', (select uuid from generic_code_list where code = '11' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'akad. arch.', '[EN] akademický architekt', (select uuid from generic_code_list where code = '11' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'akad. mal.', 'akademický maliar', (select uuid from generic_code_list where code = '12' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'akad. mal.', '[EN] akademický maliar', (select uuid from generic_code_list where code = '12' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'akad. soch.', 'akademický sochár', (select uuid from generic_code_list where code = '13' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'akad. soch.', '[EN] akademický sochár', (select uuid from generic_code_list where code = '13' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'arch.', 'architekt', (select uuid from generic_code_list where code = '14' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'arch.', '[EN] architekt', (select uuid from generic_code_list where code = '14' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Bc.', 'bakalár', (select uuid from generic_code_list where code = '15' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Bc.', '[EN] bakalár', (select uuid from generic_code_list where code = '15' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Mgr.', 'magister', (select uuid from generic_code_list where code = '16' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Mgr.', '[EN] magister', (select uuid from generic_code_list where code = '16' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'PaedDr.', 'doktor predagogiky', (select uuid from generic_code_list where code = '17' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'PaedDr.', '[EN] doktor predagogiky', (select uuid from generic_code_list where code = '17' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'PharmDr.', 'doktor farmácie', (select uuid from generic_code_list where code = '18' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'PharmDr.', '[EN] doktor farmácie', (select uuid from generic_code_list where code = '18' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'ThLic.', 'licenciát teológie', (select uuid from generic_code_list where code = '19' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'ThLic.', '[EN] licenciát teológie', (select uuid from generic_code_list where code = '19' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. biol.', 'promovaný biológ', (select uuid from generic_code_list where code = '20' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. biol.', '[EN] promovaný biológ', (select uuid from generic_code_list where code = '20' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. logop.', 'promovaný logopéd', (select uuid from generic_code_list where code = '21' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. logop.', '[EN] promovaný logopéd', (select uuid from generic_code_list where code = '21' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. ek.', 'promovaný ekonóm', (select uuid from generic_code_list where code = '22' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. ek.', '[EN] promovaný ekonóm', (select uuid from generic_code_list where code = '22' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. filol.', 'promovaný filológ', (select uuid from generic_code_list where code = '23' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. filol.', '[EN] promovaný filológ', (select uuid from generic_code_list where code = '23' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. fil.', 'promovaný filozóf', (select uuid from generic_code_list where code = '24' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. fil.', '[EN] promovaný filozóf', (select uuid from generic_code_list where code = '24' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. fyz.', 'promovaný fyzik', (select uuid from generic_code_list where code = '25' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. fyz.', '[EN] promovaný fyzik', (select uuid from generic_code_list where code = '25' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. geol.', 'promovaný geológ', (select uuid from generic_code_list where code = '26' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. geol.', '[EN] promovaný geológ', (select uuid from generic_code_list where code = '26' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. hist.', 'promovaný historik', (select uuid from generic_code_list where code = '27' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. hist.', '[EN] promovaný historik', (select uuid from generic_code_list where code = '27' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. mat.', 'promovaný matematik', (select uuid from generic_code_list where code = '28' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. mat.', '[EN] promovaný matematik', (select uuid from generic_code_list where code = '28' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. nov.', 'promovaný novinár', (select uuid from generic_code_list where code = '29' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. nov.', '[EN] promovaný novinár', (select uuid from generic_code_list where code = '29' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. ped.', 'promovaný pedagóg', (select uuid from generic_code_list where code = '30' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. ped.', '[EN] promovaný pedagóg', (select uuid from generic_code_list where code = '30' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. psych.', 'promovaný psychológ', (select uuid from generic_code_list where code = '31' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. psych.', '[EN] promovaný psychológ', (select uuid from generic_code_list where code = '31' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. práv.', 'promovaný právnik', (select uuid from generic_code_list where code = '32' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. práv.', '[EN] promovaný právnik', (select uuid from generic_code_list where code = '32' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. zub.', 'promovaný zubár', (select uuid from generic_code_list where code = '33' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. zub.', '[EN] promovaný zubár', (select uuid from generic_code_list where code = '33' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. pharm.', 'promovaný farmaceut', (select uuid from generic_code_list where code = '34' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. pharm.', '[EN] promovaný farmaceut', (select uuid from generic_code_list where code = '34' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. chem.', 'promovaný chemik', (select uuid from generic_code_list where code = '35' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. chem.', '[EN] promovaný chemik', (select uuid from generic_code_list where code = '35' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. geog.', 'promovaný geológ', (select uuid from generic_code_list where code = '36' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. geog.', '[EN] promovaný geológ', (select uuid from generic_code_list where code = '36' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. vet.', 'promovaný veterinársky lekár', (select uuid from generic_code_list where code = '37' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. vet.', '[EN] promovaný veterinársky lekár', (select uuid from generic_code_list where code = '37' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prom. knih.', 'promovaný knihovník', (select uuid from generic_code_list where code = '38' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prom. knih.', '[EN] promovaný knihovník', (select uuid from generic_code_list where code = '38' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'abs. v. š.', 'absolvent vysokej školy', (select uuid from generic_code_list where code = '39' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'abs. v. š.', '[EN] absolvent vysokej školy', (select uuid from generic_code_list where code = '39' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Dr.', 'doktor', (select uuid from generic_code_list where code = '40' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Dr.', '[EN] doktor', (select uuid from generic_code_list where code = '40' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Mgr. art.', 'magister umenia', (select uuid from generic_code_list where code = '41' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Mgr. art.', '[EN] magister umenia', (select uuid from generic_code_list where code = '41' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'MDDr.', 'doktor zubného lekárstva', (select uuid from generic_code_list where code = '42' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'MDDr.', '[EN] doktor zubného lekárstva', (select uuid from generic_code_list where code = '42' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'ppor.', 'podporučík', (select uuid from generic_code_list where code = '43' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'ppor.', '[EN] podporučík', (select uuid from generic_code_list where code = '43' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'por.', 'poručík', (select uuid from generic_code_list where code = '44' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'por.', '[EN] poručík', (select uuid from generic_code_list where code = '44' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'npor.', 'nadporučík', (select uuid from generic_code_list where code = '45' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'npor.', '[EN] nadporučík', (select uuid from generic_code_list where code = '45' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'kpt.', 'kapitán', (select uuid from generic_code_list where code = '46' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'kpt.', '[EN] kapitán', (select uuid from generic_code_list where code = '46' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'mjr.', 'major', (select uuid from generic_code_list where code = '47' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'mjr.', '[EN] major', (select uuid from generic_code_list where code = '47' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'pplk.', 'podplukovník', (select uuid from generic_code_list where code = '48' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'pplk.', '[EN] podplukovník', (select uuid from generic_code_list where code = '48' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'plk.', 'plukovník', (select uuid from generic_code_list where code = '49' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'plk.', '[EN] plukovník', (select uuid from generic_code_list where code = '49' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'gen.', 'generál', (select uuid from generic_code_list where code = '50' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'gen.', '[EN] generál', (select uuid from generic_code_list where code = '50' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Dipl.Ing.', 'diplomovaný inžinier', (select uuid from generic_code_list where code = '51' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Dipl.Ing.', '[EN] diplomovaný inžinier', (select uuid from generic_code_list where code = '51' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prof.', 'profesor', (select uuid from generic_code_list where code = '52' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prof.', '[EN] profesor', (select uuid from generic_code_list where code = '52' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Mag.', 'Magister', (select uuid from generic_code_list where code = '53' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Mag.', '[EN] Magister', (select uuid from generic_code_list where code = '53' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Dipl.Kfm.', 'Diplomovaný obchodník', (select uuid from generic_code_list where code = '54' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Dipl.Kfm.', '[EN] Diplomovaný obchodník', (select uuid from generic_code_list where code = '54' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'BcA.', 'BcA.', (select uuid from generic_code_list where code = '55' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'BcA.', '[EN] BcA.', (select uuid from generic_code_list where code = '55' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Dipl.tech.', 'Dipl.tech.', (select uuid from generic_code_list where code = '56' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Dipl.tech.', '[EN] Dipl.tech.', (select uuid from generic_code_list where code = '56' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'doc.', 'doc.', (select uuid from generic_code_list where code = '57' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'doc.', '[EN] doc.', (select uuid from generic_code_list where code = '57' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'doc. Ing.', 'doc. Ing.', (select uuid from generic_code_list where code = '58' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'doc. Ing.', '[EN] doc. Ing.', (select uuid from generic_code_list where code = '58' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'doc. MUDr.', 'doc. MUDr.', (select uuid from generic_code_list where code = '59' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'doc. MUDr.', '[EN] doc. MUDr.', (select uuid from generic_code_list where code = '59' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'doc. MVDr.', 'doc. MVDr.', (select uuid from generic_code_list where code = '60' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'doc. MVDr.', '[EN] doc. MVDr.', (select uuid from generic_code_list where code = '60' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'JUDr. Ing.', 'JUDr. Ing.', (select uuid from generic_code_list where code = '61' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'JUDr. Ing.', '[EN] JUDr. Ing.', (select uuid from generic_code_list where code = '61' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'MgA.', 'MgA.', (select uuid from generic_code_list where code = '62' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'MgA.', '[EN] MgA.', (select uuid from generic_code_list where code = '62' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'MUDr. Mgr.', 'MUDr. Mgr.', (select uuid from generic_code_list where code = '63' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'MUDr. Mgr.', '[EN] MUDr. Mgr.', (select uuid from generic_code_list where code = '63' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prof. Dr. Ing.', 'prof. Dr. Ing.', (select uuid from generic_code_list where code = '64' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prof. Dr. Ing.', '[EN] prof. Dr. Ing.', (select uuid from generic_code_list where code = '64' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'doc. PhDr.', 'doc. PhDr.', (select uuid from generic_code_list where code = '65' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'doc. PhDr.', '[EN] doc. PhDr.', (select uuid from generic_code_list where code = '65' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Mgr. Arch.', 'Mgr. Arch.', (select uuid from generic_code_list where code = '66' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Mgr. Arch.', '[EN] Mgr. Arch.', (select uuid from generic_code_list where code = '66' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Mgr. Ing.', 'Mgr. Ing.', (select uuid from generic_code_list where code = '67' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Mgr. Ing.', '[EN] Mgr. Ing.', (select uuid from generic_code_list where code = '67' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'doc. Mgr.', 'doc. Mgr.', (select uuid from generic_code_list where code = '68' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'doc. Mgr.', '[EN] doc. Mgr.', (select uuid from generic_code_list where code = '68' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'MSc.', 'MSc.', (select uuid from generic_code_list where code = '69' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'MSc.', '[EN] MSc.', (select uuid from generic_code_list where code = '69' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Mgr. Bc.', 'Mgr. Bc.', (select uuid from generic_code_list where code = '70' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Mgr. Bc.', '[EN] Mgr. Bc.', (select uuid from generic_code_list where code = '70' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'PhDr. Mgr.', 'PhDr. Mgr.', (select uuid from generic_code_list where code = '71' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'PhDr. Mgr.', '[EN] PhDr. Mgr.', (select uuid from generic_code_list where code = '71' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prof. MVDr.', 'prof. MVDr.', (select uuid from generic_code_list where code = '72' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prof. MVDr.', '[EN] prof. MVDr.', (select uuid from generic_code_list where code = '72' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prof. Dr.', 'prof. Dr.', (select uuid from generic_code_list where code = '73' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prof. Dr.', '[EN] prof. Dr.', (select uuid from generic_code_list where code = '73' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prof. Ing.', 'prof. Ing.', (select uuid from generic_code_list where code = '74' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prof. Ing.', '[EN] prof. Ing.', (select uuid from generic_code_list where code = '74' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prof. RNDr.', 'prof. RNDr.', (select uuid from generic_code_list where code = '75' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prof. RNDr.', '[EN] prof. RNDr.', (select uuid from generic_code_list where code = '75' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'ak. mal.', 'ak. mal.', (select uuid from generic_code_list where code = '76' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'ak. mal.', '[EN] ak. mal.', (select uuid from generic_code_list where code = '76' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'ak. soch.', 'ak. soch.', (select uuid from generic_code_list where code = '77' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'ak. soch.', '[EN] ak. soch.', (select uuid from generic_code_list where code = '77' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Bc. Ing.', 'Bc. Ing.', (select uuid from generic_code_list where code = '78' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Bc. Ing.', '[EN] Bc. Ing.', (select uuid from generic_code_list where code = '78' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Dipl. arch.', 'Dipl. arch.', (select uuid from generic_code_list where code = '79' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Dipl. arch.', '[EN] Dipl. arch.', (select uuid from generic_code_list where code = '79' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Dipl. ekon.', 'Dipl. ekon.', (select uuid from generic_code_list where code = '80' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Dipl. ekon.', '[EN] Dipl. ekon.', (select uuid from generic_code_list where code = '80' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Dipl. geol.', 'Dipl. geol.', (select uuid from generic_code_list where code = '81' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Dipl. geol.', '[EN] Dipl. geol.', (select uuid from generic_code_list where code = '81' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Dipl. theol.', 'Dipl. theol.', (select uuid from generic_code_list where code = '82' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Dipl. theol.', '[EN] Dipl. theol.', (select uuid from generic_code_list where code = '82' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'doc. RNDr.', 'doc. RNDr.', (select uuid from generic_code_list where code = '83' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'doc. RNDr.', '[EN] doc. RNDr.', (select uuid from generic_code_list where code = '83' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Dr. CSC', 'Dr. CSC', (select uuid from generic_code_list where code = '84' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Dr. CSC', '[EN] Dr. CSC', (select uuid from generic_code_list where code = '84' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Dr. Ing.', 'Dr. Ing.', (select uuid from generic_code_list where code = '85' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Dr. Ing.', '[EN] Dr. Ing.', (select uuid from generic_code_list where code = '85' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Ing. CSc.', 'Ing. CSc.', (select uuid from generic_code_list where code = '86' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Ing. CSc.', '[EN] Ing. CSc.', (select uuid from generic_code_list where code = '86' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Ing. doc.', 'Ing. doc.', (select uuid from generic_code_list where code = '87' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Ing. doc.', '[EN] Ing. doc.', (select uuid from generic_code_list where code = '87' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Ing. Bc.', 'Ing. Bc.', (select uuid from generic_code_list where code = '88' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Ing. Bc.', '[EN] Ing. Bc.', (select uuid from generic_code_list where code = '88' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Ing. Mgr.', 'Ing. Mgr.', (select uuid from generic_code_list where code = '89' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Ing. Mgr.', '[EN] Ing. Mgr.', (select uuid from generic_code_list where code = '89' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Ing. PhD.', 'Ing. PhD.', (select uuid from generic_code_list where code = '90' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Ing. PhD.', '[EN] Ing. PhD.', (select uuid from generic_code_list where code = '90' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Ing. PhDr.', 'Ing. PhDr.', (select uuid from generic_code_list where code = '91' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Ing. PhDr.', '[EN] Ing. PhDr.', (select uuid from generic_code_list where code = '91' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prof. PhDr.', 'prof. PhDr.', (select uuid from generic_code_list where code = '92' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prof. PhDr.', '[EN] prof. PhDr.', (select uuid from generic_code_list where code = '92' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'RNDr. Mgr.', 'RNDr. Mgr.', (select uuid from generic_code_list where code = '93' and type = 'TITLE_FRONT')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'RNDr. Mgr.', '[EN] RNDr. Mgr.', (select uuid from generic_code_list where code = '93' and type = 'TITLE_FRONT'));

INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, '501', 'TITLE_BEHIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '502', 'TITLE_BEHIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '503', 'TITLE_BEHIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '504', 'TITLE_BEHIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '505', 'TITLE_BEHIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '506', 'TITLE_BEHIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '507', 'TITLE_BEHIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '508', 'TITLE_BEHIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '509', 'TITLE_BEHIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '510', 'TITLE_BEHIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '511', 'TITLE_BEHIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '512', 'TITLE_BEHIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '513', 'TITLE_BEHIND', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'CSc.', 'kandidát vied', (select uuid from generic_code_list where code = '501' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'CSc.', '[EN] kandidát vied', (select uuid from generic_code_list where code = '501' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'DrSc.', 'doktor vied', (select uuid from generic_code_list where code = '502' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'DrSc.', '[EN] doktor vied', (select uuid from generic_code_list where code = '502' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'PhD.', 'doktor', (select uuid from generic_code_list where code = '503' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'PhD.', '[EN] doktor', (select uuid from generic_code_list where code = '503' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'ArtD.', 'doktor umenia (artis doctor)', (select uuid from generic_code_list where code = '504' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'ArtD.', '[EN] doktor umenia (artis doctor)', (select uuid from generic_code_list where code = '504' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'doc.', 'docent', (select uuid from generic_code_list where code = '505' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'doc.', '[EN] docent', (select uuid from generic_code_list where code = '505' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'prof.', 'profesor', (select uuid from generic_code_list where code = '506' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'prof.', '[EN] profesor', (select uuid from generic_code_list where code = '506' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'MBA', 'master of business administration', (select uuid from generic_code_list where code = '507' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'MBA', '[EN] master of business administration', (select uuid from generic_code_list where code = '507' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'B.S.B.A.', 'Bachelor of Science in Business Administ', (select uuid from generic_code_list where code = '508' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'B.S.B.A.', '[EN] Bachelor of Science in Business Administ', (select uuid from generic_code_list where code = '508' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'DiS. art.', 'Diplomovaný špecialista umenia', (select uuid from generic_code_list where code = '509' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'DiS. art.', '[EN] Diplomovaný špecialista umenia', (select uuid from generic_code_list where code = '509' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'RSc.', 'RSc.', (select uuid from generic_code_list where code = '510' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'RSc.', '[EN] RSc.', (select uuid from generic_code_list where code = '510' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Ph.D.', 'Ph.D.', (select uuid from generic_code_list where code = '511' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Ph.D.', '[EN] Ph.D.', (select uuid from generic_code_list where code = '511' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'MPH', 'MPH', (select uuid from generic_code_list where code = '512' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'MPH', '[EN] MPH', (select uuid from generic_code_list where code = '512' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Th.D.', 'Th.D.', (select uuid from generic_code_list where code = '513' and type = 'TITLE_BEHIND')),
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Th.D.', '[EN] Th.D.', (select uuid from generic_code_list where code = '513' and type = 'TITLE_BEHIND'));
