INSERT INTO customer_request_template_i18n (
    uuid,
    created_at,
    updated_at,
    version,
    locale,
    name,
    description,
    customer_request_template_uuid
) VALUES

(uuid_generate_v4(), now(), now(), 1, 'SK', '<PERSON><PERSON><PERSON><PERSON> odberné miesto - Zmena adresy odberného miesta', '<PERSON><PERSON><PERSON><PERSON> o zmenu adresy odberného miesta.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZAOM')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Zmena spôsobu platby a periodicity preddavkovej platby', '<PERSON><PERSON><PERSON><PERSON> o zmenu spôsobu platby a periodicity preddavkovej platby.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZSPAPPP')),
(uuid_generate_v4(), now(), now(), 1, 'SK', '<PERSON><PERSON><PERSON><PERSON> odberné miesto - Zmena výšky preddavkovej platby', '<PERSON><PERSON><PERSON><PERSON> o zmenu výšky preddavkovej platby.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZVPP')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Zmena sadzby elektriny', 'Žiadosť o zmenu sadzby dodávky elektriny', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZSE')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Zmena tarify plynu', 'Žiadosť o zmenu sadzby dodávky zemného plynu.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZTP')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Reklamácia preddavkov vo faktúre', 'Žiadosť o reklamáciu preddavkov vo faktúre za dodávku.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_RPVF')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Reklamácia faktúry', 'Žiadosť o reklamáciu faktúry.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_RF')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť obchodný partner - Sťažnosť', 'Sťažnosť obchodného partnera.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOP_S')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť obchodný partner - Zmena osobných údajov a adresy', 'Žiadosť pre zmenu osobných údajov obchodného partnera a jeho trvalej (resp. sídelnej) adresy a korešpondenčnej adresy.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOP_ZOUA')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť obchodný partner - Zmena priezviska', 'Žiadosť pre zmenu priezviska obchodného partnera.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOP_ZP')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť obchodný partner - Zmena bankových údajov', 'Žiadosť pre zmenu bankových údajov obchodného partnera.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOP_ZBU')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Odpočet', 'Žiadosť pre zadanie odpočtu.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_O')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť obchodný partner - Uhlíková stopka', 'Žiadosť pre zazmluvnenie produktu Uhlíková stopka.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOP_US')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť zmluvný účet - Aktivácia/deaktivácia eFaktúry', 'Žiadosť o aktiváciu/deaktiváciu eFaktúry.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZZU_ADEF')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť obchodný partner - Registrácia sprostredkovateľa', 'Žiadosť o registráciu sprostredkovateľa.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOP_RS')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadoť o prerušneie/obnovenie dodávky ZP/EE z dôvodu rekonštrukcie', 'Žiadosť o prerušenie/obnovu odberu.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZOPAOO')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť o úradnú skúšku meradla ZP', 'Žiadosť o preskúšanie meradla.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZOUSM')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Žiadosť o faktúru mimo cyklu', 'Žiadosť o faktúru mimo cyklu.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZOFMC')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Žiadosť o zrýchlené obnovenie dodávky zemného plynu', 'Žiadosť o zrýchlené obnovenie dodávky zemného plynu od SPP.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZOZODZP')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Prepis', 'Žiadosť o prepis odberného miesta.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_P')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Žiadosť o dodávku elektriny', 'Žiadosť o dodávku elektiny od SPP.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZODE')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Dohoda o splátkach', 'Žiadosť o dohodu o splátkach faktúry po splatnosti.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_DOS')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Potvrdenie o výške záväzkov', 'Žiadosť o potvrdenie o výške záväzkov voči SPP.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_POVZ')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Potvrdenie o zmluvnom vzťahu', 'Žiadosť o potvrdenie o zmluvnom vzťahu s SPP.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_POZV')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Žiadosť o dodávku plynu', 'Žiadosť o dodávku zemného plynu od SPP.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZODP')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Žiadosť o ukončenie zmluvy ZP/EE', 'Žiadosť o ukončenie zmluvy s SPP.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZOUZ')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Žiadosť o vystavenie duplikátu faktúry', 'Žiadosť o vystavenie duplikátu faktúry.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZOVDF')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Žiadosť o doplnkové služby', 'Žiadosť o doplnkové služby k odbernému miestu.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZODS'));
