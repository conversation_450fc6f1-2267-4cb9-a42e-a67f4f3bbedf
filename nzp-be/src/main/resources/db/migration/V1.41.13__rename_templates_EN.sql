UPDATE notification_template_i18n SET email_subject = 'Discover the Moje SPP portal in one click', email_body = '<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>at SPP, we do everything we can to make it easy and fast for you to solve your needs.</p><p>The Moje SPP customer portal is a proof of our efforts. It enables you to enter your requirements easily.<br>You will also find:</p><ul>    <li>an overview of information about your distribution points,</li>    <li>your invoices,</li>    <li>your payment and consumption history.</li></ul><p>Discover the Moje SPP portal in one click and save time today and in the future.</p><p><a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName}&amp;lastName=${customer.lastName}&amp;type=pre_registration">Discover Moje SPP now</a></p><p></@spp.notification_email_template></p>'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_BATCH_REQUEST_REGISTRATION') and locale = 'EN';

UPDATE notification_template_i18n SET email_subject = 'Discover the Moje SPP portal in one click', email_body = '<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>at SPP, we do everything we can to make it easy and fast for you to solve your needs.</p><p>The Moje SPP customer portal is a proof of our efforts. It enables you to enter your requirements easily.<br>You will also find:</p><ul>    <li>an overview of information about your distribution points,</li>    <li>your invoices,</li>    <li>your payment and consumption history.</li></ul><p>Discover the Moje SPP portal in one click and save time today and in the future.</p><p><a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName}&amp;lastName=${customer.lastName}&amp;type=pre_registration">Discover Moje SPP now</a></p><p></@spp.notification_email_template></p>'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_SAP_REGISTRATION_REQUEST') and locale = 'EN';
