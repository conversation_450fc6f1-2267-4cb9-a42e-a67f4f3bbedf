-- PMERU-1989 - make customer_notification_archive partitioned by created_at (interval: 1 year)
-- Option 1: Table exists and is partitioned -> do nothing. Changes have already been most likely done manually
-- Option 2: Table exists but isn't partitioned -> rename old table. Create new partitioned table + partitions 2021 - currentYear + 2
-- Option 3: Table doesn't exist -> Create new partitioned table + partitions 2021 - currentYear + 2

DO $$
    DECLARE
        table_exists BOOLEAN;
        v_is_partitioned BOOLEAN;
        partition_name TEXT;
        partition_range_start TEXT;
        partition_range_end TEXT;
        current_year INTEGER;
    BEGIN
        select EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER into current_year;
        select EXISTS (SELECT 1 FROM pg_catalog.pg_tables WHERE tablename = 'customer_notification_archive') into table_exists;
        SELECT CASE
                   WHEN EXISTS (SELECT 1
                                FROM pg_catalog.pg_class c
                                         JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
                                WHERE nspname = current_schema()
                                  AND relname = 'customer_notification_archive'
                                  AND relkind = 'p')
                       THEN TRUE
                   ELSE FALSE
                   END into v_is_partitioned;

        -- Check if table exists
        IF table_exists and v_is_partitioned THEN
            RAISE NOTICE 'Table customer_notification_archive exists and is partitioned. Script will do nothing! Changes have already been most likely done manually';
        ELSE

            IF table_exists THEN
                -- rename table + index
                ALTER TABLE customer_notification_archive RENAME TO customer_notification_archive_old;
                ALTER INDEX idx_customer_notification_archive_customer_account_id RENAME TO idx_customer_notification_archive_old_customer_account_id;
                RAISE NOTICE 'Table customer_notification_archive exists, but is not partitioned. Current table was renamed to customer_notification_archive_old and new partitioned table will be created. You should consider moving data from customer_notification_archive_old to partitioned customer_notification_archive and drop table customer_notification_archive_old.';
            END IF;

            -- create new partitioned table + index
            create table customer_notification_archive
            (
                uuid                       uuid,
                created_at                 timestamp with time zone,
                updated_at                 timestamp with time zone,
                version                    integer,
                source                     varchar(50),
                external_id                varchar(50),
                status                     varchar(50),
                header                     text,
                email_body                 text,
                email_subject              text,
                sms_body                   text,
                entity_type                varchar(50),
                entity_id                  varchar(50),
                phone                      varchar(20),
                email                      varchar(64),
                attributes                 text,
                attachments                text,
                locale                     varchar(5),
                notification_template_id   uuid,
                customer_account_id        uuid,
                share_from_id              uuid,
                read                       boolean,
                read_at                    timestamp with time zone,
                employee_login             text,
                synchronization_log_uuid   uuid,
                synchronization_at         timestamp with time zone,
                delivery_point_id          text,
                business_partner_id        text,
                contract_account_id        text,
                email_sent_at              timestamp with time zone,
                sms_sent_at                timestamp with time zone,
                header_url                 text,
                enabled_channels           char,
                deleted                    boolean,
                cancelled                  boolean,
                email_send                 boolean,
                sms_send                   boolean,
                notification_schedule_uuid uuid
            ) PARTITION BY RANGE (created_at);

            CREATE INDEX idx_customer_notification_archive_customer_account_id
                on customer_notification_archive (customer_account_id);

            RAISE NOTICE 'New partitioned customer_notification_archive table was created.';

            -- create partitions
            FOR year IN 2021 .. current_year + 2
                LOOP
                    partition_name := 'customer_notification_archive_' || year;
                    partition_range_start := quote_literal(year || '-01-01 00:00:00.000000 +00:00');
                    partition_range_end := quote_literal((year + 1) || '-01-01 00:00:00.000000 +00:00');
                    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF customer_notification_archive FOR VALUES FROM (%s) TO (%s)',
                                   partition_name, partition_range_start, partition_range_end);
                END LOOP;

            RAISE NOTICE 'Partitions for years % - % were created', 2021, current_year + 2;
        END IF;
    END;
$$;
