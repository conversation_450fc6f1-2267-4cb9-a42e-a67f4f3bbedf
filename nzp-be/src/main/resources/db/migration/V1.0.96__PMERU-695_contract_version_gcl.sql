update contract_version SET gcl_bill_cycle_uuid = null;
delete from generic_code_list_i18n where code_list_uuid in (select uuid from generic_code_list where type = 'CONTRACT_BILL_CYCLE');
delete from generic_code_list where type = 'CONTRACT_BILL_CYCLE';


-- CONTRACT_BILL_CYCLE

INSERT INTO
    generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'M', 'CONTRACT_BILL_CYCLE'),
    (uuid_generate_v4(), now(), now(), 1, 'R', 'CONTRACT_BILL_CYCLE');

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Mesačne', null, (select uuid from generic_code_list where code = 'M' and type = 'CONTRACT_BILL_CYCLE'));

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Ročne', null, (select uuid from generic_code_list where code = 'R' and type = 'CONTRACT_BILL_CYCLE'));

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Monthly', null, (select uuid from generic_code_list where code = 'M' and type = 'CONTRACT_BILL_CYCLE'));

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'EN', 'Yearly', null, (select uuid from generic_code_list where code = 'R' and type = 'CONTRACT_BILL_CYCLE'));
