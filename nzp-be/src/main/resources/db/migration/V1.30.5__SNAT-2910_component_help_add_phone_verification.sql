INSERT INTO component_help (uuid, created_at, updated_at, version, screen, field, status, help_order)
VALUES (uuid_generate_v4(), now(), now(), 1, 'OTC', 'OTC_PHONE_VERIFICATION_PURPOSE', 'ACTIVE', 1)
on conflict (screen ,field) do nothing ;

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, component_help_id, content) VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', (SELECT uuid FROM component_help WHERE screen = 'OTC' AND field = 'OTC_PHONE_VERIFICATION_PURPOSE'),'')
on conflict (component_help_id, locale) do nothing;

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, component_help_id, content) VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', (SELECT uuid FROM component_help WHERE screen = 'OTC' AND field = 'OTC_PHONE_VERIFICATION_PURPOSE'),'')
on conflict (component_help_id, locale) do nothing;