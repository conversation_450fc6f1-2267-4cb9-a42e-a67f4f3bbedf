
-- fix for invoice.status name
update notification_template_variable ntv
set name = 'Status faktúry'
where ntv.variable = 'invoice.status';

-- add new variables for invoice
insert into notification_template_variable select ntv.notification_template_uuid, now(), now(), 1, 'invoice.id', 'ID faktúry', 'Technický identifikátor pre faktúry', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template_variable ntv
where ntv.variable = 'invoice.status';
insert into notification_template_variable select ntv.notification_template_uuid, now(), now(), 1, 'invoice.overpaid', 'Preplatok', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template_variable ntv
where ntv.variable = 'invoice.status';
insert into notification_template_variable select ntv.notification_template_uuid, now(), now(), 1, 'invoice.reference', '<PERSON><PERSON><PERSON> faktú<PERSON>', '<PERSON><PERSON><PERSON> faktú<PERSON> z pohľadu zákazníka', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template_variable ntv
where ntv.variable = 'invoice.status';