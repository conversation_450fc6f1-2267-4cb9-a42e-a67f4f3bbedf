INSERT INTO product_i18n (
	uuid, created_at, updated_at, version, locale, name, description, product_uuid)
SELECT uuid_generate_v4(), now(), now(), 1, 'sk', p.name, p.description, p.uuid from product p;

INSERT INTO product_i18n (
	uuid, created_at, updated_at, version, locale, name, description, product_uuid)
SELECT uuid_generate_v4(), now(), now(), 1, 'en',  '[EN]' || p.name, '[EN]' || p.description, p.uuid from product p;


INSERT INTO tariff_i18n (
	uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
SELECT uuid_generate_v4(), now(), now(), 1, 'sk', t.name, t.description, t.uuid from tariff t;

INSERT INTO tariff_i18n (
	uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
SELECT uuid_generate_v4(), now(), now(), 1, 'en',  '[EN]' || t.name, '[EN]' || t.description, t.uuid from tariff t;