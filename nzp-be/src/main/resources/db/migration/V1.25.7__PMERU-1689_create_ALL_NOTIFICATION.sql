INSERT INTO component_help (uuid, created_at, updated_at, version, screen, field, status, help_order)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ALL', 'ALL_NOTIFICATION', 'ACTIVE', 2);

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, component_help_id, content) VALUES
(uuid_generate_v4(), now(), now(), 1, 'sk', (SELECT uuid FROM component_help WHERE screen = 'ALL' AND field = 'ALL_NOTIFICATION'),
'Pilotná prevádzka portálu Moje SPP');

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, component_help_id, content) VALUES
(uuid_generate_v4(), now(), now(), 1, 'en', (SELECT uuid FROM component_help WHERE screen = 'ALL' AND field = 'ALL_NOTIFICATION'),
'Pilot phase of Moje SPP portal');