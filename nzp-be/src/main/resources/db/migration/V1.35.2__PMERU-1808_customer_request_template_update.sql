update customer_request_template set status = 'INACTIVE', "version" = "version" + 1 , updated_at = now() where code = 'ZOM_ZOPAOO';

update customer_request_template_i18n set name = 'Prerušenie/obnovenie dodávky zemného plynu/elektriny z dôvodu rekonštrukcie (PDF)', "version" = "version" + 1, updated_at = now()
where customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOM_ZOPAOO')
and locale = 'SK';


update customer_request_template_i18n set name = '[EN] Žiadoť o prerušneie/obnovenie dodávky ZP/EE z dôvodu rekonštrukcie (PDF)', "version" = "version" + 1, updated_at = now()
where customer_request_template_uuid = (select uuid from customer_request_template crt where crt .code = 'ZOM_ZOPAOO')
and locale = 'EN';


update customer_request set "content" = jsonb_set(content::jsonb, '{type}', '"ZOM_ZOPAOO_PDF"'), "version" = "version" + 1, updated_at = now()
where customer_request_template_id  = (select uuid from customer_request_template  where code = 'ZOM_ZOPAOO');

update customer_request_template set code = 'ZOM_ZOPAOO_PDF', "version" = "version" + 1, updated_at = now() where code = 'ZOM_ZOPAOO';


insert into customer_request_template(
    uuid,
    created_at,
    updated_at,
    version,
    status,
    code,
    price,
    confirmation_required,
    type,
    link,
    confirmation_valid_days)
values (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZOPAOO', null, false, 'DIGITAL', null, null);

insert into customer_request_template_i18n (
    uuid,
    created_at,
    updated_at,
    version,
    locale,
    name,
    description,
    customer_request_template_uuid
) VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Prerušenie dodávky ZP z dôvodu rekonštrukcie', 'Žiadosť o prerušenie odberu.',(SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZOPAOO')),
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Žiadoť o prerušenie dodávky ZP z dôvodu rekonštrukcie', '[EN] Žiadosť o prerušenie odberu.',(SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZOPAOO'));
