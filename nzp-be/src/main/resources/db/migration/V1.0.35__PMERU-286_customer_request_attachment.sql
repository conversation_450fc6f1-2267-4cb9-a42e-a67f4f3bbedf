------------------------------
-- customer_request_attachment
------------------------------

CREATE TABLE customer_request_attachment
(
    uuid                         uuid                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      INTEGER                   NOT NULL,

    name                         CHARACTER VARYING(128),
    content                      BYTEA                     NOT NULL,
    customer_request_id          uuid                      NOT NULL,

    CONSTRAINT pk_customer_request_attachment PRIMARY KEY (uuid),
    CONSTRAINT fk_customer_request_attachmente_customer_request FOREIGN KEY (customer_request_id)
            REFERENCES customer_request (uuid) MATCH SIMPLE
            ON UPDATE NO ACTION
            ON DELETE NO ACTION
);

-- indexes
CREATE INDEX idx_customer_request_attachment_customer_request_id on customer_request_attachment(customer_request_id);
