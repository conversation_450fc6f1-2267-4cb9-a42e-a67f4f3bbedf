update notification_template_i18n nti18n
set email_body = '<PERSON><PERSON><PERSON><PERSON><PERSON>n<PERSON>, žiadosť s názvom ${customerRequest.name} zmenila stav na: ${customerRequest.status.name}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>'
where nti18n.notification_template_id = (select nt.uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_STATUS_CHANGE')
and nti18n.locale = 'SK';

update notification_template_i18n nti18n
set email_body = '[EN] Vážený zákazník, žiadosť s názvom ${customerRequest.name} zmenila stav na: ${customerRequest.status.name}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>'
where nti18n.notification_template_id = (select nt.uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_STATUS_CHANGE')
  and nti18n.locale = 'EN';