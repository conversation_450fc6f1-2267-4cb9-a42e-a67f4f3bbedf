INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'PRE_CREATED', 'CUSTOMER_REQUEST_STATUS', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk', 'Založená', null, (select uuid from generic_code_list where code like 'PRE_CREATED' and type = 'CUSTOMER_REQUEST_STATUS')),
    (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Založená', null, (select uuid from generic_code_list where code like 'PRE_CREATED' and type = 'CUSTOMER_REQUEST_STATUS'));

INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CREATED_AS_PAPER', 'CUSTOMER_REQUEST_STATUS', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk', 'Vytvorená (papierovo)', null, (select uuid from generic_code_list where code like 'CREATED_AS_PAPER' and type = 'CUSTOMER_REQUEST_STATUS')),
    (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Vytvorená (papierovo)', null, (select uuid from generic_code_list where code like 'CREATED_AS_PAPER' and type = 'CUSTOMER_REQUEST_STATUS'));

