--business_partner

alter table business_partner drop column company_type;


--business_partner_address

alter table business_partner_address drop column business_partner_id;
alter table business_partner_address add column business_partner_external_id CHARACTER VARYING(50) NOT NULL;

CREATE INDEX idx_business_partner_address_business_partner_external_id on business_partner_address(business_partner_external_id);


--business_partner_approval

alter table business_partner_approval drop column business_partner_id;
alter table business_partner_approval add column business_partner_external_id CHARACTER VARYING(50) NOT NULL;
alter table business_partner_approval alter column approval set not null;

CREATE INDEX idx_business_partner_approval_business_partner_external_id on business_partner_approval(business_partner_external_id);


--contract_account

alter table contract_account alter column status drop not null;
alter table contract_account add column email CHARACTER VARYING(64);
alter table contract_account add column payment_type CHARACTER VARYING(50);
alter table contract_account add column street CHARACTER VARYING(64);
alter table contract_account add column street_number CHARACTER VARYING(32);
alter table contract_account add column city CHARACTER VARYING(64);
alter table contract_account add column zip_code CHARACTER VARYING(32);
alter table contract_account add column country CHARACTER VARYING(64);
alter table contract_account add column iban CHARACTER VARYING(64);
alter table contract_account add column sipo CHARACTER VARYING(64);
alter table contract_account add column vs CHARACTER VARYING(64);
alter table contract_account drop column balance;
alter table contract_account drop column billing_arrangement_id;
alter table contract_account drop column business_partner_id;
alter table contract_account add column business_partner_external_id CHARACTER VARYING(50);

CREATE INDEX idx_contract_account_business_partner_external_id on contract_account(business_partner_external_id);


--billing_arrangement

drop table billing_arrangement;


--contract

alter table contract drop column type;
alter table contract add column bill_cycle CHARACTER VARYING(50);
alter table contract drop column contract_account_id;
alter table contract add column contract_account_external_id CHARACTER VARYING(50);
alter table contract drop column notice_period;
alter table contract add column notice_period numeric(19);

CREATE INDEX idx_contract_business_contract_account_external_id on contract(contract_account_external_id);


--delivery_point

alter table delivery_point drop column metering;
alter table delivery_point drop column delivery_point_address_id;
alter table delivery_point add column tariff CHARACTER VARYING(32);
alter table delivery_point add column distribution_tariff CHARACTER VARYING(32);
alter table delivery_point add column street CHARACTER VARYING(64);
alter table delivery_point add column street_number CHARACTER VARYING(32);
alter table delivery_point add column city CHARACTER VARYING(64);
alter table delivery_point add column zip_code CHARACTER VARYING(32);
alter table delivery_point add column country CHARACTER VARYING(64);
alter table delivery_point add column metering_interval boolean;
alter table delivery_point add column reading_period_type CHARACTER VARYING(50);
alter table delivery_point add column reserve_amount NUMERIC(19,2);
alter table delivery_point add column maximum_reserve_amount NUMERIC(19,2);
alter table delivery_point add column tariff_count smallint;
alter table delivery_point add column deal_amount NUMERIC(19,2);
alter table delivery_point add column maximum_daily_amount NUMERIC(19,2);
alter table delivery_point add column contract_external_id CHARACTER VARYING(50);
alter table delivery_point alter column business_partner_external_id drop not null;

CREATE INDEX idx_delivery_point_contract_external_id on delivery_point(contract_external_id);
CREATE INDEX idx_delivery_point_business_partner_external_id on delivery_point(business_partner_external_id);


--delivery_point_address

drop table delivery_point_address;


--delivery_point_metric

drop table delivery_point_metric;


--invoice

alter table invoice alter column type drop not null;
alter table invoice alter column amount drop not null;
alter table invoice drop column currency;
alter table invoice alter column due_at drop not null;
alter table invoice alter column issue_at drop not null;
alter table invoice alter column execute_at drop not null;
alter table invoice drop column delivery_point_id;
alter table invoice add column delivery_point_external_id CHARACTER VARYING(50);

CREATE INDEX idx_invoicet_delivery_point_external_id on invoice(delivery_point_external_id);


--payment

alter table payment drop column currency;
alter table payment alter column amount drop not null;
alter table payment drop column invoice_id;
alter table payment add column invoice_external_id CHARACTER VARYING(50) NOT NULL;

CREATE INDEX idx_payment_invoice_external_id on payment(invoice_external_id);


--v_sharing_summary

DROP VIEW v_sharing_summary;
CREATE VIEW v_sharing_summary AS
(select created_at, type, inherited, customer_account_uuid, target_uuid, business_partner_uuid, contract_account_uuid, contract_uuid, delivery_point_uuid, united_delivery_point_uuid, entity_type
from (
         select created_at, type, inherited, customer_account_uuid, business_partner_uuid as target_uuid, business_partner_uuid, Null::UUID as contract_account_uuid, Null::UUID as contract_uuid, Null::UUID as delivery_point_uuid, Null::UUID as united_delivery_point_uuid, CAST('BUSINESS_PARTNER' as CHARACTER VARYING(32)) as entity_type
         from business_partner_ownership
         union
         select created_at, type, inherited, customer_account_uuid, contract_account_uuid as target_uuid, Null::UUID as business_partner_uuid, contract_account_uuid, Null::UUID as contract_uuid, Null::UUID as delivery_point_uuid, Null::UUID as united_delivery_point_uuid, CAST('CONTRACT_ACCOUNT' as CHARACTER VARYING(32)) as entity_type
         from contract_account_ownership
         union
         select created_at, type, inherited, customer_account_uuid, contract_uuid as target_uuid, Null::UUID as business_partner_uuid, Null::UUID as contract_account_uuid, contract_uuid, Null::UUID as delivery_point_uuid, Null::UUID as united_delivery_point_uuid, CAST('CONTRACT' as CHARACTER VARYING(32)) as entity_type
         from contract_ownership
         union
         select created_at, type, inherited, customer_account_uuid, delivery_point_uuid as target_uuid, Null::UUID as business_partner_uuid, Null::UUID as contract_account_uuid, Null::UUID as contract_uuid, delivery_point_uuid, Null::UUID as united_delivery_point_uuid, CAST('DELIVERY_POINT' as CHARACTER VARYING(32)) as entity_type
         from delivery_point_ownership
         union
         select created_at, type, inherited, customer_account_uuid, united_delivery_point_uuid as target_uuid, Null::UUID as business_partner_uuid, Null::UUID as contract_account_uuid, Null::UUID as contract_uuid,  Null::UUID as delivery_point_uuid, united_delivery_point_uuid, CAST('UNITED_DELIVERY_POINT' as CHARACTER VARYING(32)) as entity_type
         from united_delivery_point_ownership
     ) as q
    );


--contract_product_ownership

drop table contract_product_ownership;


--contract_product

drop table contract_product;




























