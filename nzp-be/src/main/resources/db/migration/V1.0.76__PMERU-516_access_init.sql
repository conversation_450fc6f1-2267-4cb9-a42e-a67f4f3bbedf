
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('GROUP_CUSTOMERS_ACTIVATION', current_timestamp, current_timestamp, 1, 'Aktivacia a Deaktivacia Zakaznikov', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('GROUP_CUSTOMERS_VIEW', current_timestamp, current_timestamp, 1, '<PERSON><PERSON><PERSON> uctov', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('GROUP_CUSTOMERS_DELETE', current_timestamp, current_timestamp, 1, '<PERSON><PERSON><PERSON> z<PERSON> uctov', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('GROUP_BUSINESS_PARTNERS_PAIRING', current_timestamp, current_timestamp, 1, '<PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON> partnerov', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('GROUP_NOTIFICATION_TEMPLATES', current_timestamp, current_timestamp, 1, 'Sprava notifikacnych sablon', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('GROUP_REPORTING_MANAGEMENT', current_timestamp, current_timestamp, 1, 'Sprava a spustanie reportov ', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('GROUP_REPORTING', current_timestamp, current_timestamp, 1, 'Spustanie existujucich reportov', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('GROUP_SUPER_ADMIN', current_timestamp, current_timestamp, 1, 'Super admin', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('GROUP_PORTAL_ACCESS', current_timestamp, current_timestamp, 1, 'Portal access', '') ;


INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('CUSTOMERS_ACTIVATION', current_timestamp, current_timestamp, 1, 'Aktivacia zakaznickeho uctu', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('CUSTOMERS_DEACTIVATION', current_timestamp, current_timestamp, 1, 'Deaktivacia zakaznickeho uctu', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('CUSTOMERS_VIEW', current_timestamp, current_timestamp, 1, 'Prehlad zakaznikcyh uctov', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('CUSTOMERS_DELETE_ACCOUNT', current_timestamp, current_timestamp, 1, 'Vymaz zakaznickych uctov', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('BUSINESS_PARTNERS_PAIRING', current_timestamp, current_timestamp, 1, 'Naparovanie obchodneho partnera', '', false, true) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('BUSINESS_PARTNERS_UNPAIRING', current_timestamp, current_timestamp, 1, 'Odparovanie obchodneho partnera', '', false, true) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('CUSTOMERS_PASSWORD_RECOVERY', current_timestamp, current_timestamp, 1, 'Obnova hesla pre zakaznicky ucet', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('CUSTOMERS_REGISTRATION', current_timestamp, current_timestamp, 1, 'Registrovanie zakaznickeho uctu', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('AUDIT_LOGS_CUSTOMERS', current_timestamp, current_timestamp, 1, 'Prehlad auditnych logov pre zakaznika', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('AUDIT_LOGS_CUSTOMERS_WITH_EMPLOYEES', current_timestamp, current_timestamp, 1, 'Prehlad auditnych logov pre zakaznika s udajmi zamestnanaca', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('NOTIFICATION_TEMPLATES_VIEW', current_timestamp, current_timestamp, 1, 'Sprava notifikacnych sablon (Prehlad)', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('NOTIFICATION_TEMPLATES_EDIT', current_timestamp, current_timestamp, 1, 'Sprava notifikacnych sablon (Editacia)', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('REPORTS_EDIT', current_timestamp, current_timestamp, 1, 'Moznost upravovat reporty (management)', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('REPORTS_VIEW', current_timestamp, current_timestamp, 1, 'Moznost spustat reporty ', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('CUSTOMERS_ACTOR_EMPLOYEE', current_timestamp, current_timestamp, 1, 'Pravo prihlasovat sa v mene zakaznika ', '', false, true) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('PORTAL_ACCESS', current_timestamp, current_timestamp, 1, 'Pravo prihlasovat sa do portal ako zamestnanec, vo vlastnom mene', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('SSO_ADMIN', current_timestamp, current_timestamp, 1, 'Pravo do admin konzly pre SSO', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('ACCESS_GROUPS_VIEW', current_timestamp, current_timestamp, 1, '', '', false, false) ;
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('ACCESS_GROUPS_EDIT', current_timestamp, current_timestamp, 1, '', '', false, false) ;


INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('CUSTOMERS_ACTIVATION', 'GROUP_CUSTOMERS_ACTIVATION', current_timestamp, 'GRANT', '');
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('CUSTOMERS_DEACTIVATION', 'GROUP_CUSTOMERS_ACTIVATION', current_timestamp, 'GRANT', '');
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('CUSTOMERS_VIEW', 'GROUP_CUSTOMERS_VIEW', current_timestamp, 'GRANT', '');
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('CUSTOMERS_DELETE_ACCOUNT', 'GROUP_CUSTOMERS_DELETE', current_timestamp, 'GRANT', '');
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('BUSINESS_PARTNERS_PAIRING', 'GROUP_BUSINESS_PARTNERS_PAIRING', current_timestamp, 'GRANT', '');
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('BUSINESS_PARTNERS_UNPAIRING', 'GROUP_BUSINESS_PARTNERS_PAIRING', current_timestamp, 'GRANT', '');
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('NOTIFICATION_TEMPLATES_VIEW', 'GROUP_NOTIFICATION_TEMPLATES', current_timestamp, 'GRANT', '');
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('NOTIFICATION_TEMPLATES_EDIT', 'GROUP_NOTIFICATION_TEMPLATES', current_timestamp, 'GRANT', '');
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('REPORTS_EDIT', 'GROUP_REPORTING', current_timestamp, 'GRANT', '');
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('REPORTS_EDIT', 'GROUP_REPORTING_MANAGEMENT', current_timestamp, 'GRANT', '');
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('REPORTS_VIEW', 'GROUP_REPORTING', current_timestamp, 'GRANT', '');
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('PORTAL_ACCESS', 'GROUP_PORTAL_ACCESS', current_timestamp, 'GRANT', '');
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('SSO_ADMIN', 'GROUP_SUPER_ADMIN', current_timestamp, 'GRANT', '');