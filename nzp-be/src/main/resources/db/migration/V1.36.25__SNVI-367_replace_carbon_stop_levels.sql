-- INVALIDATE OLD GCL
UPDATE generic_code_list SET updated_at = now(), version = (version + 1), valid_to = now()
    WHERE type LIKE 'CARBON_STOP_PLEVEL_%'
    AND (valid_to IS NULL OR valid_to >= now());

-- ADD NEW GCL
INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, attributes)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'PLUS', 'CARBON_STOP_LEVEL', '{"price":"3"}'),
    (uuid_generate_v4(), now(), now(), 1, 'Z_PLUS', 'CARBON_STOP_LEVEL', '{"price":"5"}');

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', '<PERSON><PERSON><PERSON><PERSON> stopka+', null, (select uuid from generic_code_list where code like 'PLUS' and type = 'CARBON_STOP_LEVEL')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Uhlíková stopka Z+', null, (select uuid from generic_code_list where code like 'Z_PLUS' and type = 'CARBON_STOP_LEVEL'));

-- INVALIDATE OLD HELP
UPDATE component_help SET updated_at = now(), version = (version + 1), status = 'INACTIVE'
    WHERE screen = 'NZT'
    AND field LIKE 'NZT_ZOP_US_%';

-- ADD NEW HELP
INSERT INTO component_help (uuid, created_at, updated_at, version, screen, field, status, help_order)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'NZT', 'NZT_ZOP_US_PLUS', 'ACTIVE', 574);

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', '<p>Vysadíme za Vás <strong>4 stromy ročne</strong>, získate štartovací balík rád, videorady na mieru, video s personalizovaným poradenstvom ako aj certifikát o výsadbe stromov.</p><a href="https://www.spp.sk/sk/domacnosti/produkty-a-sluzby/znizme-spolu-uhlikovu-stopu/?utm_source=web%20spp&amp;utm_medium=homepage%20banner&amp;utm_campaign=Stromy%20" target="_blank">Zistiť viac</a>', (
    SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOP_US_PLUS'));

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'EN', '<p>Recommendation package, Custom video advice, Personalised online consulting, plant 4 trees</p> <a href="https://moje.spp.sk/" target="_blank"></a><span style="color: rgb(74, 74, 74); font-family: -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, Cantarell, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration: underline; float: none; display: inline !important; font-size: 16px;"><a href="https://moje.spp.sk/" target="_blank">More information</a></span>', (
    SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOP_US_PLUS'));


INSERT INTO component_help (uuid, created_at, updated_at, version, screen, field, status, help_order)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'NZT', 'NZT_ZOP_US_Z_PLUS', 'ACTIVE', 575);

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', '<p>Vysadíme za Vás <strong>6 stromov ročne</strong>, získate štartovací balík rád,&nbsp;videorady na mieru, video s personalizovaným poradenstvom ako aj&nbsp;certifikát o výsadbe stromov.</p><a href="https://www.spp.sk/sk/domacnosti/produkty-a-sluzby/znizme-spolu-uhlikovu-stopu/?utm_source=web%20spp&amp;utm_medium=homepage%20banner&amp;utm_campaign=Stromy%20" target="_blank"><span>Zistiť viac</span></a>', (
    SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOP_US_Z_PLUS'));

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'EN', '<p>Recommendation package, Custom video advice, Personalised online consulting, plant 6 trees</p> <a href="https://moje.spp.sk/" target="_blank"></a><span style="color: rgb(74, 74, 74); font-family: -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, Cantarell, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration: underline; float: none; display: inline !important; font-size: 16px;"><a href="https://moje.spp.sk/" target="_blank">More information</a></span>', (
    SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOP_US_Z_PLUS'));