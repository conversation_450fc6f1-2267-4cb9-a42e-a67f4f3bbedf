INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms, execution_type, report_customer_column, report_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_LOGIN_PHONE', 'ACTIVE', 'CUSTOMER_SYSTEM', 'SMS login', 'HIGH', 'Prihlásenie pomocou sms kódu', null, null, false, true, 'AUTOMATIC', null, null)
	ON CONFLICT (code) DO UPDATE
    SET execution_type = 'AUTOMATIC', default_sms = true, default_email = false, priority = 'HIGH', name ='SMS login', type = 'CUSTOMER_SYSTEM', status = 'ACTIVE';

DELETE FROM notification_template_i18n where notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_LOGIN_PHONE');

INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status)
VALUES
    (uuid_generate_v4(), now(), now(), 1, null,null, null, 'Vazeny zakaznik, toto je Vas prihlasovaci kod: ${attributes.smsCode}. Vase SPP', (select uuid from notification_template where code = 'CUSTOMER_LOGIN_PHONE'), 'sk', 'ACTIVE');


UPDATE public.notification_template_i18n
	SET version=(version + 1), sms_body= 'Vazeny zakaznik, Vas verifikacny kod pre zmenu telefonneho cisla: ${attributes.challengeCode}. Platnost je do ${attributes.challengeCodeValidTo}. Vase SPP',
	locale='sk'
	WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST');

UPDATE public.notification_template_i18n
	SET version=(version + 1), sms_body= 'Vazeny zakaznik, bolo Vám vygenerovane jednorazove heslo pre prihlasenie do NZP: ${attributes.generatedPassword}. Po prvom pouziti bude potrebne heslo zmenit. Vase SPP',
    locale='sk'
	WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD');

UPDATE public.notification_template_i18n
	SET version=(version + 1), sms_body= 'Vazeny zakaznik, toto je Vas verifikacny kod pre overenie cisla obchodneho partnera: ${attributes.challengeCode}. Vase SPP',
	locale='sk'
	WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE');








