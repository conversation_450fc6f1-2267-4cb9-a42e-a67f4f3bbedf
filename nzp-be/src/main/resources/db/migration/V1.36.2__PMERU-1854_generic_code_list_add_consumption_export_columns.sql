INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EIC_OOM', 'CONSUMPTION_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'FROM', 'CONSUMPTION_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'TO', 'CONSUMPTION_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'POWER', 'CONSUMPTION_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'UPDATED', 'CONSUMPTION_EXPORT_COLUMN', null, null, null);


INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'EIC OOM', 'EIC OOM', (select uuid from generic_code_list where code like 'EIC_OOM' and type = 'CONSUMPTION_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', 'EIC OOM', 'EIC OOM', (select uuid from generic_code_list where code like 'EIC_OOM' and type = 'CONSUMPTION_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Od', 'Od', (select uuid from generic_code_list where code like 'FROM' and type = 'CONSUMPTION_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', 'From', 'From', (select uuid from generic_code_list where code like 'FROM' and type = 'CONSUMPTION_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Do', 'Do', (select uuid from generic_code_list where code like 'TO' and type = 'CONSUMPTION_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', 'To', 'To', (select uuid from generic_code_list where code like 'TO' and type = 'CONSUMPTION_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Výkon', 'Výkon', (select uuid from generic_code_list where code like 'POWER' and type = 'CONSUMPTION_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', 'Power', 'Power', (select uuid from generic_code_list where code like 'POWER' and type = 'CONSUMPTION_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Aktualizované', 'Aktualizované', (select uuid from generic_code_list where code like 'UPDATED' and type = 'CONSUMPTION_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', 'Updated', 'Updated', (select uuid from generic_code_list where code like 'UPDATED' and type = 'CONSUMPTION_EXPORT_COLUMN'));

