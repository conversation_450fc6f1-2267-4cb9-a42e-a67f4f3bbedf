update business_partner set gcl_type_uuid = null;
delete from generic_code_list_i18n where code_list_uuid in (select uuid from generic_code_list where type = 'BUSINESS_PARTNER_TYPE');
delete from generic_code_list where type = 'BUSINESS_PARTNER_TYPE';

INSERT INTO
    generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES
    (uuid_generate_v4(), now(), now(), 1, '110', 'BUSINESS_PARTNER_KIND'),
    (uuid_generate_v4(), now(), now(), 1, '121', 'BUSINESS_PARTNER_KIND'),
    (uuid_generate_v4(), now(), now(), 1, '131', 'BUSINESS_PARTNER_KIND'),
    (uuid_generate_v4(), now(), now(), 1, '141', 'BUSINESS_PARTNER_KIND'),
    (uuid_generate_v4(), now(), now(), 1, '142', 'BUSINESS_PARTNER_KIND'),
    (uuid_generate_v4(), now(), now(), 1, '1', 'BUSINESS_PARTNER_TYPE'),
    (uuid_generate_v4(), now(), now(), 1, '2', 'BUSINESS_PARTNER_TYPE');

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'KAM', null, (select uuid from generic_code_list where code = '110' and type = 'BUSINESS_PARTNER_KIND'));

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Kom. trh - Verejný sektor', null, (select uuid from generic_code_list where code = '121' and type = 'BUSINESS_PARTNER_KIND'));

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Kom. trh - Súkromný sektor', null, (select uuid from generic_code_list where code = '131' and type = 'BUSINESS_PARTNER_KIND'));

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Masový trh - domácnosti', null, (select uuid from generic_code_list where code = '141' and type = 'BUSINESS_PARTNER_KIND'));

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Masový trh - organizácie', null, (select uuid from generic_code_list where code = '142' and type = 'BUSINESS_PARTNER_KIND'));

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Fyzická osoba', null, (select uuid from generic_code_list where code = '1' and type = 'BUSINESS_PARTNER_TYPE'));

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Právnická osoba', null, (select uuid from generic_code_list where code = '2' and type = 'BUSINESS_PARTNER_TYPE'));
