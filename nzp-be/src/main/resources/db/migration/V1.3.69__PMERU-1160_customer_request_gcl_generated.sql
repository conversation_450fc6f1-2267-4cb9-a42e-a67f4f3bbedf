INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'GENERATED', 'CUSTOMER_REQUEST_STATUS', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'sk', 'Vygenerovaná', null, (select uuid from generic_code_list where code like 'GENERATED' and type = 'CUSTOMER_REQUEST_STATUS'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'en', 'Generated', null, (select uuid from generic_code_list where code like 'GENERATED' and type = 'CUSTOMER_REQUEST_STATUS'));