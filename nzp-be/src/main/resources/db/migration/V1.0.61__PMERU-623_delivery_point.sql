-------------------------
-- delivery_point
-------------------------

alter table delivery_point alter column status DROP NOT NULL;
alter table delivery_point alter column type DROP NOT NULL;
alter table delivery_point add column synchronization_log_uuid      uuid                        NOT NULL;
alter table delivery_point add column synchronization_at            TIMESTAMP WITH TIME ZONE    NOT NULL;
alter table delivery_point add column batch_timestamp               TIMESTAMP WITH TIME ZONE;
alter table delivery_point add column version_batch_timestamp       TIMESTAMP WITH TIME ZONE;
alter table delivery_point add column facts_batch_timestamp         TIMESTAMP WITH TIME ZONE;

alter table delivery_point drop column deal_amount;
alter table delivery_point drop column maximum_daily_amount;
alter table delivery_point drop column reserve_amount;
alter table delivery_point drop column circuit_phase_count;
alter table delivery_point drop column circuit_breaker;
alter table delivery_point drop column maximum_reserve_amount;
alter table delivery_point drop column tariff_count;

alter table delivery_point drop column reading_period_type;
alter table delivery_point drop column metering_interval;
alter table delivery_point drop column distribution_tariff;
alter table delivery_point drop column tariff;

CREATE INDEX idx_delivery_point_synchronization_log_uuid on delivery_point(synchronization_log_uuid);

-------------------------
-- delivery_point Foreign Keys
-------------------------
alter table delivery_point
ADD CONSTRAINT fk_delivery_point_synchronization_log FOREIGN KEY (synchronization_log_uuid)
        REFERENCES synchronization_log (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;
-------------------------
-- delivery_point_version
-------------------------

CREATE TABLE delivery_point_version
(
    uuid                     uuid                      NOT NULL,
    created_at               TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at               TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                  integer                   NOT NULL,

    valid_from                                  TIMESTAMP WITH TIME ZONE,
    valid_to                                    TIMESTAMP WITH TIME ZONE,
    deliver_point_external_id                   CHARACTER VARYING(50),
    gcl_metering_period_type_uuid               uuid,
    tariff_rate                                 CHARACTER VARYING(50),
    synchronization_at                          TIMESTAMP WITH TIME ZONE,
    synchronization_log_uuid                    uuid,

    CONSTRAINT pk_delivery_point_version PRIMARY KEY (uuid),
    CONSTRAINT fk_delivery_point_version_gcl_metering_period_type_uuid FOREIGN KEY (gcl_metering_period_type_uuid)
        REFERENCES generic_code_list (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk_delivery_point_version_synchronization_log_uuid FOREIGN KEY (synchronization_log_uuid)
        REFERENCES synchronization_log (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_delivery_point_version_synchronization_log_uuid on delivery_point_version(synchronization_log_uuid);
CREATE INDEX idx_delivery_point_version_deliver_point_external_id on delivery_point_version(deliver_point_external_id);

-------------------------
-- delivery_point_fact
-------------------------

CREATE TABLE delivery_point_fact
(
    uuid                     uuid                      NOT NULL,
    created_at               TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at               TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                  integer                   NOT NULL,

    valid_from                  TIMESTAMP WITH TIME ZONE,
    valid_to                    TIMESTAMP WITH TIME ZONE,
    deliver_point_external_id   CHARACTER VARYING(50),
    synchronization_at          TIMESTAMP WITH TIME ZONE,
    synchronization_log_uuid    uuid,
    operand                     CHARACTER VARYING(50),
    operand_value               text,

    CONSTRAINT pk_delivery_point_fact PRIMARY KEY (uuid),
    CONSTRAINT fk_delivery_point_fact_synchronization_log_uuid FOREIGN KEY (synchronization_log_uuid)
        REFERENCES synchronization_log (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_delivery_point_fact_synchronization_log_uuid on delivery_point_fact(synchronization_log_uuid);
CREATE INDEX idx_delivery_point_fact_deliver_point_external_id on delivery_point_fact(deliver_point_external_id);