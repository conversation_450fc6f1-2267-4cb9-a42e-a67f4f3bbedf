DELETE FROM generic_code_list_i18n where code_list_uuid in (select uuid from generic_code_list where type = 'COUNTRY');
DELETE FROM generic_code_list WHERE type = 'COUNTRY';

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'AF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AQ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'DZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BH', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BW', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BV', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KH', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CV', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TW', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CX', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CC', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'YT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'HR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BJ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'DK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'DM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'DO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'EC', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SV', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GQ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ET', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ER', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'EE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FJ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AX', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'DJ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'DE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GH', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GP', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'HT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'HM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'HN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'HK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'HU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ID', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IQ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'JM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'JP', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'JO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KP', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KW', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LV', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MW', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MV', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ML', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MQ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MX', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MC', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ME', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'OM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NP', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AW', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NC', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MP', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'UM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MH', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PW', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PH', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GW', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'QA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'RE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'RO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'RU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'RW', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SH', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LC', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VC', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ST', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'RS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SC', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ZA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ZW', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ES', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'EH', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SJ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CH', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TJ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TH', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TC', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TV', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'UG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'UA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'EG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'US', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'UY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'UZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'WF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'WS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'YE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ZM', 'COUNTRY', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Afganistan', 'Afganský islamský štát', (select uuid from generic_code_list where code like 'AF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Albánsko', 'Albánska republika', (select uuid from generic_code_list where code like 'AL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Antarktída', 'Antarktída', (select uuid from generic_code_list where code like 'AQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Alžírsko', 'Alžírska demokratická ľudová republika', (select uuid from generic_code_list where code like 'DZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Americká Samoa', 'Teritórium Americkej Samoy', (select uuid from generic_code_list where code like 'AS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Andorra', 'Andorrské kniežatstvo', (select uuid from generic_code_list where code like 'AD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Angola', 'Angolská republika', (select uuid from generic_code_list where code like 'AO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Antigua a Barbuda', 'Antigua a Barbuda', (select uuid from generic_code_list where code like 'AG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Azerbajdžan', 'Azerbajdžanská republika', (select uuid from generic_code_list where code like 'AZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Argentína', 'Argentínska republika', (select uuid from generic_code_list where code like 'AR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Austrália', 'Austrálsky zväz', (select uuid from generic_code_list where code like 'AU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Rakúsko', 'Rakúska republika', (select uuid from generic_code_list where code like 'AT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bahamy', 'Bahamské spoločenstvo', (select uuid from generic_code_list where code like 'BS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bahrajn', 'Bahrajnský štát', (select uuid from generic_code_list where code like 'BH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bangladéš', 'Bangladéšska ľudová republika', (select uuid from generic_code_list where code like 'BD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Arménsko', 'Arménska republika', (select uuid from generic_code_list where code like 'AM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Barbados', 'Barbados', (select uuid from generic_code_list where code like 'BB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Belgicko', 'Belgické kráľovstvo', (select uuid from generic_code_list where code like 'BE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bermudy', 'Bermudy', (select uuid from generic_code_list where code like 'BM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bhután', 'Bhutánske kráľovstvo', (select uuid from generic_code_list where code like 'BT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bolívia', 'Bolívijská republika', (select uuid from generic_code_list where code like 'BO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bosna a Hercegovina', 'Republika Bosny a Hercegoviny', (select uuid from generic_code_list where code like 'BA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Botswana', 'Botswanská republika', (select uuid from generic_code_list where code like 'BW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bouvetov ostrov', 'Bouvetov ostrov', (select uuid from generic_code_list where code like 'BV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Brazília', 'Brazílska federatívna republika', (select uuid from generic_code_list where code like 'BR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Belize', 'Belize', (select uuid from generic_code_list where code like 'BZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Britské indickooc.ú.', 'Britské indickooceánske územie', (select uuid from generic_code_list where code like 'IO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Šalamúnove ostrovy', 'Šalamúnove ostrovy', (select uuid from generic_code_list where code like 'SB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Britské Pan.ostrovy', 'Britské Panenské ostrovy', (select uuid from generic_code_list where code like 'VG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Brunej', 'Brunejsko-darussalamský štát', (select uuid from generic_code_list where code like 'BN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bulharsko', 'Bulharská republika', (select uuid from generic_code_list where code like 'BG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mjanmarsko', 'Mjanmarský zväz', (select uuid from generic_code_list where code like 'MM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Burundi', 'Burundská republika', (select uuid from generic_code_list where code like 'BI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bielorusko', 'Bieloruská republika', (select uuid from generic_code_list where code like 'BY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kambodža', 'Kambodžské kráľovstvo', (select uuid from generic_code_list where code like 'KH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kamerun', 'Kamerunská republika', (select uuid from generic_code_list where code like 'CM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kanada', 'Kanada', (select uuid from generic_code_list where code like 'CA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kapverdy', 'Kapverdská republika', (select uuid from generic_code_list where code like 'CV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kajmanie ostrovy', 'Kajmanie ostrovy', (select uuid from generic_code_list where code like 'KY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Stredoafrická republika', 'Stredoafrická republika', (select uuid from generic_code_list where code like 'CF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Srí Lanka', 'Srílanská demokratická socialistická republika', (select uuid from generic_code_list where code like 'LK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Čad', 'Čadská republika', (select uuid from generic_code_list where code like 'TD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Čile', 'Čilská republika', (select uuid from generic_code_list where code like 'CL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Čína', 'Čínska ľudová republika', (select uuid from generic_code_list where code like 'CN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Taiwan', 'Čínska republika', (select uuid from generic_code_list where code like 'TW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Vianočný ostrov', 'Teritórium Vianočného ostrova', (select uuid from generic_code_list where code like 'CX' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kokosové ostrovy', 'Teritórium Kokosových ostrovov', (select uuid from generic_code_list where code like 'CC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kolumbia', 'Kolumbijská republika', (select uuid from generic_code_list where code like 'CO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Komory', 'Komorská federatívna islamská republika', (select uuid from generic_code_list where code like 'KM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mayotte', 'Mayotte', (select uuid from generic_code_list where code like 'YT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kongo', 'Konžská republika', (select uuid from generic_code_list where code like 'CG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kongo (býv. Zair)', 'Konžská demokratická republika', (select uuid from generic_code_list where code like 'CD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Cookove ostrovy', 'Cookove ostrovy', (select uuid from generic_code_list where code like 'CK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kostarika', 'Kostarická republika', (select uuid from generic_code_list where code like 'CR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Chorvátsko', 'Chorvátska republika', (select uuid from generic_code_list where code like 'HR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kuba', 'Kubánska republika', (select uuid from generic_code_list where code like 'CU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Cyprus', 'Cyperská republika', (select uuid from generic_code_list where code like 'CY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Česko', 'Česká republika', (select uuid from generic_code_list where code like 'CZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Benin', 'Beninská republika', (select uuid from generic_code_list where code like 'BJ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dánsko', 'Dánske kráľovstvo', (select uuid from generic_code_list where code like 'DK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dominika', 'Dominické spoločenstvo', (select uuid from generic_code_list where code like 'DM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dominikánska republika', 'Dominikánska republika', (select uuid from generic_code_list where code like 'DO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Ekvádor', 'Ekvádorská republika', (select uuid from generic_code_list where code like 'EC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Salvádor', 'Salvádorská republika', (select uuid from generic_code_list where code like 'SV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Rovníková Guinea', 'Republika Rovníkovej Guiney', (select uuid from generic_code_list where code like 'GQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Etiópia', 'Etiópia', (select uuid from generic_code_list where code like 'ET' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Eritrea', 'Eritrejský štát', (select uuid from generic_code_list where code like 'ER' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Estónsko', 'Estónska republika', (select uuid from generic_code_list where code like 'EE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Faerské ostrovy', 'Faerské ostrovy', (select uuid from generic_code_list where code like 'FO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Falklandy', 'Falklandské ostrovy', (select uuid from generic_code_list where code like 'FK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Juž. Georgia a J.S.o.', 'Južná Georgia a Južné Sandwichove ostrovy', (select uuid from generic_code_list where code like 'GS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Fidži', 'Fidžijská republika', (select uuid from generic_code_list where code like 'FJ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Fínsko', 'Fínska republika', (select uuid from generic_code_list where code like 'FI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Alandy', 'Alandy', (select uuid from generic_code_list where code like 'AX' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Francúzsko', 'Francúzska republika', (select uuid from generic_code_list where code like 'FR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Francúzska Guyana', 'Zámorský departmán Francúzskej Guyany', (select uuid from generic_code_list where code like 'GF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Francúzska Polynézia', 'Zámorské teritórium Francúzskej Polynézie', (select uuid from generic_code_list where code like 'PF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Francúzske južné územia', 'Francúzske južné a antarktické územia', (select uuid from generic_code_list where code like 'TF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Džibutsko', 'Džibutská republika', (select uuid from generic_code_list where code like 'DJ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Gabon', 'Gabonská republika', (select uuid from generic_code_list where code like 'GA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Gruzínsko', 'Gruzínska republika', (select uuid from generic_code_list where code like 'GE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Gambia', 'Gambijská republika', (select uuid from generic_code_list where code like 'GM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Palestína', 'Palestína', (select uuid from generic_code_list where code like 'PS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nemecko', 'Nemecká spolková republika', (select uuid from generic_code_list where code like 'DE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Ghana', 'Ghanská republika', (select uuid from generic_code_list where code like 'GH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Gibraltár', 'Gibraltár', (select uuid from generic_code_list where code like 'GI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kiribati', 'Kiribatská republika', (select uuid from generic_code_list where code like 'KI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Grécko', 'Grécka republika', (select uuid from generic_code_list where code like 'GR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Grónsko', 'Grónsko', (select uuid from generic_code_list where code like 'GL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Grenada', 'Grenada', (select uuid from generic_code_list where code like 'GD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Guadeloupe', 'Zámorský departmán Guadeloupu a závislých území', (select uuid from generic_code_list where code like 'GP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Guam', 'Guamské teritórium', (select uuid from generic_code_list where code like 'GU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Guatemala', 'Guatemalská republika', (select uuid from generic_code_list where code like 'GT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Guinea', 'Guinejská republika', (select uuid from generic_code_list where code like 'GN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Guyana', 'Guyanská kooperatívna republika', (select uuid from generic_code_list where code like 'GY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Haiti', 'Haitská republika', (select uuid from generic_code_list where code like 'HT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Heardov ostrov', 'Teritórium Heardovho ostrova a Macdonaldových ostrovov', (select uuid from generic_code_list where code like 'HM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Vatikán', 'Svätá Stolica (Vatikánsky mestský štát)', (select uuid from generic_code_list where code like 'VA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Honduras', 'Honduraská republika', (select uuid from generic_code_list where code like 'HN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Hongkong', 'Hongkong', (select uuid from generic_code_list where code like 'HK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Maďarsko', 'Maďarsko', (select uuid from generic_code_list where code like 'HU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Island', 'Islandská republika', (select uuid from generic_code_list where code like 'IS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'India', 'Indická republika', (select uuid from generic_code_list where code like 'IN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Indonézia', 'Indonézska republika', (select uuid from generic_code_list where code like 'ID' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Irán', 'Iránska islamská republika', (select uuid from generic_code_list where code like 'IR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Irak', 'Iracká republika', (select uuid from generic_code_list where code like 'IQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Írsko', 'Írsko', (select uuid from generic_code_list where code like 'IE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Izrael', 'Izraelský štát', (select uuid from generic_code_list where code like 'IL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Taliansko', 'Talianska republika', (select uuid from generic_code_list where code like 'IT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pobrežie slonoviny', 'Republika Pobrežia slonoviny', (select uuid from generic_code_list where code like 'CI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Jamajka', 'Jamajka', (select uuid from generic_code_list where code like 'JM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Japonsko', 'Japonsko', (select uuid from generic_code_list where code like 'JP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kazachstan', 'Kazašská republika', (select uuid from generic_code_list where code like 'KZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Jordánsko', 'Jordánske hášimovské kráľovstvo', (select uuid from generic_code_list where code like 'JO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Keňa', 'Kenská republika', (select uuid from generic_code_list where code like 'KE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kórej.ľudovodem.rep.', 'Kórejská ľudovodemokratická republika', (select uuid from generic_code_list where code like 'KP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kórejská republika', 'Kórejská republika', (select uuid from generic_code_list where code like 'KR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kuvajt', 'Kuvajtský štát', (select uuid from generic_code_list where code like 'KW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kirgizsko', 'Kirgizská republika', (select uuid from generic_code_list where code like 'KG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Laos', 'Laoská ľudovodemokratická republika', (select uuid from generic_code_list where code like 'LA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Libanon', 'Libanonská republika', (select uuid from generic_code_list where code like 'LB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Lesotho', 'Lesothské kráľovstvo', (select uuid from generic_code_list where code like 'LS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Lotyšsko', 'Lotyšská republika', (select uuid from generic_code_list where code like 'LV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Libéria', 'Libérijská republika', (select uuid from generic_code_list where code like 'LR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Líbya', 'Veľká líbyjská arabská ľudová socialistická džamahírija', (select uuid from generic_code_list where code like 'LY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Lichtenštajnsko', 'Lichtenštajnské kniežatstvo', (select uuid from generic_code_list where code like 'LI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Litva', 'Litovská republika', (select uuid from generic_code_list where code like 'LT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Luxembursko', 'Luxemburské veľkovojvodstvo', (select uuid from generic_code_list where code like 'LU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Macao', 'Macao', (select uuid from generic_code_list where code like 'MO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Madagaskar', 'Madagaskarská republika', (select uuid from generic_code_list where code like 'MG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Malawi', 'Malawijská republika', (select uuid from generic_code_list where code like 'MW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Malajzia', 'Malajzia', (select uuid from generic_code_list where code like 'MY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Maldivy', 'Maldivská republika', (select uuid from generic_code_list where code like 'MV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mali', 'Malijská republika', (select uuid from generic_code_list where code like 'ML' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Malta', 'Maltská republika', (select uuid from generic_code_list where code like 'MT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Martinik', 'Martinický zámorský departmán', (select uuid from generic_code_list where code like 'MQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mauritánia', 'Mauritánska islamská republika', (select uuid from generic_code_list where code like 'MR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Maurícius', 'Maurícijská republika', (select uuid from generic_code_list where code like 'MU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mexiko', 'Spojené štáty mexické', (select uuid from generic_code_list where code like 'MX' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Monako', 'Monacké kniežatstvo', (select uuid from generic_code_list where code like 'MC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mongolsko', 'Mongolsko', (select uuid from generic_code_list where code like 'MN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Moldavsko', 'Moldavská republika', (select uuid from generic_code_list where code like 'MD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Čierna Hora', 'Čiernohorská republika', (select uuid from generic_code_list where code like 'ME' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Montserrat', 'Montserrat', (select uuid from generic_code_list where code like 'MS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Maroko', 'Marocké kráľovstvo', (select uuid from generic_code_list where code like 'MA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mozambik', 'Mozambická republika', (select uuid from generic_code_list where code like 'MZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Omán', 'Ománsky sultanát', (select uuid from generic_code_list where code like 'OM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Namíbia', 'Namíbijská republika', (select uuid from generic_code_list where code like 'NA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nauru', 'Nauruská republika', (select uuid from generic_code_list where code like 'NR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nepál', 'Nepálske kráľovstvo', (select uuid from generic_code_list where code like 'NP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Holandsko', 'Holandské kráľovstvo', (select uuid from generic_code_list where code like 'NL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Holandské Antily', 'Holandské Antily', (select uuid from generic_code_list where code like 'AN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Aruba', 'Aruba', (select uuid from generic_code_list where code like 'AW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nová Kaledónia', 'Zamorské teritórium Novej Kaledónie a závislých území', (select uuid from generic_code_list where code like 'NC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Vanuatu', 'Vanuatská republika', (select uuid from generic_code_list where code like 'VU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nový Zéland', 'Nový Zéland', (select uuid from generic_code_list where code like 'NZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nikaragua', 'Nikaragujská republika', (select uuid from generic_code_list where code like 'NI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Niger', 'Nigerská republika', (select uuid from generic_code_list where code like 'NE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nigéria', 'Nigérijská federatívna republika', (select uuid from generic_code_list where code like 'NG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Niue', 'Samosprávne zámorské teritórium ostrova Niue', (select uuid from generic_code_list where code like 'NU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Norfolk', 'Teritórium ostrova Norfolk', (select uuid from generic_code_list where code like 'NF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nórsko', 'Nórske kráľovstvo', (select uuid from generic_code_list where code like 'NO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Severné Mariány', 'Spoločenstvo ostrovov Severné Mariány', (select uuid from generic_code_list where code like 'MP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Menšie odľahlé ostr.US', 'Menšie odľahlé ostrovy Spojených štátov', (select uuid from generic_code_list where code like 'UM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mikronézia', 'Mikronézske federatívne štáty', (select uuid from generic_code_list where code like 'FM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Marshallove ostrovy', 'Republika Marshallových ostrovov', (select uuid from generic_code_list where code like 'MH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Palau', 'Palauská republika', (select uuid from generic_code_list where code like 'PW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pakistan', 'Pakistanská islamská republika', (select uuid from generic_code_list where code like 'PK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Panama', 'Panamská republika', (select uuid from generic_code_list where code like 'PA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Papua-Nová Guinea', 'Papua-Nová Guinea', (select uuid from generic_code_list where code like 'PG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Paraguaj', 'Paraguajská republika', (select uuid from generic_code_list where code like 'PY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Peru', 'Peruánska republika', (select uuid from generic_code_list where code like 'PE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Filipíny', 'Filipínska republika', (select uuid from generic_code_list where code like 'PH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pitcairnove ostrovy', 'Pitcairnove ostrovy', (select uuid from generic_code_list where code like 'PN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Poľsko', 'Poľská republika', (select uuid from generic_code_list where code like 'PL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Portugalsko', 'Portugalská republika', (select uuid from generic_code_list where code like 'PT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Guinea-Bissau', 'Guinejsko-bissauská republika', (select uuid from generic_code_list where code like 'GW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Východný Timor', 'Východný Timor', (select uuid from generic_code_list where code like 'TL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Portoriko', 'Portorické spoločenstvo', (select uuid from generic_code_list where code like 'PR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Katar', 'Katarský štát', (select uuid from generic_code_list where code like 'QA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Réunion', 'Réunionský zámorský departmán', (select uuid from generic_code_list where code like 'RE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Rumunsko', 'Rumunsko', (select uuid from generic_code_list where code like 'RO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Rusko', 'Ruská federácia', (select uuid from generic_code_list where code like 'RU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Rwanda', 'Rwandská republika', (select uuid from generic_code_list where code like 'RW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Svätá Helena', 'Svätá Helena a závislé územia', (select uuid from generic_code_list where code like 'SH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Svätý Krištof', 'Svätý Krištof a Nevis', (select uuid from generic_code_list where code like 'KN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Anguilla', 'Anguilla', (select uuid from generic_code_list where code like 'AI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Svätá Lucia', 'Svätá Lucia', (select uuid from generic_code_list where code like 'LC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Saint Pierre a Miquelon', 'Ostrovy Saint Pierre a Miquelon', (select uuid from generic_code_list where code like 'PM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Svätý Vincent', 'Svätý Vincent a Grenadíny', (select uuid from generic_code_list where code like 'VC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'San Maríno', 'Sanmarínska republika', (select uuid from generic_code_list where code like 'SM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Svätý Tomáš', 'Demokratická republika Svätého Tomáša a Princovho ostrova', (select uuid from generic_code_list where code like 'ST' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Saudská Arábia', 'Saudskoarabské kráľovstvo', (select uuid from generic_code_list where code like 'SA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Senegal', 'Senegalská republika', (select uuid from generic_code_list where code like 'SN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Srbsko', 'Srbská republika', (select uuid from generic_code_list where code like 'RS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Seychely', 'Seychelská republika', (select uuid from generic_code_list where code like 'SC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Sierra Leone', 'Sierraleonská republika', (select uuid from generic_code_list where code like 'SL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Singapur', 'Singapurská republika', (select uuid from generic_code_list where code like 'SG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Slovensko', 'Slovenská republika', (select uuid from generic_code_list where code like 'SK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Vietnam', 'Vietnamská socialistická republika', (select uuid from generic_code_list where code like 'VN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Slovinsko', 'Slovinská republika', (select uuid from generic_code_list where code like 'SI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Somálsko', 'Somálska demokratická republika', (select uuid from generic_code_list where code like 'SO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Južná Afrika', 'Juhoafrická republika', (select uuid from generic_code_list where code like 'ZA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zimbabwe', 'Zimbabwianska republika', (select uuid from generic_code_list where code like 'ZW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Španielsko', 'Španielske kráľovstvo', (select uuid from generic_code_list where code like 'ES' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Západná Sahara', 'Západná Sahara', (select uuid from generic_code_list where code like 'EH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Sudán', 'Sudánska republika', (select uuid from generic_code_list where code like 'SD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Surinam', 'Surinamská republika', (select uuid from generic_code_list where code like 'SR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Svalbard Jan Mayen', 'Svalbard Jan Mayen', (select uuid from generic_code_list where code like 'SJ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Eswatini', 'Eswatinské kráľovstvo', (select uuid from generic_code_list where code like 'SZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Švédsko', 'Švédske kráľovstvo', (select uuid from generic_code_list where code like 'SE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Švajčiarsko', 'Švajčiarska konfederácia', (select uuid from generic_code_list where code like 'CH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Sýria', 'Sýrska arabská republika', (select uuid from generic_code_list where code like 'SY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Tadžikistan', 'Tadžická republika', (select uuid from generic_code_list where code like 'TJ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Thajsko', 'Thajské kráľovstvo', (select uuid from generic_code_list where code like 'TH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Togo', 'Togská republika', (select uuid from generic_code_list where code like 'TG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Tokelau', 'Tokelauské ostrovy', (select uuid from generic_code_list where code like 'TK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Tonga', 'Tongské kráľovstvo', (select uuid from generic_code_list where code like 'TO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Trinidad a Tobago', 'Republika Trinidadu a Tobaga', (select uuid from generic_code_list where code like 'TT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Spojené arabské emiráty', 'Spojené arabské emiráty', (select uuid from generic_code_list where code like 'AE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Tunisko', 'Tuniská republika', (select uuid from generic_code_list where code like 'TN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Turecko', 'Turecká republika', (select uuid from generic_code_list where code like 'TR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Turkménsko', 'Turkménsko', (select uuid from generic_code_list where code like 'TM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Turks a Caicos', 'Ostrovy Turks a Caicos', (select uuid from generic_code_list where code like 'TC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Tuvalu', 'Tuvalu', (select uuid from generic_code_list where code like 'TV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Uganda', 'Ugandská republika', (select uuid from generic_code_list where code like 'UG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Ukrajina', 'Ukrajina', (select uuid from generic_code_list where code like 'UA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Severné Macedónsko', 'Severomacedónska republika', (select uuid from generic_code_list where code like 'MK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Egypt', 'Egyptská arabská republika', (select uuid from generic_code_list where code like 'EG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Spojené kráľovstvo', 'Spojené kráľovstvo Veľkej Británie a Severného Írska', (select uuid from generic_code_list where code like 'GB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Tanzánia', 'Tanzánijská zjednotená republika', (select uuid from generic_code_list where code like 'TZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Spojené štáty', 'Spojené štáty americké', (select uuid from generic_code_list where code like 'US' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Americké panenské ostr.', 'Panenské ostrovy Spojených štátov', (select uuid from generic_code_list where code like 'VI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Burkina', 'Burkina Faso', (select uuid from generic_code_list where code like 'BF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Uruguaj', 'Uruguajská východná republika', (select uuid from generic_code_list where code like 'UY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Uzbekistan', 'Uzbecká republika', (select uuid from generic_code_list where code like 'UZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Venezuela', 'Venezuelská bolívarovská republika', (select uuid from generic_code_list where code like 'VE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Wallis a Futuna', 'Zámorské teritórium Wallisu a Futuny', (select uuid from generic_code_list where code like 'WF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Samoa', 'Samojský nezávislý štát', (select uuid from generic_code_list where code like 'WS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Jemen', 'Jemenská republika', (select uuid from generic_code_list where code like 'YE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Srbsko a Čierna Hora', 'Srbsko a Čierna Hora', (select uuid from generic_code_list where code like 'CS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zambia', 'Zambijská republika', (select uuid from generic_code_list where code like 'ZM' and type = 'COUNTRY'));


INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Afghanistan', 'Afghanistan', (select uuid from generic_code_list where code like 'AF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Albania', 'Republic of Albania', (select uuid from generic_code_list where code like 'AL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Antarctica', 'Antarctica', (select uuid from generic_code_list where code like 'AQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Algeria', 'People`s Democratic Republic of Algeria', (select uuid from generic_code_list where code like 'DZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'American Samoa', 'American Samoa', (select uuid from generic_code_list where code like 'AS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Andorra', 'Principality of Andorra', (select uuid from generic_code_list where code like 'AD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Angola', 'Republic of Angola', (select uuid from generic_code_list where code like 'AO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Antigua and Barbuda', 'Antigua and Barbuda', (select uuid from generic_code_list where code like 'AG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Azerbaijan', 'Republic of Azerbaijan', (select uuid from generic_code_list where code like 'AZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Argentina', 'Argentine Republic', (select uuid from generic_code_list where code like 'AR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Australia', 'Australia', (select uuid from generic_code_list where code like 'AU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Austria', 'Republic of Austria', (select uuid from generic_code_list where code like 'AT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bahamas', 'Commonwealth of The Bahamas', (select uuid from generic_code_list where code like 'BS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bahrain', 'Kingdom of Bahrain', (select uuid from generic_code_list where code like 'BH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bangladesh', 'People`s Republic of Bangladesh', (select uuid from generic_code_list where code like 'BD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Armenia', 'Republic of Armenia', (select uuid from generic_code_list where code like 'AM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Barbados', 'Barbados', (select uuid from generic_code_list where code like 'BB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Belgium', 'Kingdom of Belgium', (select uuid from generic_code_list where code like 'BE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bermuda', 'Bermuda', (select uuid from generic_code_list where code like 'BM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bhutan', 'Kingdom of Bhutan', (select uuid from generic_code_list where code like 'BT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bolivia', 'Republic of Bolivia', (select uuid from generic_code_list where code like 'BO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bosnia and Herzegovina', 'Bosnia and Herzegovina', (select uuid from generic_code_list where code like 'BA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Botswana', 'Republic of Botswana', (select uuid from generic_code_list where code like 'BW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bouvet Island', 'Bouvet Island', (select uuid from generic_code_list where code like 'BV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Brazil', 'Federative Republic of Brazil', (select uuid from generic_code_list where code like 'BR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Belize', 'Belize', (select uuid from generic_code_list where code like 'BZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'British Ind.Ocean Terr.', 'British Indian Ocean Territory', (select uuid from generic_code_list where code like 'IO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Solomon Islands', 'Solomon Islands', (select uuid from generic_code_list where code like 'SB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Virgin Islands, British', 'British Virgin Islands', (select uuid from generic_code_list where code like 'VG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Brunei Darussalam', 'Brunei Darussalam', (select uuid from generic_code_list where code like 'BN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bulgaria', 'Republic of Bulgaria', (select uuid from generic_code_list where code like 'BG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Myanmar', 'Union of Myanmar', (select uuid from generic_code_list where code like 'MM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Burundi', 'Republic of Burundi', (select uuid from generic_code_list where code like 'BI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Belarus', 'Republic of Belarus', (select uuid from generic_code_list where code like 'BY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cambodia', 'Kingdom of Cambodia', (select uuid from generic_code_list where code like 'KH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cameroon', 'Republic of Cameroon', (select uuid from generic_code_list where code like 'CM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Canada', 'Canada', (select uuid from generic_code_list where code like 'CA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cabo Verde', 'Republic of Cabo Verde', (select uuid from generic_code_list where code like 'CV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cayman Islands', 'Cayman Islands', (select uuid from generic_code_list where code like 'KY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Central African Rep.', 'Central African Republic', (select uuid from generic_code_list where code like 'CF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Sri Lanka', 'Democratic Socialist Republic of Sri Lanka', (select uuid from generic_code_list where code like 'LK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Chad', 'Republic of Chad', (select uuid from generic_code_list where code like 'TD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Chile', 'Republic of Chile', (select uuid from generic_code_list where code like 'CL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'China', 'People`s Republic of China', (select uuid from generic_code_list where code like 'CN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Taiwan', 'Republic of China', (select uuid from generic_code_list where code like 'TW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Christmas Island', 'Christmas Island', (select uuid from generic_code_list where code like 'CX' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cocos (Keeling) Islands', 'Cocos (Keeling) Islands', (select uuid from generic_code_list where code like 'CC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Colombia', 'Republic of Colombia', (select uuid from generic_code_list where code like 'CO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Comoros', 'Union of the Comoros', (select uuid from generic_code_list where code like 'KM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mayotte', 'Mayotte', (select uuid from generic_code_list where code like 'YT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Congo', 'Republic of the Congo', (select uuid from generic_code_list where code like 'CG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Congo, the Dem.R. of the', 'The Democratic Republic of the Congo', (select uuid from generic_code_list where code like 'CD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cook Islands', 'Cook Islands', (select uuid from generic_code_list where code like 'CK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Costa Rica', 'Republic of Costa Rica', (select uuid from generic_code_list where code like 'CR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Croatia', 'Republic of Croatia', (select uuid from generic_code_list where code like 'HR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cuba', 'Republic of Cuba', (select uuid from generic_code_list where code like 'CU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cyprus', 'Republic of Cyprus', (select uuid from generic_code_list where code like 'CY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Czechia', 'Czech Republic', (select uuid from generic_code_list where code like 'CZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Benin', 'Republic of Benin', (select uuid from generic_code_list where code like 'BJ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Denmark', 'Kingdom of Denmark', (select uuid from generic_code_list where code like 'DK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Dominica', 'Commonwealth of Dominica', (select uuid from generic_code_list where code like 'DM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Dominican republic', 'Dominican Republic', (select uuid from generic_code_list where code like 'DO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Ecuador', 'Republic of Ecuador', (select uuid from generic_code_list where code like 'EC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'El Salvador', 'Republic of El Salvador', (select uuid from generic_code_list where code like 'SV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Equatorial Guinea', 'Republic of Equatorial Guinea', (select uuid from generic_code_list where code like 'GQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Ethiopia', 'Federal Democratic Republic of Ethiopia', (select uuid from generic_code_list where code like 'ET' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Eritrea', 'Eritrea', (select uuid from generic_code_list where code like 'ER' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Estonia', 'Republic of Estonia', (select uuid from generic_code_list where code like 'EE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Faroe Islands', 'Faroe Islands', (select uuid from generic_code_list where code like 'FO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Falkland Isl. (Malvinas)', 'Falkland Islands (Malvinas)', (select uuid from generic_code_list where code like 'FK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'S.Georgia, s.Sand. Isl.', 'South Georgia and the South Sandwich Islands', (select uuid from generic_code_list where code like 'GS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Fiji', 'Republic of the Fiji Islands', (select uuid from generic_code_list where code like 'FJ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Finland', 'Republic of Finland', (select uuid from generic_code_list where code like 'FI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Aland Islands', 'Aland Islands', (select uuid from generic_code_list where code like 'AX' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'France', 'French Republic', (select uuid from generic_code_list where code like 'FR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'French Guiana', 'French Guiana', (select uuid from generic_code_list where code like 'GF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'French Polynesia', 'French Polynesia', (select uuid from generic_code_list where code like 'PF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'French Southern Territ.', 'French Southern Territories', (select uuid from generic_code_list where code like 'TF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Djibouti', 'Republic of Djibouti', (select uuid from generic_code_list where code like 'DJ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Gabon', 'Gabonese Republic', (select uuid from generic_code_list where code like 'GA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Georgia', 'Georgia', (select uuid from generic_code_list where code like 'GE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Gambia', 'Republic of the Gambia', (select uuid from generic_code_list where code like 'GM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Palestin.Territ.Occupied', 'Occupied Palestinian Territory', (select uuid from generic_code_list where code like 'PS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Germany', 'Federal Republic of Germany', (select uuid from generic_code_list where code like 'DE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Ghana', 'Republic of Ghana', (select uuid from generic_code_list where code like 'GH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Gibraltar', 'Gibraltar', (select uuid from generic_code_list where code like 'GI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Kiribati', 'Republic of Kiribati', (select uuid from generic_code_list where code like 'KI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Greece', 'Hellenic Republic', (select uuid from generic_code_list where code like 'GR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Greenland', 'Greenland', (select uuid from generic_code_list where code like 'GL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Grenada', 'Grenada', (select uuid from generic_code_list where code like 'GD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Guadeloupe', 'Guadeloupe', (select uuid from generic_code_list where code like 'GP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Guam', 'Guam', (select uuid from generic_code_list where code like 'GU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Guatemala', 'Republic of Guatemala', (select uuid from generic_code_list where code like 'GT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Guinea', 'Republic of Guinea', (select uuid from generic_code_list where code like 'GN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Guyana', 'Co-operative Republic of Guyana', (select uuid from generic_code_list where code like 'GY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Haiti', 'Republic of Haiti', (select uuid from generic_code_list where code like 'HT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Heard and McDon.Islands', 'Heard Island and McDonald Islands', (select uuid from generic_code_list where code like 'HM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Holy See (Vatican CS)', 'Holy See (Vatican City State)', (select uuid from generic_code_list where code like 'VA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Honduras', 'Republic of Honduras', (select uuid from generic_code_list where code like 'HN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Hong Kong', 'Hong Kong Special Administrative Region of China', (select uuid from generic_code_list where code like 'HK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Hungary', 'Republic of Hungary', (select uuid from generic_code_list where code like 'HU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Iceland', 'Republic of Iceland', (select uuid from generic_code_list where code like 'IS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'India', 'Republic of India', (select uuid from generic_code_list where code like 'IN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Indonesia', 'Republic of Indonesia', (select uuid from generic_code_list where code like 'ID' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Iran,Islamic Republic of', 'Islamic Republic of Iran', (select uuid from generic_code_list where code like 'IR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Iraq', 'Republic of Iraq', (select uuid from generic_code_list where code like 'IQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Ireland', 'Ireland', (select uuid from generic_code_list where code like 'IE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Israel', 'State of Israel', (select uuid from generic_code_list where code like 'IL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Italy', 'Italian Republic', (select uuid from generic_code_list where code like 'IT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Côte d`Ivoire', 'Republic of Côte d`Ivoire', (select uuid from generic_code_list where code like 'CI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Jamaica', 'Jamaica', (select uuid from generic_code_list where code like 'JM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Japan', 'Japan', (select uuid from generic_code_list where code like 'JP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Kazakhstan', 'Republic of Kazakhstan', (select uuid from generic_code_list where code like 'KZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Jordan', 'Hashemite Kingdom of Jordan', (select uuid from generic_code_list where code like 'JO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Kenya', 'Republic of Kenya', (select uuid from generic_code_list where code like 'KE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Korea, Dem.Peopl.Rep. of', 'Democratic People`s Republic of Korea', (select uuid from generic_code_list where code like 'KP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Korea, Republic of', 'Republic of Korea', (select uuid from generic_code_list where code like 'KR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Kuwait', 'State of Kuwait', (select uuid from generic_code_list where code like 'KW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Kyrgyzstan', 'Kyrgyz Republic', (select uuid from generic_code_list where code like 'KG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Lao People`s Dem. Rep.', 'Lao People`s Democratic Republic', (select uuid from generic_code_list where code like 'LA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Lebanon', 'Lebanese Republic', (select uuid from generic_code_list where code like 'LB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Lesotho', 'Kingdom of Lesotho', (select uuid from generic_code_list where code like 'LS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Latvia', 'Republic of Latvia', (select uuid from generic_code_list where code like 'LV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Liberia', 'Republic of Liberia', (select uuid from generic_code_list where code like 'LR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Libyan Arab Jamahiriya', 'Socialist People`s Libyan Arab Jamahiriya', (select uuid from generic_code_list where code like 'LY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Liechtenstein', 'Principality of Liechtenstein', (select uuid from generic_code_list where code like 'LI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Lithuania', 'Republic of Lithuania', (select uuid from generic_code_list where code like 'LT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Luxembourg', 'Grand Duchy of Luxembourg', (select uuid from generic_code_list where code like 'LU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Macao', 'Macao Special Administrative Region of China', (select uuid from generic_code_list where code like 'MO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Madagascar', 'Republic of Madagascar', (select uuid from generic_code_list where code like 'MG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Malawi', 'Republic of Malawi', (select uuid from generic_code_list where code like 'MW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Malaysia', 'Malaysia', (select uuid from generic_code_list where code like 'MY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Maldives', 'Republic of Maldives', (select uuid from generic_code_list where code like 'MV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mali', 'Republic of Mali', (select uuid from generic_code_list where code like 'ML' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Malta', 'Republic of Malta', (select uuid from generic_code_list where code like 'MT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Martinique', 'Martinique', (select uuid from generic_code_list where code like 'MQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mauritania', 'Islamic Republic of Mauritania', (select uuid from generic_code_list where code like 'MR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mauritius', 'Republic of Mauritius', (select uuid from generic_code_list where code like 'MU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mexico', 'United Mexican States', (select uuid from generic_code_list where code like 'MX' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Monaco', 'Principality of Monaco', (select uuid from generic_code_list where code like 'MC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mongolia', 'Mongolia', (select uuid from generic_code_list where code like 'MN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Moldova, Republic of', 'Republic of Moldova', (select uuid from generic_code_list where code like 'MD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Montenegro', 'Republic of Montenegro', (select uuid from generic_code_list where code like 'ME' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Montserrat', 'Montserrat', (select uuid from generic_code_list where code like 'MS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Morocco', 'Kingdom of Morocco', (select uuid from generic_code_list where code like 'MA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mozambique', 'Republic of Mozambique', (select uuid from generic_code_list where code like 'MZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Oman', 'Sultanate of Oman', (select uuid from generic_code_list where code like 'OM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Namibia', 'Republic of Namibia', (select uuid from generic_code_list where code like 'NA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Nauru', 'Republic of Nauru', (select uuid from generic_code_list where code like 'NR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Nepal', 'Kingdom of Nepal', (select uuid from generic_code_list where code like 'NP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Netherlands', 'Kingdom of the Netherlands', (select uuid from generic_code_list where code like 'NL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Netherlands Antilles', 'Netherlands Antilles', (select uuid from generic_code_list where code like 'AN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Aruba', 'Aruba', (select uuid from generic_code_list where code like 'AW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'New Caledonia', 'New Caledonia', (select uuid from generic_code_list where code like 'NC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Vanuatu', 'Republic of Vanuatu', (select uuid from generic_code_list where code like 'VU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'New Zealand', 'New Zealand', (select uuid from generic_code_list where code like 'NZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Nicaragua', 'Republic of Nicaragua', (select uuid from generic_code_list where code like 'NI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Niger', 'Republic of the Niger', (select uuid from generic_code_list where code like 'NE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Nigeria', 'Federal Republic of Nigeria', (select uuid from generic_code_list where code like 'NG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Niue', 'Republic of Niue', (select uuid from generic_code_list where code like 'NU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Norfolk Island', 'Norfolk Island', (select uuid from generic_code_list where code like 'NF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Norway', 'Kingdom of Norway', (select uuid from generic_code_list where code like 'NO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Northern Mariana Islands', 'Commonwealth of the Northern Mariana Islands', (select uuid from generic_code_list where code like 'MP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'US Minor Outlying Isl.', 'United States Minor Outlying Islands', (select uuid from generic_code_list where code like 'UM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Micronesia, Fed.St. of', 'Federated States of Micronesia', (select uuid from generic_code_list where code like 'FM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Marshall Islands', 'Republic of the Marshall Islands', (select uuid from generic_code_list where code like 'MH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Palau', 'Republic of Palau', (select uuid from generic_code_list where code like 'PW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Pakistan', 'Islamic Republic of Pakistan', (select uuid from generic_code_list where code like 'PK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Panama', 'Republic of Panama', (select uuid from generic_code_list where code like 'PA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Papua New Guinea', 'Papua New Guinea', (select uuid from generic_code_list where code like 'PG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Paraguay', 'Republic of Paraguay', (select uuid from generic_code_list where code like 'PY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Peru', 'Republic of Peru', (select uuid from generic_code_list where code like 'PE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Philippines', 'Republic of Philippines', (select uuid from generic_code_list where code like 'PH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Pitcairn', 'Pitcairn Islands', (select uuid from generic_code_list where code like 'PN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Poland', 'Republic of Poland', (select uuid from generic_code_list where code like 'PL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Portugal', 'Portuguese Republic', (select uuid from generic_code_list where code like 'PT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Guinea-Bissau', 'Republic of Guinea-Bissau', (select uuid from generic_code_list where code like 'GW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'East Timor', 'Democratic Republic of East Timor', (select uuid from generic_code_list where code like 'TL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Puerto Rico', 'Puerto Rico', (select uuid from generic_code_list where code like 'PR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Qatar', 'State of Qatar', (select uuid from generic_code_list where code like 'QA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Réunion', 'Réunion', (select uuid from generic_code_list where code like 'RE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Romania', 'Romania', (select uuid from generic_code_list where code like 'RO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Russian Federation', 'Russian Federation', (select uuid from generic_code_list where code like 'RU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Rwanda', 'Rwandese Republic', (select uuid from generic_code_list where code like 'RW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Saint Helena', 'Saint Helena', (select uuid from generic_code_list where code like 'SH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Saint Kitts and Nevis', 'Saint Kitts and Nevis', (select uuid from generic_code_list where code like 'KN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Anguilla', 'Anguilla', (select uuid from generic_code_list where code like 'AI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Saint Lucia', 'Saint Lucia', (select uuid from generic_code_list where code like 'LC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'St. Pierre and Miquelon', 'Saint Pierre and Miquelon', (select uuid from generic_code_list where code like 'PM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'St.Vincent,the Grenadin.', 'Saint Vincent and the Grenadines', (select uuid from generic_code_list where code like 'VC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'San Marino', 'Republic of San Marino', (select uuid from generic_code_list where code like 'SM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Sao Tome and Principe', 'Democratic Republic of Sao Tome and Principe', (select uuid from generic_code_list where code like 'ST' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Saudi Arabia', 'Kingdom of Saudi Arabia', (select uuid from generic_code_list where code like 'SA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Senegal', 'Republic of Senegal', (select uuid from generic_code_list where code like 'SN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Serbia', 'Republic of Serbia', (select uuid from generic_code_list where code like 'RS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Seychelles', 'Republic of Seychelles', (select uuid from generic_code_list where code like 'SC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Sierra Leone', 'Republic of Sierra Leone', (select uuid from generic_code_list where code like 'SL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Singapore', 'Republic of Singapore', (select uuid from generic_code_list where code like 'SG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Slovakia', 'Slovak Republic', (select uuid from generic_code_list where code like 'SK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Viet Nam', 'Socialist Republic of Viet Nam', (select uuid from generic_code_list where code like 'VN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Slovenia', 'Republic of Slovenia', (select uuid from generic_code_list where code like 'SI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Somalia', 'Somali Republic', (select uuid from generic_code_list where code like 'SO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'South Africa', 'Republic of South Africa', (select uuid from generic_code_list where code like 'ZA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Zimbabwe', 'Republic of Zimbabwe', (select uuid from generic_code_list where code like 'ZW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Spain', 'Kingdom of Spain', (select uuid from generic_code_list where code like 'ES' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Western Sahara', 'Western Sahara', (select uuid from generic_code_list where code like 'EH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Sudan', 'Republic of the Sudan', (select uuid from generic_code_list where code like 'SD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Suriname', 'Republic of Suriname', (select uuid from generic_code_list where code like 'SR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Svalbard and Jan Mayen', 'Svalbard and Jan Mayen', (select uuid from generic_code_list where code like 'SJ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Eswatini', 'Kingdom of Eswatini', (select uuid from generic_code_list where code like 'SZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Sweden', 'Kingdom of Sweden', (select uuid from generic_code_list where code like 'SE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Switzerland', 'Swiss Confederation', (select uuid from generic_code_list where code like 'CH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Syrian Arab Republic', 'Syrian Arab Republic', (select uuid from generic_code_list where code like 'SY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Tajikistan', 'Republic of Tajikistan', (select uuid from generic_code_list where code like 'TJ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Thailand', 'Kingdom of Thailand', (select uuid from generic_code_list where code like 'TH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Togo', 'Togolese Republic', (select uuid from generic_code_list where code like 'TG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Tokelau', 'Tokelau', (select uuid from generic_code_list where code like 'TK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Tonga', 'Kingdom of Tonga', (select uuid from generic_code_list where code like 'TO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Trinidad and Tobago', 'Republic of Trinidad and Tobago', (select uuid from generic_code_list where code like 'TT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'United Arab Emirates', 'United Arab Emirates', (select uuid from generic_code_list where code like 'AE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Tunisia', 'Republic of Tunisia', (select uuid from generic_code_list where code like 'TN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Turkey', 'Republic of Turkey', (select uuid from generic_code_list where code like 'TR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Turkmenistan', 'Turkmenistan', (select uuid from generic_code_list where code like 'TM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Turks and Caicos Islands', 'Turks and Caicos Islands', (select uuid from generic_code_list where code like 'TC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Tuvalu', 'Tuvalu', (select uuid from generic_code_list where code like 'TV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Uganda', 'Republic of Uganda', (select uuid from generic_code_list where code like 'UG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Ukraine', 'Ukraine', (select uuid from generic_code_list where code like 'UA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'North Macedonia', 'Republic of North Macedonia', (select uuid from generic_code_list where code like 'MK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Egypt', 'Arab Republic of Egypt', (select uuid from generic_code_list where code like 'EG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'United Kingdom', 'United Kingdom of Great Britain and Northern Ireland', (select uuid from generic_code_list where code like 'GB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Tanzania, United Rep.of', 'United Republic of Tanzania', (select uuid from generic_code_list where code like 'TZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'United States', 'United States of America', (select uuid from generic_code_list where code like 'US' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Virgin Islands, U.S.', 'Virgin Islands of the United States', (select uuid from generic_code_list where code like 'VI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Burkina Faso', 'Burkina Faso', (select uuid from generic_code_list where code like 'BF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Uruguay', 'Eastern Republic of Uruguay', (select uuid from generic_code_list where code like 'UY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Uzbekistan', 'Republic of Uzbekistan', (select uuid from generic_code_list where code like 'UZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Venezuela', 'Bolivarian Republic of Venezuela', (select uuid from generic_code_list where code like 'VE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Wallis and Futuna', 'Wallis and Futuna', (select uuid from generic_code_list where code like 'WF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Samoa', 'Independent State of Samoa', (select uuid from generic_code_list where code like 'WS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Yemen', 'Republic of Yemen', (select uuid from generic_code_list where code like 'YE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Serbia and Montenegro', 'Serbia and Montenegro', (select uuid from generic_code_list where code like 'CS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Zambia', 'Republic of Zambia', (select uuid from generic_code_list where code like 'ZM' and type = 'COUNTRY'));