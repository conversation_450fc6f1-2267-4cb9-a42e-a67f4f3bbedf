-- add template_variable
insert into template_variable (uuid, created_at, updated_at , "name" , "type" , variable , "version" )
 values (uuid_generate_v4(), now(), now(), 'Externá vonkajšia URL na ktorej je spustený portlál', 'STRING', 'portalExternalUrl', 1);

-- update tempalte variables
insert into template_notification_variable (notification_template_uuid, template_variable_uuid)
    (select nt.uuid, tv.uuid from notification_template nt join template_variable tv on tv.variable = 'portalExternalUrl');

-- update template
update notification_template_i18n set email_body = regexp_replace(email_body, 'http[s]?://[^/]+', '${portalExternalUrl}');

