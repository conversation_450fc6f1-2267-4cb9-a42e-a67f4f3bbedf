

alter table audit_log rename column customer to customer_account_uuid;
alter table audit_log rename column employee to employee_account_uuid;
alter table audit_log add column business_partner_external_id CHARACTER VARYING(50);

alter table audit_log add constraint fk_audit_log_customer_account foreign key (customer_account_uuid) references customer_account(uuid) match simple 
	on update no action
	on delete no action;
	
alter table audit_log add constraint fk_audit_log_employee_account foreign key (employee_account_uuid) references employee_account(uuid) match simple 
	on update no action
	on delete no action;