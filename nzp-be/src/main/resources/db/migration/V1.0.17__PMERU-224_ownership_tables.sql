-------------------
-- business_partner_ownership
-------------------

CREATE TABLE business_partner_ownership
(
    created_at              TIMESTAMP WITH TIME ZONE    NOT NULL,

    type                    CHARACTER VARYING(32)       NOT NULL,
    inherited               boolean                     NOT NULL,
    customer_account_uuid   uuid                        NOT NULL,

    business_partner_uuid   uuid,

    CONSTRAINT pk_business_partner_ownership PRIMARY KEY (customer_account_uuid, business_partner_uuid),
    CONSTRAINT fk_business_partner_ownership_customer_account FOREIGN KEY (customer_account_uuid)
        REFERENCES customer_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk_business_partner_ownership_business_partner FOREIG<PERSON> KEY (business_partner_uuid)
        REFERENCES business_partner (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);



-------------------
-- contract_account_ownership
-------------------

CREATE TABLE contract_account_ownership
(
    created_at              TIMESTAMP WITH TIME ZONE    NOT NULL,

    type                    CHARACTER VARYING(32)       NOT NULL,
    inherited               boolean   			        NOT NULL,
    customer_account_uuid   uuid                        NOT NULL,

    contract_account_uuid   uuid,

    CONSTRAINT pk_contract_account_ownership PRIMARY KEY (customer_account_uuid, contract_account_uuid),
    CONSTRAINT fk_contract_account_ownership_customer_account FOREIGN KEY (customer_account_uuid)
        REFERENCES customer_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
     CONSTRAINT fk_contract_account_ownership_contract_account FOREIGN KEY (contract_account_uuid)
        REFERENCES contract_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);



-------------------
-- contract_ownership
-------------------

CREATE TABLE contract_ownership
(
    created_at              TIMESTAMP WITH TIME ZONE    NOT NULL,

    type                    CHARACTER VARYING(32)       NOT NULL,
    inherited               boolean   			        NOT NULL,
    customer_account_uuid   uuid                        NOT NULL,

    contract_uuid           uuid,

    CONSTRAINT pk_contract_ownership PRIMARY KEY (customer_account_uuid, contract_uuid),
    CONSTRAINT fk_contract_ownership_customer_account FOREIGN KEY (customer_account_uuid)
        REFERENCES customer_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
     CONSTRAINT fk_contract_ownership_contract FOREIGN KEY (contract_uuid)
        REFERENCES contract (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);



-------------------
-- contract_product_ownership
-------------------

CREATE TABLE contract_product_ownership
(
    created_at              TIMESTAMP WITH TIME ZONE    NOT NULL,

    type                    CHARACTER VARYING(32)       NOT NULL,
    inherited               boolean   			        NOT NULL,
    customer_account_uuid   uuid                        NOT NULL,

    contract_product_uuid   uuid,

    CONSTRAINT pk_contract_product_ownership PRIMARY KEY (customer_account_uuid, contract_product_uuid),
    CONSTRAINT fk_contract_product_ownership_customer_account FOREIGN KEY (customer_account_uuid)
        REFERENCES customer_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
     CONSTRAINT fk_contract_product_ownership_contract_product FOREIGN KEY (contract_product_uuid)
        REFERENCES contract_product (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);



-------------------
-- contract_product_ownership
-------------------

CREATE TABLE delivery_point_ownership
(
    created_at              TIMESTAMP WITH TIME ZONE    NOT NULL,

    type                    CHARACTER VARYING(32)       NOT NULL,
    inherited               boolean   			        NOT NULL,
    customer_account_uuid   uuid                        NOT NULL,

    delivery_point_uuid     uuid,

    CONSTRAINT pk_delivery_point_ownership PRIMARY KEY (customer_account_uuid, delivery_point_uuid),
    CONSTRAINT fk_delivery_point_ownership_customer_account FOREIGN KEY (customer_account_uuid)
        REFERENCES customer_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk_delivery_point_ownership_delivery_point FOREIGN KEY (delivery_point_uuid)
        REFERENCES delivery_point (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

CREATE VIEW v_sharing_summary AS
(select created_at, type, inherited, customer_account_uuid, target_uuid, business_partner_uuid, contract_account_uuid, contract_uuid, contract_product_uuid, delivery_point_uuid, entity_type
from (
         select created_at, type, inherited, customer_account_uuid, business_partner_uuid as target_uuid, business_partner_uuid, Null::UUID as contract_account_uuid, Null::UUID as contract_uuid, Null::UUID as contract_product_uuid, Null::UUID as delivery_point_uuid, CAST('BUSINESS_PARTNER' as CHARACTER VARYING(32)) as entity_type
         from business_partner_ownership
         union
         select created_at, type, inherited, customer_account_uuid, contract_account_uuid as target_uuid, Null::UUID as business_partner_uuid, contract_account_uuid, Null::UUID as contract_uuid, Null::UUID as contract_product_uuid, Null::UUID as delivery_point_uuid, CAST('CONTRACT_ACCOUNT' as CHARACTER VARYING(32)) as entity_type
         from contract_account_ownership
         union
         select created_at, type, inherited, customer_account_uuid, contract_uuid as target_uuid, Null::UUID as business_partner_uuid, Null::UUID as contract_account_uuid, contract_uuid, Null::UUID as contract_product_uuid, Null::UUID as delivery_point_uuid, CAST('CONTRACT' as CHARACTER VARYING(32)) as entity_type
         from contract_ownership
         union
         select created_at, type, inherited, customer_account_uuid, contract_product_uuid as target_uuid, Null::UUID as business_partner_uuid, Null::UUID as contract_account_uuid, Null::UUID as contract_uuid, contract_product_uuid, Null::UUID as delivery_point_uuid, CAST('CONTRACT_PRODUCT' as CHARACTER VARYING(32)) as entity_type
         from contract_product_ownership
         union
         select created_at, type, inherited, customer_account_uuid, delivery_point_uuid as target_uuid, Null::UUID as business_partner_uuid, Null::UUID as contract_account_uuid, Null::UUID as contract_uuid, Null::UUID as contract_product_uuid, delivery_point_uuid, CAST('DELIVERY_POINT' as CHARACTER VARYING(32)) as entity_type
         from delivery_point_ownership
     ) as q
    )

