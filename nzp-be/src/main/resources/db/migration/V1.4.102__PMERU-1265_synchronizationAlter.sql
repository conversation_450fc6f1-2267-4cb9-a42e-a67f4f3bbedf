alter table united_delivery_point add column synchronization_log_uuid uuid;
alter table united_delivery_point add column synchronization_at timestamp with time zone;

-- indexes
create index idx_united_delivery_point_synchronization_at on united_delivery_point(synchronization_at);
create index idx_united_delivery_point_synchronization_log_uuid on united_delivery_point(synchronization_log_uuid);

-- FK
alter table united_delivery_point
    add constraint fk_united_delivery_point_synchronization_log foreign key (synchronization_log_uuid)
        references synchronization_log (uuid) match simple
        on update no action
        on delete no action;