------------------------------------------------------------------------------------------------------------------------
-------- PMERU-1646: Dotiahnutie do Notification COntexty pre DP hodnoty pre type (ZP/EE) - lokalizovane
------------------------------------------------------------------------------------------------------------------------

-- ADD generic code list values

INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid) VALUES (uuid_generate_v4(), now(), now(), 1, 'ZP', 'DELIVERY_POINT_TYPE', null, null, null);
INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid) VALUES (uuid_generate_v4(), now(), now(), 1, 'EE', 'DELIVERY_POINT_TYPE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid) VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Zemný plyn', null, (SELECT uuid FROM generic_code_list WHERE type = 'DELIVERY_POINT_TYPE' AND code = 'ZP'));
INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid) VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Zemný plyn', null, (SELECT uuid FROM generic_code_list WHERE type = 'DELIVERY_POINT_TYPE' AND code = 'ZP'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid) VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Elektrická energia', null, (SELECT uuid FROM generic_code_list WHERE type = 'DELIVERY_POINT_TYPE' AND code = 'EE'));
INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid) VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Elektrická energia', null, (SELECT uuid FROM generic_code_list WHERE type = 'DELIVERY_POINT_TYPE' AND code = 'EE'));

-- Change notification_template_variable where variable = "deliveryPoint.type" (Typ odberného miesta -> Typ odberného miesta (skratka))

UPDATE notification_template_variable
SET name = 'Typ odberného miesta (skratka)', version = version + 1, updated_at = now()
WHERE variable = 'deliveryPoint.type';

-- Update notification templates where is used "deliveryPoint.type" (change it to: "deliveryPoint.typeName.name")

INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid)
SELECT notification_template_uuid, now(), now(), 1, 'deliveryPoint.typeName.name', 'Typ odberného miesta', description, type, notification_template_execution_type, uuid_generate_v4()
FROM notification_template_variable WHERE variable = 'deliveryPoint.type';

-- Update notification templates to use new variable

UPDATE notification_template_i18n
SET email_body = REPLACE (email_body, '${deliveryPoint.type}', '${deliveryPoint.typeName.name}'), version = version + 1, updated_at = now()
WHERE email_body like '%{deliveryPoint.type}%';

UPDATE notification_template_i18n
SET email_subject = REPLACE (email_subject, '${deliveryPoint.type}', '${deliveryPoint.typeName.name}'), version = version + 1, updated_at = now()
WHERE email_subject like '%{deliveryPoint.type}%';

UPDATE notification_template_i18n
SET sms_body = REPLACE (sms_body, '${deliveryPoint.type}', '${deliveryPoint.typeName.name}'), version = version + 1, updated_at = now()
WHERE sms_body like '%{deliveryPoint.type}%';

UPDATE notification_template_i18n
SET header = REPLACE (header, '${deliveryPoint.type}', '${deliveryPoint.typeName.name}'), version = version + 1, updated_at = now()
WHERE header like '%{deliveryPoint.type}%';
