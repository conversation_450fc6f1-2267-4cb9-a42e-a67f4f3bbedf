
CREATE TABLE invoice_tmp (
	id text NOT NULL,
	created_at timestamptz NOT NULL,
	updated_at timestamptz NOT NULL,
	version int4 NOT NULL,
	external_id varchar(50) NOT NULL,
	status varchar(20) NOT NULL,
	type_group varchar(50) NULL,
	amount numeric(19,2) NULL,
	due_at date NULL,
	issue_at date NULL,
	vs varchar(50) NULL,
	notification_created_at timestamptz NULL,
	locked_by varchar(50) NULL,
	retry_count int4 NULL,
	notification_duedate_at timestamptz NULL,
	synchronization_log_uuid uuid NULL,
	synchronization_at timestamptz NULL,
	payment_req_notif_sent bool NULL,
	file_archive_id text NULL,
	type text NULL,
	contract_account_id text NULL,
	contains_payment_request bool NULL,
	contains_plament_plan bool NULL,
	unpaid numeric(19,2) NULL,
	invalidated bool NULL,
	reference text NOT NULL,
	sap_id text NOT NULL,
	overpaid numeric(19,2) NULL
);

-- transfer and transform
INSERT INTO invoice_tmp
(id, created_at, updated_at, version, external_id, status, type_group, amount, due_at, issue_at, vs, notification_created_at, locked_by, retry_count, notification_duedate_at, synchronization_log_uuid, synchronization_at, payment_req_notif_sent, file_archive_id, type, contract_account_id, contains_payment_request, contains_plament_plan, unpaid, invalidated, reference, sap_id, overpaid)
SELECT inv.id, inv.created_at, inv.updated_at, inv.version, inv.external_id, inv.status, inv.type_group, inv.amount, inv.due_at, inv.issue_at, inv.vs, inv.notification_created_at, inv.locked_by, inv.retry_count, inv.notification_duedate_at, inv.synchronization_log_uuid, inv.synchronization_at, inv.payment_req_notif_sent, inv.file_archive_id, gcl.code, inv.contract_account_id, inv.contains_payment_request, inv.contains_plament_plan, inv.unpaid, inv.invalidated, inv.reference, inv.sap_id, inv.overpaid
FROM invoice inv
left outer join generic_code_list gcl on gcl.uuid = inv.gcl_type_uuid ;

--drop foreign reference
alter table invoice_ownership drop constraint fk_invoice_ownership_invoice;

-- drop old
drop table invoice;
-- replace with new
ALTER TABLE invoice_tmp RENAME TO invoice;

-- create constraints and indexes
ALTER table invoice add CONSTRAINT pk_invoice PRIMARY KEY (id);
ALTER table invoice add CONSTRAINT fk_invoice_synchronization_log FOREIGN KEY (synchronization_log_uuid) REFERENCES synchronization_log(uuid);

CREATE INDEX idx_invoice_contract_account_id ON invoice(contract_account_id);
CREATE INDEX idx_invoice_issue_at ON invoice(issue_at);
CREATE INDEX idx_invoice_sap_id ON invoice(sap_id);
CREATE INDEX idx_invoice_synchronization_log_uuid ON invoice(synchronization_log_uuid);
CREATE UNIQUE INDEX unique_idx_invoice_external_id ON invoice(external_id);
CREATE INDEX idx_invoice_type ON invoice(type);

-- recreate foreign reference
alter table invoice_ownership add CONSTRAINT fk_invoice_ownership_invoice FOREIGN KEY (invoice_id) REFERENCES invoice(id);
