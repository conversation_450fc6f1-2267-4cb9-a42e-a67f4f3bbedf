create table customer_device
(
    uuid        uuid                     not null
        constraint pk_customer_device
            primary key,
    created_at  timestamp with time zone not null,
    updated_at  timestamp with time zone not null,
    version     integer                  not null,
    token       varchar(1000)            not null,
    name        varchar(300),
    customer_id uuid                     not null
        constraint fk_customer_device_customer_id
            references customer_account
);

create index idx_customer_device_customer
    on customer_device (customer_id);

create unique index idx_customer_device_token
    on customer_device (token);

