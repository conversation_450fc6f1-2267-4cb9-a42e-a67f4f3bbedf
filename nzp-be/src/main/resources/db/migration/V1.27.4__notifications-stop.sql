
ALTER TABLE customer_notification ADD COLUMN cancelled BOOLEAN;
ALTER TABLE customer_notification ADD COLUMN email_send BOOLEAN;
ALTER TABLE customer_notification ADD COLUMN sms_send BOOLEAN;
ALTER TABLE customer_notification ADD COLUMN notification_schedule_uuid uuid;

-- indexes
create index idx_customer_notification_notification_schedule_uuid on customer_notification(notification_schedule_uuid);

-- FK
alter table customer_notification
    add constraint fk_customer_notification_notification_schedule foreign key (notification_schedule_uuid)
        references notification_schedule (uuid) match simple
        on update no action
        on delete no action;