ALTER TABLE contract DROP COLUMN binding_to;
ALTER TABLE contract DROP COLUMN notice_period;
ALTER TABLE contract DROP COLUMN bill_cycle;
ALTER TABLE contract DROP COLUMN status;

CREATE TABLE contract_version
(
    uuid                    uuid                      NOT NULL,
    created_at              TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at              TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                 INTEGER                   NOT NULL,
    item                    INTEGER,
    product                 CHARACTER VARYING(50),
    valid_from              DATE,
    valid_to                DATE,
    binding_period          INTEGER,
    binding_to              DATE,
    gcl_bill_cycle_uuid     uuid,
    notice_period           INTEGER,
    advance_pay_amount      NUMERIC(19, 2),
    ee_tariff_count         CHARACTER VARYING(32),
    contract_external_id    CHARACTER VARYING(50),

    CONSTRAINT pk_contract_version PRIMARY KEY (uuid),

    CONSTRAINT fk_contract_version_gcl_bill_cycle_uuid FOREIGN KEY (gcl_bill_cycle_uuid)
                REFERENCES generic_code_list (uuid) MATCH SIMPLE
                ON UPDATE NO ACTION
                ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

CREATE INDEX idx_contract_version_external_id ON contract_version(contract_external_id);