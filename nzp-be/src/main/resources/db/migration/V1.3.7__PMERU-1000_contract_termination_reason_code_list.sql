INSERT INTO
    generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SUPPLIER_CHANGE', 'CONTRACT_TERMINATION_REASON'),
    (uuid_generate_v4(), now(), now(), 1, 'FUEL_CHANGE', 'CONTRACT_TERMINATION_REASON'),
    (uuid_generate_v4(), now(), now(), 1, 'DEATH', 'CONTRACT_TERMINATION_REASON'),
    (uuid_generate_v4(), now(), now(), 1, 'COMPANY_TERMINATION', 'CONTRACT_TERMINATION_REASON'),
    (uuid_generate_v4(), now(), now(), 1, 'REAL_ESTATE_SALE', 'CONTRACT_TERMINATION_REASON'),
    (uuid_generate_v4(), now(), now(), 1, 'DEMOLITION_WORKS', 'CONTRACT_TERMINATION_REASON'),
    (uuid_generate_v4(), now(), now(), 1, 'LEASE_TERMINATION', 'CONTRACT_TERMINATION_REASON'),
    (uuid_generate_v4(), now(), now(), 1, 'WITHOUT_GIVING_REASON', 'CONTRACT_TERMINATION_REASON');

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmena dodávateľa', null, (select uuid from generic_code_list where code = 'SUPPLIER_CHANGE' and type = 'CONTRACT_TERMINATION_REASON')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Prechod na iné palivo', null, (select uuid from generic_code_list where code = 'FUEL_CHANGE' and type = 'CONTRACT_TERMINATION_REASON')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Úmrtie odberateľa', null, (select uuid from generic_code_list where code = 'DEATH' and type = 'CONTRACT_TERMINATION_REASON')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Zánik spoločnostia', null, (select uuid from generic_code_list where code = 'COMPANY_TERMINATION' and type = 'CONTRACT_TERMINATION_REASON')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Predaj nehnuteľnosti', null, (select uuid from generic_code_list where code = 'REAL_ESTATE_SALE' and type = 'CONTRACT_TERMINATION_REASON')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Búracie práce', null, (select uuid from generic_code_list where code = 'DEMOLITION_WORKS' and type = 'CONTRACT_TERMINATION_REASON')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Ukončenie nájomnej zmluvy', null, (select uuid from generic_code_list where code = 'LEASE_TERMINATION' and type = 'CONTRACT_TERMINATION_REASON')),
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Bez udania dôvodu', null, (select uuid from generic_code_list where code = 'WITHOUT_GIVING_REASON' and type = 'CONTRACT_TERMINATION_REASON'));
