
CREATE TABLE contract_account_tmp (
	id text NOT NULL,
	created_at timestamptz NOT NULL,
	updated_at timestamptz NOT NULL,
	version int4 NOT NULL,
	external_id varchar(50) NOT NULL,
	status varchar(50) NULL,
	e_invoice bool NULL,
	email varchar(64) NULL,
	street varchar(64) NULL,
	street_number varchar(32) NULL,
	city varchar(64) NULL,
	zip_code varchar(32) NULL,
	country varchar(64) NULL,
	iban varchar(64) NULL,
	sipo varchar(64) NULL,
	business_partner_id varchar(50) NULL,
	synchronization_log_uuid uuid NULL,
	synchronization_at timestamptz NULL,
	payment_type text NULL,
	post_street varchar(64) NULL,
	post_street_number varchar(32) NULL,
	post_city varchar(64) NULL,
	post_zip_code varchar(32) NULL,
	post_country varchar(64) NULL,
	first_name varchar(100) NULL,
	last_name varchar(100) NULL,
	name varchar(100) NULL
);

-- transfer and transform
INSERT INTO contract_account_tmp
(id, created_at, updated_at, version, external_id, status, e_invoice, email, street, street_number, city, zip_code, country, iban, sipo, business_partner_id,
synchronization_log_uuid, synchronization_at, payment_type, post_street, post_street_number, post_city, post_zip_code, post_country, first_name, last_name, name)

SELECT ca.id, ca.created_at, ca.updated_at, ca.version, ca.external_id, ca.status, ca.e_invoice, ca.email, ca.street, ca.street_number, ca.city, ca.zip_code, ca.country, ca.iban, ca.sipo, ca.business_partner_id,
ca.synchronization_log_uuid, ca.synchronization_at, gcl.code, ca.post_street, ca.post_street_number, ca.post_city, ca.post_zip_code, ca.post_country, ca.first_name, ca.last_name, ca.name
FROM contract_account ca
left outer join generic_code_list gcl on gcl.uuid = ca.gcl_payment_type_uuid ;

--drop foreign reference
alter table contract_account_ownership drop constraint fk_contract_account_ownership_contract_account;

-- drop old
drop table contract_account;
-- replace with new
ALTER TABLE contract_account_tmp RENAME TO contract_account;

-- create constraints and indexes
ALTER TABLE contract_account add CONSTRAINT pk_contract_account PRIMARY KEY (id);
ALTER TABLE contract_account add CONSTRAINT fk_contract_account_synchronization_log FOREIGN KEY (synchronization_log_uuid) REFERENCES synchronization_log(uuid);

CREATE INDEX idx_contract_account_business_partner_external_id ON contract_account(business_partner_id);
CREATE INDEX idx_contract_account_synchronization_log_uuid ON contract_account(synchronization_log_uuid);
CREATE UNIQUE INDEX unique_idx_contract_account_external_id ON contract_account(external_id);

CREATE INDEX idx_contract_account_payment_type ON contract_account(payment_type);


-- recreate foreign reference
alter table contract_account_ownership add constraint fk_contract_account_ownership_contract_account FOREIGN KEY (contract_account_id) REFERENCES contract_account(id);