DELETE FROM generic_code_list_i18n where code_list_uuid in (select uuid from generic_code_list where type = 'COUNTRY');
DELETE FROM generic_code_list WHERE type = 'COUNTRY';

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'AFG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ALB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ATA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'DZA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ASM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AND', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AGO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ATG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AZE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ARG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AUS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AUT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BHS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BHR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BGD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ARM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BRB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BEL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BMU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BTN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BOL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BIH', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BWA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BVT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BRA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BLZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IOT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SLB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VGB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BRN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BGR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MMR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BDI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BLR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KHM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CMR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CAN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CPV', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CYM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CAF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LKA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TCD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CHL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CHN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TWN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CXR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CCK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'COL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'COM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MYT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'COG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'COD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'COK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CRI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'HRV', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CUB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CYP', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CZE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BEN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'DNK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'DMA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'DOM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ECU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SLV', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GNQ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ETH', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ERI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'EST', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FRO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FLK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SGS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FJI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FIN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ALA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FRA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GUF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PYF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ATF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'DJI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GAB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GEO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GMB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PSE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'DEU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GHA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GIB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KIR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GRC', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GRL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GRD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GLP', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GUM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GTM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GIN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GUY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'HTI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'HMD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VAT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'HND', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'HKG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'HUN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ISL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IND', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IDN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IRN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IRQ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'IRL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ISR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ITA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CIV', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'JAM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'JPN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KAZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'JOR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KEN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PRK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KOR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KWT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KGZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LAO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LBN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LSO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LVA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LBR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LBY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LIE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LTU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LUX', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MAC', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MDG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MWI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MYS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MDV', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MLI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MLT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MTQ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MRT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MUS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MEX', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MCO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MNG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MDA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MNE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MSR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MAR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MOZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'OMN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NAM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NRU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NPL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NLD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ANT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ABW', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NCL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VUT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NZL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NIC', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NER', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NGA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NIU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NFK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NOR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MNP', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'UMI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FSM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MHL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PLW', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PAK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PAN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PNG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PRY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PER', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PHL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PCN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'POL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PRT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GNB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TLS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PRI', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'QAT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'REU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ROU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'RUS', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'RWA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SHN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'KNA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'AIA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'LCA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SPM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VCT', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SMR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'STP', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SAU', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SEN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SRB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SYC', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SLE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SGP', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SVK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VNM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SVN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SOM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ZAF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ZWE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ESP', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ESH', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SDN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SUR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SJM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SWZ', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SWE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'CHE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SYR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TJK', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'THA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TGO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TKL', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TON', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TTO', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ARE', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TUN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TUR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TKM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TCA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TUV', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'UGA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'UKR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'MKD', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'EGY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'GBR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TZA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'USA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VIR', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BFA', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'URY', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'UZB', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VEN', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'WLF', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'WSM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'YEM', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SCG', 'COUNTRY', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ZMB', 'COUNTRY', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Afganistan', 'Afganský islamský štát', (select uuid from generic_code_list where code like 'AFG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Albánsko', 'Albánska republika', (select uuid from generic_code_list where code like 'ALB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Antarktída', 'Antarktída', (select uuid from generic_code_list where code like 'ATA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Alžírsko', 'Alžírska demokratická ľudová republika', (select uuid from generic_code_list where code like 'DZA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Americká Samoa', 'Teritórium Americkej Samoy', (select uuid from generic_code_list where code like 'ASM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Andorra', 'Andorrské kniežatstvo', (select uuid from generic_code_list where code like 'AND' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Angola', 'Angolská republika', (select uuid from generic_code_list where code like 'AGO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Antigua a Barbuda', 'Antigua a Barbuda', (select uuid from generic_code_list where code like 'ATG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Azerbajdžan', 'Azerbajdžanská republika', (select uuid from generic_code_list where code like 'AZE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Argentína', 'Argentínska republika', (select uuid from generic_code_list where code like 'ARG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Austrália', 'Austrálsky zväz', (select uuid from generic_code_list where code like 'AUS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Rakúsko', 'Rakúska republika', (select uuid from generic_code_list where code like 'AUT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bahamy', 'Bahamské spoločenstvo', (select uuid from generic_code_list where code like 'BHS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bahrajn', 'Bahrajnský štát', (select uuid from generic_code_list where code like 'BHR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bangladéš', 'Bangladéšska ľudová republika', (select uuid from generic_code_list where code like 'BGD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Arménsko', 'Arménska republika', (select uuid from generic_code_list where code like 'ARM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Barbados', 'Barbados', (select uuid from generic_code_list where code like 'BRB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Belgicko', 'Belgické kráľovstvo', (select uuid from generic_code_list where code like 'BEL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bermudy', 'Bermudy', (select uuid from generic_code_list where code like 'BMU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bhután', 'Bhutánske kráľovstvo', (select uuid from generic_code_list where code like 'BTN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bolívia', 'Bolívijská republika', (select uuid from generic_code_list where code like 'BOL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bosna a Hercegovina', 'Republika Bosny a Hercegoviny', (select uuid from generic_code_list where code like 'BIH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Botswana', 'Botswanská republika', (select uuid from generic_code_list where code like 'BWA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bouvetov ostrov', 'Bouvetov ostrov', (select uuid from generic_code_list where code like 'BVT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Brazília', 'Brazílska federatívna republika', (select uuid from generic_code_list where code like 'BRA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Belize', 'Belize', (select uuid from generic_code_list where code like 'BLZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Britské indickooc.ú.', 'Britské indickooceánske územie', (select uuid from generic_code_list where code like 'IOT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Šalamúnove ostrovy', 'Šalamúnove ostrovy', (select uuid from generic_code_list where code like 'SLB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Britské Pan.ostrovy', 'Britské Panenské ostrovy', (select uuid from generic_code_list where code like 'VGB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Brunej', 'Brunejsko-darussalamský štát', (select uuid from generic_code_list where code like 'BRN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bulharsko', 'Bulharská republika', (select uuid from generic_code_list where code like 'BGR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mjanmarsko', 'Mjanmarský zväz', (select uuid from generic_code_list where code like 'MMR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Burundi', 'Burundská republika', (select uuid from generic_code_list where code like 'BDI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Bielorusko', 'Bieloruská republika', (select uuid from generic_code_list where code like 'BLR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kambodža', 'Kambodžské kráľovstvo', (select uuid from generic_code_list where code like 'KHM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kamerun', 'Kamerunská republika', (select uuid from generic_code_list where code like 'CMR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kanada', 'Kanada', (select uuid from generic_code_list where code like 'CAN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kapverdy', 'Kapverdská republika', (select uuid from generic_code_list where code like 'CPV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kajmanie ostrovy', 'Kajmanie ostrovy', (select uuid from generic_code_list where code like 'CYM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Stredoafrická republika', 'Stredoafrická republika', (select uuid from generic_code_list where code like 'CAF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Srí Lanka', 'Srílanská demokratická socialistická republika', (select uuid from generic_code_list where code like 'LKA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Čad', 'Čadská republika', (select uuid from generic_code_list where code like 'TCD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Čile', 'Čilská republika', (select uuid from generic_code_list where code like 'CHL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Čína', 'Čínska ľudová republika', (select uuid from generic_code_list where code like 'CHN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Taiwan', 'Čínska republika', (select uuid from generic_code_list where code like 'TWN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Vianočný ostrov', 'Teritórium Vianočného ostrova', (select uuid from generic_code_list where code like 'CXR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kokosové ostrovy', 'Teritórium Kokosových ostrovov', (select uuid from generic_code_list where code like 'CCK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kolumbia', 'Kolumbijská republika', (select uuid from generic_code_list where code like 'COL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Komory', 'Komorská federatívna islamská republika', (select uuid from generic_code_list where code like 'COM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mayotte', 'Mayotte', (select uuid from generic_code_list where code like 'MYT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kongo', 'Konžská republika', (select uuid from generic_code_list where code like 'COG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kongo (býv. Zair)', 'Konžská demokratická republika', (select uuid from generic_code_list where code like 'COD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Cookove ostrovy', 'Cookove ostrovy', (select uuid from generic_code_list where code like 'COK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kostarika', 'Kostarická republika', (select uuid from generic_code_list where code like 'CRI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Chorvátsko', 'Chorvátska republika', (select uuid from generic_code_list where code like 'HRV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kuba', 'Kubánska republika', (select uuid from generic_code_list where code like 'CUB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Cyprus', 'Cyperská republika', (select uuid from generic_code_list where code like 'CYP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Česko', 'Česká republika', (select uuid from generic_code_list where code like 'CZE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Benin', 'Beninská republika', (select uuid from generic_code_list where code like 'BEN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dánsko', 'Dánske kráľovstvo', (select uuid from generic_code_list where code like 'DNK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dominika', 'Dominické spoločenstvo', (select uuid from generic_code_list where code like 'DMA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dominikánska republika', 'Dominikánska republika', (select uuid from generic_code_list where code like 'DOM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Ekvádor', 'Ekvádorská republika', (select uuid from generic_code_list where code like 'ECU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Salvádor', 'Salvádorská republika', (select uuid from generic_code_list where code like 'SLV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Rovníková Guinea', 'Republika Rovníkovej Guiney', (select uuid from generic_code_list where code like 'GNQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Etiópia', 'Etiópia', (select uuid from generic_code_list where code like 'ETH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Eritrea', 'Eritrejský štát', (select uuid from generic_code_list where code like 'ERI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Estónsko', 'Estónska republika', (select uuid from generic_code_list where code like 'EST' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Faerské ostrovy', 'Faerské ostrovy', (select uuid from generic_code_list where code like 'FRO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Falklandy', 'Falklandské ostrovy', (select uuid from generic_code_list where code like 'FLK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Juž. Georgia a J.S.o.', 'Južná Georgia a Južné Sandwichove ostrovy', (select uuid from generic_code_list where code like 'SGS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Fidži', 'Fidžijská republika', (select uuid from generic_code_list where code like 'FJI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Fínsko', 'Fínska republika', (select uuid from generic_code_list where code like 'FIN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Alandy', 'Alandy', (select uuid from generic_code_list where code like 'ALA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Francúzsko', 'Francúzska republika', (select uuid from generic_code_list where code like 'FRA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Francúzska Guyana', 'Zámorský departmán Francúzskej Guyany', (select uuid from generic_code_list where code like 'GUF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Francúzska Polynézia', 'Zámorské teritórium Francúzskej Polynézie', (select uuid from generic_code_list where code like 'PYF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Francúzske južné územia', 'Francúzske južné a antarktické územia', (select uuid from generic_code_list where code like 'ATF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Džibutsko', 'Džibutská republika', (select uuid from generic_code_list where code like 'DJI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Gabon', 'Gabonská republika', (select uuid from generic_code_list where code like 'GAB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Gruzínsko', 'Gruzínska republika', (select uuid from generic_code_list where code like 'GEO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Gambia', 'Gambijská republika', (select uuid from generic_code_list where code like 'GMB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Palestína', 'Palestína', (select uuid from generic_code_list where code like 'PSE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nemecko', 'Nemecká spolková republika', (select uuid from generic_code_list where code like 'DEU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Ghana', 'Ghanská republika', (select uuid from generic_code_list where code like 'GHA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Gibraltár', 'Gibraltár', (select uuid from generic_code_list where code like 'GIB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kiribati', 'Kiribatská republika', (select uuid from generic_code_list where code like 'KIR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Grécko', 'Grécka republika', (select uuid from generic_code_list where code like 'GRC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Grónsko', 'Grónsko', (select uuid from generic_code_list where code like 'GRL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Grenada', 'Grenada', (select uuid from generic_code_list where code like 'GRD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Guadeloupe', 'Zámorský departmán Guadeloupu a závislých území', (select uuid from generic_code_list where code like 'GLP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Guam', 'Guamské teritórium', (select uuid from generic_code_list where code like 'GUM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Guatemala', 'Guatemalská republika', (select uuid from generic_code_list where code like 'GTM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Guinea', 'Guinejská republika', (select uuid from generic_code_list where code like 'GIN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Guyana', 'Guyanská kooperatívna republika', (select uuid from generic_code_list where code like 'GUY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Haiti', 'Haitská republika', (select uuid from generic_code_list where code like 'HTI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Heardov ostrov', 'Teritórium Heardovho ostrova a Macdonaldových ostrovov', (select uuid from generic_code_list where code like 'HMD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Vatikán', 'Svätá Stolica (Vatikánsky mestský štát)', (select uuid from generic_code_list where code like 'VAT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Honduras', 'Honduraská republika', (select uuid from generic_code_list where code like 'HND' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Hongkong', 'Hongkong', (select uuid from generic_code_list where code like 'HKG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Maďarsko', 'Maďarsko', (select uuid from generic_code_list where code like 'HUN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Island', 'Islandská republika', (select uuid from generic_code_list where code like 'ISL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'India', 'Indická republika', (select uuid from generic_code_list where code like 'IND' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Indonézia', 'Indonézska republika', (select uuid from generic_code_list where code like 'IDN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Irán', 'Iránska islamská republika', (select uuid from generic_code_list where code like 'IRN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Irak', 'Iracká republika', (select uuid from generic_code_list where code like 'IRQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Írsko', 'Írsko', (select uuid from generic_code_list where code like 'IRL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Izrael', 'Izraelský štát', (select uuid from generic_code_list where code like 'ISR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Taliansko', 'Talianska republika', (select uuid from generic_code_list where code like 'ITA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pobrežie slonoviny', 'Republika Pobrežia slonoviny', (select uuid from generic_code_list where code like 'CIV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Jamajka', 'Jamajka', (select uuid from generic_code_list where code like 'JAM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Japonsko', 'Japonsko', (select uuid from generic_code_list where code like 'JPN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kazachstan', 'Kazašská republika', (select uuid from generic_code_list where code like 'KAZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Jordánsko', 'Jordánske hášimovské kráľovstvo', (select uuid from generic_code_list where code like 'JOR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Keňa', 'Kenská republika', (select uuid from generic_code_list where code like 'KEN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kórej.ľudovodem.rep.', 'Kórejská ľudovodemokratická republika', (select uuid from generic_code_list where code like 'PRK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kórejská republika', 'Kórejská republika', (select uuid from generic_code_list where code like 'KOR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kuvajt', 'Kuvajtský štát', (select uuid from generic_code_list where code like 'KWT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kirgizsko', 'Kirgizská republika', (select uuid from generic_code_list where code like 'KGZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Laos', 'Laoská ľudovodemokratická republika', (select uuid from generic_code_list where code like 'LAO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Libanon', 'Libanonská republika', (select uuid from generic_code_list where code like 'LBN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Lesotho', 'Lesothské kráľovstvo', (select uuid from generic_code_list where code like 'LSO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Lotyšsko', 'Lotyšská republika', (select uuid from generic_code_list where code like 'LVA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Libéria', 'Libérijská republika', (select uuid from generic_code_list where code like 'LBR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Líbya', 'Veľká líbyjská arabská ľudová socialistická džamahírija', (select uuid from generic_code_list where code like 'LBY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Lichtenštajnsko', 'Lichtenštajnské kniežatstvo', (select uuid from generic_code_list where code like 'LIE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Litva', 'Litovská republika', (select uuid from generic_code_list where code like 'LTU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Luxembursko', 'Luxemburské veľkovojvodstvo', (select uuid from generic_code_list where code like 'LUX' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Macao', 'Macao', (select uuid from generic_code_list where code like 'MAC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Madagaskar', 'Madagaskarská republika', (select uuid from generic_code_list where code like 'MDG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Malawi', 'Malawijská republika', (select uuid from generic_code_list where code like 'MWI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Malajzia', 'Malajzia', (select uuid from generic_code_list where code like 'MYS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Maldivy', 'Maldivská republika', (select uuid from generic_code_list where code like 'MDV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mali', 'Malijská republika', (select uuid from generic_code_list where code like 'MLI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Malta', 'Maltská republika', (select uuid from generic_code_list where code like 'MLT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Martinik', 'Martinický zámorský departmán', (select uuid from generic_code_list where code like 'MTQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mauritánia', 'Mauritánska islamská republika', (select uuid from generic_code_list where code like 'MRT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Maurícius', 'Maurícijská republika', (select uuid from generic_code_list where code like 'MUS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mexiko', 'Spojené štáty mexické', (select uuid from generic_code_list where code like 'MEX' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Monako', 'Monacké kniežatstvo', (select uuid from generic_code_list where code like 'MCO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mongolsko', 'Mongolsko', (select uuid from generic_code_list where code like 'MNG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Moldavsko', 'Moldavská republika', (select uuid from generic_code_list where code like 'MDA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Čierna Hora', 'Čiernohorská republika', (select uuid from generic_code_list where code like 'MNE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Montserrat', 'Montserrat', (select uuid from generic_code_list where code like 'MSR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Maroko', 'Marocké kráľovstvo', (select uuid from generic_code_list where code like 'MAR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mozambik', 'Mozambická republika', (select uuid from generic_code_list where code like 'MOZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Omán', 'Ománsky sultanát', (select uuid from generic_code_list where code like 'OMN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Namíbia', 'Namíbijská republika', (select uuid from generic_code_list where code like 'NAM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nauru', 'Nauruská republika', (select uuid from generic_code_list where code like 'NRU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nepál', 'Nepálske kráľovstvo', (select uuid from generic_code_list where code like 'NPL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Holandsko', 'Holandské kráľovstvo', (select uuid from generic_code_list where code like 'NLD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Holandské Antily', 'Holandské Antily', (select uuid from generic_code_list where code like 'ANT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Aruba', 'Aruba', (select uuid from generic_code_list where code like 'ABW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nová Kaledónia', 'Zamorské teritórium Novej Kaledónie a závislých území', (select uuid from generic_code_list where code like 'NCL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Vanuatu', 'Vanuatská republika', (select uuid from generic_code_list where code like 'VUT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nový Zéland', 'Nový Zéland', (select uuid from generic_code_list where code like 'NZL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nikaragua', 'Nikaragujská republika', (select uuid from generic_code_list where code like 'NIC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Niger', 'Nigerská republika', (select uuid from generic_code_list where code like 'NER' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nigéria', 'Nigérijská federatívna republika', (select uuid from generic_code_list where code like 'NGA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Niue', 'Samosprávne zámorské teritórium ostrova Niue', (select uuid from generic_code_list where code like 'NIU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Norfolk', 'Teritórium ostrova Norfolk', (select uuid from generic_code_list where code like 'NFK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Nórsko', 'Nórske kráľovstvo', (select uuid from generic_code_list where code like 'NOR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Severné Mariány', 'Spoločenstvo ostrovov Severné Mariány', (select uuid from generic_code_list where code like 'MNP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Menšie odľahlé ostr.USA', 'Menšie odľahlé ostrovy Spojených štátov', (select uuid from generic_code_list where code like 'UMI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mikronézia', 'Mikronézske federatívne štáty', (select uuid from generic_code_list where code like 'FSM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Marshallove ostrovy', 'Republika Marshallových ostrovov', (select uuid from generic_code_list where code like 'MHL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Palau', 'Palauská republika', (select uuid from generic_code_list where code like 'PLW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pakistan', 'Pakistanská islamská republika', (select uuid from generic_code_list where code like 'PAK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Panama', 'Panamská republika', (select uuid from generic_code_list where code like 'PAN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Papua-Nová Guinea', 'Papua-Nová Guinea', (select uuid from generic_code_list where code like 'PNG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Paraguaj', 'Paraguajská republika', (select uuid from generic_code_list where code like 'PRY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Peru', 'Peruánska republika', (select uuid from generic_code_list where code like 'PER' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Filipíny', 'Filipínska republika', (select uuid from generic_code_list where code like 'PHL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pitcairnove ostrovy', 'Pitcairnove ostrovy', (select uuid from generic_code_list where code like 'PCN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Poľsko', 'Poľská republika', (select uuid from generic_code_list where code like 'POL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Portugalsko', 'Portugalská republika', (select uuid from generic_code_list where code like 'PRT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Guinea-Bissau', 'Guinejsko-bissauská republika', (select uuid from generic_code_list where code like 'GNB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Východný Timor', 'Východný Timor', (select uuid from generic_code_list where code like 'TLS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Portoriko', 'Portorické spoločenstvo', (select uuid from generic_code_list where code like 'PRI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Katar', 'Katarský štát', (select uuid from generic_code_list where code like 'QAT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Réunion', 'Réunionský zámorský departmán', (select uuid from generic_code_list where code like 'REU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Rumunsko', 'Rumunsko', (select uuid from generic_code_list where code like 'ROU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Rusko', 'Ruská federácia', (select uuid from generic_code_list where code like 'RUS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Rwanda', 'Rwandská republika', (select uuid from generic_code_list where code like 'RWA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Svätá Helena', 'Svätá Helena a závislé územia', (select uuid from generic_code_list where code like 'SHN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Svätý Krištof', 'Svätý Krištof a Nevis', (select uuid from generic_code_list where code like 'KNA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Anguilla', 'Anguilla', (select uuid from generic_code_list where code like 'AIA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Svätá Lucia', 'Svätá Lucia', (select uuid from generic_code_list where code like 'LCA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Saint Pierre a Miquelon', 'Ostrovy Saint Pierre a Miquelon', (select uuid from generic_code_list where code like 'SPM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Svätý Vincent', 'Svätý Vincent a Grenadíny', (select uuid from generic_code_list where code like 'VCT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'San Maríno', 'Sanmarínska republika', (select uuid from generic_code_list where code like 'SMR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Svätý Tomáš', 'Demokratická republika Svätého Tomáša a Princovho ostrova', (select uuid from generic_code_list where code like 'STP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Saudská Arábia', 'Saudskoarabské kráľovstvo', (select uuid from generic_code_list where code like 'SAU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Senegal', 'Senegalská republika', (select uuid from generic_code_list where code like 'SEN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Srbsko', 'Srbská republika', (select uuid from generic_code_list where code like 'SRB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Seychely', 'Seychelská republika', (select uuid from generic_code_list where code like 'SYC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Sierra Leone', 'Sierraleonská republika', (select uuid from generic_code_list where code like 'SLE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Singapur', 'Singapurská republika', (select uuid from generic_code_list where code like 'SGP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Slovensko', 'Slovenská republika', (select uuid from generic_code_list where code like 'SVK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Vietnam', 'Vietnamská socialistická republika', (select uuid from generic_code_list where code like 'VNM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Slovinsko', 'Slovinská republika', (select uuid from generic_code_list where code like 'SVN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Somálsko', 'Somálska demokratická republika', (select uuid from generic_code_list where code like 'SOM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Južná Afrika', 'Juhoafrická republika', (select uuid from generic_code_list where code like 'ZAF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zimbabwe', 'Zimbabwianska republika', (select uuid from generic_code_list where code like 'ZWE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Španielsko', 'Španielske kráľovstvo', (select uuid from generic_code_list where code like 'ESP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Západná Sahara', 'Západná Sahara', (select uuid from generic_code_list where code like 'ESH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Sudán', 'Sudánska republika', (select uuid from generic_code_list where code like 'SDN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Surinam', 'Surinamská republika', (select uuid from generic_code_list where code like 'SUR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Svalbard Jan Mayen', 'Svalbard Jan Mayen', (select uuid from generic_code_list where code like 'SJM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Eswatini', 'Eswatinské kráľovstvo', (select uuid from generic_code_list where code like 'SWZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Švédsko', 'Švédske kráľovstvo', (select uuid from generic_code_list where code like 'SWE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Švajčiarsko', 'Švajčiarska konfederácia', (select uuid from generic_code_list where code like 'CHE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Sýria', 'Sýrska arabská republika', (select uuid from generic_code_list where code like 'SYR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Tadžikistan', 'Tadžická republika', (select uuid from generic_code_list where code like 'TJK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Thajsko', 'Thajské kráľovstvo', (select uuid from generic_code_list where code like 'THA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Togo', 'Togská republika', (select uuid from generic_code_list where code like 'TGO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Tokelau', 'Tokelauské ostrovy', (select uuid from generic_code_list where code like 'TKL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Tonga', 'Tongské kráľovstvo', (select uuid from generic_code_list where code like 'TON' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Trinidad a Tobago', 'Republika Trinidadu a Tobaga', (select uuid from generic_code_list where code like 'TTO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Spojené arabské emiráty', 'Spojené arabské emiráty', (select uuid from generic_code_list where code like 'ARE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Tunisko', 'Tuniská republika', (select uuid from generic_code_list where code like 'TUN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Turecko', 'Turecká republika', (select uuid from generic_code_list where code like 'TUR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Turkménsko', 'Turkménsko', (select uuid from generic_code_list where code like 'TKM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Turks a Caicos', 'Ostrovy Turks a Caicos', (select uuid from generic_code_list where code like 'TCA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Tuvalu', 'Tuvalu', (select uuid from generic_code_list where code like 'TUV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Uganda', 'Ugandská republika', (select uuid from generic_code_list where code like 'UGA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Ukrajina', 'Ukrajina', (select uuid from generic_code_list where code like 'UKR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Severné Macedónsko', 'Severomacedónska republika', (select uuid from generic_code_list where code like 'MKD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Egypt', 'Egyptská arabská republika', (select uuid from generic_code_list where code like 'EGY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Spojené kráľovstvo', 'Spojené kráľovstvo Veľkej Británie a Severného Írska', (select uuid from generic_code_list where code like 'GBR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Tanzánia', 'Tanzánijská zjednotená republika', (select uuid from generic_code_list where code like 'TZA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Spojené štáty', 'Spojené štáty americké', (select uuid from generic_code_list where code like 'USA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Americké panenské ostr.', 'Panenské ostrovy Spojených štátov', (select uuid from generic_code_list where code like 'VIR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Burkina', 'Burkina Faso', (select uuid from generic_code_list where code like 'BFA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Uruguaj', 'Uruguajská východná republika', (select uuid from generic_code_list where code like 'URY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Uzbekistan', 'Uzbecká republika', (select uuid from generic_code_list where code like 'UZB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Venezuela', 'Venezuelská bolívarovská republika', (select uuid from generic_code_list where code like 'VEN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Wallis a Futuna', 'Zámorské teritórium Wallisu a Futuny', (select uuid from generic_code_list where code like 'WLF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Samoa', 'Samojský nezávislý štát', (select uuid from generic_code_list where code like 'WSM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Jemen', 'Jemenská republika', (select uuid from generic_code_list where code like 'YEM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Srbsko a Čierna Hora', 'Srbsko a Čierna Hora', (select uuid from generic_code_list where code like 'SCG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zambia', 'Zambijská republika', (select uuid from generic_code_list where code like 'ZMB' and type = 'COUNTRY'));


INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Afghanistan', 'Afghanistan', (select uuid from generic_code_list where code like 'AFG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Albania', 'Republic of Albania', (select uuid from generic_code_list where code like 'ALB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Antarctica', 'Antarctica', (select uuid from generic_code_list where code like 'ATA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Algeria', 'People`s Democratic Republic of Algeria', (select uuid from generic_code_list where code like 'DZA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'American Samoa', 'American Samoa', (select uuid from generic_code_list where code like 'ASM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Andorra', 'Principality of Andorra', (select uuid from generic_code_list where code like 'AND' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Angola', 'Republic of Angola', (select uuid from generic_code_list where code like 'AGO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Antigua and Barbuda', 'Antigua and Barbuda', (select uuid from generic_code_list where code like 'ATG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Azerbaijan', 'Republic of Azerbaijan', (select uuid from generic_code_list where code like 'AZE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Argentina', 'Argentine Republic', (select uuid from generic_code_list where code like 'ARG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Australia', 'Australia', (select uuid from generic_code_list where code like 'AUS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Austria', 'Republic of Austria', (select uuid from generic_code_list where code like 'AUT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bahamas', 'Commonwealth of The Bahamas', (select uuid from generic_code_list where code like 'BHS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bahrain', 'Kingdom of Bahrain', (select uuid from generic_code_list where code like 'BHR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bangladesh', 'People`s Republic of Bangladesh', (select uuid from generic_code_list where code like 'BGD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Armenia', 'Republic of Armenia', (select uuid from generic_code_list where code like 'ARM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Barbados', 'Barbados', (select uuid from generic_code_list where code like 'BRB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Belgium', 'Kingdom of Belgium', (select uuid from generic_code_list where code like 'BEL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bermuda', 'Bermuda', (select uuid from generic_code_list where code like 'BMU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bhutan', 'Kingdom of Bhutan', (select uuid from generic_code_list where code like 'BTN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bolivia', 'Republic of Bolivia', (select uuid from generic_code_list where code like 'BOL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bosnia and Herzegovina', 'Bosnia and Herzegovina', (select uuid from generic_code_list where code like 'BIH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Botswana', 'Republic of Botswana', (select uuid from generic_code_list where code like 'BWA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bouvet Island', 'Bouvet Island', (select uuid from generic_code_list where code like 'BVT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Brazil', 'Federative Republic of Brazil', (select uuid from generic_code_list where code like 'BRA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Belize', 'Belize', (select uuid from generic_code_list where code like 'BLZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'British Ind.Ocean Terr.', 'British Indian Ocean Territory', (select uuid from generic_code_list where code like 'IOT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Solomon Islands', 'Solomon Islands', (select uuid from generic_code_list where code like 'SLB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Virgin Islands, British', 'British Virgin Islands', (select uuid from generic_code_list where code like 'VGB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Brunei Darussalam', 'Brunei Darussalam', (select uuid from generic_code_list where code like 'BRN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Bulgaria', 'Republic of Bulgaria', (select uuid from generic_code_list where code like 'BGR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Myanmar', 'Union of Myanmar', (select uuid from generic_code_list where code like 'MMR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Burundi', 'Republic of Burundi', (select uuid from generic_code_list where code like 'BDI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Belarus', 'Republic of Belarus', (select uuid from generic_code_list where code like 'BLR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cambodia', 'Kingdom of Cambodia', (select uuid from generic_code_list where code like 'KHM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cameroon', 'Republic of Cameroon', (select uuid from generic_code_list where code like 'CMR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Canada', 'Canada', (select uuid from generic_code_list where code like 'CAN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cabo Verde', 'Republic of Cabo Verde', (select uuid from generic_code_list where code like 'CPV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cayman Islands', 'Cayman Islands', (select uuid from generic_code_list where code like 'CYM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Central African Rep.', 'Central African Republic', (select uuid from generic_code_list where code like 'CAF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Sri Lanka', 'Democratic Socialist Republic of Sri Lanka', (select uuid from generic_code_list where code like 'LKA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Chad', 'Republic of Chad', (select uuid from generic_code_list where code like 'TCD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Chile', 'Republic of Chile', (select uuid from generic_code_list where code like 'CHL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'China', 'People`s Republic of China', (select uuid from generic_code_list where code like 'CHN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Taiwan', 'Republic of China', (select uuid from generic_code_list where code like 'TWN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Christmas Island', 'Christmas Island', (select uuid from generic_code_list where code like 'CXR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cocos (Keeling) Islands', 'Cocos (Keeling) Islands', (select uuid from generic_code_list where code like 'CCK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Colombia', 'Republic of Colombia', (select uuid from generic_code_list where code like 'COL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Comoros', 'Union of the Comoros', (select uuid from generic_code_list where code like 'COM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mayotte', 'Mayotte', (select uuid from generic_code_list where code like 'MYT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Congo', 'Republic of the Congo', (select uuid from generic_code_list where code like 'COG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Congo, the Dem.R. of the', 'The Democratic Republic of the Congo', (select uuid from generic_code_list where code like 'COD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cook Islands', 'Cook Islands', (select uuid from generic_code_list where code like 'COK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Costa Rica', 'Republic of Costa Rica', (select uuid from generic_code_list where code like 'CRI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Croatia', 'Republic of Croatia', (select uuid from generic_code_list where code like 'HRV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cuba', 'Republic of Cuba', (select uuid from generic_code_list where code like 'CUB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Cyprus', 'Republic of Cyprus', (select uuid from generic_code_list where code like 'CYP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Czechia', 'Czech Republic', (select uuid from generic_code_list where code like 'CZE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Benin', 'Republic of Benin', (select uuid from generic_code_list where code like 'BEN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Denmark', 'Kingdom of Denmark', (select uuid from generic_code_list where code like 'DNK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Dominica', 'Commonwealth of Dominica', (select uuid from generic_code_list where code like 'DMA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Dominican republic', 'Dominican Republic', (select uuid from generic_code_list where code like 'DOM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Ecuador', 'Republic of Ecuador', (select uuid from generic_code_list where code like 'ECU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'El Salvador', 'Republic of El Salvador', (select uuid from generic_code_list where code like 'SLV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Equatorial Guinea', 'Republic of Equatorial Guinea', (select uuid from generic_code_list where code like 'GNQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Ethiopia', 'Federal Democratic Republic of Ethiopia', (select uuid from generic_code_list where code like 'ETH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Eritrea', 'Eritrea', (select uuid from generic_code_list where code like 'ERI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Estonia', 'Republic of Estonia', (select uuid from generic_code_list where code like 'EST' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Faroe Islands', 'Faroe Islands', (select uuid from generic_code_list where code like 'FRO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Falkland Isl. (Malvinas)', 'Falkland Islands (Malvinas)', (select uuid from generic_code_list where code like 'FLK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'S.Georgia, s.Sand. Isl.', 'South Georgia and the South Sandwich Islands', (select uuid from generic_code_list where code like 'SGS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Fiji', 'Republic of the Fiji Islands', (select uuid from generic_code_list where code like 'FJI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Finland', 'Republic of Finland', (select uuid from generic_code_list where code like 'FIN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Aland Islands', 'Aland Islands', (select uuid from generic_code_list where code like 'ALA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'France', 'French Republic', (select uuid from generic_code_list where code like 'FRA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'French Guiana', 'French Guiana', (select uuid from generic_code_list where code like 'GUF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'French Polynesia', 'French Polynesia', (select uuid from generic_code_list where code like 'PYF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'French Southern Territ.', 'French Southern Territories', (select uuid from generic_code_list where code like 'ATF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Djibouti', 'Republic of Djibouti', (select uuid from generic_code_list where code like 'DJI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Gabon', 'Gabonese Republic', (select uuid from generic_code_list where code like 'GAB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Georgia', 'Georgia', (select uuid from generic_code_list where code like 'GEO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Gambia', 'Republic of the Gambia', (select uuid from generic_code_list where code like 'GMB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Palestin.Territ.Occupied', 'Occupied Palestinian Territory', (select uuid from generic_code_list where code like 'PSE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Germany', 'Federal Republic of Germany', (select uuid from generic_code_list where code like 'DEU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Ghana', 'Republic of Ghana', (select uuid from generic_code_list where code like 'GHA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Gibraltar', 'Gibraltar', (select uuid from generic_code_list where code like 'GIB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Kiribati', 'Republic of Kiribati', (select uuid from generic_code_list where code like 'KIR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Greece', 'Hellenic Republic', (select uuid from generic_code_list where code like 'GRC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Greenland', 'Greenland', (select uuid from generic_code_list where code like 'GRL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Grenada', 'Grenada', (select uuid from generic_code_list where code like 'GRD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Guadeloupe', 'Guadeloupe', (select uuid from generic_code_list where code like 'GLP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Guam', 'Guam', (select uuid from generic_code_list where code like 'GUM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Guatemala', 'Republic of Guatemala', (select uuid from generic_code_list where code like 'GTM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Guinea', 'Republic of Guinea', (select uuid from generic_code_list where code like 'GIN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Guyana', 'Co-operative Republic of Guyana', (select uuid from generic_code_list where code like 'GUY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Haiti', 'Republic of Haiti', (select uuid from generic_code_list where code like 'HTI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Heard and McDon.Islands', 'Heard Island and McDonald Islands', (select uuid from generic_code_list where code like 'HMD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Holy See (Vatican CS)', 'Holy See (Vatican City State)', (select uuid from generic_code_list where code like 'VAT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Honduras', 'Republic of Honduras', (select uuid from generic_code_list where code like 'HND' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Hong Kong', 'Hong Kong Special Administrative Region of China', (select uuid from generic_code_list where code like 'HKG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Hungary', 'Republic of Hungary', (select uuid from generic_code_list where code like 'HUN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Iceland', 'Republic of Iceland', (select uuid from generic_code_list where code like 'ISL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'India', 'Republic of India', (select uuid from generic_code_list where code like 'IND' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Indonesia', 'Republic of Indonesia', (select uuid from generic_code_list where code like 'IDN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Iran,Islamic Republic of', 'Islamic Republic of Iran', (select uuid from generic_code_list where code like 'IRN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Iraq', 'Republic of Iraq', (select uuid from generic_code_list where code like 'IRQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Ireland', 'Ireland', (select uuid from generic_code_list where code like 'IRL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Israel', 'State of Israel', (select uuid from generic_code_list where code like 'ISR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Italy', 'Italian Republic', (select uuid from generic_code_list where code like 'ITA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Côte d`Ivoire', 'Republic of Côte d`Ivoire', (select uuid from generic_code_list where code like 'CIV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Jamaica', 'Jamaica', (select uuid from generic_code_list where code like 'JAM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Japan', 'Japan', (select uuid from generic_code_list where code like 'JPN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Kazakhstan', 'Republic of Kazakhstan', (select uuid from generic_code_list where code like 'KAZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Jordan', 'Hashemite Kingdom of Jordan', (select uuid from generic_code_list where code like 'JOR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Kenya', 'Republic of Kenya', (select uuid from generic_code_list where code like 'KEN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Korea, Dem.Peopl.Rep. of', 'Democratic People`s Republic of Korea', (select uuid from generic_code_list where code like 'PRK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Korea, Republic of', 'Republic of Korea', (select uuid from generic_code_list where code like 'KOR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Kuwait', 'State of Kuwait', (select uuid from generic_code_list where code like 'KWT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Kyrgyzstan', 'Kyrgyz Republic', (select uuid from generic_code_list where code like 'KGZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Lao People`s Dem. Rep.', 'Lao People`s Democratic Republic', (select uuid from generic_code_list where code like 'LAO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Lebanon', 'Lebanese Republic', (select uuid from generic_code_list where code like 'LBN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Lesotho', 'Kingdom of Lesotho', (select uuid from generic_code_list where code like 'LSO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Latvia', 'Republic of Latvia', (select uuid from generic_code_list where code like 'LVA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Liberia', 'Republic of Liberia', (select uuid from generic_code_list where code like 'LBR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Libyan Arab Jamahiriya', 'Socialist People`s Libyan Arab Jamahiriya', (select uuid from generic_code_list where code like 'LBY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Liechtenstein', 'Principality of Liechtenstein', (select uuid from generic_code_list where code like 'LIE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Lithuania', 'Republic of Lithuania', (select uuid from generic_code_list where code like 'LTU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Luxembourg', 'Grand Duchy of Luxembourg', (select uuid from generic_code_list where code like 'LUX' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Macao', 'Macao Special Administrative Region of China', (select uuid from generic_code_list where code like 'MAC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Madagascar', 'Republic of Madagascar', (select uuid from generic_code_list where code like 'MDG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Malawi', 'Republic of Malawi', (select uuid from generic_code_list where code like 'MWI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Malaysia', 'Malaysia', (select uuid from generic_code_list where code like 'MYS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Maldives', 'Republic of Maldives', (select uuid from generic_code_list where code like 'MDV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mali', 'Republic of Mali', (select uuid from generic_code_list where code like 'MLI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Malta', 'Republic of Malta', (select uuid from generic_code_list where code like 'MLT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Martinique', 'Martinique', (select uuid from generic_code_list where code like 'MTQ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mauritania', 'Islamic Republic of Mauritania', (select uuid from generic_code_list where code like 'MRT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mauritius', 'Republic of Mauritius', (select uuid from generic_code_list where code like 'MUS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mexico', 'United Mexican States', (select uuid from generic_code_list where code like 'MEX' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Monaco', 'Principality of Monaco', (select uuid from generic_code_list where code like 'MCO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mongolia', 'Mongolia', (select uuid from generic_code_list where code like 'MNG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Moldova, Republic of', 'Republic of Moldova', (select uuid from generic_code_list where code like 'MDA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Montenegro', 'Republic of Montenegro', (select uuid from generic_code_list where code like 'MNE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Montserrat', 'Montserrat', (select uuid from generic_code_list where code like 'MSR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Morocco', 'Kingdom of Morocco', (select uuid from generic_code_list where code like 'MAR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Mozambique', 'Republic of Mozambique', (select uuid from generic_code_list where code like 'MOZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Oman', 'Sultanate of Oman', (select uuid from generic_code_list where code like 'OMN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Namibia', 'Republic of Namibia', (select uuid from generic_code_list where code like 'NAM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Nauru', 'Republic of Nauru', (select uuid from generic_code_list where code like 'NRU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Nepal', 'Kingdom of Nepal', (select uuid from generic_code_list where code like 'NPL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Netherlands', 'Kingdom of the Netherlands', (select uuid from generic_code_list where code like 'NLD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Netherlands Antilles', 'Netherlands Antilles', (select uuid from generic_code_list where code like 'ANT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Aruba', 'Aruba', (select uuid from generic_code_list where code like 'ABW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'New Caledonia', 'New Caledonia', (select uuid from generic_code_list where code like 'NCL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Vanuatu', 'Republic of Vanuatu', (select uuid from generic_code_list where code like 'VUT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'New Zealand', 'New Zealand', (select uuid from generic_code_list where code like 'NZL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Nicaragua', 'Republic of Nicaragua', (select uuid from generic_code_list where code like 'NIC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Niger', 'Republic of the Niger', (select uuid from generic_code_list where code like 'NER' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Nigeria', 'Federal Republic of Nigeria', (select uuid from generic_code_list where code like 'NGA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Niue', 'Republic of Niue', (select uuid from generic_code_list where code like 'NIU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Norfolk Island', 'Norfolk Island', (select uuid from generic_code_list where code like 'NFK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Norway', 'Kingdom of Norway', (select uuid from generic_code_list where code like 'NOR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Northern Mariana Islands', 'Commonwealth of the Northern Mariana Islands', (select uuid from generic_code_list where code like 'MNP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'US Minor Outlying Isl.', 'United States Minor Outlying Islands', (select uuid from generic_code_list where code like 'UMI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Micronesia, Fed.St. of', 'Federated States of Micronesia', (select uuid from generic_code_list where code like 'FSM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Marshall Islands', 'Republic of the Marshall Islands', (select uuid from generic_code_list where code like 'MHL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Palau', 'Republic of Palau', (select uuid from generic_code_list where code like 'PLW' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Pakistan', 'Islamic Republic of Pakistan', (select uuid from generic_code_list where code like 'PAK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Panama', 'Republic of Panama', (select uuid from generic_code_list where code like 'PAN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Papua New Guinea', 'Papua New Guinea', (select uuid from generic_code_list where code like 'PNG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Paraguay', 'Republic of Paraguay', (select uuid from generic_code_list where code like 'PRY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Peru', 'Republic of Peru', (select uuid from generic_code_list where code like 'PER' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Philippines', 'Republic of Philippines', (select uuid from generic_code_list where code like 'PHL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Pitcairn', 'Pitcairn Islands', (select uuid from generic_code_list where code like 'PCN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Poland', 'Republic of Poland', (select uuid from generic_code_list where code like 'POL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Portugal', 'Portuguese Republic', (select uuid from generic_code_list where code like 'PRT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Guinea-Bissau', 'Republic of Guinea-Bissau', (select uuid from generic_code_list where code like 'GNB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'East Timor', 'Democratic Republic of East Timor', (select uuid from generic_code_list where code like 'TLS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Puerto Rico', 'Puerto Rico', (select uuid from generic_code_list where code like 'PRI' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Qatar', 'State of Qatar', (select uuid from generic_code_list where code like 'QAT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Réunion', 'Réunion', (select uuid from generic_code_list where code like 'REU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Romania', 'Romania', (select uuid from generic_code_list where code like 'ROU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Russian Federation', 'Russian Federation', (select uuid from generic_code_list where code like 'RUS' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Rwanda', 'Rwandese Republic', (select uuid from generic_code_list where code like 'RWA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Saint Helena', 'Saint Helena', (select uuid from generic_code_list where code like 'SHN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Saint Kitts and Nevis', 'Saint Kitts and Nevis', (select uuid from generic_code_list where code like 'KNA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Anguilla', 'Anguilla', (select uuid from generic_code_list where code like 'AIA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Saint Lucia', 'Saint Lucia', (select uuid from generic_code_list where code like 'LCA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'St. Pierre and Miquelon', 'Saint Pierre and Miquelon', (select uuid from generic_code_list where code like 'SPM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'St.Vincent,the Grenadin.', 'Saint Vincent and the Grenadines', (select uuid from generic_code_list where code like 'VCT' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'San Marino', 'Republic of San Marino', (select uuid from generic_code_list where code like 'SMR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Sao Tome and Principe', 'Democratic Republic of Sao Tome and Principe', (select uuid from generic_code_list where code like 'STP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Saudi Arabia', 'Kingdom of Saudi Arabia', (select uuid from generic_code_list where code like 'SAU' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Senegal', 'Republic of Senegal', (select uuid from generic_code_list where code like 'SEN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Serbia', 'Republic of Serbia', (select uuid from generic_code_list where code like 'SRB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Seychelles', 'Republic of Seychelles', (select uuid from generic_code_list where code like 'SYC' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Sierra Leone', 'Republic of Sierra Leone', (select uuid from generic_code_list where code like 'SLE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Singapore', 'Republic of Singapore', (select uuid from generic_code_list where code like 'SGP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Slovakia', 'Slovak Republic', (select uuid from generic_code_list where code like 'SVK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Viet Nam', 'Socialist Republic of Viet Nam', (select uuid from generic_code_list where code like 'VNM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Slovenia', 'Republic of Slovenia', (select uuid from generic_code_list where code like 'SVN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Somalia', 'Somali Republic', (select uuid from generic_code_list where code like 'SOM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'South Africa', 'Republic of South Africa', (select uuid from generic_code_list where code like 'ZAF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Zimbabwe', 'Republic of Zimbabwe', (select uuid from generic_code_list where code like 'ZWE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Spain', 'Kingdom of Spain', (select uuid from generic_code_list where code like 'ESP' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Western Sahara', 'Western Sahara', (select uuid from generic_code_list where code like 'ESH' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Sudan', 'Republic of the Sudan', (select uuid from generic_code_list where code like 'SDN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Suriname', 'Republic of Suriname', (select uuid from generic_code_list where code like 'SUR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Svalbard and Jan Mayen', 'Svalbard and Jan Mayen', (select uuid from generic_code_list where code like 'SJM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Eswatini', 'Kingdom of Eswatini', (select uuid from generic_code_list where code like 'SWZ' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Sweden', 'Kingdom of Sweden', (select uuid from generic_code_list where code like 'SWE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Switzerland', 'Swiss Confederation', (select uuid from generic_code_list where code like 'CHE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Syrian Arab Republic', 'Syrian Arab Republic', (select uuid from generic_code_list where code like 'SYR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Tajikistan', 'Republic of Tajikistan', (select uuid from generic_code_list where code like 'TJK' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Thailand', 'Kingdom of Thailand', (select uuid from generic_code_list where code like 'THA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Togo', 'Togolese Republic', (select uuid from generic_code_list where code like 'TGO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Tokelau', 'Tokelau', (select uuid from generic_code_list where code like 'TKL' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Tonga', 'Kingdom of Tonga', (select uuid from generic_code_list where code like 'TON' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Trinidad and Tobago', 'Republic of Trinidad and Tobago', (select uuid from generic_code_list where code like 'TTO' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'United Arab Emirates', 'United Arab Emirates', (select uuid from generic_code_list where code like 'ARE' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Tunisia', 'Republic of Tunisia', (select uuid from generic_code_list where code like 'TUN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Turkey', 'Republic of Turkey', (select uuid from generic_code_list where code like 'TUR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Turkmenistan', 'Turkmenistan', (select uuid from generic_code_list where code like 'TKM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Turks and Caicos Islands', 'Turks and Caicos Islands', (select uuid from generic_code_list where code like 'TCA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Tuvalu', 'Tuvalu', (select uuid from generic_code_list where code like 'TUV' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Uganda', 'Republic of Uganda', (select uuid from generic_code_list where code like 'UGA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Ukraine', 'Ukraine', (select uuid from generic_code_list where code like 'UKR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'North Macedonia', 'Republic of North Macedonia', (select uuid from generic_code_list where code like 'MKD' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Egypt', 'Arab Republic of Egypt', (select uuid from generic_code_list where code like 'EGY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'United Kingdom', 'United Kingdom of Great Britain and Northern Ireland', (select uuid from generic_code_list where code like 'GBR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Tanzania, United Rep.of', 'United Republic of Tanzania', (select uuid from generic_code_list where code like 'TZA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'United States', 'United States of America', (select uuid from generic_code_list where code like 'USA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Virgin Islands, U.S.', 'Virgin Islands of the United States', (select uuid from generic_code_list where code like 'VIR' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Burkina Faso', 'Burkina Faso', (select uuid from generic_code_list where code like 'BFA' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Uruguay', 'Eastern Republic of Uruguay', (select uuid from generic_code_list where code like 'URY' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Uzbekistan', 'Republic of Uzbekistan', (select uuid from generic_code_list where code like 'UZB' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Venezuela', 'Bolivarian Republic of Venezuela', (select uuid from generic_code_list where code like 'VEN' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Wallis and Futuna', 'Wallis and Futuna', (select uuid from generic_code_list where code like 'WLF' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Samoa', 'Independent State of Samoa', (select uuid from generic_code_list where code like 'WSM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Yemen', 'Republic of Yemen', (select uuid from generic_code_list where code like 'YEM' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Serbia and Montenegro', 'Serbia and Montenegro', (select uuid from generic_code_list where code like 'SCG' and type = 'COUNTRY')),
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Zambia', 'Republic of Zambia', (select uuid from generic_code_list where code like 'ZMB' and type = 'COUNTRY'));