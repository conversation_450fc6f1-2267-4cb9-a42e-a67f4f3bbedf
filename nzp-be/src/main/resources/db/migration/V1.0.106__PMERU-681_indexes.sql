DROP INDEX idx_business_partner_external_id;
CREATE UNIQUE INDEX unique_idx_business_partner_external_id on business_partner(external_id);

DROP INDEX idx_contract_account_external_id;
CREATE UNIQUE INDEX unique_idx_contract_account_external_id on contract_account(external_id);

DROP INDEX idx_contract_external_id;
CREATE UNIQUE INDEX unique_idx_contract_external_id on contract(external_id);

DROP INDEX idx_delivery_point_external_id;
CREATE UNIQUE INDEX unique_idx_delivery_point_external_id on delivery_point(external_id);

DROP INDEX idx_invoice_external_id;
CREATE UNIQUE INDEX unique_idx_invoice_external_id on invoice(external_id);

DROP INDEX idx_payment_external_id;
CREATE UNIQUE INDEX unique_idx_payment_external_id on payment(external_id);

DROP INDEX idx_meter_reading_external_id;
CREATE UNIQUE INDEX unique_idx_meter_reading_external_id on meter_reading(external_id);

CREATE INDEX unique_idx_customer_account_external_id on customer_account(external_id);

CREATE INDEX unique_idx_customer_request_external_id on customer_request(external_id);

CREATE INDEX unique_idx_customer_request_note_external_id on customer_request_note(external_id);

CREATE INDEX unique_idx_customer_notification_external_id on customer_notification(external_id);

