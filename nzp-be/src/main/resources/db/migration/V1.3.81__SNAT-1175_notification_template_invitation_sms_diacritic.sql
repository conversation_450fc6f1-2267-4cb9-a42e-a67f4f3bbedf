----------------------------------------------------------------------------
------------------ SNAT-1175 DELETE diacritic from invitation SMS
----------------------------------------------------------------------------

UPDATE notification_template_i18n
SET version=(version + 1),
    sms_body = 'Vazeny zakaznik, bolo Vam vygenerovane jednorazove heslo pre prihlasenie do NZP: ${attributes.generatedPassword}. Po prvom pouziti bude potrebne heslo zmenit. Vase SPP',
    updated_at = now()
WHERE notification_template_id in
      (select uuid from notification_template where code in ('CUSTOMER_INVITATION_PASSWORD'))
  AND lower(locale) = 'sk';

UPDATE notification_template_i18n
SET version=(version + 1),
    sms_body = '[EN] Vazeny zakaznik, bolo Vam vygenerovane jednorazove heslo pre prihlasenie do NZP: ${attributes.generatedPassword}. Po prvom pouziti bude potrebne heslo zmenit. Vase SPP',
    updated_at = now()
WHERE notification_template_id in
      (select uuid from notification_template where code in ('CUSTOMER_INVITATION_PASSWORD'))
  AND lower(locale) = 'en';
