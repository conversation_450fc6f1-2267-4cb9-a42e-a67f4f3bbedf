alter table customer_request  add column contract_id varchar(50);

update customer_request cr
set contract_id = (
    select c.id from contract c
    where c.business_partner_id = cr.business_partner_id
    and c.delivery_point_id = cr.delivery_point_id
    and c.effective_to >= current_date
    and c.effective_from <= current_date
    order by c.effective_to desc limit 1
    )
where cr.delivery_point_id is not null;
