------------------------------------------------------------------------------------------------------------------------
-------- PMERU-1647: [PENT] [4.1.2][M1] Business logic - možnost vložení neplatných dat (API)
------------------------------------------------------------------------------------------------------------------------

UPDATE notification_template_i18n nti
SET updated_at = now(), version = version + 1, email_body = '<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}">úspešne ste si nastavili&nbsp;telefónne číslo na&nbsp;&nbsp;${customer.phone}.<br>Od tohto okamihu ho môžete používať aj pre prihlásenie do portálu Moje SPP.</@spp.notification_email_template>'
WHERE notification_template_id IN (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PHONE_CHANGE_SUCCESS')
  AND lower(locale) = 'sk';

UPDATE notification_template_i18n nti
SET updated_at = now(), version = version + 1, email_body = '<#import "spp.macros_en.ftl" as spp><@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}">[EN] úspešne ste si nastavili&nbsp;telefónne číslo na&nbsp;&nbsp;${customer.phone}.<br>Od tohto okamihu ho môžete používať aj pre prihlásenie do portálu Moje SPP.</@spp.notification_email_template>'
WHERE notification_template_id IN (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PHONE_CHANGE_SUCCESS')
  AND lower(locale) = 'en';
