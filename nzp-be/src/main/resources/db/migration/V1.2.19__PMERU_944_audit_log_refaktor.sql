
ALTER TABLE audit_log DROP CONSTRAINT fk_audit_log_customer_account;
DROP INDEX idx_audit_customer;

ALTER TABLE audit_log RENAME COLUMN customer_account_uuid			TO logged_customer_account_uuid;
ALTER TABLE audit_log RENAME COLUMN customer_account_email			TO logged_customer_account_email;
ALTER TABLE audit_log RENAME COLUMN customer_account_first_name		TO logged_customer_account_first_name;
ALTER TABLE audit_log RENAME COLUMN customer_account_last_name		TO logged_customer_account_last_name;

ALTER TABLE audit_log DROP COLUMN customer_account_phone;

alter table audit_log add constraint fk_audit_log_logged_customer_account foreign key (logged_customer_account_uuid) references customer_account(uuid) match simple 
	on update no action
	on delete no action;
	
CREATE INDEX idx_audit_logged_customer ON audit_log (logged_customer_account_uuid);
	
	
	

ALTER TABLE audit_log ADD COLUMN related_customer_account_uuid 			uuid;
ALTER TABLE audit_log ADD COLUMN related_customer_account_email 		CHARACTER VARYING(64);
ALTER TABLE audit_log ADD COLUMN related_customer_account_first_name 	CHARACTER VARYING(100);
ALTER TABLE audit_log ADD COLUMN related_customer_account_last_name 	CHARACTER VARYING(100);

alter table audit_log add constraint fk_audit_log_related_customer_account foreign key (related_customer_account_uuid) references customer_account(uuid) match simple
	on update no action
	on delete no action;
	
CREATE INDEX idx_audit_customer ON audit_log (related_customer_account_uuid);

