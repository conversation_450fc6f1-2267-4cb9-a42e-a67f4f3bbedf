INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, execution_type,
    description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SHARING_INVITATION', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Zdielanie neregistrovanemu uzivatelovy ', 'LOW', 'AUTOMATIC',
    'Notifikacia zdielania neregistrovanemu uzivatelovi', null, null, true, false);

INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, status, header, email_body,
    email_subject, sms_body,
    notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'Nastavenie zdieľania odberného miesta', '<PERSON><PERSON><PERSON><PERSON> de<PERSON>, práve V<PERSON>m bolo nastavené zdieľanie odberného miesta od zákazníka ${attributes.sharingFrom}. Va<PERSON>e SPP',
    'Nastavenie zdieľania odberného miesta', '<PERSON><PERSON><PERSON><PERSON>, práve Vám bolo nastavené zdieľanie odberného miesta od zákazníka ${attributes.sharingFrom}. Vaše SPP',
    (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1), 'SK');

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
	VALUES ((select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
	VALUES ((select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
	VALUES ((select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
	VALUES ((select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION' and version = 1));