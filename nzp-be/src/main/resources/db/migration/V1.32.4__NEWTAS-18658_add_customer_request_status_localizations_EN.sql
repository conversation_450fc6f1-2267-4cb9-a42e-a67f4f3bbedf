------------------------------------------------------------------------------------------------------------------------
----------- NEWTAS-18658 - SPP NZP prod -  Incident 491393: Nespravne lokalizacie pri statusoch ziadosti
------------------------------------------------------------------------------------------------------------------------

UPDATE generic_code_list_i18n
SET updated_at = now(), version = version + 1, name = 'Canceled by the customer before sending to SPP'
WHERE code_list_uuid = (select uuid from generic_code_list where code = 'CANCELLED_BY_USER' and "type"='CUSTOMER_REQUEST_STATUS') and LOWER(locale) = 'en';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = version + 1, name = 'Ready to send to SPP'
WHERE code_list_uuid = (select uuid from generic_code_list where code = 'CREATED' and "type"='CUSTOMER_REQUEST_STATUS') and LOWER(locale) = 'en';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = version + 1, name = 'Ready to print'
WHERE code_list_uuid = (select uuid from generic_code_list where code = 'GENERATED' and "type"='CUSTOMER_REQUEST_STATUS') and LOWER(locale) = 'en';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = version + 1, name = 'Work in progress'
WHERE code_list_uuid = (select uuid from generic_code_list where code = 'PRE_CREATED' and "type"='CUSTOMER_REQUEST_STATUS') and LOWER(locale) = 'en';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = version + 1, name = 'Sent to SPP'
WHERE code_list_uuid = (select uuid from generic_code_list where code = 'REGISTERED' and "type"='CUSTOMER_REQUEST_STATUS') and LOWER(locale) = 'en';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = version + 1, name = 'Canceled'
WHERE code_list_uuid = (select uuid from generic_code_list where code = 'SAP_CANCELLED' and "type"='CUSTOMER_REQUEST_STATUS') and LOWER(locale) = 'en';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = version + 1, name = 'Canceled by customer in SPP'
WHERE code_list_uuid = (select uuid from generic_code_list where code = 'SAP_CANCELLED_BY_USER' and "type"='CUSTOMER_REQUEST_STATUS') and LOWER(locale) = 'en';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = version + 1, name = 'Finished'
WHERE code_list_uuid = (select uuid from generic_code_list where code = 'SAP_FINISHED' and "type"='CUSTOMER_REQUEST_STATUS') and LOWER(locale) = 'en';
