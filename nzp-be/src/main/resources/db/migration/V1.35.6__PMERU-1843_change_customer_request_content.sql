-------------------------------------
-- customer_request content change --
-------------------------------------

-- create customer requests backup
drop table if exists customer_request_backup;
create table customer_request_backup as select * from customer_request;

-- insert entity references
insert into customer_request_contract (
	uuid,
	created_at,
	updated_at,
	version,
	customer_request_id,
	contract_account_id,
	contract_id,
	delivery_point_id
)
select
	uuid_generate_v4(),
	now(),
	now(),
	1,
	uuid,
	contract_account_id,
	contract_id,
	delivery_point_id
from customer_request
where contract_account_id is not null
	or contract_id is not null
	or delivery_point_id is not null;
update customer_request
set content = content::jsonb || jsonb('{"entityReferences":[{}]}')
where content is not null and (content::jsonb->'metadata') is not null and (
	(content::jsonb->'metadata'->'deliveryPointId') is not null or
	(content::jsonb->'metadata'->'contractAccountId') is not null or
	(content::jsonb->'metadata'->'contractId') is not null
);

-- update customer requests content
update customer_request
set content = jsonb_set(content::jsonb, '{entityReferences,0,deliveryPointId}', content::jsonb->'metadata'->'deliveryPointId')
where content is not null and (content::jsonb->'metadata') is not null and (content::jsonb->'metadata'->'deliveryPointId') is not null;

update customer_request
set content = jsonb_set(content::jsonb, '{entityReferences,0,contractAccountId}', content::jsonb->'metadata'->'contractAccountId')
where content is not null and (content::jsonb->'metadata') is not null and (content::jsonb->'metadata'->'contractAccountId') is not null;

update customer_request
set content = jsonb_set(content::jsonb, '{entityReferences,0,contractId}', content::jsonb->'metadata'->'contractId')
where content is not null and (content::jsonb->'metadata') is not null and (content::jsonb->'metadata'->'contractId') is not null;

update customer_request
set content = content::jsonb #- '{metadata,deliveryPointId}'
where content is not null and (content::jsonb->'metadata') is not null and (content::jsonb->'metadata'->'deliveryPointId') is not null;

update customer_request
set content = content::jsonb #- '{metadata,contractAccountId}'
where content is not null and (content::jsonb->'metadata') is not null and (content::jsonb->'metadata'->'contractAccountId') is not null;

update customer_request
set content = content::jsonb #- '{metadata,contractId}'
where content is not null and (content::jsonb->'metadata') is not null and (content::jsonb->'metadata'->'contractId') is not null;
