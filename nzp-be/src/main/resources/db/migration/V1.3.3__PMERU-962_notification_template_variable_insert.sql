
-- common fields that are always present
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.email', '<PERSON><PERSON>', null, 'STRING' from notification_template nt;
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.firstName', '<PERSON><PERSON>', null, 'STRING' from notification_template nt where nt.code not in ('CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', null, 'STRING' from notification_template nt where nt.code not in ('CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', null, 'STRING' from notification_template nt where nt.code not in ('CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.email', 'Email zalogovan<PERSON> p<PERSON>', null, 'STRING' from notification_template nt;
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.firstName', 'Meno zalogovaného používateľa', null, 'STRING' from notification_template nt;
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.lastName', 'Priezvisko zalogovaného používateľa', null, 'STRING' from notification_template nt;
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.phone', 'Telefónne číslo zalogovaného používateľa', null, 'STRING' from notification_template nt;
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationTemplateCode', 'Kód notifikácie', null, 'STRING' from notification_template nt;
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', null, 'STRING' from notification_template nt;
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', null, 'STRING' from notification_template nt;
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', null, 'STRING' from notification_template nt;
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', null, 'STRING' from notification_template nt where nt.code not in ('CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', null, 'STRING' from notification_template nt where nt.code not in ('CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', null, 'STRING' from notification_template nt where nt.code not in ('CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', null, 'STRING' from notification_template nt where nt.code not in ('CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', null, 'STRING' from notification_template nt where nt.code not in ('CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácie cielenej entity', null, 'STRING' from notification_template nt where nt.code not in ('CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity', null, 'STRING' from notification_template nt where nt.code not in ('CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', 'Týka sa iba SAP entít.', 'STRING' from notification_template nt where nt.code not in ('CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'locale', 'Lokalizácia', null, 'NUMBER' from notification_template nt;
-------------------------------------------------------------------------------------------------------------
-- challenge codes
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.challengeCode', 'Verifikačný kód', 'Verifikačný kód zaslaný zákazníkovi na overenie identity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_REGISTRATION_REQUEST', 'CUSTOMER_PASSWORD_RECOVERY_REQUEST', 'CUSTOMER_EMAIL_CHANGE_REQUEST', 'CUSTOMER_PHONE_CHANGE_REQUEST', 'CUSTOMER_DELETE_REQUEST', 'BUSINESS_PARTNER_PAIRING_CHALLENGE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.challengeCodeUuid', 'ID verifikačného kódu', 'ID verifikačného kódu zaslaného zákazníkovi', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_REGISTRATION_REQUEST', 'CUSTOMER_PASSWORD_RECOVERY_REQUEST', 'CUSTOMER_EMAIL_CHANGE_REQUEST', 'CUSTOMER_PHONE_CHANGE_REQUEST', 'CUSTOMER_DELETE_REQUEST', 'BUSINESS_PARTNER_PAIRING_CHALLENGE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.challengeCodeValidTo', 'Dátum platnosti verifikačného kódu', 'Dátum platnosti dokedy je platný verifikačný kód zaslaný zákazníkovi', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_REGISTRATION_REQUEST', 'CUSTOMER_PASSWORD_RECOVERY_REQUEST', 'CUSTOMER_EMAIL_CHANGE_REQUEST', 'CUSTOMER_PHONE_CHANGE_REQUEST', 'CUSTOMER_DELETE_REQUEST', 'BUSINESS_PARTNER_PAIRING_CHALLENGE');
-------------------------------------------------------------------------------------------------------------
-- sharing
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingUuid', 'ID zákazníka pre zdieľanie', 'ID zákazníka, ktorému bolo nastavené zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingUuid', 'ID zákazníka pre zdieľanie', 'ID zákazníka, ktorý nastavil zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingUuid', 'ID zákazníka pre zdieľanie', 'ID zákazníka, ktorému bolo ukončené zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingUuid', 'ID zákazníka pre zdieľanie', 'ID zákazníka, ktorý ukončil zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_CONSUMER_REVOKE');

insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingEmail', 'Email zákazníka pre zdieľanie', 'Email zákazníka, ktorému bolo nastavené zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingEmail', 'Email zákazníka pre zdieľanie', 'Email zákazníka, ktorý nastavil zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingEmail', 'Email zákazníka pre zdieľanie', 'Email zákazníka, ktorému bolo ukončené zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingEmail', 'Email zákazníka pre zdieľanie', 'Email zákazníka, ktorý ukončil zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_CONSUMER_REVOKE');

insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingFirstName', 'Meno zákazníka pre zdieľanie', 'Meno zákazníka, ktorému bolo nastavené zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingFirstName', 'Meno zákazníka pre zdieľanie', 'Meno zákazníka, ktorý nastavil zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingFirstName', 'Meno zákazníka pre zdieľanie', 'Meno zákazníka, ktorému bolo ukončené zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingFirstName', 'Meno zákazníka pre zdieľanie', 'Meno zákazníka, ktorý ukončil zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_CONSUMER_REVOKE');

insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingLastName', 'Priezvisko zákazníka pre zdieľanie', 'Priezvisko zákazníka, ktorému bolo nastavené zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingLastName', 'Priezvisko zákazníka pre zdieľanie', 'Priezvisko zákazníka, ktorý nastavil zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_INVITATION');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingLastName', 'Priezvisko zákazníka pre zdieľanie', 'Priezvisko zákazníka, ktorému bolo ukončené zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.sharingLastName', 'Priezvisko zákazníka pre zdieľanie', 'Priezvisko zákazníka, ktorý ukončil zdieľanie entity', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_CONSUMER_REVOKE');
-----------------------------------------------------------------------
-- other special attributes
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.reason', 'Dôvod', 'Dôvod, kvôli ktorému bola vykonaná daná akcia', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_DEACTIVATION_SUCCESS');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.generatedPassword', 'Jednorazové heslo', 'Vygenerované jednorazové heslo potrebné zmeniť po prihlásení', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_INVITATION_PASSWORD');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.initializePairRequest', 'Automatické párovanie', 'Znak či ide o automatizované párovanie. Dostupné iba v prípade automatizovaného párovania ako napr. pri registrácii, alebo úprave tel. čísla.', 'BOOLEAN' from notification_template nt where nt.code in ('BUSINESS_PARTNER_PAIRING_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.verifyPairingPhone', 'Telefónne číslo zákazníka', 'Telefónne číslo pre overenie počas párovania', 'STRING' from notification_template nt where nt.code in ('BUSINESS_PARTNER_PAIRING_VERIFY');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.notificationType', 'Typ notifikácie', 'Typ notifikácie (napr.: email, sms)', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_INVOICE_SAP_ISSUED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.notificationSend', 'Dátum notifikácie', 'Dátum kedy bola naimportovaná notifikácia zo SAPu', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_INVOICE_SAP_ISSUED');

insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.notificationUuid', 'Uuid pôvodnej notifikácie', 'Uuid pôvodnej notifikácie, na základe ktorej používateľ nahlásil podozrivú aktivitu', 'STRING' from notification_template nt where nt.code in ('SUSPICIOUS_ACTIVITY');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.source', 'Zdroj', 'Zdroj podozrivej aktivity', 'STRING' from notification_template nt where nt.code in ('SUSPICIOUS_ACTIVITY');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.firstName', 'Meno', 'Meno používateľa, ktorý nahlásil podozrivú aktivitu', 'STRING' from notification_template nt where nt.code in ('SUSPICIOUS_ACTIVITY');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.lastName', 'Priezvisko', 'Priezvisko používateľa, ktorý nahlásil podozrivú aktivitu', 'STRING' from notification_template nt where nt.code in ('SUSPICIOUS_ACTIVITY');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.email', 'Email', 'Email používateľa, ktorý nahlásil podozrivú aktivitu', 'STRING' from notification_template nt where nt.code in ('SUSPICIOUS_ACTIVITY');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.phone', 'Tel. číslo', 'Tel. číslo používateľa, ktorý nahlásil podozrivú aktivitu', 'STRING' from notification_template nt where nt.code in ('SUSPICIOUS_ACTIVITY');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.description', 'Opis', 'Opis podozrivej aktivity', 'STRING' from notification_template nt where nt.code in ('SUSPICIOUS_ACTIVITY');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.customerRequestNote', 'Poznámka', 'Poznámka k žiadosti', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_REQUEST_NOTE_CREATE');
-----------------------------------------------------------------------
-- target: BusinessPartner: BUSINESS_PARTNER_PAIRING_REQUEST, BUSINESS_PARTNER_UNPAIRING_REQUEST, BUSINESS_PARTNER_UNPAIRING_SUCCESS, BUSINESS_PARTNER_PAIRING_CHALLENGE, BUSINESS_PARTNER_PAIRING_VERIFY
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.name', 'Názov obchodného partnera', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.firstName', 'Meno obchodného partnera', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.lastName', 'Priezvisko obchodného partnera', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.email', 'Email obchodného partnera', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.phone', 'Telefonné číslo obchodného partnera', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.street', 'Ulica obchodného partnera', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.streetNumber', 'Číslo domu obchodného partnera', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.city', 'Obec obchodného partnera', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.zipCode', 'PSČ obchodného partnera', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.country', 'Štát obchodného partnera', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amFirstName', 'Obchodný partner - meno manažéra predaja', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amLastName', 'Obchodný partner - priezvisko manažéra predaja', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amEmail', 'Obchodný partner - email manažéra predaja', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amPhone', 'Obchodný partner - telefónne číslo manažéra predaja', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amName', 'Obchodný partner - meno pobočky', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
-----------------------------------------------------------------------
-- target: DeliveryPoint: DELIVERY_POINT_CHECK_RK, DELIVERY_POINT_CHECK_MRK, DELIVERY_POINT_CHECK_ZM, DELIVERY_POINT_CHECK_DMM, DELIVERY_POINT_AM_CHECK_RK, DELIVERY_POINT_AM_CHECK_MRK
-- target: UnitedDeliveryPoint: CUSTOMER_SHARING_OWNER_GRANT, CUSTOMER_SHARING_CONSUMER_GRANT, CUSTOMER_SHARING_OWNER_REVOKE, CUSTOMER_SHARING_CONSUMER_REVOKE, CUSTOMER_SHARING_INVITATION, BUSINESS_PARTNER_PAIRING_SUCCESS
-- for UDP/DP, we will have available: BusinessPartner, Contract, ContractAccount, DeliveryPoint
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.type', 'Typ odberného miesta', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.eic', 'Odberné miesto - eic', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.pod', 'Odberné miesto - pod', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.street', 'Ulica odberného miesta', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.streetNumber', 'Číslo odberného miesta', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.city', 'Obec odberného miesta', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.zipCode', 'PSČ odberného miesta', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.country', 'Štát odberného miesta', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
-- Contract
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contract.effectiveFrom', 'Zmluva - účinnosť od', null, 'TIMESTAMP' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contract.effectiveTo', 'Zmluva - účinnosť do', null, 'TIMESTAMP' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contract.product', 'Produkt zmluvy', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contract.billCycle', 'Zmluva - fakturačný cyklus', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contract.eeTariff', 'Zmluva - eeTariff', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
-- ContractAccount
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.email', 'Email zmluvného účtu', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.street', 'Zmluvný účet - ulica', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.streetNumber', 'Zmluvný účet - číslo', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.city', 'Zmluvný účet - obec', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.zipCode', 'Zmluvný účet - PSČ', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.country', 'Zmluvný účet - štát', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.name', 'Zmluvný účet - názov organizácie', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.lastName', 'Zmluvný účet - priezvisko', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.firstName', 'Zmluvný účet - meno', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.postCountry', 'Zmluvný účet - korešpondenčná adresa - štát', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.postZipCode', 'Zmluvný účet - korešpondenčná adresa -  PSČ', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.postCity', 'Zmluvný účet - korešpondenčná adresa -  mesto', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.postStreetNumber', 'Zmluvný účet - korešpondenčná adresa - popisné číslo', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'contractAccount.postStreet', 'Zmluvný účet - korešpondenčná adresa -  ulica', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
-----------------------------------------------------------------------
-- target: Invoice: CUSTOMER_INVOICE_ISSUED, CUSTOMER_INVOICE_CREDIT_ISSUED, CUSTOMER_INVOICE_ADVANCE_ISSUED, CUSTOMER_INVOICE_SAP_ISSUED, CUSTOMER_INVOICE_BEFORE_DUE, CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.status', 'Status zamestnanca', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.amount', 'Čiastka faktúry', null, 'NUMBER' from notification_template nt where nt.code in ('CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.dueAt', 'Faktúra - splatné do', null, 'TIMESTAMP' from notification_template nt where nt.code in ('CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.issueAt', 'Faktúra - vystavené', null, 'TIMESTAMP' from notification_template nt where nt.code in ('CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.executeAt', 'Faktúra - dátum dodania', null, 'TIMESTAMP' from notification_template nt where nt.code in ('CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.vs', 'Faktúra - vs', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.typeGroup', 'Typ faktúry', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
-----------------------------------------------------------------------
-- target: CustomerRequest: CUSTOMER_REQUEST_STATUS_CHANGE, CUSTOMER_REQUEST_NOTE_CREATE, CUSTOMER_REQUEST_CANCEL_REQUEST
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.externalId', 'SAP ID žiadosti', 'Dostupné iba ak bola žiadosť zaevidovaná v SAPe', 'STRING' from notification_template nt where nt.code in ('CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.name', 'Názov žiadosti', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.status.code', 'Kód stavu žiadosti', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.status.name', 'Stav žiadosti', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST');