-- insert values for CUSTOMER_REQUEST_STATUS
INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CREATED', 'CUSTOMER_REQUEST_STATUS', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk', 'Vytvorená', null, (select uuid from generic_code_list where code like 'CREATED' and type = 'CUSTOMER_REQUEST_STATUS'));

-------------------------------------------------------------------------------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REGISTERED', 'CUSTOMER_REQUEST_STATUS', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'sk', 'Zaregistrovaná', null, (select uuid from generic_code_list where code like 'REGISTERED' and type = 'CUSTOMER_REQUEST_STATUS'));

-------------------------------------------------------------------------------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CANCELLATION', 'CUSTOMER_REQUEST_STATUS', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'sk', 'Prebieha zrušenie', null, (select uuid from generic_code_list where code like 'CANCELLATION' and type = 'CUSTOMER_REQUEST_STATUS'));

-------------------------------------------------------------------------------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CANCELLED', 'CUSTOMER_REQUEST_STATUS', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'sk', 'Zrušená', null, (select uuid from generic_code_list where code like 'CANCELLED' and type = 'CUSTOMER_REQUEST_STATUS'));

-------------------------------------------------------------------------------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CANCELLED_BY_CUSTOMER', 'CUSTOMER_REQUEST_STATUS', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'sk', 'Zrušená zákazníkom', null, (select uuid from generic_code_list where code like 'CANCELLED_BY_CUSTOMER' and type = 'CUSTOMER_REQUEST_STATUS'));

-------------------------------------------------------------------------------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'FINISHED', 'CUSTOMER_REQUEST_STATUS', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'sk', 'Dokončená', null, (select uuid from generic_code_list where code like 'FINISHED' and type = 'CUSTOMER_REQUEST_STATUS'));

-------------------------------------------------------------------------------------------------------------------------

