------------------------
-- customer_request_note
------------------------

CREATE TABLE customer_request_note
(
    uuid                         uuid                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      INTEGER                   NOT NULL,

    external_id                  CHARACTER VARYING(50),
    issue_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    status                       CHARACTER VARYING(50)     NOT NULL,
    text                         TEXT                      NOT NULL,
    customer_request_id          uuid                      NOT NULL,

    CONSTRAINT pk_customer_request_note PRIMARY KEY (uuid),
    CONSTRAINT fk_customer_request_note_customer_request FOREIGN KEY (customer_request_id)
            REFERENCES customer_request (uuid) MATCH SIMPLE
            ON UPDATE NO ACTION
            ON DELETE NO ACTION
);

-- indexes
CREATE INDEX idx_customer_request_note_status on customer_request_note(status);
CREATE INDEX idx_customer_request_note_customer_request_id on customer_request_note(customer_request_id);
