-- AUDIT log code
INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SAP_REGISTRATION_REQUEST', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Registrácia portálového účtu zo SAP', null, (select uuid from generic_code_list where code like 'CUSTOMER_SAP_REGISTRATION_REQUEST' and type = 'AUDIT_LOG_CODE'));

-- AUDIT log code
INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SAP_REGISTRATION_CHALLENGE_CODE_EXPIRED', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Expirácia vygenerovaného tokenu zo SAP registrácie', null, (select uuid from generic_code_list where code like 'SAP_REGISTRATION_CHALLENGE_CODE_EXPIRED' and type = 'AUDIT_LOG_CODE'));


-- CUSTOMER_REGISTARTION_BATCH
INSERT INTO notification_template (
    uuid, created_at, updated_at, version, code, status, type, execution_type, name, priority, description, attributes, template_group, default_email, default_sms, enable_email, enable_sms, enable_portal)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SAP_REGISTRATION_REQUEST', 'ACTIVE', 'CUSTOMER_SYSTEM', 'AUTOMATIC', 'Pre-Regitrácia portálového účtu zp SAP', 'LOW', 'Notifikácia o predregistrácii portálového účtu zo SAP', null, null, true, false, true, false, false);

-- CREATE COMPLETION DONE TEMPLATE I18Ns
INSERT INTO notification_template_i18n (
    uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status, header_url)
VALUES (
    uuid_generate_v4(), now(), now(), 1, NULL,
    '<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"><p>v SPP robíme všetko pre to, aby sme Vám zjednodušili</p><p>to, čo potrebujete vybaviť.</p><p>Dôkazom našej snahy je aj nový zákaznícky portál</p><p>Moje SPP, ktorý Vám ponúka jednoduchý prehľad</p><p>Vašich odberných miest, faktúr, či umožňuje podať</p><p>žiadosť z pohodlia Vášho domova.</p><p>Pozývame Vás — objavte portál Moje SPP jediným</p><p>klikom a ušetrite čas dnes aj nabudúce.</p><p><a href="${portalExternalUrl}/preregistration/password?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}">Objaviť Moje SPP teraz</a></p></@spp.notification_email_template>',
    'Objavte jedným klikom nový zákaznícky portál',
    NULL,
    (SELECT uuid FROM notification_template
        WHERE code = 'CUSTOMER_SAP_REGISTRATION_REQUEST'),
    'SK', 'ACTIVE', NULL), (

    uuid_generate_v4(), now(), now(), 1, NULL,
    '<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"><p>v SPP robíme všetko pre to, aby sme Vám zjednodušili</p><p>to, čo potrebujete vybaviť.</p><p>Dôkazom našej snahy je aj nový zákaznícky portál</p><p>Moje SPP, ktorý Vám ponúka jednoduchý prehľad</p><p>Vašich odberných miest, faktúr, či umožňuje podať</p><p>žiadosť z pohodlia Vášho domova.</p><p>Pozývame Vás — objavte portál Moje SPP jediným</p><p>klikom a ušetrite čas dnes aj nabudúce.</p><p><a href="${portalExternalUrl}/preregistration/password?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}">Objaviť Moje SPP teraz</a></p></@spp.notification_email_template>',
    '[EN] Objavte jedným klikom nový zákaznícky portál',
    NULL,
    (SELECT uuid FROM notification_template
        WHERE code = 'CUSTOMER_SAP_REGISTRATION_REQUEST'),
    'EN', 'ACTIVE', NULL);

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.email', 'Email zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.firstName', 'Meno zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'notificationTemplateCode', 'Kód notifikácie', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácie cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', 'Týka sa iba SAP entít.', 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'locale', 'Lokalizácia', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'attributes.challengeCodeUuid', 'ID verifikačného kódu zaslaného zákazníkovi', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'attributes.challengeCode', 'Verifikačný kód', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'attributes.challengeCodeValidTo', 'Dátum platnosti verifikačného kódu', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_SAP_REGISTRATION_REQUEST');