------------------------------------
-- employee_account
------------------------------------

create table employee_account (
    uuid                           uuid                      NOT NULL,
    created_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                        INTEGER                   NOT NULL,
    email                          CHARACTER VARYING(64)     NOT NULL,
    status                         CHARACTER VARYING(50)     NOT NULL,
    first_name                     CHARACTER VARYING(64)    NOT NULL,
    last_name                      CHARACTER VARYING(64)    NOT NULL,
    registration_at                TIMESTAMP WITH TIME ZONE  NOT NULL,
    constraint pk_employee_account primary key (uuid)
);

create index idx_employee_account_email on employee_account (email);


------------------------------------
-- access_right
------------------------------------

create table access_right (
    code                           CHARACTER VARYING(50)     NOT NULL,
    created_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                        INTEGER                   NOT NULL,
    name                           CHARACTER VARYING(64),
    description                    text,
    constraint pk_account_access_right primary key (code)
);


------------------------------------
-- access_role
------------------------------------

create table access_role (
    code                           CHARACTER VARYING(50)     NOT NULL,
    created_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                        INTEGER                   NOT NULL,
    name                           CHARACTER VARYING(64),
    description                    text,
    constraint pk_employee_account_role primary key (code)
);


------------------------------------
-- access_role_right
------------------------------------

create table access_role_right (
    access_right_code              CHARACTER VARYING(50)     NOT NULL,
    access_role_code               CHARACTER VARYING(50)     NOT NULL,
    constraint pk_access_role_right primary key (access_right_code, access_role_code),
    constraint fk_access_role_right_access_right_code foreign key (access_right_code) references access_right,
    constraint fk_access_role_right_access_role_code foreign key (access_role_code) references access_role
);


------------------------------------
-- employee_account_access
------------------------------------

create table employee_account_access (
    uuid                           uuid                      NOT NULL,
    created_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    access_right_code              CHARACTER VARYING(50)     NOT NULL,
    access_role_code               CHARACTER VARYING(50)     NOT NULL,
    operation                      CHARACTER VARYING(50)     NOT NULL,
    employee_account_id            uuid                      NOT NULL,
    constraint pk_employee_account_access primary key (uuid),
    constraint fk_employee_account_access_employee_account_id foreign key (employee_account_id) references employee_account,
    constraint fk_employee_account_access_access_right_code foreign key (access_right_code) references access_right,
    constraint fk_employee_account_access_access_role_code foreign key (access_role_code) references access_role
);

create index idx_employee_account_access_employee_id_right_role on employee_account_access (employee_account_id, access_right_code, access_role_code);
