
UPDATE generic_code_list SET type = 'DELIVERY_POINT_READING_CYCLE_TYPE' where type ='DELIVERY_POINT_METERING_PERIOD_TYPE';


INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Dekádny', null, (select uuid from generic_code_list where code like 'DEKADNA' and type = 'DELIVERY_POINT_READING_CYCLE_TYPE')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Denný', null, (select uuid from generic_code_list where code like 'DENNA' and type = 'DELIVERY_POINT_READING_CYCLE_TYPE')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', '<PERSON><PERSON><PERSON><PERSON>', null, (select uuid from generic_code_list where code like 'MESACNA' and type = 'DELIVERY_POINT_READING_CYCLE_TYPE')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Ročný', null, (select uuid from generic_code_list where code like 'ROCNA' and type = 'DELIVERY_POINT_READING_CYCLE_TYPE')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Týždenný', null, (select uuid from generic_code_list where code like 'TYZDENNA' and type = 'DELIVERY_POINT_READING_CYCLE_TYPE'));