

insert into template_variable (uuid, created_at, updated_at , "name" , "type" , variable , "version" ) values (uuid_generate_v4(), now(), now(), 'Telefónne číslo z<PERSON>azníka.', 'STRING', 'attributes.verifyPairingPhone', 1);

insert into template_notification_variable (notification_template_uuid , template_variable_uuid )
select nt.uuid as ntId, tv.uuid as tvId from notification_template nt join template_variable tv on (tv.variable = 'attributes.verifyPairingPhone')
where nt.code = 'BUSINESS_PARTNER_PAIRING_VERIFY';

update notification_template_i18n set sms_body = null where notification_template_id = (select uuid from notification_template nt where nt.code = 'BUSINESS_PARTNER_PAIRING_VERIFY');