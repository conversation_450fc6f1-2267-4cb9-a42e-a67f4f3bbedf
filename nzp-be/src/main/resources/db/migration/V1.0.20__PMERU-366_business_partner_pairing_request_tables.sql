-----------------------------------
-- business_partner_pairing_request
-----------------------------------

CREATE TABLE business_partner_pairing_request
(
    uuid                         uuid                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      integer                   NOT NULL,
    
    action                       CHARACTER VARYING(50)     NOT NULL,
    status                       CHARACTER VARYING(50)     NOT NULL,
    retry_count                  integer,
    
    business_partner_id          uuid                      NOT NULL,
    customer_account_id          uuid                      NOT NULL,

    CONSTRAINT pk_bppr PRIMARY KEY (uuid),
    CONSTRAINT fk_bppr_business_partner FOR<PERSON><PERSON><PERSON> KEY (business_partner_id)
        REFERENCES business_partner (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk_bppr_delivery_point FOREIGN KEY (customer_account_id)
        REFERENCES customer_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_bppr_action_status_retry_count on business_partner_pairing_request(action, status, retry_count);
CREATE INDEX idx_bppr_business_partner_id on business_partner_pairing_request(business_partner_id);
CREATE INDEX idx_bppr_customer_account_id on business_partner_pairing_request(customer_account_id);