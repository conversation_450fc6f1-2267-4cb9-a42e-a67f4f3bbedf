----------------------------------------------------------
--- CREATE table: customer_password_history
----------------------------------------------------------

create table customer_password_history (
    uuid                                                uuid                                NOT NULL,
    customer_id                                         uuid                                NOT NULL,
    password                                            varchar(512)                        NOT NULL,
    created_at                                          TIMESTAMP WITH TIME ZONE            NOT NULL,
    constraint pk_customer_password_history             primary key (uuid),
    constraint fk_customer_password_history_customer_id foreign key (customer_id) references public.customer_account
)
WITH (
    OIDS = FALSE
);

create index idx_customer_password_history_created_at on customer_password_history (created_at desc);
