CREATE TABLE component_help
(
    uuid                    uuid                      NOT NULL,
    created_at              TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at              TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                 INTEGER                   NOT NULL,
    screen                  CHARACTER VARYING(100)    NOT NULL,
    field                   CHARACTER VARYING(100)    NOT NULL,
    status                  CHARACTER VARYING(50)     NOT NULL,
    help_order              INTEGER,

    CONSTRAINT pk_component_help PRIMARY KEY (uuid)
);


CREATE TABLE component_help_I18N
(
    uuid                    uuid                      NOT NULL,
    created_at              TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at              TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                 INTEGER                   NOT NULL,
    locale                  CHARACTER VARYING(5)      NOT NULL,
    content                 text                      NOT NULL,
    component_help_id       uuid                      NOT NULL,

    CONSTRAINT pk_component_help_I18N PRIMARY KEY (uuid),

    CONSTRAINT fk_component_help_uuid FOREIGN KEY (component_help_id)
                REFERENCES component_help (uuid) MATCH SIMPLE
                ON UPDATE NO ACTION
                ON DELETE NO ACTION
);