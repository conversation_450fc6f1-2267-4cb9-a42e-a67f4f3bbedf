-- SPPNZP-163 - Change name for customer_request_template ZOP_P + add EN version

INSERT INTO customer_request_template_i18n (
    uuid,
    created_at,
    updated_at,
    version,
    locale,
    name,
    description,
    customer_request_template_uuid
) VALUES
    (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Žiadosť obchodný partner - Podnet', '[EN] Podnet obchodného partnera.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOP_P'));

update customer_request_template_i18n
set name = 'Žiadosť obchodný partner - Podnet', description = 'Podnet obchodného partnera.', version = version + 1, updated_at = now()
where lower(locale) = 'sk' and customer_request_template_uuid = (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOP_P')
