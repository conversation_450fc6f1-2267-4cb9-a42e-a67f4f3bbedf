-- Do not send notifications if account is not active
update notification_template
set active_account_only = true
where code in ('CUSTOMER_INVOICE_BEFORE_DUE', 'METER_READING_CUSTOMER', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'DELIVERY_POINT_AM_CHECK_DMM');
