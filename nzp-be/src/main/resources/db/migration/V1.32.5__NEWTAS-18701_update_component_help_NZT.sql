------------------------------------------------------------------------------------------------------------------------
----------- NEWTAS-18701 - SPP NZP test - Incident 491412
------------------------------------------------------------------------------------------------------------------------

-- Create component_help records if not exist
INSERT INTO component_help (uuid, created_at, updated_at, version, screen, field, status, help_order) VALUES (uuid_generate_v4(), now(), now(), 1, 'NZT', 'NZT_ZOM_RF', 'ACTIVE', 541) ON CONFLICT DO NOTHING;

-- Create component_help_i18n records if not exist
INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id) VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', '<p>Ak potrebujete reklamovať chybu vo faktúre, tak ste tu správne.</p>', (SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOM_RF')) ON CONFLICT DO NOTHING;
INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id) VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '<p>Did you find an error in your billing invoice? File a complaint.</p>', (SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOM_RF')) ON CONFLICT DO NOTHING;

-- Update component_help_i18n records
UPDATE component_help_i18n
SET updated_at = now(), version = version + 1, content = '<p>Ak potrebujete reklamovať chybu vo faktúre, tak ste tu správne.</p>'
WHERE component_help_id = (SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOM_RF') AND lower(locale) = 'sk';
UPDATE component_help_i18n
SET updated_at = now(), version = version + 1, content = '<p>Did you find an error in your billing invoice? File a complaint.</p>'
WHERE component_help_id = (SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOM_RF') AND lower(locale) = 'en';
