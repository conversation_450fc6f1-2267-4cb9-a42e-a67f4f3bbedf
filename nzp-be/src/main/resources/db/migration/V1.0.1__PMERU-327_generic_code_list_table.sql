CREATE TABLE generic_code_list
(
    uuid          uuid                      NOT NULL,
    created_at    TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at    TIMESTAMP WITH TIME ZONE  NOT NULL,
    version       integer                    NOT NULL,
    code          CHARACTER VARYING(255)    NOT NULL,
    type          CHARACTER VARYING(128)    NOT NULL,
    valid_from    TIMESTAMP WITH TIME ZONE,
    valid_to      TIMESTAMP WITH TIME ZONE,
    parent_uuid   uuid,

    CONSTRAINT pk_generic_code_list PRIMARY KEY (uuid),
    CONSTRAINT fk_generic_code_list_parent_uuid FOREIGN KEY (parent_uuid)
        REFERENCES generic_code_list (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_generic_code_list_type_code on generic_code_list(type, code);
CREATE INDEX idx_generic_code_list_parent on generic_code_list(parent_uuid);