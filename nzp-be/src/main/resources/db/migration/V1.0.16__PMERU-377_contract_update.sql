-------------------
-- contract_product 
-------------------

alter table contract_product add column product                  CHARACTER VARYING(512);
alter table contract_product add column tariff                   CHARACTER VARYING(512);

alter table contract_product add column bill_cycle               CHARACTER VARYING(50)           NOT NULL;
alter table contract_product add column deal_amount              NUMERIC(19,2);
alter table contract_product add column maximum_daily_amount     NUMERIC(19,2);
alter table contract_product add column reserve_amount           NUMERIC(19,2);
alter table contract_product add column maximum_reserve_amount   NUMERIC(19,2);
alter table contract_product add column reading_period_type      CHARACTER VARYING(50);

alter table contract_product add column delivery_point_id        uuid;



-----------
-- contract
-----------

alter table contract DROP column bill_cycle;
alter table contract DROP column deal_amount;
alter table contract DROP column maximum_daily_amount;
alter table contract DROP column reserve_amount;
alter table contract DROP column maximum_reserve_amount;
alter table contract DROP column reading_period_type;