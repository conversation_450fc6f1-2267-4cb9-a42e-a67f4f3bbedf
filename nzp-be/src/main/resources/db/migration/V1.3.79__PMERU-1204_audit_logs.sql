

alter table audit_log add column logged_customer_account_ft text;
alter table audit_log add column logged_customer_account_name text;
alter table audit_log add column related_customer_account_ft text;
alter table audit_log add column related_customer_account_name text;
alter table audit_log add column employee_name text;
alter table audit_log add column employee_ft text;
alter table audit_log add column business_partner_ft text;
alter table audit_log add column business_partner_name text;
alter table audit_log add column entity_item text;

update audit_log SET logged_customer_account_name = concat(logged_customer_account_first_name,' ', logged_customer_account_last_name);
update audit_log SET related_customer_account_name = concat(related_customer_account_first_name,' ', related_customer_account_last_name);
update audit_log SET employee_name = concat(employee_first_name,' ', employee_last_name);

-- cant remove accent without unaccent extension
update audit_log SET logged_customer_account_ft = concat(logged_customer_account_last_name,' ', logged_customer_account_first_name);
update audit_log SET related_customer_account_ft = concat(related_customer_account_last_name,' ', related_customer_account_first_name);
update audit_log SET employee_ft = concat(employee_last_name,' ', employee_first_name);


alter table audit_log drop column logged_customer_account_first_name;
alter table audit_log drop column logged_customer_account_last_name;
alter table audit_log drop column related_customer_account_first_name;
alter table audit_log drop column related_customer_account_last_name;
alter table audit_log drop column employee_last_name;
alter table audit_log drop column employee_first_name;
alter table audit_log drop column entity_ft;
alter table audit_log drop column entity_name;

CREATE INDEX idx_audit_log_logged_customer_ft on audit_log(logged_customer_account_ft);
CREATE INDEX idx_audit_log_related_customer_ft on audit_log(related_customer_account_ft);
CREATE INDEX idx_audit_log_employee_ft on audit_log(employee_ft);
CREATE INDEX idx_audit_log_business_partner_ft on audit_log(business_partner_ft);