-----------------------------------------------------------------------------------------------
---------------- ADD AuditLogCodes - unsuccessful login
-----------------------------------------------------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_LOGIN_UNSUCCESS_NOT_ACTIVE', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Prihlasovanie: Neaktívny zákaznícky účet', 'Prihlasovanie: Neaktívny zákaznícky účet', (select uuid from generic_code_list where code like 'CUSTOMER_LOGIN_UNSUCCESS_NOT_ACTIVE' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Prihlasovanie: Neaktívny zákaznícky účet', '[EN] Prihlasovanie: Neaktívny zákaznícky účet', (select uuid from generic_code_list where code like 'CUSTOMER_LOGIN_UNSUCCESS_NOT_ACTIVE' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_LOGIN_UNSUCCESS_BLOCKED', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Prihlasovanie: Blokovaný zákaznícky účet', 'Prihlasovanie: Blokovaný zákaznícky účet', (select uuid from generic_code_list where code like 'CUSTOMER_LOGIN_UNSUCCESS_BLOCKED' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Prihlasovanie: Blokovaný zákaznícky účet', '[EN] Prihlasovanie: Blokovaný zákaznícky účet', (select uuid from generic_code_list where code like 'CUSTOMER_LOGIN_UNSUCCESS_BLOCKED' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EMPLOYEE_LOGIN_UNSUCCESS_MISSING_RIGHTS', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Prihlasovanie: Chýbajúce prístupové práva zamestnanca', 'Prihlasovanie: Chýbajúce prístupové práva', (select uuid from generic_code_list where code like 'EMPLOYEE_LOGIN_UNSUCCESS_MISSING_RIGHTS' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Prihlasovanie: Chýbajúce prístupové práva zamestnanca', '[EN] Prihlasovanie: Chýbajúce prístupové práva', (select uuid from generic_code_list where code like 'EMPLOYEE_LOGIN_UNSUCCESS_MISSING_RIGHTS' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EMPLOYEE_LOGIN_UNSUCCESS_NOT_ACTIVE', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Prihlasovanie: Neaktívny zamestnanecký účet', 'Prihlasovanie: Neaktívny zamestnanecký účet', (select uuid from generic_code_list where code like 'EMPLOYEE_LOGIN_UNSUCCESS_NOT_ACTIVE' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Prihlasovanie: Neaktívny zamestnanecký účet', '[EN] Prihlasovanie: Neaktívny zamestnanecký účet', (select uuid from generic_code_list where code like 'EMPLOYEE_LOGIN_UNSUCCESS_NOT_ACTIVE' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EMPLOYEE_LOGIN_UNSUCCESS_EXPIRED', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Prihlasovanie: Expirovaný zamestnanecký účet', 'Prihlasovanie: Expirovaný zamestnanecký účet', (select uuid from generic_code_list where code like 'EMPLOYEE_LOGIN_UNSUCCESS_EXPIRED' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Prihlasovanie: Expirovaný zamestnanecký účet', '[EN] Prihlasovanie: Expirovaný zamestnanecký účet', (select uuid from generic_code_list where code like 'EMPLOYEE_LOGIN_UNSUCCESS_EXPIRED' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EMPLOYEE_LOGIN_UNSUCCESS_BLOCKED', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Prihlasovanie: Blokovaný zamestnanecký účet', 'Prihlasovanie: Blokovaný zamestnanecký účet', (select uuid from generic_code_list where code like 'EMPLOYEE_LOGIN_UNSUCCESS_BLOCKED' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Prihlasovanie: Blokovaný zamestnanecký účet', '[EN] Prihlasovanie: Blokovaný zamestnanecký účet', (select uuid from generic_code_list where code like 'EMPLOYEE_LOGIN_UNSUCCESS_BLOCKED' and type = 'AUDIT_LOG_CODE'));
