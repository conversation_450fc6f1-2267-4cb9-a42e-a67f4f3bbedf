-------------------------
-- delivery_point_address
-------------------------

CREATE TABLE delivery_point_address
(
    uuid                     uuid                      NOT NULL,
    created_at               TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at               TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                  integer                   NOT NULL,
    
    external_id              CHARACTER VARYING(50)     NOT NULL,
    street                   CHARACTER VARYING(64),
    number                   CHARACTER VARYING(32),
    city                     CHARACTER VARYING(64),
    zip_code                  CHARACTER VARYING(32),
    country                  CHARACTER VARYING(64),

    CONSTRAINT pk_delivery_point_address PRIMARY KEY (uuid)
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_delivery_point_address_external_id on delivery_point_address(external_id);



-----------------
-- delivery_point
-----------------

CREATE TABLE delivery_point
(
    uuid                         uuid                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      integer                   NOT NULL,
    
    external_id                  CHARACTER VARYING(50)     NOT NULL,
    status                       CHARACTER VARYING(20)     NOT NULL,
    type                         CHARACTER VARYING(50)     NOT NULL,
    metering                     CHARACTER VARYING(50),
    
    pod                          CHARACTER VARYING(50),
    
    eic                          CHARACTER VARYING(50),
    circuit_breaker              boolean,
    circuit_phase_count          NUMERIC(1),
    
    delivery_point_address_id    uuid                      NOT NULL,

    CONSTRAINT pk_delivery_point PRIMARY KEY (uuid),
    CONSTRAINT fk_delivery_point_delivery_point_address FOREIGN KEY (delivery_point_address_id)
        REFERENCES delivery_point_address (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_delivery_point_external_id on delivery_point(external_id);
CREATE INDEX idx_delivery_point_status_type on delivery_point(status, type);
CREATE INDEX idx_delivery_point_pod on delivery_point(pod);
CREATE INDEX idx_delivery_point_eic on delivery_point(eic);



------------------------
-- delivery_point_metric
------------------------

CREATE TABLE delivery_point_metric
(
    uuid                     uuid                      NOT NULL,
    created_at               TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at               TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                  integer                   NOT NULL,
    
    type                     CHARACTER VARYING(50)     NOT NULL,
    value                    NUMERIC(19,2)             NOT NULL,
    
    delivery_point_id        uuid                      NOT NULL,

    CONSTRAINT pk_delivery_point_metric PRIMARY KEY (uuid),
    CONSTRAINT fk_delivery_point_metric_delivery_point FOREIGN KEY (delivery_point_id)
        REFERENCES delivery_point (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_delivery_point_metric_delivery_point_id on delivery_point_metric(delivery_point_id);