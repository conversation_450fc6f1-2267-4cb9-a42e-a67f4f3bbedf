UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='DPI Plyn' WHERE product_uuid = (select uuid from product where code = 'DPI_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixácia - samostatný hedging - Plyn' WHERE product_uuid = (select uuid from product where code = 'FIX_HEDG_IO_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 12 Plyn' WHERE product_uuid = (select uuid from product where code = 'FIX_12_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 24 EWI Plyn' WHERE product_uuid = (select uuid from product where code = 'FIX_24_EWI_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 24 Plyn' WHERE product_uuid = (select uuid from product where code = 'FIX_24_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 24 REA Plyn' WHERE product_uuid = (select uuid from product where code = 'FIX_24_REA_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 24 RTC Plyn' WHERE product_uuid = (select uuid from product where code = 'FIX_24_RTC_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 24 SP Plyn' WHERE product_uuid = (select uuid from product where code = 'FIX_24_SP_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Indexovaná cena Plyn' WHERE product_uuid = (select uuid from product where code = 'INDEX_IO_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Kombi A24 EWI Plyn' WHERE product_uuid = (select uuid from product where code = 'KOMBI_A24_EWI_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Kombi A24 RTC Plyn' WHERE product_uuid = (select uuid from product where code = 'KOMBI_A24_RTC_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Kombi A24 SP Plyn' WHERE product_uuid = (select uuid from product where code = 'KOMBI_A24_SP_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Kombi B24 RTC Plyn' WHERE product_uuid = (select uuid from product where code = 'KOMBI_B24_RTC_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Kombi C24 RTC Plyn' WHERE product_uuid = (select uuid from product where code = 'KOMBI_C24_RTC_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='M3/M4 Plyn' WHERE product_uuid = (select uuid from product where code = 'M3_M4_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Regulovaný cenník Plyn' WHERE product_uuid = (select uuid from product where code = 'REGUL_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Štandardný cenník Plyn' WHERE product_uuid = (select uuid from product where code = 'STANDARD_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Supervýhodne 12 RTC Plyn' WHERE product_uuid = (select uuid from product where code = 'SUPER_12_RTC_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Supervýhodne 24 EWI Plyn' WHERE product_uuid = (select uuid from product where code = 'SUPER_24_EWI_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Supervýhodne 24 RTC Plyn' WHERE product_uuid = (select uuid from product where code = 'SUPER_24_RTC_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='SV IO P' WHERE product_uuid = (select uuid from product where code = 'SV_IO_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Plyn Výhodne+' WHERE product_uuid = (select uuid from product where code = 'VYHODNE_PLUS_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Výhodne 12 Plyn' WHERE product_uuid = (select uuid from product where code = 'VYHODNE_12_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Výhodne 24 Plyn' WHERE product_uuid = (select uuid from product where code = 'VYHODNE_24_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Cenový profil Elektrina' WHERE product_uuid = (select uuid from product where code = 'CENPROF_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Elektrina Verným' WHERE product_uuid = (select uuid from product where code = 'ELE_VERNYM_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixácia – samostatný hedging Elektrina' WHERE product_uuid = (select uuid from product where code = 'FIX_HEDG_IO_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 24 Elektrina' WHERE product_uuid = (select uuid from product where code = 'FIX_24_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Nefixný cenník MPO Elektrina' WHERE product_uuid = (select uuid from product where code = 'MPO_') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Štandardný cenník Elektrina' WHERE product_uuid = (select uuid from product where code = 'STANDARD_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='SV IO EE' WHERE product_uuid = (select uuid from product where code = 'SV_IO_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Tarifný profil Elektrina' WHERE product_uuid = (select uuid from product where code = 'TARIFPROF_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='VIP Elektrina' WHERE product_uuid = (select uuid from product where code = 'VIP_E') and LOWER(locale) = 'sk';
