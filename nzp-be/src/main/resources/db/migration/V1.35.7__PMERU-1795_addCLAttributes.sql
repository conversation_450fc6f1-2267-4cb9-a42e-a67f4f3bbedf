UPDATE generic_code_list SET updated_at = now(), version = (version + 1), attributes = '{"months":"1"}' WHERE type = 'ADVANCE_PAYMENT_PERIOD' AND code = 'MONTHLY';
UPDATE generic_code_list SET updated_at = now(), version = (version + 1), attributes = '{"months":"3"}' WHERE type = 'ADVANCE_PAYMENT_PERIOD' AND code = 'QUARTERLY';
UPDATE generic_code_list SET updated_at = now(), version = (version + 1), attributes = '{"months":"6"}' WHERE type = 'ADVANCE_PAYMENT_PERIOD' AND code = 'HALF_YEARLY';
UPDATE generic_code_list SET updated_at = now(), version = (version + 1), attributes = '{"months":"12"}' WHERE type = 'ADVANCE_PAYMENT_PERIOD' AND code = 'YEARLY';

UPDATE generic_code_list SET updated_at = now(), version = (version + 1), attributes = '{"months":"0"}' WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '00';
UPDATE generic_code_list SET updated_at = now(), version = (version + 1), attributes = '{"months":"1"}' WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '01';
UPDATE generic_code_list SET updated_at = now(), version = (version + 1), attributes = '{"months":"2"}' WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '02';
UPDATE generic_code_list SET updated_at = now(), version = (version + 1), attributes = '{"months":"3"}' WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '03';
UPDATE generic_code_list SET updated_at = now(), version = (version + 1), attributes = '{"months":"4"}' WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '04';
UPDATE generic_code_list SET updated_at = now(), version = (version + 1), attributes = '{"months":"6"}' WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '06';
UPDATE generic_code_list SET updated_at = now(), version = (version + 1), attributes = '{"months":"12"}' WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '12';
