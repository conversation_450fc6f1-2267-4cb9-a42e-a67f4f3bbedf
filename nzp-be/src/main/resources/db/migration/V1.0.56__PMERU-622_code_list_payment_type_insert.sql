-- insert values for payment_type
INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'F', 'PAYMENT_TYPE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Bankové inkaso z inej banky', null, (select uuid from generic_code_list where code like 'F' and type = 'PAYMENT_TYPE'));


INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'I', 'PAYMENT_TYPE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Bankové inkaso', null, (select uuid from generic_code_list where code like 'I' and type = 'PAYMENT_TYPE'));


INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'P', 'PAYMENT_TYPE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Prevodný príkaz - príjem', null, (select uuid from generic_code_list where code like 'P' and type = 'PAYMENT_TYPE'));


INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'S', 'PAYMENT_TYPE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'SIPO - inkaso', null, (select uuid from generic_code_list where code like 'S' and type = 'PAYMENT_TYPE'));


INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'U', 'PAYMENT_TYPE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Poštová poukážka', null, (select uuid from generic_code_list where code like 'U' and type = 'PAYMENT_TYPE'));
