update generic_code_list
	set "attributes" = '{"default":"true", "order":"1"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'CREATED_AT';

update generic_code_list
	set "attributes" = '{"default":"true", "order":"2"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'CODE';

update generic_code_list
	set "attributes" = '{"default":"false", "order":"3"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'ENTITY_TYPE';

update generic_code_list
	set "attributes" = '{"default":"false", "order":"4"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'ENTITY_ITEM';

update generic_code_list
	set "attributes" = '{"default":"false", "order":"5"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'ENTITY_REFERENCE';

update generic_code_list
	set "attributes" = '{"default":"false", "order":"6"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'EMPLOYEE_LOGIN';

update generic_code_list
	set "attributes" = '{"default":"false", "order":"7"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'EMPLOYEE_NAME';

update generic_code_list
	set "attributes" = '{"default":"false", "order":"8"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'EMPLOYEE_EMAIL';

update generic_code_list
	set "attributes" = '{"default":"false", "order":"9"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'EMPLOYEE_PRESENT';

update generic_code_list
	set "attributes" = '{"default":"true", "order":"10"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'BUSINESS_PARTNER_NAME';

update generic_code_list
	set "attributes" = '{"default":"true", "order":"11"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'BUSINESS_PARTNER_EXTERNAL_ID';

update generic_code_list
	set "attributes" = '{"default":"true", "order":"12"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'RELATED_CUSTOMER_NAME';

update generic_code_list
	set "attributes" = '{"default":"true", "order":"13"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'RELATED_CUSTOMER_EMAIL';

update generic_code_list
	set "attributes" = '{"default":"true", "order":"14"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'LOGGED_CUSTOMER_NAME';

update generic_code_list
	set "attributes" = '{"default":"true", "order":"15"}'
	where "type" = 'AUDIT_LOG_EXPORT_COLUMN' and code = 'LOGGED_CUSTOMER_EMAIL';