INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
    VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REGISTRATION_CLEAR_PASSWORD_AND_HISTORY', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
    VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Vymazanie hesla a histórie hesiel zákazníka', null, (select uuid from generic_code_list where code like 'CUSTOMER_REGISTRATION_CLEAR_PASSWORD_AND_HISTORY' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
    VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[E<PERSON>] Vymazanie hesla a histórie hesiel zákazn<PERSON>a', null, (select uuid from generic_code_list where code like 'CUSTOMER_REGISTRATION_CLEAR_PASSWORD_AND_HISTORY' and type = 'AUDIT_LOG_CODE'));
