
alter table invoice drop column delivery_point_id;
alter table invoice drop column execute_at;

alter table invoice add column contract_account_id text;
CREATE INDEX idx_invoice_contract_account_id on invoice(contract_account_id);
CREATE INDEX idx_invoice_payment_req_invoice_id on invoice(payment_req_invoice_id);

CREATE TABLE invoice_delivery_point
(
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    invoice_id                   text                      NOT NULL,
    delivery_point_id            text                      NOT NULL,

    CONSTRAINT pk_invoice_delivery_point PRIMARY KEY (invoice_id, delivery_point_id)
)
WITH (
    OIDS = FALSE
);

CREATE INDEX idx_invoice_delivery_point_invoice_id on invoice_delivery_point(invoice_id);
CREATE INDEX idx_invoice_delivery_point_delivery_point_id on invoice_delivery_point(delivery_point_id);


CREATE TABLE invoice_raw
(
    invoice_id                   text                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      INTEGER                   NOT NULL,

    item1                        INTEGER                   NOT NULL,
    item2                        INTEGER                   NOT NULL,
    item3                        INTEGER                   NOT NULL,
    gcl_type_uuid                UUID,
    amount                       NUMERIC(19,2),
    execute_at                   DATE,
    status                       CHARACTER VARYING(50)     NOT NULL,
    payment_req_invoice_id       TEXT,
    delivery_point_id            TEXT,
    contract_id                  TEXT,
    repayment_plan_id            TEXT,
    contract_account_id          TEXT,
    payment_id                   TEXT,
    payment_req_notif_sent       boolean,
    file_archive_id              TEXT,
    due_at                       DATE,
    issue_at                     DATE,
    vs                           TEXT,
    synchronization_log_uuid     UUID,
    synchronization_at           TIMESTAMP WITH TIME ZONE  NOT NULL,

    CONSTRAINT pk_invoice_raw PRIMARY KEY (invoice_id, item1, item2, item3),
    CONSTRAINT fk_invoice_raw_synchronization_log FOREIGN KEY (synchronization_log_uuid)
        REFERENCES synchronization_log (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk_invoice_raw_gcl_type_uuid FOREIGN KEY (gcl_type_uuid)
        REFERENCES generic_code_list (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

CREATE INDEX idx_invoice_raw_synchronization_log_uuid on invoice_raw(synchronization_log_uuid);
CREATE INDEX idx_invoice_raw_repayment_plan_id on invoice_raw(repayment_plan_id);



DROP INDEX unique_idx_payment_external_id;
alter table payment DROP CONSTRAINT pk_payment;
alter table payment ADD  CONSTRAINT pk_payment PRIMARY KEY (id, invoice_id);