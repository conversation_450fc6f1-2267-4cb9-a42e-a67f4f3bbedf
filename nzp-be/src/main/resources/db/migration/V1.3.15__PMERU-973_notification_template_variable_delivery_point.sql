-- deliveryPoint.tariffRate and category variables
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.tariffRate', '<PERSON><PERSON><PERSON>/sadzba na odbernom mieste', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.category', 'Kategória odberného miesta', null, 'STRING' from notification_template nt where nt.code in ('CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_SHARING_INVITATION', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_SAP_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE');