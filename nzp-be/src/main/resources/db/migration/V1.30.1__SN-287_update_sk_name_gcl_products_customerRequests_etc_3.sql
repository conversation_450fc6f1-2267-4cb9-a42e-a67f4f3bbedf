------------------------------------------------------------------------------------------------------------------------
-------- SN-287: 19. Spotreba - v tabulke odpoctov suffix " - SAP" dat prec
------------------------------------------------------------------------------------------------------------------------

-- Update tariff descriptions (SK)

UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Dvojpásmová sadzba so strednou spotrebou elektriny - doba platnosti nízkej tarify (NT) je 8 hodín denne (pre strednú spotrebu vo vysokej tarife (VT)).' WHERE tariff_uuid = (select uuid from tariff where code = 'C5') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Dvojpásmová sadzba s vyššou spotrebou elektriny - doba platnosti nízkej tarify (NT) je 8 hodín denne (pre vyššiu spotrebu vo vysokej tarife (VT)).' WHERE tariff_uuid = (select uuid from tariff where code = 'C6') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Dvojpásmová sadzba - doba platnosti nízkej tarify (NT) je 20 hodín denne (priamo výhrevné elektrické spotrebiče sú blokované v čase platnosti vysokej tarify (VT)).' WHERE tariff_uuid = (select uuid from tariff where code = 'C7') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Dvojpásmová sadzba pre tepelné čerpadlo - doba platnosti nízkej tarify (NT) je 20 hodín denne (výhrevné elektrické spotrebiče sú blokované v čase platnosti vysokej tarify (VT))' WHERE tariff_uuid = (select uuid from tariff where code = 'C8') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='DD1 je sadzba vhodná pre bežnú spotrebu v menšej domácnosti. Meranie spotreby elektriny nie je rozdelené do pásiem.' WHERE tariff_uuid = (select uuid from tariff where code = 'DD1') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='DD2 je sadzba vhodná pre bežnú spotrebu vo väčšej domácnosti. Meranie spotreby elektriny nie je rozdelené do pásiem.' WHERE tariff_uuid = (select uuid from tariff where code = 'DD2') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='DD3 je sadzba vhodná u odberateľov, ktorí dokážu spotrebovať podstatnú časť elektriny v čase nízkej tarify (NT), napríklad na akumulačný ohrev vody. NT sa poskytuje minimálne 8 hodín denne s fixne určeným časom prevádzky v pásme NT v nepretržitom trvaní aspoň tri hodiny, pričom blokovanie elektrických spotrebičov sa nevyžaduje.' WHERE tariff_uuid = (select uuid from tariff where code = 'DD3') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='DD4 je sadzba vhodná na akumulačné vykurovanie. Nízka tarifa (NT) sa poskytuje minimálne 8 hodín denne s blokovaním akumulačných elektrických spotrebičov počas doby platnosti vysokej tarify (VT).' WHERE tariff_uuid = (select uuid from tariff where code = 'DD4') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='DD5 je sadzba určená pre priamovýhrevné vykurovanie. Sadzbu možno prideliť pre plne elektrifikované odberné miesto s pevne inštalovanými elektrickými priamovýhrevnými spotrebičmi do ich technického blokovania samostatným elektrickým obvodom počas platnosti vysokej tarify (VT). Nízka tarifa (NT) sa poskytuje zabezpečením ich technického blokovania samostatným elektrickým obvodom počas platnosti VT. NT sa poskytuje minimálne 20 hodín.' WHERE tariff_uuid = (select uuid from tariff where code = 'DD5') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='DD6 je sadzba určená pre odberné miesta, kde sa na vykurovanie a prípravu teplej úžitkovej vody využíva tepelné čerpadlo. Nízka tarifa (NT) sa poskytuje minimálne 20 hodín denne s blokovaním elektrických spotrebičov na vykurovanie počas doby platnosti vysokej tarify (VT).' WHERE tariff_uuid = (select uuid from tariff where code = 'DD6') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='DD7 je sadzba určená pre odberné miesta s prevažujúcou spotrebou počas víkendov. Nízka tarifa (NT) sa poskytuje celoročne od piatka od 15.00 hod. do pondelka do 6.00 hod.' WHERE tariff_uuid = (select uuid from tariff where code = 'DD7') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='DD8 je sadzba určená pre akumulačné vykurovanie. Nízka tarifa (NT) sa poskytuje minimálne 8 hodín denne s blokovaním akumulačných elektrických spotrebičov počas doby platnosti vysokej tarify (VT) s určeným minimálnym inštalovaným výkonom akumulačných spotrebičov.' WHERE tariff_uuid = (select uuid from tariff where code = 'DD8') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa D1 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 0 (vrátane) do 2 138 kWh (vrátane) - približne od 0 (vrátane) do 200 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'D1') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa D2 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 2 138 kWh do 18 173 kWh (vrátane) - približne od 200 m3 do 1 700 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'D2') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa D3 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 18 173 kWh do 42 760 kWh (vrátane) - približne od 1 700 m3 do 4 000 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'D3') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa D4 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 42 760 kWh do 69 485 kWh (vrátane) - približne od 4 000 m3 do 6 500 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'D4') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa D5 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 69 485 kWh do 85 000 kWh (vrátane) - približne od 6 500 m3 do 7 951m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'D5') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa D6 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 85 000 kWh do 100 000 kWh (vrátane) - približne od 7 951 m3 do 9 355m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'D6') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa D7 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 100 000 kWh do 300 000 kWh (vrátane) - približne od 9 355 m3 do 28 064 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'D7') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa D8 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 300 000 kWh do 641 400 kWh (vrátane) - približne od 28 064 m3 do 60 000 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'D8') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa M1 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 0 (vrátane) do 2 138 kWh (vrátane) - približne od 0 (vrátane) do 200 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'M1') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa M2 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 2 138 kWh do 18 173 kWh (vrátane) - približne od 200 m3 do 1 700 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'M2') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa M3 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 18 173 kWh do 42 760 kWh (vrátane) - približne od 1 700 m3 do 4 000 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'M3') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa M4 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 42 760 kWh do 69 485 kWh (vrátane) - približne od 4 000 m3 do 6 500 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'M4') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa M5 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 69 485 kWh do 85 000 kWh (vrátane) - približne od 6 500 m3 do 7 951m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'M5') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa M6 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 85 000 kWh do 100 000 kWh (vrátane) - približne od 7 951 m3 do 9 355m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'M6') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa M7 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 100 000 kWh do 300 000 kWh (vrátane) - približne od 9 355 m3 do 28 064 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'M7') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa M8 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 300 000 kWh do 641 400 kWh (vrátane) - približne od 28 064 m3 do 60 000 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'M8') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa S10 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 2 000 do 4 000 MWh (vrátane) - približne od 187 091 do 374 181 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'S10') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa S9 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 641 do 2 000 MWh (vrátane) - približne od 60 000 do 187 091 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'S9') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V11 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 4 000 do 8 000 MWh (vrátane) - približne od 374 091 do 748 363 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V11') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V12 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 8 000 do 14 000 MWh (vrátane) - približne od 748 363 do 1 309 635 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V12') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V13 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 14 000 do 22 000 MWh (vrátane) - približne od 1 309 635 do 2 057 992 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V13') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V14 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 22 000 do 50 000 MWh (vrátane) - približne od 2 057 992 do 4 677 268 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V14') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V15 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 50 000 do 100 000 MWh (vrátane) - približne od 4 677 268 do 9 354 537 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V15') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V16 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 100 000 do 250 000 MWh (vrátane) - približne od 9 354 537 do 23 386 342 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V16') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V17 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 250 000 do 1 000 000 MWh (vrátane) - približne od 23 386 342 do 93 545 370 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V17') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V18 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 1 000 000 do 1 600 000 MWh (vrátane) - približne od 93 545 370 do 149 672 591 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V18') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V19 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 1 000 000 do 1 600 000 MWh (vrátane) - približne od 149 672 591 do 196 445 276 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V19') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V20 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 1 600 000 do 2 100 000 MWh (vrátane) - približne od 196 445 276 do 252 572 498 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V20') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V21 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 2 100 000 do 2 700 000 MWh (vrátane) - približne od 252 572 498 do 299 345 182 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V21') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V22 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 3 200 000 do 3 750 000 MWh (vrátane) - približne od 299 345 182 do 350 795 136 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V22') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V23 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 3 750 000 do 4 280 000 MWh (vrátane) - približne od 350 795 136 do 400 374 181 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V23') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V24 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 4 280 000 do 4 810 000 MWh (vrátane) - približne od 400 374 181 do 499 953 227 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V24') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V25 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 4 810 000 do 5 345 000 MWh (vrátane) - približne od 499 953 227 do 500 000 000 m3 (vrátane).' WHERE tariff_uuid = (select uuid from tariff where code = 'V25') and LOWER(locale) = 'sk';
UPDATE tariff_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "description"='Tarifa V26 je odporúčaná pre odberné miesta, ktorých predpokladaný odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov bude v rozmedzí od 5 345 000 MWh - približne od 500 000 000 m3).' WHERE tariff_uuid = (select uuid from tariff where code = 'V26') and LOWER(locale) = 'sk';
