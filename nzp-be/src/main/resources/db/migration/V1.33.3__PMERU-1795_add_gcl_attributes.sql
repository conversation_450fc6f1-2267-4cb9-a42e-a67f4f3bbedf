-- Add attributes to GCL
ALTER TABLE generic_code_list           ADD COLUMN attributes text;
ALTER TABLE generic_code_list_i18n      ADD COLUMN attributes text;

-- Disable CARBON_STOP_PLEVEL_EE | EE2
UPDATE generic_code_list
SET updated_at = now(), version = (version + 1), valid_to = now()
WHERE type = 'CARBON_STOP_PLEVEL_EE'
AND code = 'EE2';

-- Add pricing to CARBON_STOP_PLEVEL*
UPDATE generic_code_list
SET updated_at = now(), version = (version + 1), attributes = '{"price":"1.6"}'
WHERE type = 'CARBON_STOP_PLEVEL_EE'
AND code = 'EE3';

UPDATE generic_code_list
SET updated_at = now(), version = (version + 1), attributes = '{"price":"4.10"}'
WHERE type = 'CARBON_STOP_PLEVEL_ZP'
AND code = 'ZP2';

UPDATE generic_code_list
SET updated_at = now(), version = (version + 1), attributes = '{"price":"11.30"}'
WHERE type = 'CARBON_STOP_PLEVEL_ZP'
AND code = 'ZP3';

-- Add description to CARBON_STOP_PLEVEL* i18ns

UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), description = 'Štartovací balík rád, Videorady na mieru, Šetrime ďalej online, Personalizované online poradenstvo, výsadbu 2 stromov'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CARBON_STOP_PLEVEL_EE' AND code = 'EE3')
AND LOWER(locale) = 'sk';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), description = 'Starter advice package, Custom video advice, We save further online, Personalized online advice, planting of 2 trees'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CARBON_STOP_PLEVEL_EE' AND code = 'EE3')
AND LOWER(locale) = 'en';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), description = 'Štartovací balík rád, Videorady na mieru, Šetrime ďalej online, Personalizované online poradenstvo, výsadbu 5 stromov'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CARBON_STOP_PLEVEL_ZP' AND code = 'ZP2')
AND LOWER(locale) = 'sk';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), description = 'Starter advice package, Custom video advice, We save further online, Personalized online advice, planting of 5 trees'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CARBON_STOP_PLEVEL_ZP' AND code = 'ZP2')
AND LOWER(locale) = 'en';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), description = 'Štartovací balík rád, Videorady na mieru, Šetrime ďalej online, Personalizované online poradenstvo, výsadbu 13 stromov'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CARBON_STOP_PLEVEL_ZP' AND code = 'ZP3')
AND LOWER(locale) = 'sk';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), description = 'Starter advice package, Custom video advice, We save further online, Personalized online advice, planting of 13 trees'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CARBON_STOP_PLEVEL_ZP' AND code = 'ZP3')
AND LOWER(locale) = 'en';