INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_METER_READING_INFO', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', '<PERSON><PERSON><PERSON>ý môj odpočet', '<PERSON><PERSON><PERSON><PERSON> môj odpočet', (select uuid from generic_code_list where code like 'DELIVERY_POINT_METER_READING_INFO' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[E<PERSON>] <PERSON>adaný môj odpočet', '[E<PERSON>] <PERSON>adaný môj odpočet', (select uuid from generic_code_list where code like 'DELIVERY_POINT_METER_READING_INFO' and type = 'AUDIT_LOG_CODE'));