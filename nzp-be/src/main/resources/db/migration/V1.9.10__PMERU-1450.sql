
ALTER TABLE delivery_point_version ADD COLUMN reading_cycle_type text ;
update delivery_point_version dpv set reading_cycle_type = gcl.code from generic_code_list gcl where dpv.gcl_reading_cycle_type_uuid is not null and gcl.uuid = dpv.gcl_reading_cycle_type_uuid;
ALTER TABLE delivery_point_version DROP COLUMN gcl_reading_cycle_type_uuid;
CREATE INDEX idx_delivery_point_version_reading_cycle_type ON delivery_point_version(reading_cycle_type);

ALTER TABLE contract_version ADD COLUMN bill_cycle text ;
update contract_version cv set bill_cycle = gcl.code from generic_code_list gcl where cv.gcl_bill_cycle_uuid is not null and gcl.uuid = cv.gcl_bill_cycle_uuid;
ALTER TABLE contract_version DROP COLUMN gcl_bill_cycle_uuid;
CREATE INDEX idx_contract_version_bill_cycle ON contract_version(bill_cycle);

update contract_version
set ee_tariff_count = case when ee_tariff_count = 'T1' then '1T' when ee_tariff_count = 'T2' then '2T' else ee_tariff_count end
where ee_tariff_count  is not null;
