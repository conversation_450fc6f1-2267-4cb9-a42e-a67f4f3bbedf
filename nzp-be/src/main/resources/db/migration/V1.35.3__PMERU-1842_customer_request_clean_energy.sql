-- ZOM_CE CUSTOMER REQUEST TEMPLATE
INSERT INTO customer_request_template(
	uuid, created_at, updated_at, version, status, code, price, type, link, confirmation_required, confirmation_valid_days)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_CE', 2.0, 'DIGITAL', null, true, 50);

-- ZOM_CE CUSTOMER REQUEST TEMPLATE I18N
INSERT INTO customer_request_template_i18n (
    uuid, created_at, updated_at, version, locale, name, description, customer_request_template_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Čistá elektrina', 'Žiadosť o dodávku čistej elektriny.',
        (SELECT uuid FROM customer_request_template crt WHERE crt.code = 'ZOM_CE'));

INSERT INTO customer_request_template_i18n (
    uuid, created_at, updated_at, version, locale, name, description, customer_request_template_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Žiadosť odberné miesto - Čistá elektrina', '[EN] Žiadosť o dodávku čistej elektriny.',
        (SELECT uuid FROM customer_request_template crt WHERE crt.code = 'ZOM_CE'));