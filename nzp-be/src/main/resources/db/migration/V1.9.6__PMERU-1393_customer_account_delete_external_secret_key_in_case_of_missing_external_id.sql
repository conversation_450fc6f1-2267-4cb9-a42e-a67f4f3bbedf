-----------------------------------------------------------------------------------------------
-- CHANGE customer_account - delete external_secret_key in case of missing external_id
-- ADD AuditLogCode - SERVICE_CALL_GENERATE_USER_PASSWORD - call generate user password on SAP
-----------------------------------------------------------------------------------------------

UPDATE customer_account SET external_secret_key = NULL WHERE external_id IS NULL;

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SERVICE_CALL_GENERATE_USER_PASSWORD', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Odoslanie požiadavky na vygenerovanie hesla', 'Odoslanie požiadavky na vygenerovanie hesla', (select uuid from generic_code_list where code like 'SERVICE_CALL_GENERATE_USER_PASSWORD' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Service call generateUserPassword', '[EN] Service call generateUserPassword', (select uuid from generic_code_list where code like 'SERVICE_CALL_GENERATE_USER_PASSWORD' and type = 'AUDIT_LOG_CODE'));
