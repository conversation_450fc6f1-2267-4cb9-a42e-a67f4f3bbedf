INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'BUSINESS_PARTNER_SYNC_PORTAL','AUDIT_LOG_CODE', null, null, null);


INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Synchronizácia obchodného partnera z portálu', 'Synchronizácia obchodného partnera z portálu',
(select uuid from generic_code_list where code = 'BUSINESS_PARTNER_SYNC_PORTAL' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Synchronization business partner by portal', 'Synchronization business partner by portal',
(select uuid from generic_code_list where code = 'BUSINESS_PARTNER_SYNC_PORTAL' and type = 'AUDIT_LOG_CODE'));