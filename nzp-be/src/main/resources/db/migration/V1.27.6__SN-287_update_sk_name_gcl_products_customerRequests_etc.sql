------------------------------------------------------------------------------------------------------------------------
-------- SN-287: 19. Spotreba - v tabulke odpoctov suffix " - SAP" dat prec
------------------------------------------------------------------------------------------------------------------------

-- Update generic code list name translation (sk)

UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Individuálna obsluha' WHERE code_list_uuid = (select uuid from generic_code_list where code = '110' and "type"='BUSINESS_PARTNER_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Individuálna obsluha' WHERE code_list_uuid = (select uuid from generic_code_list where code = '121' and "type"='BUSINESS_PARTNER_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Individuálna obsluha' WHERE code_list_uuid = (select uuid from generic_code_list where code = '131' and "type"='BUSINESS_PARTNER_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Hromadná obsluha Domácnosti' WHERE code_list_uuid = (select uuid from generic_code_list where code = '141' and "type"='BUSINESS_PARTNER_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Hromadná obsluha Maloodber' WHERE code_list_uuid = (select uuid from generic_code_list where code = '142' and "type"='BUSINESS_PARTNER_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Poplat.-upom.MU25a27' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'N1' and "type"='INVOICE_TYPE') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Asistenčné služby3' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'N9' and "type"='INVOICE_TYPE') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Číslo meradla' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'DEVICE_NUMBER' and "type"='METER_READING_EXPORT_COLUMN') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Oprava odpočtu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'A1' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Samoodpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'B1' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Kontrola samoodpočtu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'B2' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z dôvodu doúčtovania spotreby' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'DS' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet pri obnovení dodávky po úhrade' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'D1' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet pri obnovení dodávky ' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'D2' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet pri prerušení dodávky' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'E1' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z dôvodou neoprávneného odberu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'E2' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z dôvodou neoprávneného odberu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'E3' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Oprava odpočtu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'HF' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet stanovený odhadom' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'HM' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Oprava odpočtu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'HO' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Oprava odpočtu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'HS' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Oprava odpočtu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'HU' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Oprava odpočtu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'HV' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Oprava odpočtu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'H1' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Oprava odpočtu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'H2' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Oprava odpočtu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'H3' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Oprava odpočtu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'H4' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Oprava odpočtu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'H5' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Oprava odpočtu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'H6' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Oprava odpotu' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'H7' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Samoodpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'KL' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fyzický odpočet dodaný od distribútora' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'KM' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fyzický odpočet dodaný od distribútora' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'KP' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z dôvodu zmeny odberateľa' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'K1' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z dôvodu požiadavky zákazníka o prerušenie dodávky' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'L1' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z dôvodu požiadavky zákazníka o obnovenie dodávky' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'L2' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'M1' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'M2' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'M3' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'M4' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z dôvodu zmeny dodávateľa' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'ON' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z dôvodu zmeny dodávateľa' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'OS' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet pri obnovení odberného miesta' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'SO' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet pri prerušení odberného miesta' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'SP' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet odhadom' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'S1' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet odhadom' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'S2' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet odhadom' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'S3' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Samoodpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'W1' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Samoodpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'W2' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'XX' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'X1' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fyzický odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '01' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Samoodpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '02' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet odhadom' WHERE code_list_uuid = (select uuid from generic_code_list where code = '03' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet vypočítaný' WHERE code_list_uuid = (select uuid from generic_code_list where code = '04' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet odhadom' WHERE code_list_uuid = (select uuid from generic_code_list where code = '05' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Samoodpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '06' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fyzický odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '07' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet odhadom' WHERE code_list_uuid = (select uuid from generic_code_list where code = '13' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fyzický odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '84' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet odhadom' WHERE code_list_uuid = (select uuid from generic_code_list where code = '85' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fyzický odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '86' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fyzický odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '87' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet odhadom' WHERE code_list_uuid = (select uuid from generic_code_list where code = '88' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fyzický odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '89' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fyzický odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '94' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet odhadom' WHERE code_list_uuid = (select uuid from generic_code_list where code = '95' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fyzický odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '96' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fyzický odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '97' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet odhadom' WHERE code_list_uuid = (select uuid from generic_code_list where code = '98' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fyzický odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '99' and "type"='METER_READING_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '01' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '02' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '03' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '04' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '05' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '06' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z technického dôvodu' WHERE code_list_uuid = (select uuid from generic_code_list where code = '07' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z technického dôvodu' WHERE code_list_uuid = (select uuid from generic_code_list where code = '08' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '09' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fyzický odpočet dodaný od distribútora' WHERE code_list_uuid = (select uuid from generic_code_list where code = '10' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z technického dôvodu' WHERE code_list_uuid = (select uuid from generic_code_list where code = '11' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z technického dôvodu' WHERE code_list_uuid = (select uuid from generic_code_list where code = '12' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet vypočítaný' WHERE code_list_uuid = (select uuid from generic_code_list where code = '14' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z technického dôvodu' WHERE code_list_uuid = (select uuid from generic_code_list where code = '15' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z technického dôvodu' WHERE code_list_uuid = (select uuid from generic_code_list where code = '16' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z technického dôvodu' WHERE code_list_uuid = (select uuid from generic_code_list where code = '17' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet pri opätovnom pripojení odberného miesta' WHERE code_list_uuid = (select uuid from generic_code_list where code = '18' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet' WHERE code_list_uuid = (select uuid from generic_code_list where code = '19' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z dôvodu kontroly meradla' WHERE code_list_uuid = (select uuid from generic_code_list where code = '20' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z technického dôvodu' WHERE code_list_uuid = (select uuid from generic_code_list where code = '21' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z technického dôvodu' WHERE code_list_uuid = (select uuid from generic_code_list where code = '22' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet pri montáži/demontáži meradla' WHERE code_list_uuid = (select uuid from generic_code_list where code = '23' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet pri zmene montáže' WHERE code_list_uuid = (select uuid from generic_code_list where code = '24' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet pri zmene montáže' WHERE code_list_uuid = (select uuid from generic_code_list where code = '25' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z dôvodu zmeny zmluvy' WHERE code_list_uuid = (select uuid from generic_code_list where code = '26' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z dôvodu zmeny odberateľa' WHERE code_list_uuid = (select uuid from generic_code_list where code = '27' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z technického dôvodu' WHERE code_list_uuid = (select uuid from generic_code_list where code = '28' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet z technického dôvodu' WHERE code_list_uuid = (select uuid from generic_code_list where code = '29' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet odhadom' WHERE code_list_uuid = (select uuid from generic_code_list where code = '90' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet odhadom' WHERE code_list_uuid = (select uuid from generic_code_list where code = '91' and "type"='METER_READING_REASON') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Prepočítavač meranie A 1radový' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'A1' and "type"='METER_READING_REGISTER_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Prepočítavač meranie A 2radový' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'A2' and "type"='METER_READING_REGISTER_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Prepočítavač meranie B 1radový' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'B1' and "type"='METER_READING_REGISTER_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Prepočítavač meranie B 2radový' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'B2' and "type"='METER_READING_REGISTER_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Prepočítavač meranie C 1radový' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'C1' and "type"='METER_READING_REGISTER_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Prepočítavač meranie C 2radový' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'C2' and "type"='METER_READING_REGISTER_KIND') and LOWER(locale) = 'sk';
UPDATE generic_code_list_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='SIPO ' WHERE code_list_uuid = (select uuid from generic_code_list where code = 'S' and "type"='PAYMENT_TYPE') and LOWER(locale) = 'sk';

-- Update customer_request_template_i18n name translation (sk)

UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Odpočet spotreby zemného plynu/ elektriny*' WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOM_O') and LOWER(locale) = 'sk';
UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Prepis majiteľa odberného miesta' WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOM_P') and LOWER(locale) = 'sk';
UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Vystavenie faktúry mimo cyklu' WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOM_ZOFMC') and LOWER(locale) = 'sk';
UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Prerušenie/obnovenie dodávky zemného plynu/ elektriny z dôvodu rekonštrukcie*' WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOM_ZOPAOO') and LOWER(locale) = 'sk';
UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Úradná skúška meradla na spotrebu zemného plynu' WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOM_ZOUSM') and LOWER(locale) = 'sk';
UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Ukončenie zmluvy na dodávku zemného plynu/ elektriny*' WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOM_ZOUZ') and LOWER(locale) = 'sk';
UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Vystavenie duplikátu faktúry' WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOM_ZOVDF') and LOWER(locale) = 'sk';
UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Zrýchlené obnovenie dodávky zemného plynu' WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOM_ZOZODZP') and LOWER(locale) = 'sk';
UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Zmena periodicity preddavkovej platby' WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOM_ZSPAPPP') and LOWER(locale) = 'sk';
UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Žiadosť o produkt Uhlíková stopka' WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOP_US') and LOWER(locale) = 'sk';
UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Zmena bankových údajov' WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOP_ZBU') and LOWER(locale) = 'sk';

-- deactivate - reason: nepoužíva sa

UPDATE customer_request_template SET version = version + 1, updated_at = now(), status = 'INACTIVE' WHERE code = 'ZOM_ZODS';

-- Update product_i18n name translation (sk)

UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='DPI Plyn / DPI_P' WHERE product_uuid = (select uuid from product where code = 'DPI_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixácia - samostatný hedging - Plyn / FIX_HEDG_IO_P' WHERE product_uuid = (select uuid from product where code = 'FIX_HEDG_IO_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 12 Plyn / Fixná cena 12 Plyn' WHERE product_uuid = (select uuid from product where code = 'FIX_12_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 24 EW Plyn / FIX_24_EWI_P' WHERE product_uuid = (select uuid from product where code = 'FIX_24_EWI_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 24 Plyn / FIX_24_P' WHERE product_uuid = (select uuid from product where code = 'FIX_24_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 24 REA Plyn / FIX_24_REA_P' WHERE product_uuid = (select uuid from product where code = 'FIX_24_REA_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 24 RTC Plyn / FIX_24_RTC_P' WHERE product_uuid = (select uuid from product where code = 'FIX_24_RTC_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 24 SP Plyn / FIX_24_SP_P' WHERE product_uuid = (select uuid from product where code = 'FIX_24_SP_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Indexovaná cena Plyn / INDEX_IO_P' WHERE product_uuid = (select uuid from product where code = 'INDEX_IO_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Kombi A24 EWI Plyn / KOMBI_A24_EWI_P' WHERE product_uuid = (select uuid from product where code = 'KOMBI_A24_EWI_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Kombi A24 RTC Plyn / KOMBI_A24_RTC_P' WHERE product_uuid = (select uuid from product where code = 'KOMBI_A24_RTC_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Kombi A24 SP Plyn / KOMBI_A24_SP_P' WHERE product_uuid = (select uuid from product where code = 'KOMBI_A24_SP_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Kombi B24 RTC Plyn / KOMBI_B24_RTC_P' WHERE product_uuid = (select uuid from product where code = 'KOMBI_B24_RTC_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Kombi C24 RTC Plyn / KOMBI_C24_RTC_P' WHERE product_uuid = (select uuid from product where code = 'KOMBI_C24_RTC_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='M3/M4 Plyn / M3_M4_P' WHERE product_uuid = (select uuid from product where code = 'M3_M4_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Regulovaný cenník Plyn / REGUL_P' WHERE product_uuid = (select uuid from product where code = 'REGUL_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Štandardný cenník Plyn / Štandardný cenník Plyn' WHERE product_uuid = (select uuid from product where code = 'STANDARD_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Supervýhodne 12 RTC Plyn / SUPER_12_RTC_P' WHERE product_uuid = (select uuid from product where code = 'SUPER_12_RTC_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Supervýhodne 24 EWI Plyn / SUPER_24_EWI_P' WHERE product_uuid = (select uuid from product where code = 'SUPER_24_EWI_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Supervýhodne 24 RTC Plyn / SUPER_24_RTC_P' WHERE product_uuid = (select uuid from product where code = 'SUPER_24_RTC_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='SV IO P / SV_IO_P' WHERE product_uuid = (select uuid from product where code = 'SV_IO_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Plyn Výhodne+ / VYHODNE_PLUS_P' WHERE product_uuid = (select uuid from product where code = 'VYHODNE_PLUS_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Výhodne 12 Plyn / VYHODNE_12_P' WHERE product_uuid = (select uuid from product where code = 'VYHODNE_12_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Výhodne 24 Plyn / VYHODNE_24_P' WHERE product_uuid = (select uuid from product where code = 'VYHODNE_24_P') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Cenový profil Elektrina / CENPROF_E' WHERE product_uuid = (select uuid from product where code = 'CENPROF_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Elektrina Verným / ELE_VERNYM_E' WHERE product_uuid = (select uuid from product where code = 'ELE_VERNYM_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixácia – samostatný hedging Elektrina / FIX_HEDG_IO_E' WHERE product_uuid = (select uuid from product where code = 'FIX_HEDG_IO_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Fixná cena 24 Elektrina / FIX_24_E' WHERE product_uuid = (select uuid from product where code = 'FIX_24_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Nefixný cenník MPO Elektrina / MPO_ NEREG_E' WHERE product_uuid = (select uuid from product where code = 'MPO_') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Štandardný cenník Elektrina / STANDARD_E' WHERE product_uuid = (select uuid from product where code = 'STANDARD_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='SV IO EE / SV_IO_E' WHERE product_uuid = (select uuid from product where code = 'SV_IO_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='Tarifný profil Elektrina / TARIFPROF_E' WHERE product_uuid = (select uuid from product where code = 'TARIFPROF_E') and LOWER(locale) = 'sk';
UPDATE product_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "name"='VIP Elektrina / VIP_E' WHERE product_uuid = (select uuid from product where code = 'VIP_E') and LOWER(locale) = 'sk';

-- Deactivate products - nebudeme deaktivovat kvoli vyjadreniu SPP: "nie je potrebná aktivita, produkty je potrebné ignorovať"

-- reason: neponuka sa
-- UPDATE product
-- SET version = version + 1, updated_at = now(), status = 'INACTIVE'
-- WHERE code IN ('EXKLUSIV_P', 'EVE_E', 'EVE_II_2018_E', 'EVE_2018_E', 'FIX_12_E', 'ONLINE_ZLAVA_E', 'TOP_2016_E');

-- reason: technický produkt - použitý len pri migrácii, nepoužíva sa na zmluvách
-- UPDATE product
-- SET version = version + 1, updated_at = now(), status = 'INACTIVE'
-- WHERE code = 'REGUL_E';

-- reason: technický produkt, nepoužíva sa na zmluvách
-- UPDATE product
-- SET version = version + 1, updated_at = now(), status = 'INACTIVE'
-- WHERE code IN ('DUMMY_P', 'GAS_REF_HO', 'GAS_REF_IO_SV', 'DUMMY_E', 'ELE_REF_HO', 'ELE_REF_IO_SV', 'MOD_NEUTILITY', 'PDO_UTIL');

-- reason: zmigrovaný zo starého systému už sa nezazmluvňuje
-- UPDATE product
--SET version = version + 1, updated_at = now(), status = 'INACTIVE'
-- WHERE code IN ('ASIST_ELE_N', 'ASIST_N', 'ASIST_PLYN_N', 'ASIST_PRAV_N', 'ASIST_PRAV_2_N', 'ASIST_2_N', 'IT_ASIST_N', 'IT_ASIST_2_N', 'MIG_ASIST_N', 'MIG_ASIST_2_N', 'MIG_BALIK_N', 'POIST_DOM_N', 'POIST_PLAT_EN_N', 'ZDRAV_ASIST_N', 'ZDRAV_ASIST_2_N');

-- reason: Balíček produktov, nie samostatný produkt
-- UPDATE product
-- SET version = version + 1, updated_at = now(), status = 'INACTIVE'
-- WHERE code IN ('BAL_ELE_GAS', 'BAL_GAS_NEUT', 'PDO_IC_PACK', 'BAL_ELE_GAS_5NEUT', 'BAL_ELE_NEUT', 'BAL_2ELE_2GAS_NEUT');

-- Update notification_template_i18n sms_body translation (sk template)

UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "sms_body"=NULL WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "sms_body"='Vazeny zakaznik, zasielame Vam verifikacny kod pre overenie zakaznickeho cisla ${attributes.challengeCode}. Vase SPP' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "sms_body"='Vazeny zakaznik, zasielame Vam kod pre prihlasenie do uctu: ${attributes.smsCode}. Vase SPP' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_LOGIN_PHONE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "sms_body"='Poziadavka na zmenu telefonneho cisla bola zamietnuta. Kontaktujte nas, prosím. Vase SPP' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "sms_body"='Vazeny zakaznik, zasielame Vam jednorazove heslo pre prihlasenie do portalu: ${attributes.generatedPassword}. Po prvom pouziti je potrebne heslo zmenit. Vase SPP' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD') and LOWER(locale) = 'sk';

-- Update notification_template_i18n email_subject (sk template)

UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Priradenie odberných miest k portálovému účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST_AUTOMATIC') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Upozornenie na bližiaci sa termín splatnosti faktúry' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Upozornenie na bližiaci sa termín splatnosti preddavku ' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Dokončenie registrácie do portálu Moje SPP' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Heslo bolo úspešne obnovené' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='e-mailová adresa bola úspešne zmenená' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Potvrdenie zmeny hesla' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Úspešné priradenie odberných miest k portálovému účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Zrušenie priradenia odberných miest' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Úspešné priradenie odberných miest k portálovému účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Neúspešná zmena e-mailovej adresy' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Udelenie zdieľania odberných miest' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Úspešné nastavenie zdieľania odberných miest' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Zrušenie zdieľania odberných miest' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Zrušenie zdieľania odberných miest' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Potvrdenie o prijatí platby' WHERE notification_template_id = (select uuid from notification_template where code = 'EPAY_TRANSACTION_FINISHED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Dokončenie obnovy hesla' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Pozývame Vás na portál Moje SPP' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVITATION') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Pozývame Vás na portál Moje SPP' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVITATION_ACTIVE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Zrušenie priradenia odberného miesta' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Požiadavka na priradenie odberných miest' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Vystavenie preddavkovej platby za spotrebu na odbernom mieste typu ${deliveryPoint.typeName.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Pridanie poznámky k žiadosti ${customerRequest.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Neúspešná registrácia na portáli Moje SPP' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Potvrdenie deaktivácie účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Potvrdenie zmeny e-mailovej adresy' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Vyrovnanie preplatku za spotrebu na odbernom mieste typu ${deliveryPoint.typeName.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Zadajte samoodpočet spotreby na odbernom mieste typu ${deliveryPoint.typeName.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'METER_READING_CUSTOMER') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Upozornenie na vystavenie faktúry za spotrebu na odbernom mieste typu ${deliveryPoint.typeName.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Vyžiadanie zrušenia žiadosti s názvom ${customerRequest.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Zablokovanie prístupu k portálovému účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_LOCK') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Zablokovanie prístupu k portálovému účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'EMPLOYEE_LOCK') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Zamietnutie priradenia odberných miest k portálovému účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_VERIFY_REVOKE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Zaregistrovanie žiadosti s názvom ${customerRequest.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Zrušenie žiadosti číslo ${customerRequest.externalId} zo strany zákazníka' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_subject"='Potvrdenie zrušenia žiadosti ${customerRequest.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCELLED_CUSTOMER') and LOWER(locale) = 'sk';

-- Update notification_template_i18n header (sk template)

UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Priradenie odberných miest k portálovému účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST_AUTOMATIC') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Upozornenie na bližiaci sa termín splatnosti faktúry' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Upozornenie na bližiaci sa termín splatnosti preddavku ' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Dokončenie registrácie do portálu Moje SPP' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Úspešná obnova hesla' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Úspešná zmena e-mailovej adresy' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Potvrdenie zmeny hesla' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Úspešné priradenie odberných miest k portálovému účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Zrušenie priradenia odberných miest' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Úspešné priradenie odberných miest k portálovému účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Neúspešná zmena e-mailovej adresy' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Udelenie zdieľania odberných miest' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Úspešné nastavenie zdieľania odberných miest' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Zrušenie zdieľania odberných miest' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Zrušenie zdieľania odberných miest' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Potvrdenie o prijatí platby' WHERE notification_template_id = (select uuid from notification_template where code = 'EPAY_TRANSACTION_FINISHED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Dokončenie obnovy hesla' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Pozývame Vás na portál Moje SPP' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVITATION') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Pozývame Vás na portál Moje SPP' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVITATION_ACTIVE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Zrušenie priradenia odberného miesta' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Požiadavka na priradenie odberných miest' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Vystavenie preddavkovej platby za spotrebu na odbernom mieste typu ${deliveryPoint.typeName.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Pridanie poznámky k žiadosti ${customerRequest.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Neúspešná registrácia na portáli Moje SPP' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Potvrdenie deaktivácie účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Potvrdenie zmeny e-mailovej adresy' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Vyrovnanie preplatku za spotrebu na odbernom mieste typu ${deliveryPoint.typeName.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Zadajte samoodpočet spotreby na odbernom mieste typu ${deliveryPoint.typeName.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'METER_READING_CUSTOMER') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Upozornenie na vystavenie faktúry za spotrebu na odbernom mieste typu ${deliveryPoint.typeName.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Vyžiadanie zrušenia žiadosti s názvom ${customerRequest.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Zablokovanie prístupu k portálovému účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_LOCK') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Zablokovanie prístupu k portálovému účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'EMPLOYEE_LOCK') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Zamietnutie priradenia odberných miest k portálovému účtu' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_VERIFY_REVOKE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Zaregistrovanie žiadosti s názvom ${customerRequest.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Zrušenie žiadosti číslo ${customerRequest.externalId} zo strany zákazníka' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "header"='Potvrdenie zrušenia žiadosti ${customerRequest.name}' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCELLED_CUSTOMER') and LOWER(locale) = 'sk';

-- Update notification_template_i18n email_body (sk template) - podla excel

UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>priradenie odberných miest k Vášmu portálovému účtu bolo automaticky spustené (zahájené). O ukončení procesu Vás budeme informovať.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST_AUTOMATIC') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>blíži sa dátum splatnosti Vašej faktúry za spotrebu na odbernom mieste s adresou ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city}.</p><p>Ak máte zriadené inkaso, úhrada prebehne automaticky k dátumu splatnosti faktúry.</p><p>Ak platíte prevodným príkazom, platbu môžete zrealizovať priamo vo svojom <a href="${portalExternalUrl}/invoices/${invoice.id}">portálovom účte</a></span> vo výške ${invoice.amount} eur s variabilným symbolom ${invoice.vs}.</p><p>V prípade preplatku Vám peniaze zašleme na Vás bankový účet.</p><p><a href="${portalExternalUrl}/invoices/${invoice.id}">Prejsť na úhradu faktúry</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>blíži sa dátum splatnosti Vašej preddavkovej platby za spotrebu na odbernom mieste s adresou ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city}.</p><p>Ak máte zriadené inkaso, úhrada prebehne automaticky k dátumu splatnosti faktúry.</p><p>Ak platíte prevodným príkazom, platbu môžete zrealizovať priamo vo svojom <a href="${portalExternalUrl}/invoices/${invoice.id}">portálovom účte</a> vo výške ${invoice.amount} eur s variabilným symbolom ${invoice.vs}.</p><p>V prípade preplatku Vám peniaze zašleme na Vás bankový účet.</p><p><a href="${portalExternalUrl}/invoices/${invoice.id}">Prejsť na úhradu preddavkovej platby</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>Vaša registrácia bola úspešná.  Odteraz môžete neobmedzene využívať výhody portálu Moje SPP.</p><br><p><a href="${portalExternalUrl}">Prihlásiť sa</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>úspešne ste si obnovili heslo pre prihlásenie do svojho portálového účtu. Od tohto okamihu Vám slúži ako prihlasovací údaj.</p><p><a href="${portalExternalUrl}">Prejsť na prihlásenie</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>úspešne ste si zmenili e-mailovú adresu k svojmu portálovému účtu.</p><p>Od tohto okamihu Vám môže slúžiť ako prihlasovací údaj.</p><a href="${portalExternalUrl}">Prejsť na prihlásenie</a></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>úspešne ste si zmenili prihlasovacie heslo do svojho portálového účtu. Odteraz môžete neobmedzene využívať výhody portálu Moje SPP.</p><br><p><a href="${portalExternalUrl}">Prejsť na prihlásenie</a></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>k Vášmu portálovému účtu boli priradené Vaše odberné miesta.</p><p>Od tohto okamihu si vo Vašom účte môžete pozrieť k nim detailné údaje.</p><p><a href="${portalExternalUrl}/delivery-points">Prejsť na odberné miesta</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>na Vášom portálovom účte bolo zrušené priradenie požadovaných odberných miest.</p><p>Od tohto okamihu sa nebude zobrazovať detail týchto odberných miest vo Vašom účte.</p><p><a href="${portalExternalUrl}/delivery-points">Prejsť na odberné miesta</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>k Vášmu portálovému účtu boli priradené požadované odberné miesta.</p><p>Od tohto okamihu si vo Vašom účte účte môžete pozrieť k nim detailné údaje.</p><p><a href="${portalExternalUrl}/delivery-points">Prejsť na odberné miesta</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>ľutujeme, požiadavka na zmenu e-mailovej adresy bola zamietnutá. Účet so zadanou e-mailovou adresou je už vytvorený. Ak ste k nemu zabudli heslo, kliknite na odkaz <a href="${portalExternalUrl}/login-issues">ZMENA HESLA</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>úspešne ste si nastavili&nbsp;telefónne číslo na&nbsp;&nbsp;${customer.phone}. Od tohto okamihu ho môžete používať aj pre prihlásenie do portálu Moje SPP.</p><p><a href="${portalExternalUrl}">Prejsť na prihlásenie</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>zákazník ${attributes.sharingEmail} Vám dal zdieľať údaje zo svojho portálového účtu.</p><p>Pre prístup k týmto zdieľaným údajom musíte mať aktívny účet na portáli Moje SPP.</p><p><a href="${portalExternalUrl}">Prejsť na prihlásenie</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>úspešne ste nastavili zdieľanie svojich odberných miest zákazníkovi ${attributes.sharingEmail}.</p><p>Od tohto okamihu vidí údaje k Vašim zdieľaným odberným miestam.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>zákazník ${attributes.sharingEmail} Vám zrušil zdieľania údajov k jeho odberným miestam.</p><p><a href="${portalExternalUrl}">Prejsť na prihlásenie</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>práve ste zrušili zdieľanie svojich odberných miest zákazníkovi ${attributes.sharingEmail}.</p><p>Od tohto okamihu už nevidí údaje k Vašim odberným miestam.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozorňujeme Vás, že na odbernom mieste na adrese  ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city} ste prekročili Vašu dennú spotrebu plynu. V prípade potreby, kontaktujte svojho manažéra predaja.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_ZM') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozorňujeme Vás, že na odbernom mieste na adrese  ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city} ste prekročili Vašu maximálnu dennú spotrebu plynu. V prípade potreby, kontaktujte svojho manažéra predaja.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_DMM') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozornenie, že zákazník ${businessPartner.name}, číslo obchodného partnera ${businessPartner.externalId} prekročil maximálnu dennú spotrebu plynu na svojom odbernom mieste.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'DELIVERY_POINT_AM_CHECK_DMM') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozorňujeme Vás, že na odbernom mieste na adrese  ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city} ste prekročili Vašu maximálnu rezervovanú kapacitu pre spotrebu elektriny. V prípade potreby, kontaktujte svojho manažéra predaja.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MRK') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozornenie, že zákazník ${businessPartner.name}, číslo obchodného partnera ${businessPartner.externalId} prekročil maximálnu rezervovanú kapacitu pre spotrebu elektriny na svojom odbernom mieste.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'DELIVERY_POINT_AM_CHECK_MRK') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozorňujeme Vás, že na odbernom mieste na adrese  ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city} ste prekročili Vašu rezervovanú kapacitu pre spotrebu elektriny. V prípade potreby, kontaktujte svojho manažéra predaja.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RK') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozornenie, že zákazník ${businessPartner.name}, číslo obchodného partnera ${businessPartner.externalId} prekročil rezervovanú kapacitu pre spotrebu elektriny na svojom odbernom mieste.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'DELIVERY_POINT_AM_CHECK_RK') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>pre zákaznícky účet ${customer.email} sme obdržali požiadavku na párovanie obchodného partnera číslo ${businessPartner.externalId}.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>prijali sme od Vás platbu vo výške ${customerTransaction.amount} eur.Zobrazuje sa vo Vašom portálovom účte v časti Uskutočnené platby. K vyrovnaniu platby dôjde až po priradení úhrady k platobnému dokladu.</p><br><p><a href="${portalExternalUrl}/invoices">Prejsť na stránku zobrazenie úhrad</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'EPAY_TRANSACTION_FINISHED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>pre dokončenie obnovy hesla k Vášmu osobnému účtu je potrebné urobiť ešte jeden krok.</p><p>Potvrďte zmenu kliknutím na <a href="${portalExternalUrl}/confirm-password-recovery?challengeCodeUuid=${attributes.challengeCodeUuid}&challengeCode=${attributes.challengeCode}">Obnoviť heslo</a> alebo prekopírovaním nasledovného odkazu do svojho prehliadača</p><p>${portalExternalUrl}/confirm-password-recovery?challengeCodeUuid=${attributes.challengeCodeUuid}&challengeCode=${attributes.challengeCode}.</p><p>Odkaz je platný do ${attributes.challengeCodeValidTo}.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>ponúkame Vám služby portálu Moje SPP. Vytvorili sme Vám portálový účet.</p><p><a href="${portalExternalUrl}">Prihláste</a> sa Vašou e-mailovou adresou a jednorázovým heslom, ktoré sme Vám poslali na Vaše mobilné telefónne číslo.</p><p>Využívajte naplno služby poskytované portálom Moje SPP.</p><p><a href="${portalExternalUrl}">Prejsť na prihlásenie</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVITATION') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>využívajte naplno služby poskytované portálom Moje SPP.</p><p><a href="${portalExternalUrl}">Prejsť na prihlásenie</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVITATION_ACTIVE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>pracujeme na Vašej požiadavke na zrušenie priradenia požadovaných odberných miest z Vášho účtu.</p><p>O jej úspešnom vyriešení Vás budeme informovať.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>pracujeme na Vašej požiadavke priradenia odberných miest k Vášmu účtu.</p><p>O jej úspešnom vyriešení Vás budeme informovať.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>radi by sme Vás upozornili, že sme Vám vystavili preddavkovú platbu za spotrebu na odbernom mieste s adresou ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city}, vo výške ${invoice.amount} eur.</p><p>Úhradu môžete zrealizovať vo svojom portálovom účte.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozorňujeme Vás, že na odbernom mieste na adrese  ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city} ste prekročili Vašu maximálnu rezervovanú kapacitu pre spotrebu elektriny. V prípade potreby, kontaktujte svojho manažéra predaja.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozorňujeme Vás, že na odbernom mieste na adrese  ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city} ste prekročili Vašu rezervovanú kapacitu pre spotrebu elektriny. V prípade potreby, kontaktujte svojho manažéra predaja.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>ľutujeme, požiadavka na registráciu na portáli Moje SPP bola zamietnutá. Účet so zadanou e-mailovou adresou je už vytvorený. Ak ste k nemu zabudli heslo, kliknite na odkaz <a href="${portalExternalUrl}/login-issues">ZMENA HESLA</a>.</p><p>V prípade potreby nás kontaktujte.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>mrzí nás, že už nechcete využívať služby portálu Moje SPP a požiadali ste o deaktiváciu svojho účetu.</p><p>Pre potvrdenie Vášho rozhodnutia kliknite na <a href="${portalExternalUrl}/confirm-account-removal?challengeCode=${attributes.challengeCode}&challengeCodeUuid=${attributes.challengeCodeUuid}">POTVRDENIE DEAKTIVÁCIE ÚČTU</a> alebo prekopírujte nasledovný odkaz do svojho prehliadača:</p><p>${portalExternalUrl}/confirm-account-removal?challengeCode=${attributes.challengeCode}&challengeCodeUuid=${attributes.challengeCodeUuid}</p><p>Odkaz je platný do ${attributes.challengeCodeValidTo}.</p><p>Spracovanie požiadavky pozostáva z dvoch krokov:</p><ul><li>zrušenie priradenia odberných miest</li><li>vymazanie portálového účtu</li></ul><p>O ich vyriešení Vás budeme informovať.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>pre dokončenie zmeny e-mailovej adresy k portálovému účtu kliknite na <a href="${portalExternalUrl}/confirm-email-change?challengeCode=${attributes.challengeCode}&challengeCodeUuid=${attributes.challengeCodeUuid}">ZMENIŤ E-MAIL</a> alebo prekopírujte nasledovný odkaz do svojho prehliadača:</p><p>${portalExternalUrl}/confirm-email-change?challengeCode=${attributes.challengeCode}&challengeCodeUuid=${attributes.challengeCodeUuid}</p><p>Odkaz je platný do ${attributes.challengeCodeValidTo}.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>na Váš bankový účet sme odoslali preplatok, ktorý vznikol po vyúčtovaní spotreby na odbernom mieste typu ${deliveryPoint.typeName.name} na Vašom odbernom mieste s adresou ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city}.</p><p>Všetky svoje úhrady si možete pozrieť v <a href="${portalExternalUrl}/invoices">portálovom účte</a>.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>v týchto dňoch máte možnosť nahlásiť samoodpočet spotreby na odbernom mieste typu ${deliveryPoint.typeName.name} s adresou ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city}.</p><p>Vykonanie samoodpočtu online je jednoduché a má veľkú výhodu — nemusíte čakať na odpočtára.</p><p>Odčítajte, prosím, stav na svojom meradla a zadajte ho do portálového účtu.</p><p><a href="${portalExternalUrl}/delivery-points/${unitedDeliveryPoint.id}/self-read">Prejsť na zadanie samoodpočtu</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'METER_READING_CUSTOMER') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>Vaša požiadavka na zrušenie portálového účtu bola zrealizovaná a Váš účet bol vymazaný.</p><p>Sme radi, že ste využívali služby portálu Moje SPP.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>vystavili sme Vám faktúru za spotrebu na odbernom mieste typu ${deliveryPoint.typeName.name} s adresou ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city}.</p><p>Faktúru si môžete pozrieť vo svojom portálovom účte.</p><p><a href="${portalExternalUrl}/invoices">Prejsť na faktúry.</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>evidujeme Vašu požiadavku na zrušenie žiadosti s názvom ${customerRequest.name}</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>ľutujeme, ale prístup k Vášmu účtu bol z dôvodu ${attributes.reason} zablokovaný.</p><p>Počkajte a svoje prihlásenie zopakujte neskôr.</p><p><a href="${portalExternalUrl}">Prejsť na prihlásenie</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_LOCK') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>ľutujeme, ale prístup k Vášmu účtu bol z dôvodu ${attributes.reason} zablokovaný.</p><p>Počkajte a svoje prihlásenie zopakujte neskôr.</p><p><a href="${portalExternalUrl}">Prejsť na prihlásenie</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'EMPLOYEE_LOCK') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>ľutujeme, ale Vaša požiadavka na priradenie odberných miest k Vášmu portálovému účtu bola zamietnutá.</p><p>Prosím, vyskúšajte neskôr alebo nás kontaktujte.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'BUSINESS_PARTNER_VERIFY_REVOKE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>Vaša registrácia je takmer kompletná. Ostáva posledný krok k jej dokončeniu.</p><p>Prosím, pre potvrdenie registrácie kliknite na <a href="${portalExternalUrl}/confirm-registration?challengeCode=${attributes.challengeCode}&challengeCodeUuid=${attributes.challengeCodeUuid}">DOKONČIŤ REGISTRÁCIU</a> alebo prekopírujte nasledovný odkaz do svojho prehliadača:</p><p>${portalExternalUrl}/confirm-registration?challengeCode=${attributes.challengeCode}&challengeCodeUuid=${attributes.challengeCodeUuid}</p><p>Odkaz je platný do ${attributes.challengeCodeValidTo}.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>zákazník ${attributes.sharingEmail} Vám dal zdieľať údaje zo svojho portálového účtu.</p><p>Pre prístup k týmto zdieľaným údajom musíte mať aktívny účet na portáli Moje SPP.</p><p><a href="${portalExternalUrl}">Prejsť na registráciu/prihlásenie</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_SHARING_INVITATION') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>Vaša požiadavka s názvom ${customerRequest.name} zmenila stav na ${customerRequest.status.name}.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>Vaša požiadavka s názvom ${customerRequest.name} zmenila stav na ${customerRequest.status.name}.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>pracujeme na riešení Vašej požiadavky ${customerRequest.name}.</p><p>K zadanej žiadosti sme pridali poznámku ${attributes.customerRequestNote}.</p><p><a href="${portalExternalUrl}/customer-requests/${customerRequest.uuid}">Prejsť na detail</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>Vaša požiadavka s názvom ${customerRequest.name} bola zaregistrovaná. O postupe riešenia Vás budeme informovať.</p><p><a href="${portalExternalUrl}/customer-requests/${customerRequest.uuid}">Prejsť na detail žiadosti</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER') and LOWER(locale) = 'sk';

UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>blíži sa dátum splatnosti Vašej faktúry za spotrebu <#if deliveryPoints?has_content>  <#if deliveryPoints?size == 1> na odbernom mieste s adresou ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}.  <#else>  na odberných miestach s adresami    <ul>    <#list deliveryPoints as dp>      <li>${dp.street} ${dp.streetNumber}, ${dp.city}</li>    </#list>    </ul>  </#if></#if></p><p>Ak máte zriadené inkaso, úhrada prebehne automaticky k dátumu splatnosti faktúry.</p><p>Spôsom platby SIPO, vyžaduje úhradu cez Slovenskú poštu.</p><p>Ak platíte prevodom, platbu môžete zrealizovať priamo vo svojom <a href="${portalExternalUrl}/invoices/${invoice.id}">portálovom účte</a></span> vo výške ${invoice.amount} eur s variabilným symbolom ${invoice.vs}.</p><p>V prípade preplatku Vám peniaze zašleme na Vás bankový účet.</p><p><a href="${portalExternalUrl}/invoices/${invoice.id}">Prejsť na úhradu faktúry</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>blíži sa dátum splatnosti Vašej preddavkovej platby za spotrebu <#if deliveryPoints?has_content>  <#if deliveryPoints?size == 1> na odbernom mieste s adresou ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}.  <#else>    na odberných miestach s adresami    <ul>    <#list deliveryPoints as dp>      <li>${dp.street} ${dp.streetNumber}, ${dp.city}</li>    </#list>    </ul>  </#if></#if></p><p>Ak máte zriadené inkaso, úhrada prebehne automaticky k dátumu splatnosti faktúry.</p><p>Spôsom platby SIPO, vyžaduje úhradu cez Slovenskú poštu.</p><p>Ak platíte prevodným príkazom, platbu môžete zrealizovať priamo vo svojom <a href="${portalExternalUrl}/invoices/${invoice.id}">portálovom účte</a> vo výške ${invoice.amount} eur s variabilným symbolom ${invoice.vs}.</p><p>V prípade preplatku Vám peniaze zašleme na Vás bankový účet.</p><p><a href="${portalExternalUrl}/invoices/${invoice.id}">Prejsť na úhradu preddavkovej platby</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>vystavili sme Vám preddavkovú platbu za spotrebu <#if deliveryPoints?has_content>  <#if deliveryPoints?size == 1> na odbernom mieste s adresou ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}.  <#else>    na odberných miestach s adresami    <ul>    <#list deliveryPoints as dp>      <li>${dp.street} ${dp.streetNumber}, ${dp.city}</li>    </#list>    </ul>  </#if></#if>, vo výške ${invoice.amount} eur. </p><p>Úhradu môžete zrealizovať vo svojom portálovom účte.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>na Váš bankový účet sme odoslali preplatok, ktorý vznikol po vyúčtovaní spotreby <#if deliveryPoints?has_content>  <#if deliveryPoints?size == 1> na odbernom mieste s adresou ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}.  <#else>    na odberných miestach s adresami    <ul>    <#list deliveryPoints as dp>      <li>${dp.street} ${dp.streetNumber}, ${dp.city}</li>    </#list>    </ul>  </#if></#if></p><p>Všetky svoje úhrady si možete pozrieť v <a href="${portalExternalUrl}/invoices">portálovom účte</a>.</p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED') and LOWER(locale) = 'sk';
UPDATE notification_template_i18n SET updated_at=now(), "version"= "version"+1, locale='sk', "email_body"='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>vystavili sme Vám faktúru za spotrebu <#if deliveryPoints?has_content>  <#if deliveryPoints?size == 1> na odbernom mieste s adresou ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}.  <#else>    na odberných miestach s adresami    <ul>    <#list deliveryPoints as dp>      <li>${dp.street} ${dp.streetNumber}, ${dp.city}</li>    </#list>    </ul>  </#if></#if></p><p>Faktúru si môžete pozrieť vo svojom portálovom účte.</p><p><a href="${portalExternalUrl}/invoices">Prejsť na faktúry.</a></p></@spp.notification_email_template>' WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED') and LOWER(locale) = 'sk';

-- FIX notification_template_variables

DELETE FROM notification_template_variable WHERE variable IN ('email', 'phone');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_INVOICE_BEFORE_DUE')
  AND (nv.variable LIKE 'contract.%'
    OR nv.variable LIKE 'logged.customer.%'
    OR nv.variable = 'unitedDeliveryPoint.id');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE')
  AND (nv.variable LIKE 'contract.%'
    OR nv.variable LIKE 'logged.customer.%'
    OR nv.variable = 'unitedDeliveryPoint.id');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REGISTRATION_SUCCESS')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS')
  AND (nv.variable LIKE 'logged.customer.%');
-- Odstranujem target informacie, alebo mame dat do target zakaznicky ucet?
-- DELETE FROM notification_template_variable nv
-- WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS')
--  AND (nv.variable LIKE 'target.entity.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS')
  AND nv.variable LIKE 'target.entity.%';
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC')
  AND (nv.variable LIKE 'contract.%'
    OR nv.variable LIKE 'contractAccount.%'
    OR nv.variable LIKE 'deliveryPoint.%'
    OR nv.variable LIKE 'unitedDeliveryPoint.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS')
  AND (nv.variable LIKE 'logged.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'BUSINESS_PARTNER_PAIRING_SUCCESS')
  AND (nv.variable LIKE 'contract.%'
    OR nv.variable LIKE 'contractAccount.%'
    OR nv.variable LIKE 'deliveryPoint.%'
    OR nv.variable LIKE 'unitedDeliveryPoint.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'SUSPICIOUS_ACTIVITY')
  AND (nv.variable LIKE 'target.entity.%' OR nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_SHARING_CONSUMER_GRANT')
  AND (nv.variable LIKE 'contract.%'
    OR nv.variable LIKE 'contractAccount.%'
    OR nv.variable LIKE 'deliveryPoint.%'
    OR nv.variable LIKE 'unitedDeliveryPoint.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_SHARING_OWNER_GRANT')
  AND (nv.variable LIKE 'contract.%'
    OR nv.variable LIKE 'contractAccount.%'
    OR nv.variable LIKE 'deliveryPoint.%'
    OR nv.variable LIKE 'unitedDeliveryPoint.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_SHARING_CONSUMER_REVOKE')
  AND (nv.variable LIKE 'contract.%'
    OR nv.variable LIKE 'contractAccount.%'
    OR nv.variable LIKE 'deliveryPoint.%'
    OR nv.variable LIKE 'unitedDeliveryPoint.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_SHARING_OWNER_REVOKE')
  AND (nv.variable LIKE 'contract.%'
    OR nv.variable LIKE 'contractAccount.%'
    OR nv.variable LIKE 'deliveryPoint.%'
    OR nv.variable LIKE 'unitedDeliveryPoint.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_ZM')
  AND (nv.variable LIKE 'logged.customer.%');
insert into notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid)
    values ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_ZM'), now(), now(), 1, 'attributes.consumptionExceeded.from', 'Interval nameranej hodnoty od', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4());
insert into notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid)
    values ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_ZM'), now(), now(), 1, 'attributes.consumptionExceeded.to', 'Interval nameranej hodnoty do', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4());
insert into notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid)
    values ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_ZM'), now(), now(), 1, 'attributes.consumptionExceeded.value', 'Nameraná hodnota', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4());
insert into notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid)
    values ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_ZM'), now(), now(), 1, 'attributes.consumptionLimit', 'Maximálny odber', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4());
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_DMM')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM')
  AND (nv.variable LIKE 'logged.customer.%');
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.amEmail', 'Obchodný partner - email manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.amFirstName', 'Obchodný partner - meno manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.amLastName', 'Obchodný partner - priezvisko manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.amName', 'Obchodný partner - meno pobočky', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.amPhone', 'Obchodný partner - telefónne číslo manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.city', 'Obec obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.country', 'Štát obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.email', 'Email obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.externalId', 'SAP ID obchodného partnera', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.firstName', 'Meno obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.lastName', 'Priezvisko obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.name', 'Názov obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.phone', 'Telefonné číslo obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.street', 'Ulica obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.streetNumber', 'Číslo domu obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'businessPartner.zipCode', 'PSČ obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contract.billCycle', 'Zmluva - fakturačný cyklus', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contract.eeTariff', 'Zmluva - eeTariff', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contract.effectiveFrom', 'Zmluva - účinnosť od', null, 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contract.effectiveTo', 'Zmluva - účinnosť do', null, 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contract.product', 'Produkt zmluvy', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.city', 'Zmluvný účet - obec', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.country', 'Zmluvný účet - štát', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.email', 'Email zmluvného účtu', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.firstName', 'Zmluvný účet - meno', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.lastName', 'Zmluvný účet - priezvisko', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.name', 'Zmluvný účet - názov organizácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.postCity', 'Zmluvný účet - korešpondenčná adresa -  mesto', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.postCountry', 'Zmluvný účet - korešpondenčná adresa - štát', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.postStreet', 'Zmluvný účet - korešpondenčná adresa -  ulica', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.postStreetNumber', 'Zmluvný účet - korešpondenčná adresa - popisné číslo', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.postZipCode', 'Zmluvný účet - korešpondenčná adresa -  PSČ', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.street', 'Zmluvný účet - ulica', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.streetNumber', 'Zmluvný účet - číslo', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'contractAccount.zipCode', 'Zmluvný účet - PSČ', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'deliveryPoint.category', 'Kategória odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'deliveryPoint.city', 'Obec odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'deliveryPoint.country', 'Štát odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'deliveryPoint.eic', 'Odberné miesto - eic', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'deliveryPoint.pod', 'Odberné miesto - pod', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'deliveryPoint.street', 'Ulica odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'deliveryPoint.streetNumber', 'Číslo odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'deliveryPoint.tariffRate', 'Tarifa/sadzba na odbernom mieste', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 2, 'deliveryPoint.type', 'Typ odberného miesta (skratka)', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'deliveryPoint.typeName.name', 'Typ odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'deliveryPoint.zipCode', 'PSČ odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_DMM'), now(), now(), 1, 'unitedDeliveryPoint.id', 'ID zlúčeného odberného miesta', 'Technický identifikátor pre zlúčené odberné miesto', 'STRING', 'AUTOMATIC', uuid_generate_v4());
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_MRK')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_MRK')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_RK')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_AM_CHECK_RK')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'EPAY_TRANSACTION_FINISHED')
  AND (nv.variable in ('deliveryPoints', 'unitedDeliveryPoint.id'));
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_INVITATION')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_INVITATION_ACTIVE')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED')
  AND (nv.variable LIKE 'logged.customer.%' OR nv.variable LIKE 'contract.%' OR nv.variable = 'unitedDeliveryPoint.id');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_NOTE_CREATE')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_INVOICE_CREDIT_ISSUED')
  AND (nv.variable LIKE 'logged.customer.%' OR nv.variable LIKE 'contract.%' OR nv.variable = 'unitedDeliveryPoint.id');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'METER_READING_CUSTOMER')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_DELETE_SUCCESS')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_INVOICE_ISSUED')
  AND (nv.variable LIKE 'logged.customer.%' OR nv.variable LIKE 'contract.%' OR nv.variable = 'unitedDeliveryPoint.id');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'BUSINESS_PARTNER_VERIFY_REVOKE')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REGISTRATION_REQUEST')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_SHARING_INVITATION')
  AND (nv.variable LIKE 'contract.%' OR nv.variable LIKE 'contractAccount.%' OR nv.variable LIKE 'deliveryPoint.%' OR nv.variable = 'unitedDeliveryPoint.id');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_STATUS_CHANGE')
  AND (nv.variable LIKE 'logged.customer.%');
-- Mimo zmien Email body (len uprava template variables)
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_ACTIVATION_SUCCESS')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_DEACTIVATION_SUCCESS')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_INVITATION_PASSWORD')
  AND (nv.variable LIKE 'logged.customer.%');
DELETE FROM notification_template_variable nv
WHERE nv.notification_template_uuid = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_SHARING_INVITATION')
  AND (nv.variable LIKE 'contract.%' OR nv.variable LIKE 'customer.%' OR nv.variable LIKE 'deliveryPoint.%' OR nv.variable = 'unitedDeliveryPoint.id' OR nv.variable LIKE 'target.customer.%');

-- ADD customerRequest.uuid into templates which holds customer request information

INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE'), now(), now(), 1, 'customerRequest.uuid', 'ID žiadosti', 'ID žiadosti v systeme NZP', 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_CANCELLED_CUSTOMER'), now(), now(), 1, 'customerRequest.uuid', 'ID žiadosti', 'ID žiadosti v systeme NZP', 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_CANCEL_REQUEST'), now(), now(), 1, 'customerRequest.uuid', 'ID žiadosti', 'ID žiadosti v systeme NZP', 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER'), now(), now(), 1, 'customerRequest.uuid', 'ID žiadosti', 'ID žiadosti v systeme NZP', 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_NOTE_CREATE'), now(), now(), 1, 'customerRequest.uuid', 'ID žiadosti', 'ID žiadosti v systeme NZP', 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_STATUS_CHANGE'), now(), now(), 1, 'customerRequest.uuid', 'ID žiadosti', 'ID žiadosti v systeme NZP', 'STRING', 'AUTOMATIC', uuid_generate_v4());

INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS'), now(), now(), 1, 'customer.email', 'Email zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS'), now(), now(), 1, 'customer.firstName', 'Meno zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS'), now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS'), now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS'), now(), now(), 1, 'locale', 'Lokalizácia', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS'), now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS'), now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS'), now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS'), now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS'), now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS'), now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());

INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'customer.email', 'Email zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'customer.firstName', 'Meno zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'locale', 'Lokalizácia', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', 'Týka sa iba SAP entít.', 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácie cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'logged.customer.email', 'Email zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'logged.customer.firstName', 'Meno zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'logged.customer.lastName', 'Priezvisko zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type, notification_template_execution_type, uuid) VALUES ((SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED'), now(), now(), 1, 'logged.customer.phone', 'Telefónne číslo zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4());
