
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EMAIL', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Email', 'Email zákazníka', (select uuid from generic_code_list where code like 'EMAIL' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Email', '[EN] <PERSON><PERSON> z<PERSON>azn<PERSON>a', (select uuid from generic_code_list where code like 'EMAIL' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'TYPE', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Typ účtu', 'Typ zákazníckeho účtu', (select uuid from generic_code_list where code like 'TYPE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Typ účtu', '[EN] Typ zákazníckeho účtu', (select uuid from generic_code_list where code like 'TYPE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'STATUS', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Stav', 'Stav zákazníckeho účtu', (select uuid from generic_code_list where code like 'STATUS' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Stav', '[EN] Stav zákazníckeho účtu', (select uuid from generic_code_list where code like 'STATUS' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ACTIVATION_AT', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dátum aktivácie', 'Dátum aktivácie', (select uuid from generic_code_list where code like 'ACTIVATION_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Dátum aktivácie', '[EN] Dátum aktivácie', (select uuid from generic_code_list where code like 'ACTIVATION_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REGISTRATION_AT', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dátum registrácie', 'Dátum registrácie', (select uuid from generic_code_list where code like 'REGISTRATION_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Dátum registrácie', '[EN] Dátum registrácie', (select uuid from generic_code_list where code like 'REGISTRATION_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'LOCK_UNTIL', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Uzamknutý do', 'Uzamknutý do', (select uuid from generic_code_list where code like 'LOCK_UNTIL' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Uzamknutý do', '[EN] Uzamknutý do', (select uuid from generic_code_list where code like 'LOCK_UNTIL' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'LOGIN_SUCCESS_AT', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Posledné prihlásenie', 'Posledné prihlásenie', (select uuid from generic_code_list where code like 'LOGIN_SUCCESS_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Posledné prihlásenie', '[EN] Posledné prihlásenie', (select uuid from generic_code_list where code like 'LOGIN_SUCCESS_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'LOGIN_UNSUCCESS_AT', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Posledné neúspešné prihlásenie', 'Posledné neúspešné prihlásenie', (select uuid from generic_code_list where code like 'LOGIN_UNSUCCESS_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Posledné neúspešné prihlásenie', '[EN] Posledné neúspešné prihlásenie', (select uuid from generic_code_list where code like 'LOGIN_UNSUCCESS_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'PASSWORD_UPDATED_AT', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dátum aktualizácie hesla', 'Dátum aktualizácie hesla', (select uuid from generic_code_list where code like 'PASSWORD_UPDATED_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Dátum aktualizácie hesla', '[EN] Dátum aktualizácie hesla', (select uuid from generic_code_list where code like 'PASSWORD_UPDATED_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DEACTIVATION_REASON', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dôvod deaktivácie', 'Dôvod deaktivácie', (select uuid from generic_code_list where code like 'DEACTIVATION_REASON' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Dôvod deaktivácie', '[EN] Dôvod deaktivácie', (select uuid from generic_code_list where code like 'DEACTIVATION_REASON' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'LOCALE', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Jazyk', 'Jazyk', (select uuid from generic_code_list where code like 'LOCALE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Jazyk', '[EN] Jazyk', (select uuid from generic_code_list where code like 'LOCALE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'PHONE', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Telefónne číslo', 'Telefónne číslo', (select uuid from generic_code_list where code like 'PHONE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Telefónne číslo', '[EN] Telefónne číslo', (select uuid from generic_code_list where code like 'PHONE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'FIRST_NAME', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Meno', 'Meno', (select uuid from generic_code_list where code like 'FIRST_NAME' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Meno', '[EN] Meno', (select uuid from generic_code_list where code like 'FIRST_NAME' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'LAST_NAME', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Priezvisko', 'Priezvisko', (select uuid from generic_code_list where code like 'LAST_NAME' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Priezvisko', '[EN] Priezvisko', (select uuid from generic_code_list where code like 'LAST_NAME' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CREATED_AT', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dátum vytvorenia účtu', 'Dátum vytvorenia účtu', (select uuid from generic_code_list where code like 'CREATED_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Dátum vytvorenia účtu', '[EN] Dátum vytvorenia účtu', (select uuid from generic_code_list where code like 'CREATED_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------
------------------ BP --------------------------
------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_QUEUE', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Typ obsluhy', 'Typ obsluhy', (select uuid from generic_code_list where code like 'BP_QUEUE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Typ obsluhy', '[EN] Typ obsluhy', (select uuid from generic_code_list where code like 'BP_QUEUE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_COMPANY_REGISTRATION_NUMBER', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'IČO', 'IČO', (select uuid from generic_code_list where code like 'BP_COMPANY_REGISTRATION_NUMBER' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] IČO', '[EN] IČO', (select uuid from generic_code_list where code like 'BP_COMPANY_REGISTRATION_NUMBER' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_TAX_ID_NUMBER', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'DIČ', 'DIČ', (select uuid from generic_code_list where code like 'BP_TAX_ID_NUMBER' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] DIČ', '[EN] DIČ', (select uuid from generic_code_list where code like 'BP_TAX_ID_NUMBER' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_VAT_REGISTRATION_NUMBER', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'IČ DPH', 'IČ DPH', (select uuid from generic_code_list where code like 'BP_VAT_REGISTRATION_NUMBER' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] IČ DPH', '[EN] IČ DPH', (select uuid from generic_code_list where code like 'BP_VAT_REGISTRATION_NUMBER' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_NAME', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Názov', 'Názov obchodného partnera', (select uuid from generic_code_list where code like 'BP_NAME' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Názov', '[EN] Názov obchodného partnera', (select uuid from generic_code_list where code like 'BP_NAME' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_FIRST_NAME', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Meno', 'Meno obchodného partnera', (select uuid from generic_code_list where code like 'BP_FIRST_NAME' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Meno', '[EN] Meno obchodného partnera', (select uuid from generic_code_list where code like 'BP_FIRST_NAME' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_LAST_NAME', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Priezvisko', 'Priezvisko obchodného partnera', (select uuid from generic_code_list where code like 'BP_LAST_NAME' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Priezvisko', '[EN] Priezvisko obchodného partnera', (select uuid from generic_code_list where code like 'BP_LAST_NAME' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_EMAIL', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Email', 'Email', (select uuid from generic_code_list where code like 'BP_EMAIL' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Email', '[EN] Email', (select uuid from generic_code_list where code like 'BP_EMAIL' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_PHONE', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Telefónne číslo', 'Telefónne číslo', (select uuid from generic_code_list where code like 'BP_PHONE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Telefónne číslo', '[EN] Telefónne číslo', (select uuid from generic_code_list where code like 'BP_PHONE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_SYNCHRONIZATION_AT', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Posledná synchronizácia', 'Posledná synchronizácia', (select uuid from generic_code_list where code like 'BP_SYNCHRONIZATION_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Posledná synchronizácia', '[EN] Posledná synchronizácia', (select uuid from generic_code_list where code like 'BP_SYNCHRONIZATION_AT' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_PRIMARY_STREET', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Ulica', 'Ulica', (select uuid from generic_code_list where code like 'BP_PRIMARY_STREET' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Ulica', '[EN] Ulica', (select uuid from generic_code_list where code like 'BP_PRIMARY_STREET' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_PRIMARY_STREET_NUMBER', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Číslo ulice', 'Číslo ulice', (select uuid from generic_code_list where code like 'BP_PRIMARY_STREET_NUMBER' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Číslo ulice', '[EN] Číslo ulice', (select uuid from generic_code_list where code like 'BP_PRIMARY_STREET_NUMBER' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_PRIMARY_CITY', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mesto', 'Mesto', (select uuid from generic_code_list where code like 'BP_PRIMARY_CITY' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Mesto', '[EN] Mesto', (select uuid from generic_code_list where code like 'BP_PRIMARY_CITY' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_PRIMARY_ZIP_CODE', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'PSČ', 'PSČ', (select uuid from generic_code_list where code like 'BP_PRIMARY_ZIP_CODE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] PSČ', '[EN] PSČ', (select uuid from generic_code_list where code like 'BP_PRIMARY_ZIP_CODE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_PRIMARY_COUNTRY', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Krajina', 'Krajina', (select uuid from generic_code_list where code like 'BP_PRIMARY_COUNTRY' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Krajina', '[EN] Krajina', (select uuid from generic_code_list where code like 'BP_PRIMARY_COUNTRY' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_ACCOUNT_MANAGER', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Manažér účtu', 'Manažér účtu', (select uuid from generic_code_list where code like 'BP_ACCOUNT_MANAGER' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Manažér účtu', '[EN] Manažér účtu', (select uuid from generic_code_list where code like 'BP_ACCOUNT_MANAGER' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_APPROVAL', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Súhlas', 'Súhlas', (select uuid from generic_code_list where code like 'BP_APPROVAL' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Súhlas', '[EN] Súhlas', (select uuid from generic_code_list where code like 'BP_APPROVAL' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BP_APPROVAL_TYPE', 'CUSTOMER_ACCOUNT_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Typ súhlasu', 'Typ súhlasu', (select uuid from generic_code_list where code like 'BP_APPROVAL_TYPE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Typ súhlasu', '[EN] Typ súhlasu', (select uuid from generic_code_list where code like 'BP_APPROVAL_TYPE' and type = 'CUSTOMER_ACCOUNT_EXPORT_COLUMN'));


------------------------------------------------------------------------------------------------
----------------------------------- AUDIT_LOG_EXPORT_COLUMN ------------------------------------
------------------------------------------------------------------------------------------------


INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CREATED_AT', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dátum vytvorenia', 'Dátum vytvorenia', (select uuid from generic_code_list where code like 'CREATED_AT' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Dátum vytvorenia', '[EN] Dátum vytvorenia', (select uuid from generic_code_list where code like 'CREATED_AT' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CODE', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kód záznamu', 'Kód záznamu', (select uuid from generic_code_list where code like 'CODE' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Kód záznamu', '[EN] Kód záznamu', (select uuid from generic_code_list where code like 'CODE' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ENTITY_TYPE', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Typ entity', 'Typ entity', (select uuid from generic_code_list where code like 'ENTITY_TYPE' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Typ entity', '[EN] Typ entity', (select uuid from generic_code_list where code like 'ENTITY_TYPE' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ENTITY_ITEM', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Položka entity', 'Položka entity', (select uuid from generic_code_list where code like 'ENTITY_ITEM' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Položka entity', '[EN] Položka entity', (select uuid from generic_code_list where code like 'ENTITY_ITEM' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ENTITY_REFERENCE', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Referencia na entitu', 'Referencia na entitu', (select uuid from generic_code_list where code like 'ENTITY_REFERENCE' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Referencia na entitu', '[EN] Referencia na entitu', (select uuid from generic_code_list where code like 'ENTITY_REFERENCE' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EMPLOYEE_LOGIN', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Prihlasovacie meno zamestnanca', 'Prihlasovacie meno zamestnanca', (select uuid from generic_code_list where code like 'EMPLOYEE_LOGIN' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Prihlasovacie meno zamestnanca', '[EN] Prihlasovacie meno zamestnanca', (select uuid from generic_code_list where code like 'EMPLOYEE_LOGIN' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EMPLOYEE_NAME', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Meno zamestnanca', 'Meno zamestnanca', (select uuid from generic_code_list where code like 'EMPLOYEE_NAME' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Meno zamestnanca', '[EN] Meno zamestnanca', (select uuid from generic_code_list where code like 'EMPLOYEE_NAME' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EMPLOYEE_EMAIL', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Email zamestnanca', 'Email zamestnanca', (select uuid from generic_code_list where code like 'EMPLOYEE_EMAIL' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Email zamestnanca', '[EN] Email zamestnanca', (select uuid from generic_code_list where code like 'EMPLOYEE_EMAIL' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EMPLOYEE_PRESENT', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Prítomný zamestnanec', 'Prítomný zamestnanec', (select uuid from generic_code_list where code like 'EMPLOYEE_PRESENT' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Prítomný zamestnanec', '[EN] Prítomný zamestnanec', (select uuid from generic_code_list where code like 'EMPLOYEE_PRESENT' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_NAME', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Meno obchodného partnera', 'Meno obchodného partnera', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_NAME' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Meno obchodného partnera', '[EN] Meno obchodného partnera', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_NAME' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_EXTERNAL_ID', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Číslo obchodného partnera', 'Číslo obchodného partnera', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_EXTERNAL_ID' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Číslo obchodného partnera', '[EN] Číslo obchodného partnera', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_EXTERNAL_ID' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'RELATED_CUSTOMER_NAME', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Meno zákazníka z dotknutého účtu', 'Meno zákazníka z dotknutého účtu', (select uuid from generic_code_list where code like 'RELATED_CUSTOMER_NAME' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Meno zákazníka z dotknutého účtu', '[EN] Meno zákazníka z dotknutého účtu', (select uuid from generic_code_list where code like 'RELATED_CUSTOMER_NAME' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'RELATED_CUSTOMER_EMAIL', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Email pre dotknutý zákaznícky účet', 'Email pre dotknutý zákaznícky účet', (select uuid from generic_code_list where code like 'RELATED_CUSTOMER_EMAIL' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Email pre dotknutý zákaznícky účet', '[EN] Email pre dotknutý zákaznícky účet', (select uuid from generic_code_list where code like 'RELATED_CUSTOMER_EMAIL' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'LOGGED_CUSTOMER_NAME', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Meno prihláseného zákazníka', 'Meno prihláseného zákazníka', (select uuid from generic_code_list where code like 'LOGGED_CUSTOMER_NAME' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Meno prihláseného zákazníka', '[EN] Meno prihláseného zákazníka', (select uuid from generic_code_list where code like 'LOGGED_CUSTOMER_NAME' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'LOGGED_CUSTOMER_EMAIL', 'AUDIT_LOG_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Email prihláseného zákazníka', 'Email prihláseného zákazníka', (select uuid from generic_code_list where code like 'LOGGED_CUSTOMER_EMAIL' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Email prihláseného zákazníka', '[EN] Email prihláseného zákazníka', (select uuid from generic_code_list where code like 'LOGGED_CUSTOMER_EMAIL' and type = 'AUDIT_LOG_EXPORT_COLUMN'));

