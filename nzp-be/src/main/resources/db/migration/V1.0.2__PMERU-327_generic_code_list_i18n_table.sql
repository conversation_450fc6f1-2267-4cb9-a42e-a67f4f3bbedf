CREATE TABLE generic_code_list_i18n
(
    uuid            uuid                      NOT NULL,
    created_at      TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at      TIMESTAMP WITH TIME ZONE  NOT NULL,
    version         integer                    NOT NULL,
    locale          CHARACTER VARYING(8)      NOT NULL,
    name            <PERSON><PERSON><PERSON><PERSON>R VARYING(255),
    description     CHARACTER VARYING(1024),
    code_list_uuid  uuid     NOT NULL,

    CONSTRAINT pk_generic_code_list_i18n PRIMARY KEY (uuid),
    CONSTRAINT fk_generic_code_list_i18n_codelist_uuid FOREIGN KEY (code_list_uuid)
        REFERENCES generic_code_list (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE UNIQUE INDEX idx_generic_code_list_i18n_cl_locale on generic_code_list_i18n(code_list_uuid, locale);