ALTER TABLE business_partner_consent RENAME TO business_partner_consent_old;

CREATE TABLE business_partner_consent
(
    uuid                        uuid                      NOT NULL,
    created_at                  TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                  TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                     integer                   NOT NULL,

    communication_channel       CHARACTER VARYING(50)     NOT NULL,
    consent                     VARCHAR(50)  			  NOT NULL,

    status                      VARCHAR(50)               NOT NULL,
    business_partner_id         VARCHAR(50)               NOT NULL,

    synchronization_at          TIMESTAMP WITH TIME ZONE  NOT NULL,
    synchronization_log_uuid    uuid                      NOT NULL,

    valid_from                  TIMESTAMP WITH TIME ZONE  NOT NULL,
    valid_to                    TIMESTAMP WITH TIME ZONE  NOT NULL,

    consent_from                TIMESTAMP WITH TIME ZONE,
    consent_to                  TIMESTAMP WITH TIME ZONE,
    partner_consent             BOOLEAN DEFAULT FALSE,
    reminder_send_at            TIMESTAMP WITH TIME ZONE,
    locked_by                   <PERSON>RCHA<PERSON>(50),
    retry_count                 INTEGER,
    CONSTRAINT pk_business_partner_consent PRIMARY KEY (uuid)
);

-- Indexes
CREATE INDEX idx_business_partner_approval_business_partner_id ON business_partner_consent(business_partner_id);

update generic_code_list set code = 'BP_CONSENT_COMMUNICATION_CHANNEL' where code = 'BP_CONSENT_TYPE';

update generic_code_list_i18n set name = 'Communication channel', description = 'Communication channel' where locale = 'EN' and code_list_uuid = (select uuid from generic_code_list where code = 'BP_CONSENT_COMMUNICATION_CHANNEL');
update generic_code_list_i18n set name = 'Komunikačný kanál', description = 'Komunikačný kanál' where locale = 'SK' and code_list_uuid = (select uuid from generic_code_list where code = 'BP_CONSENT_COMMUNICATION_CHANNEL');