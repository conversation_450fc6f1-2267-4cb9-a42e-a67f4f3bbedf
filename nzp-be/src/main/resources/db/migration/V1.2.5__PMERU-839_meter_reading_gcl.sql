
INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'VT', 'METER_READING_REGISTER_KIND', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ST', 'METER_READING_REGISTER_KIND', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PL', 'METER_READING_REGISTER_KIND', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NT', 'METER_READING_REGISTER_KIND', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'C2', 'METER_READING_REGISTER_KIND', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'C1', 'METER_READING_REGISTER_KIND', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'B2', 'METER_READING_REGISTER_KIND', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'B1', 'METER_READING_REGISTER_KIND', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'A2', 'METER_READING_REGISTER_KIND', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'A1', 'METER_READING_REGISTER_KIND', null, null, null);


INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Vysoká tarifa', null, (select uuid from generic_code_list where code like 'VT' and type = 'METER_READING_REGISTER_KIND')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Špičková tarifa', null, (select uuid from generic_code_list where code like 'ST' and type = 'METER_READING_REGISTER_KIND')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Plynomer 1 rada', null, (select uuid from generic_code_list where code like 'PL' and type = 'METER_READING_REGISTER_KIND')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Nízka tarifa', null, (select uuid from generic_code_list where code like 'NT' and type = 'METER_READING_REGISTER_KIND')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Prepočitávač meranie C 2radový', null, (select uuid from generic_code_list where code like 'C2' and type = 'METER_READING_REGISTER_KIND')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Prepočitávač meranie C 1radový', null, (select uuid from generic_code_list where code like 'C1' and type = 'METER_READING_REGISTER_KIND')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Prepočitávač meranie B 2radový', null, (select uuid from generic_code_list where code like 'B2' and type = 'METER_READING_REGISTER_KIND')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Prepočitávač meranie B 1radový', null, (select uuid from generic_code_list where code like 'B1' and type = 'METER_READING_REGISTER_KIND')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Prepočitávač meranie a 1radový', null, (select uuid from generic_code_list where code like 'A2' and type = 'METER_READING_REGISTER_KIND')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Prepočitávač meranie A 1radový', null, (select uuid from generic_code_list where code like 'A1' and type = 'METER_READING_REGISTER_KIND'));