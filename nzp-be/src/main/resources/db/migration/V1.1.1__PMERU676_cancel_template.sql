INSERT INTO config_parameter
    (created_at, updated_at, version, id, value, target, type)
VALUES
    (now(), now(), 1, 'customer.request.cancelEmail', 'zia<PERSON><PERSON>@spp.sk', 'UI', 'STRING');
------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, execution_type, name, priority,
    description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REQUEST_CANCEL_REQUEST', 'ACTIVE', 'CUSTOMER_SYSTEM', 'AUTOMATIC', '<PERSON><PERSON><PERSON><PERSON> zia<PERSON>',
    'LOW', 'Notifikacia ohladom zrusenia ziadosti', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, status, header, email_body,
    email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'Zrušenie žiadosti zákazníka', 'Zákazník z id ${attributes.requestExternalId} požiadal o zrušenie zaregistrovanej žiadosti: ${attributes.customerRequest}',
    'Zrušenie žiadosti zákazníka', null, (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1), 'SK');
------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
VALUES ((select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
VALUES ((select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
VALUES ((select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
VALUES ((select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
   VALUES ((select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
   VALUES ((select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
   VALUES ((select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
   VALUES ((select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
   VALUES ((select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
   VALUES ((select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
   VALUES ((select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
   VALUES ((select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
   VALUES ((select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
   VALUES ((select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
   VALUES ((select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
   VALUES ((select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1));