-----------------------------------------------------------------------------------------------
---------------- RENAME AuditLogCodes - account not found in process of login
-----------------------------------------------------------------------------------------------

UPDATE generic_code_list
SET code = 'IDM_CUSTOMER_SEARCH_ID_ATTEMPT', updated_at = now(), version = version + 1
where code = 'CUSTOMER_NOTFOUND_ID_LOGIN_ATTEMPT' and type = 'AUDIT_LOG_CODE';
UPDATE generic_code_list_i18n
SET updated_at = now(),
    version = version + 1,
    name = 'Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa identifikátora',
    description = 'Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa identifikátora'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE code LIKE 'IDM_CUSTOMER_SEARCH_ID_ATTEMPT' AND type = 'AUDIT_LOG_CODE')
  AND locale = 'SK';
UPDATE generic_code_list_i18n
SET updated_at = now(),
    version = version + 1,
    name = '[EN] Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa identifikátora',
    description = '[EN] Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa identifikátora'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE code LIKE 'IDM_CUSTOMER_SEARCH_ID_ATTEMPT' AND type = 'AUDIT_LOG_CODE')
  AND locale = 'EN';

UPDATE generic_code_list
SET code = 'IDM_CUSTOMER_SEARCH_EMAIL_ATTEMPT', updated_at = now(), version = version + 1
where code = 'CUSTOMER_NOTFOUND_EMAIL_LOGIN_ATTEMPT' and type = 'AUDIT_LOG_CODE';
UPDATE generic_code_list_i18n
SET updated_at = now(),
    version = version + 1,
    name = 'Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa emailovej adresy',
    description = 'Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa emailovej adresy'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE code LIKE 'IDM_CUSTOMER_SEARCH_EMAIL_ATTEMPT' AND type = 'AUDIT_LOG_CODE')
  AND locale = 'SK';
UPDATE generic_code_list_i18n
SET updated_at = now(),
    version = version + 1,
    name = '[EN] Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa emailovej adresy',
    description = '[EN] Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa emailovej adresy'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE code LIKE 'IDM_CUSTOMER_SEARCH_EMAIL_ATTEMPT' AND type = 'AUDIT_LOG_CODE')
  AND locale = 'EN';

UPDATE generic_code_list
SET code = 'IDM_CUSTOMER_SEARCH_PHONE_ATTEMPT', updated_at = now(), version = version + 1
where code = 'CUSTOMER_NOTFOUND_PHONE_LOGIN_ATTEMPT' and type = 'AUDIT_LOG_CODE';
UPDATE generic_code_list_i18n
SET updated_at = now(),
    version = version + 1,
    name = 'Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa telefónneho čísla',
    description = 'Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa telefónneho čísla'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE code LIKE 'IDM_CUSTOMER_SEARCH_PHONE_ATTEMPT' AND type = 'AUDIT_LOG_CODE')
  AND locale = 'SK';
UPDATE generic_code_list_i18n
SET updated_at = now(),
    version = version + 1,
    name = '[EN] Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa telefónneho čísla',
    description = '[EN] Prihlasovanie: Vyhľadanie zákazníckeho účtu telefónneho čísla'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE code LIKE 'IDM_CUSTOMER_SEARCH_PHONE_ATTEMPT' AND type = 'AUDIT_LOG_CODE')
  AND locale = 'EN';

UPDATE generic_code_list
SET code = 'IDM_CUSTOMER_SEARCH_FACEBOOK_ID_ATTEMPT', updated_at = now(), version = version + 1
where code = 'CUSTOMER_NOTFOUND_FACEBOOK_LOGIN_ATTEMPT' and type = 'AUDIT_LOG_CODE';
UPDATE generic_code_list_i18n
SET updated_at = now(),
    version = version + 1,
    name = 'Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa facebook identifikátora',
    description = 'Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa facebook identifikátora'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE code LIKE 'IDM_CUSTOMER_SEARCH_FACEBOOK_ID_ATTEMPT' AND type = 'AUDIT_LOG_CODE')
  AND locale = 'SK';
UPDATE generic_code_list_i18n
SET updated_at = now(),
    version = version + 1,
    name = '[EN] Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa facebook identifikátora',
    description = '[EN] Prihlasovanie: Vyhľadanie zákazníckeho účtu facebook identifikátora'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE code LIKE 'IDM_CUSTOMER_SEARCH_FACEBOOK_ID_ATTEMPT' AND type = 'AUDIT_LOG_CODE')
  AND locale = 'EN';

UPDATE generic_code_list
SET code = 'IDM_CUSTOMER_SEARCH_GOOGLE_ID_ATTEMPT', updated_at = now(), version = version + 1
where code = 'CUSTOMER_NOTFOUND_GOOGLE_LOGIN_ATTEMPT' and type = 'AUDIT_LOG_CODE';
UPDATE generic_code_list_i18n
SET updated_at = now(),
    version = version + 1,
    name = 'Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa google identifikátora',
    description = 'Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa google identifikátora'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE code LIKE 'IDM_CUSTOMER_SEARCH_GOOGLE_ID_ATTEMPT' AND type = 'AUDIT_LOG_CODE')
  AND locale = 'SK';
UPDATE generic_code_list_i18n
SET updated_at = now(),
    version = version + 1,
    name = '[EN] Prihlasovanie: Vyhľadanie zákazníckeho účtu podľa google identifikátora',
    description = '[EN] Prihlasovanie: Vyhľadanie zákazníckeho účtu google identifikátora'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE code LIKE 'IDM_CUSTOMER_SEARCH_GOOGLE_ID_ATTEMPT' AND type = 'AUDIT_LOG_CODE')
  AND locale = 'EN';

UPDATE generic_code_list
SET code = 'IDM_EMPLOYEE_SEARCH_ATTEMPT', updated_at = now(), version = version + 1
where code = 'EMPLOYEE_NOTFOUND_LOGIN_ATTEMPT' and type = 'AUDIT_LOG_CODE';
UPDATE generic_code_list_i18n
SET updated_at = now(),
    version = version + 1,
    name = 'Prihlasovanie: Vyhľadanie zamestnaneckého účtu podľa loginu',
    description = 'Prihlasovanie: Vyhľadanie zamestnaneckého účtu podľa loginu'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE code LIKE 'IDM_EMPLOYEE_SEARCH_ATTEMPT' AND type = 'AUDIT_LOG_CODE')
  AND locale = 'SK';
UPDATE generic_code_list_i18n
SET updated_at = now(),
    version = version + 1,
    name = '[EN] Prihlasovanie: Vyhľadanie zamestnaneckého účtu podľa loginu',
    description = '[EN] Prihlasovanie: Vyhľadanie zamestnaneckého účtu podľa loginu'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE code LIKE 'IDM_EMPLOYEE_SEARCH_ATTEMPT' AND type = 'AUDIT_LOG_CODE')
  AND locale = 'EN';

UPDATE audit_log
SET code = 'IDM_CUSTOMER_SEARCH_ID_ATTEMPT'
WHERE code = 'CUSTOMER_NOTFOUND_ID_LOGIN_ATTEMPT';

UPDATE audit_log
SET code = 'IDM_CUSTOMER_SEARCH_EMAIL_ATTEMPT'
WHERE code = 'CUSTOMER_NOTFOUND_EMAIL_LOGIN_ATTEMPT';

UPDATE audit_log
SET code = 'IDM_CUSTOMER_SEARCH_PHONE_ATTEMPT'
WHERE code = 'CUSTOMER_NOTFOUND_PHONE_LOGIN_ATTEMPT';

UPDATE audit_log
SET code = 'IDM_CUSTOMER_SEARCH_FACEBOOK_ID_ATTEMPT'
WHERE code = 'CUSTOMER_NOTFOUND_FACEBOOK_LOGIN_ATTEMPT';

UPDATE audit_log
SET code = 'IDM_CUSTOMER_SEARCH_GOOGLE_ID_ATTEMPT'
WHERE code = 'CUSTOMER_NOTFOUND_GOOGLE_LOGIN_ATTEMPT';

UPDATE audit_log
SET code = 'IDM_EMPLOYEE_SEARCH_ATTEMPT'
WHERE code = 'EMPLOYEE_NOTFOUND_LOGIN_ATTEMPT';
