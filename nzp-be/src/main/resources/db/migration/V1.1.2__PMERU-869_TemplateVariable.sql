-- add template_variable
insert into template_variable (uuid, created_at, updated_at , "name" , "type" , variable , "version" )
 values (uuid_generate_v4(), now(), now(), '<PERSON>známka k <PERSON>', 'STRING', 'attributes.customerRequestNote', 1);

-- add link
insert into template_notification_variable (notification_template_uuid , template_variable_uuid ) values (
    (select nt.uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_NOTE_CREATE'),
    (select tv.uuid from template_variable tv where tv.variable = 'attributes.customerRequestNote')
)


