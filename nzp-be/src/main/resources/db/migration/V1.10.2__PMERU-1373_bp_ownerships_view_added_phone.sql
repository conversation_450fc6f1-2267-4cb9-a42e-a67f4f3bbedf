DROP VIEW v_business_partner_extended_ownerships;
CREATE VIEW v_business_partner_extended_ownerships AS (

    SELECT bpo.business_partner_id AS business_partner_id, bpo.customer_account_uuid AS customer_account_uuid, bpo.type AS type, NULL AS phone
    FROM business_partner_ownership bpo

UNION

    SELECT udp.business_partner_id AS business_partner_id, udpo.customer_account_uuid AS customer_account_uuid, udpo.type AS type, NULL AS phone
    FROM united_delivery_point_ownership udpo
    JOIN united_delivery_point udp ON udp.uuid = udpo.united_delivery_point_uuid
    WHERE udpo.type = 'SHARING'

UNION

    SELECT bppr.business_partner_id AS business_partner_id, bppr.customer_account_id AS customer_account_uuid, NULL AS type, bppr.phone AS phone
    FROM business_partner_pairing_request bppr
    WHERE status = 'VERIFY'
);