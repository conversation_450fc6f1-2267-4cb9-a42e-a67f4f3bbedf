
-- move referencie to other place
alter table delivery_point drop column contract_id;
alter table delivery_point drop column business_partner_id;
alter table delivery_point drop column united_delivery_point_id;

-- to contract
alter table contract add column business_partner_id character varying(50);
alter table contract add column delivery_point_id character varying(50);
alter table contract add column united_delivery_point_id uuid;

-- Add indexess
create index idx_contract_business_partner_id on contract(business_partner_id);
create index idx_contract_delivery_point_id on contract(delivery_point_id);
create index idx_contract_united_delivery_point_id on contract(united_delivery_point_id);

-- FK
alter table contract add constraint fk_contract_united_delivery_point
FOREIGN KEY (united_delivery_point_id) REFERENCES united_delivery_point (uuid);

-- CREATE VIEW
create or replace view v_delivery_point as (
    select
    dp.id,
    dp.created_at,
    dp.updated_at,
    dp."version",
    dp.external_id,
    dp."type",
    dp.pod,
    dp.eic,
    dp.street,
    dp.street_number,
    dp.city,
    dp.zip_code,
    dp.country,
    dp.synchronization_log_uuid,
    dp.synchronization_at,
    dp.valid_from,
    dp.valid_to,
    dp.device_number,
    dp.notification_rkmrk_checked_at,
    dp.notification_zm_checked_at,
    dp.notification_dmm_checked_at,
    c.id as contract_id,
    c.business_partner_id as business_partner_id,
    c.united_delivery_point_id as united_delivery_point_id
    from delivery_point dp
    join contract c on c.delivery_point_id = dp.id and c.effective_from < current_date and c.effective_to > current_date);
