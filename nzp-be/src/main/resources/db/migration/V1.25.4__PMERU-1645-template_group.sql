UPDATE notification_template SET template_group ='INVOICE' where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE';
UPDATE notification_template SET template_group ='INVOICE' where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED';
UPDATE notification_template SET template_group ='INVOICE' where code = 'CUSTOMER_INVOICE_BEFORE_DUE';
UPDATE notification_template SET template_group ='INVOICE' where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED';
UPDATE notification_template SET template_group ='INVOICE' where code = 'CUSTOMER_INVOICE_ISSUED';
UPDATE notification_template SET template_group ='INVOICE' where code = 'CUSTOMER_INVOICE_SAP_ISSUED';
UPDATE notification_template SET template_group ='CUSTOMER_REQUEST' where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST';
UPDATE notification_template SET template_group ='CUSTOMER_REQUEST' where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER';
UPDATE notification_template SET template_group ='CUSTOMER_REQUEST' where code = 'CUSTOMER_REQUEST_CANCELLED_CUSTOMER';
UPDATE notification_template SET template_group ='CUSTOMER_REQUEST' where code = 'CUSTOMER_REQUEST_NOTE_CREATE';
UPDATE notification_template SET template_group ='CUSTOMER_REQUEST' where code = 'CUSTOMER_REQUEST_REGISTER';
UPDATE notification_template SET template_group ='CUSTOMER_REQUEST' where code = 'CUSTOMER_REQUEST_STATUS_CHANGE';
UPDATE notification_template SET template_group ='SHARING' where code = 'CUSTOMER_SHARING_CONSUMER_GRANT';
UPDATE notification_template SET template_group ='SHARING' where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE';
UPDATE notification_template SET template_group ='SHARING' where code = 'CUSTOMER_SHARING_INVITATION';
UPDATE notification_template SET template_group ='SHARING' where code = 'CUSTOMER_SHARING_OWNER_GRANT';
UPDATE notification_template SET template_group ='SHARING' where code = 'CUSTOMER_SHARING_OWNER_REVOKE';
UPDATE notification_template SET template_group ='LIMIT_OVERFLOW' where code = 'DELIVERY_POINT_AM_CHECK_DMM';
UPDATE notification_template SET template_group ='LIMIT_OVERFLOW' where code = 'DELIVERY_POINT_AM_CHECK_MRK';
UPDATE notification_template SET template_group ='LIMIT_OVERFLOW' where code = 'DELIVERY_POINT_AM_CHECK_RK';
UPDATE notification_template SET template_group ='LIMIT_OVERFLOW' where code = 'DELIVERY_POINT_CHECK_DMM';
UPDATE notification_template SET template_group ='LIMIT_OVERFLOW' where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY';
UPDATE notification_template SET template_group ='LIMIT_OVERFLOW' where code = 'DELIVERY_POINT_CHECK_MRK';
UPDATE notification_template SET template_group ='LIMIT_OVERFLOW' where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY';
UPDATE notification_template SET template_group ='LIMIT_OVERFLOW' where code = 'DELIVERY_POINT_CHECK_RK';
UPDATE notification_template SET template_group ='LIMIT_OVERFLOW' where code = 'DELIVERY_POINT_CHECK_ZM';