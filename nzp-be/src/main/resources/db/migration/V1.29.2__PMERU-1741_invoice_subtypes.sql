-- Rename GCL i18ns "<PERSON><PERSON><PERSON><PERSON> stopka" -> "Nekomoditný produkt"
UPDATE generic_code_list_i18n
SET name = 'Nekomoditný produkt', updated_at = now()
WHERE code_list_uuid = (
    SELECT uuid FROM generic_code_list
    WHERE type = 'INVOICE_TYPE'
    AND code = 'M2')
AND locale = 'sk';

UPDATE generic_code_list_i18n
SET name = '[EN] Nekomoditný produkt', updated_at = now()
WHERE code_list_uuid = (
    SELECT uuid FROM generic_code_list
    WHERE type = 'INVOICE_TYPE'
    AND code = 'M2')
AND locale = 'en';

-- Add "invoice_subtype" to GCL & Invoice
ALTER TABLE invoice                 ADD COLUMN sub_type              CHARACTER VARYING(256);

-- Add "NP_US" & "NP_CE" M2 subtypes to GCL
INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES (uuid_generate_v4(), now(), now(), 1, 'NP_US', 'INVOICE_SUB_TYPE');

INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES (uuid_generate_v4(), now(), now(), 1, 'NP_CE', 'INVOICE_SUB_TYPE');

-- Add i18n for "NP_US" & "NP_CE" subtypes
INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Uhlíková stopka ZP', (SELECT uuid FROM generic_code_list WHERE type = 'INVOICE_SUB_TYPE' AND code = 'NP_US'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Uhlíková stopka ZP', (SELECT uuid FROM generic_code_list WHERE type = 'INVOICE_SUB_TYPE' AND code = 'NP_US'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Čistá elektrina', (SELECT uuid FROM generic_code_list WHERE type = 'INVOICE_SUB_TYPE' AND code = 'NP_CE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Čistá elektrina', (SELECT uuid FROM generic_code_list WHERE type = 'INVOICE_SUB_TYPE' AND code = 'NP_CE'));