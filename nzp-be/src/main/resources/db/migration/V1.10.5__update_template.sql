update notification_template_i18n
set email_body = '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">'
|| regexp_replace(email_body, 'Vážený zákazník, |Vážený manažér predaja, |Dobrý deň, |Vážený zamestnanec, |Vaše SPP', '') ||
'</@spp.notification_email_template>'
where email_body is not null and email_body != '' and lower(locale) = 'sk' and notification_template_id in (select nt.uuid from notification_template nt where nt.execution_type = 'AUTOMATIC');

update notification_template_i18n
set email_body = '<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">'
|| regexp_replace(email_body, 'Vážený zákazník, |Vážený manažér predaja, |Dobr<PERSON>, |Vážený zamestnanec, |Vaše SPP', '') ||
'</@spp.notification_email_template>'
where email_body is not null and email_body != '' and lower(locale) = 'en' and notification_template_id in (select nt.uuid from notification_template nt where nt.execution_type = 'AUTOMATIC');