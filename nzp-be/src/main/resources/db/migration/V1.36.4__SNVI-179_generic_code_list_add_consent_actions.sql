INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACCEPT', 'CONSENT_ACTION', null, null, null);

INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'REJECT', 'CONSENT_ACTION', null, null, null);


INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Potvrdené', null, (select uuid from generic_code_list where code like 'ACCEPT' and type = 'CONSENT_ACTION'));
INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', 'Accept', null, (select uuid from generic_code_list where code like 'ACCEPT' and type = 'CONSENT_ACTION'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Zamietnuté', null, (select uuid from generic_code_list where code like 'REJECT' and type = 'CONSENT_ACTION'));
INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', 'Reject', null, (select uuid from generic_code_list where code like 'REJECT' and type = 'CONSENT_ACTION'));


