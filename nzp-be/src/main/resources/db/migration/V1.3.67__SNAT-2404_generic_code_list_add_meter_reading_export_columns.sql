-- insert values for METER_READING_EXPORT_COLUMN
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CATEGORY', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'VALUE_HIGH', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'VALUE', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'READ_AT', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REGISTER', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'UNITS', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_TYPE', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_ADDRESS_COUNTRY', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_ADDRESS_CITY', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_ADDRESS_STREET', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_ADDRESS_STREET_NUMBER', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_ADDRESS_STREET_ZIP_CODE', 'METER_READING_EXPORT_COLUMN', null, null, null);

-- TODO ADD more

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kategória', 'Kategória', (select uuid from generic_code_list where code like 'CATEGORY' and type = 'METER_READING_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Kategória', '[EN] Kategória', (select uuid from generic_code_list where code like 'CATEGORY' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Hodnota (Vysoká tarifa)', 'Hodnota (Vysoká tarifa)', (select uuid from generic_code_list where code like 'VALUE_HIGH' and type = 'METER_READING_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Hodnota (Vysoká tarifa)', '[EN] Hodnota (Vysoká tarifa)', (select uuid from generic_code_list where code like 'VALUE_HIGH' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Hodnota', 'Hodnota', (select uuid from generic_code_list where code like 'VALUE' and type = 'METER_READING_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Hodnota', '[EN] Hodnota', (select uuid from generic_code_list where code like 'VALUE' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Čas odpočtu', 'Čas odpočtu', (select uuid from generic_code_list where code like 'READ_AT' and type = 'METER_READING_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Čas odpočtu', '[EN] Čas odpočtu', (select uuid from generic_code_list where code like 'READ_AT' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Register', 'Register', (select uuid from generic_code_list where code like 'REGISTER' and type = 'METER_READING_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Register', '[EN] Register', (select uuid from generic_code_list where code like 'REGISTER' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Jednotky', 'Jednotky', (select uuid from generic_code_list where code like 'UNITS' and type = 'METER_READING_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Jednotky', '[EN] Jednotky', (select uuid from generic_code_list where code like 'UNITS' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Typ odberného miesta', 'Typ odberného miesta', (select uuid from generic_code_list where code like 'DELIVERY_POINT_TYPE' and type = 'METER_READING_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Typ odberného miesta', '[EN] Typ odberného miesta', (select uuid from generic_code_list where code like 'DELIVERY_POINT_TYPE' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Krajina', 'Krajina', (select uuid from generic_code_list where code like 'DELIVERY_POINT_ADDRESS_COUNTRY' and type = 'METER_READING_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Krajina', '[EN] Krajina', (select uuid from generic_code_list where code like 'DELIVERY_POINT_ADDRESS_COUNTRY' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Mesto', 'Mesto', (select uuid from generic_code_list where code like 'DELIVERY_POINT_ADDRESS_CITY' and type = 'METER_READING_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Mesto', '[EN] Mesto', (select uuid from generic_code_list where code like 'DELIVERY_POINT_ADDRESS_CITY' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Ulica', 'Ulica', (select uuid from generic_code_list where code like 'DELIVERY_POINT_ADDRESS_STREET' and type = 'METER_READING_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Ulica', '[EN] Ulica', (select uuid from generic_code_list where code like 'DELIVERY_POINT_ADDRESS_STREET' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Číslo', 'Číslo', (select uuid from generic_code_list where code like 'DELIVERY_POINT_ADDRESS_STREET_NUMBER' and type = 'METER_READING_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Číslo', 'Číslo', (select uuid from generic_code_list where code like 'DELIVERY_POINT_ADDRESS_STREET_NUMBER' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'PSČ', 'PSČ', (select uuid from generic_code_list where code like 'DELIVERY_POINT_ADDRESS_STREET_ZIP_CODE' and type = 'METER_READING_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] PSČ', '[EN] PSČ', (select uuid from generic_code_list where code like 'DELIVERY_POINT_ADDRESS_STREET_ZIP_CODE' and type = 'METER_READING_EXPORT_COLUMN'));
