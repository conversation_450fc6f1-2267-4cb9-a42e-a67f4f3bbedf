INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES (uuid_generate_v4(), now(), now(), 1, 'AUDIT_LOG_CODE', 'SCHEDULE_NOTIFICATION_CREATED');

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Spustenie manuálnej notifikácie', (SELECT uuid FROM generic_code_list WHERE type = 'SCHEDULE_NOTIFICATION_CREATED' AND code = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Spustenie manuálnej notifikácie', (SELECT uuid FROM generic_code_list WHERE type = 'SCHEDULE_NOTIFICATION_CREATED' AND code = 'AUDIT_LOG_CODE'));