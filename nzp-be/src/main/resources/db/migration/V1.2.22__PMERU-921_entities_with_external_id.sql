DROP VIEW v_sharing_summary;

-- PREREQUISITY for DB extension -- have to be executed with proper rights
-- create extension if not exists pgcrypto;


create or replace function extIdEncoder(text) returns  text
	 as $$ select replace(replace(replace(encode(digest(CONCAT('Goe9AE2qd0O7UyX',$1), 'sha1'), 'base64'),'+','-'),'/','_'),'=',''); $$
  LANGUAGE SQL
  IMMUTABLE
  RETURNS NULL ON NULL INPUT;


-- drop FK
alter table business_partner_pairing_request drop constraint fk_bppr_delivery_point;
alter table customer_request drop constraint fk_customer_request_point_id;
alter table customer_notification drop constraint fk_notification_delivery_point_id;
alter table customer_notification drop constraint fk_notification_business_partner_id;
alter table customer_request drop constraint fk_customer_request_business_partner_id;

alter table customer_account_challenge_code drop constraint fk_customer_account_challenge_code_business_partner_id;
alter table business_partner_ownership drop constraint fk_business_partner_ownership_business_partner;
alter table business_partner_pairing_request drop constraint fk_bppr_business_partner;
alter table contract_ownership drop constraint fk_contract_ownership_contract;
alter table contract_account_ownership drop constraint fk_contract_account_ownership_contract_account;



-- ADD migration column
ALTER TABLE business_partner_pairing_request ADD COLUMN delivery_point_tmp_id  text;
ALTER TABLE customer_request ADD COLUMN delivery_point_tmp_id  text;
ALTER TABLE customer_notification ADD COLUMN delivery_point_tmp_id  text;
ALTER TABLE customer_notification ADD COLUMN business_partner_tmp_id  text;
ALTER TABLE customer_request ADD COLUMN business_partner_tmp_id  text;

ALTER TABLE customer_account_challenge_code ADD COLUMN business_partner_tmp_id  text;
ALTER TABLE business_partner_ownership ADD COLUMN business_partner_tmp_id  text;
ALTER TABLE business_partner_pairing_request ADD COLUMN business_partner_tmp_id  text;
ALTER TABLE contract_ownership ADD COLUMN contract_tmp_id  text;
ALTER TABLE contract_account_ownership ADD COLUMN contract_account_tmp_id  text;

-- encode existing to migration column
UPDATE customer_request alt SET delivery_point_tmp_id = extIdEncoder(base_alt.external_id) FROM delivery_point base_alt WHERE alt.delivery_point_id is not null and alt.delivery_point_id = base_alt.uuid;
UPDATE customer_notification alt SET delivery_point_tmp_id = extIdEncoder(base_alt.external_id) FROM delivery_point base_alt WHERE alt.delivery_point_id is not null and alt.delivery_point_id = base_alt.uuid;
UPDATE customer_notification alt SET business_partner_tmp_id = extIdEncoder(base_alt.external_id) FROM business_partner base_alt WHERE alt.business_partner_id is not null and alt.business_partner_id = base_alt.uuid;
UPDATE customer_request alt SET business_partner_tmp_id = extIdEncoder(base_alt.external_id) FROM business_partner base_alt WHERE alt.business_partner_id is not null and alt.business_partner_id = base_alt.uuid;

UPDATE customer_account_challenge_code alt SET business_partner_tmp_id = extIdEncoder(base_alt.external_id) FROM business_partner base_alt WHERE alt.business_partner_id is not null and alt.business_partner_id = base_alt.uuid;
UPDATE business_partner_ownership alt SET business_partner_tmp_id = extIdEncoder(base_alt.external_id) FROM business_partner base_alt WHERE alt.business_partner_uuid is not null and alt.business_partner_uuid = base_alt.uuid;
UPDATE business_partner_pairing_request alt SET business_partner_tmp_id = extIdEncoder(base_alt.external_id) FROM business_partner base_alt WHERE alt.business_partner_id is not null and alt.business_partner_id = base_alt.uuid;
UPDATE contract_ownership alt SET contract_tmp_id = extIdEncoder(base_alt.external_id) FROM contract base_alt WHERE alt.contract_uuid is not null and alt.contract_uuid = base_alt.uuid;
UPDATE contract_account_ownership alt SET contract_account_tmp_id = extIdEncoder(base_alt.external_id) FROM contract_account base_alt WHERE alt.contract_account_uuid is not null and alt.contract_account_uuid = base_alt.uuid;



-- drop primary key

alter table invoice drop CONSTRAINT pk_invoice;
alter table invoice alter column uuid drop not null;
update invoice set uuid = null;
alter table invoice rename column uuid to id;
alter table invoice alter column id type  text;
update invoice set id = extIdEncoder(external_id);

alter table meter_reading drop CONSTRAINT pk_meter_reading;
alter table meter_reading alter column uuid drop not null;
update meter_reading set uuid = null;
alter table meter_reading rename column uuid to id;
alter table meter_reading alter column id type  text;
update meter_reading set id = extIdEncoder(CASE WHEN external_id IS NULL THEN uuid_generate_v4()::text ELSE external_ID END);

alter table payment drop CONSTRAINT pk_payment;
alter table payment alter column uuid drop not null;
update payment set uuid = null;
alter table payment rename column uuid to id;
alter table payment alter column id type  text;
update payment set id = extIdEncoder(external_id);

alter table delivery_point drop constraint pk_delivery_point;
alter table delivery_point alter column uuid drop not null;
update delivery_point set uuid = null;
alter table delivery_point rename column uuid to id;
alter table delivery_point alter column id type  text;
update delivery_point set id = extIdEncoder(external_id);

alter table business_partner drop constraint pk_business_partner;
alter table business_partner alter column uuid drop not null;
update business_partner set uuid = null;
alter table business_partner rename column uuid to id;
alter table business_partner alter column id type  text;
update business_partner set id = extIdEncoder(external_id);

alter table contract drop constraint pk_contract;
alter table contract alter column uuid drop not null;
update contract set uuid = null;
alter table contract rename column uuid to id;
alter table contract alter column id type  text;
update contract set id = extIdEncoder(external_id);

alter table contract_account drop constraint pk_contract_account;
alter table contract_account alter column uuid drop not null;
update contract_account set uuid = null;
alter table contract_account rename column uuid to id;
alter table contract_account alter column id type  text;
update contract_account set id = extIdEncoder(external_id);

alter table contract_account rename column business_partner_external_id to business_partner_id;
alter table contract rename column contract_account_external_id to contract_account_id;
alter table contract_version rename column contract_external_id to contract_id;
alter table delivery_point rename column contract_external_id to contract_id;
alter table delivery_point rename column business_partner_external_id to business_partner_id;
alter table delivery_point_version rename column deliver_point_external_id to delivery_point_id;
alter table delivery_point_fact rename column deliver_point_external_id to delivery_point_id;
alter table invoice rename column delivery_point_external_id to delivery_point_id;
alter table meter_reading rename column delivery_point_external_id to delivery_point_id;
alter table invoice rename column payment_req_invoice_ext_id to payment_req_invoice_id;


-- drop old ref
ALTER TABLE customer_request DROP COLUMN delivery_point_id;
ALTER TABLE customer_notification DROP COLUMN delivery_point_id;
ALTER TABLE customer_notification DROP COLUMN business_partner_id;
ALTER TABLE customer_request DROP COLUMN business_partner_id;

ALTER TABLE customer_account_challenge_code DROP COLUMN business_partner_id;
ALTER TABLE business_partner_ownership DROP COLUMN business_partner_uuid;
ALTER TABLE business_partner_pairing_request DROP COLUMN business_partner_id;
ALTER TABLE contract_ownership DROP COLUMN contract_uuid;
ALTER TABLE contract_account_ownership DROP COLUMN contract_account_uuid;

-- rename migration column to ref
ALTER TABLE customer_request RENAME COLUMN delivery_point_tmp_id TO delivery_point_id;
ALTER TABLE customer_notification RENAME COLUMN delivery_point_tmp_id TO delivery_point_id;
ALTER TABLE customer_notification RENAME COLUMN business_partner_tmp_id TO business_partner_id;
ALTER TABLE customer_request RENAME COLUMN business_partner_tmp_id TO business_partner_id;

ALTER TABLE customer_account_challenge_code RENAME COLUMN business_partner_tmp_id TO business_partner_id;
ALTER TABLE business_partner_ownership RENAME COLUMN business_partner_tmp_id TO business_partner_id;
ALTER TABLE business_partner_pairing_request RENAME COLUMN business_partner_tmp_id TO business_partner_id;
ALTER TABLE contract_ownership RENAME COLUMN contract_tmp_id TO contract_id;
ALTER TABLE contract_account_ownership RENAME COLUMN contract_account_tmp_id TO contract_account_id;

-- ADD PK

alter table delivery_point ADD CONSTRAINT pk_delivery_point PRIMARY KEY (id);
alter table business_partner add CONSTRAINT pk_business_partner PRIMARY KEY (id);
alter table contract add CONSTRAINT pk_contract PRIMARY KEY (id);
alter table contract_account add CONSTRAINT pk_contract_account PRIMARY KEY (id);

alter table invoice ADD CONSTRAINT pk_invoice PRIMARY KEY (id);
alter table meter_reading ADD CONSTRAINT pk_meter_reading PRIMARY KEY (id);
alter table payment ADD CONSTRAINT pk_payment PRIMARY KEY (id);

-- ADD FK
alter table customer_request ADD CONSTRAINT fk_customer_request_point_id FOREIGN KEY (delivery_point_id) REFERENCES delivery_point (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION;
alter table customer_notification ADD CONSTRAINT fk_notification_delivery_point_id FOREIGN KEY (delivery_point_id) REFERENCES delivery_point (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION;
alter table customer_notification ADD CONSTRAINT fk_notification_business_partner_id FOREIGN KEY (business_partner_id) REFERENCES business_partner (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION;
alter table customer_request ADD CONSTRAINT fk_customer_request_business_partner_id FOREIGN KEY (business_partner_id) REFERENCES business_partner (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION;

alter table customer_account_challenge_code ADD CONSTRAINT fk_customer_account_challenge_code_business_partner_id FOREIGN KEY (business_partner_id) REFERENCES business_partner (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION;
alter table business_partner_ownership ADD CONSTRAINT fk_business_partner_ownership_business_partner FOREIGN KEY (business_partner_id) REFERENCES business_partner (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION;
alter table business_partner_pairing_request ADD CONSTRAINT fk_bppr_business_partner FOREIGN KEY (business_partner_id) REFERENCES business_partner (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION;
alter table contract_ownership ADD CONSTRAINT fk_contract_ownership_contract FOREIGN KEY (contract_id) REFERENCES contract (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION;
alter table contract_account_ownership ADD CONSTRAINT fk_contract_account_ownership_contract_account FOREIGN KEY (contract_account_id) REFERENCES contract_account (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION;

CREATE VIEW v_sharing_summary AS
(select created_at, type, inherited, customer_account_uuid, target_id, business_partner_id, contract_account_id, contract_id, delivery_point_id, united_delivery_point_uuid, entity_type, grantor_customer_account_uuid
from (
     select created_at, type, inherited, customer_account_uuid, business_partner_id as target_id, business_partner_id, Null as contract_account_id, Null as contract_id, Null as delivery_point_id, Null::UUID as united_delivery_point_uuid, CAST('BUSINESS_PARTNER' as CHARACTER VARYING(32)) as entity_type, grantor_customer_account_uuid
     from business_partner_ownership
     union
     select created_at, type, inherited, customer_account_uuid, contract_account_id as target_id, Null as business_partner_id, contract_account_id, Null as contract_id, Null as delivery_point_id, Null::UUID as united_delivery_point_uuid, CAST('CONTRACT_ACCOUNT' as CHARACTER VARYING(32)) as entity_type, grantor_customer_account_uuid
     from contract_account_ownership
     union
     select created_at, type, inherited, customer_account_uuid, contract_id as target_id, Null as business_partner_id, Null as contract_account_id, contract_id, Null as delivery_point_id, Null::UUID as united_delivery_point_uuid, CAST('CONTRACT' as CHARACTER VARYING(32)) as entity_type, grantor_customer_account_uuid
     from contract_ownership
     union
     select created_at, type, inherited, customer_account_uuid, united_delivery_point_uuid::text as target_id, Null as business_partner_id, Null as contract_account_id, Null as contract_id, Null as delivery_point_id, united_delivery_point_uuid, CAST('UNITED_DELIVERY_POINT' as CHARACTER VARYING(32)) as entity_type, grantor_customer_account_uuid
     from united_delivery_point_ownership
   ) as q
  );



ALTER TABLE united_delivery_point RENAME COLUMN business_partner_external_id  TO business_partner_id;