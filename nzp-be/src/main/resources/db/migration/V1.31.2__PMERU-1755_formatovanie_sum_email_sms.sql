-------CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE start

---- posledny update z flyway 1.27.6 337r
update notification_template_i18n
set email_body = '<#setting locale="sk">
<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>blíži sa dátum splatnosti Vašej preddavkovej platby za spotrebu <#if deliveryPoints?has_content>  <#if deliveryPoints?size == 1> na odbernom mieste s adresou ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}.  <#else>    na odberných miestach s adresami    <ul>    <#list deliveryPoints as dp>      <li>${dp.street} ${dp.streetNumber}, ${dp.city}</li>    </#list>    </ul>  </#if></#if></p><p>Ak máte z<PERSON> ink<PERSON>, úhrada prebehne automaticky k dátumu splatnosti faktúry.</p><p>Spôsom platby SIPO, vyžaduje úhradu cez Slovenskú poštu.</p><p>Ak platíte prevodným príkazom, platbu môžete zrealizovať priamo vo svojom <a href="${portalExternalUrl}/invoices/${invoice.id}">portálovom účte</a> vo výške ${invoice.amount?string(",##0.00")} EUR s variabilným symbolom ${invoice.vs}.</p><p>V prípade preplatku Vám peniaze zašleme na Vás bankový účet.</p><p><a href="${portalExternalUrl}/invoices/${invoice.id}">Prejsť na úhradu preddavkovej platby</a></p></@spp.notification_email_template>'
where notification_template_id = (select nt.uuid from notification_template nt where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE')
and locale = 'SK';


--- fl 1.18.5 28r.
update notification_template_i18n
set email_body = '<#setting locale="en">
<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] pre zákazníka s číslom ${businessPartner.externalId} sa blíži splatnosť preddavku s číslom ${invoice.reference} vo výške ${invoice.amount?string(",##0.00")} EUR. Úhradu môžete zrealizovať aj prostredníctvom nášho portálu.<br><a href="${portalExternalUrl}/invoice/${invoice.id}">Prejsť na faktúru</a></@spp.notification_email_template>'
where notification_template_id = (select nt.uuid from notification_template nt where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE')
  and locale = 'EN';

-------CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE END


-------CUSTOMER_INVOICE_BEFORE_DUE START

---- 1.27.6 r336
update notification_template_i18n
set email_body = '<#setting locale="sk">
<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie=""><p>blíži sa dátum splatnosti Vašej faktúry za spotrebu <#if deliveryPoints?has_content>  <#if deliveryPoints?size == 1> na odbernom mieste s adresou ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}.  <#else>  na odberných miestach s adresami    <ul>    <#list deliveryPoints as dp>      <li>${dp.street} ${dp.streetNumber}, ${dp.city}</li>    </#list>    </ul>  </#if></#if></p><p>Ak máte zriadené inkaso, úhrada prebehne automaticky k dátumu splatnosti faktúry.</p><p>Spôsom platby SIPO, vyžaduje úhradu cez Slovenskú poštu.</p><p>Ak platíte prevodom, platbu môžete zrealizovať priamo vo svojom <a href="${portalExternalUrl}/invoices/${invoice.id}">portálovom účte</a></span> vo výške ${invoice.amount?string(",##0.00")} eur s variabilným symbolom ${invoice.vs}.</p><p>V prípade preplatku Vám peniaze zašleme na Vás bankový účet.</p><p><a href="${portalExternalUrl}/invoices/${invoice.id}">Prejsť na úhradu faktúry</a></p></@spp.notification_email_template>'
where notification_template_id = (select nt.uuid from notification_template nt where code = 'CUSTOMER_INVOICE_BEFORE_DUE')
and locale = 'SK';

------ 1.18.5 r 13
update notification_template_i18n
set email_body = '<#setting locale="en">
<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] pre zákazníka s číslom ${businessPartner.externalId} sa blíži splatnosť faktúry s číslom ${invoice.reference} vo výške ${invoice.amount?string(",##0.00")} EUR. Úhradu môžete zrealizovať aj prostredníctvom nášho portálu.<br><a href="${portalExternalUrl}/invoice/${invoice.id}">Prejsť na faktúru</a></@spp.notification_email_template>'
where notification_template_id = (select nt.uuid from notification_template nt where code = 'CUSTOMER_INVOICE_BEFORE_DUE')
  and locale = 'EN';
----- CUSTOMER_INVOICE_BEFORE_DUE END


---------- CUSTOMER_INVOICE_ADVANCE_ISSUED START
---- 1.27.6 r338
update notification_template_i18n
set email_body = '<#setting locale="sk">
<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie=""><p>vystavili sme Vám preddavkovú platbu za spotrebu <#if deliveryPoints?has_content>  <#if deliveryPoints?size == 1> na odbernom mieste s adresou ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}.  <#else>    na odberných miestach s adresami    <ul>    <#list deliveryPoints as dp>      <li>${dp.street} ${dp.streetNumber}, ${dp.city}</li>    </#list>    </ul>  </#if></#if>, vo výške ${invoice.amount?string(",##0.00")} EUR. </p><p>Úhradu môžete zrealizovať vo svojom portálovom účte.</p></@spp.notification_email_template>'
where notification_template_id = (select nt.uuid from notification_template nt where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED')
and locale = 'SK';

---EN verzia neobsahuje invoice.amount  invoice.unpaid invoice.overpaid

--------- CUSTOMER_INVOICE_ADVANCE_ISSUED END