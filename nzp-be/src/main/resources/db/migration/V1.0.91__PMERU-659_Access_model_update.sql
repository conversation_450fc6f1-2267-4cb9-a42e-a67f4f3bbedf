
drop table access_group_right;
drop table access_group;

---------------
-- access_group
---------------

create table access_group (
    uuid                           uuid                      NOT NULL,
    created_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                        INTEGER                   NOT NULL,
    
    code                           CHARACTER VARYING(50)     NOT NULL,
    name                           CHARACTER VARYING(64),
    description                    text,
    
    constraint pk_access_group primary key (uuid)
)
WITH (
    OIDS = FALSE
);

create index idx_access_group_code on access_group (code);



---------------------
-- access_group_right
---------------------

create table access_group_right (
    created_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    
    access_group_uuid              uuid                      NOT NULL,
    access_right_code              CHARACTER VARYING(50)     NOT NULL,
    
    operation                      <PERSON><PERSON><PERSON>TER VARYING(50)     NOT NULL,
    queue                          CHARACTER VARYING(50),
    
    constraint pk_access_group_right primary key (access_group_uuid,access_right_code),
    constraint fk_access_group_right_access_group_uuid foreign key (access_group_uuid) references access_group,
    constraint fk_access_group_right_access_right_code foreign key (access_right_code) references access_right
)
WITH (
    OIDS = FALSE
);


DELETE FROM access_group_right;
DELETE FROM access_group;

INSERT INTO access_group (uuid, code, created_at , updated_at ,"version", name, description) values (uuid_generate_v4(), 'NZP_CUSTOMERS_ACTIVATION', current_timestamp, current_timestamp, 1, 'Aktivacia a Deaktivacia Zakaznikov', '') ;
INSERT INTO access_group (uuid, code, created_at , updated_at ,"version", name, description) values (uuid_generate_v4(), 'NZP_CUSTOMERS_VIEW', current_timestamp, current_timestamp, 1, 'Prehlad zakaznickych uctov', '') ;
INSERT INTO access_group (uuid, code, created_at , updated_at ,"version", name, description) values (uuid_generate_v4(), 'NZP_CUSTOMERS_DELETE', current_timestamp, current_timestamp, 1, 'Vymaz zakaznickych uctov', '') ;
INSERT INTO access_group (uuid, code, created_at , updated_at ,"version", name, description) values (uuid_generate_v4(), 'NZP_BUSINESS_PARTNERS_PAIRING', current_timestamp, current_timestamp, 1, 'Parovanie obchodnych partnerov', '') ;
INSERT INTO access_group (uuid, code, created_at , updated_at ,"version", name, description) values (uuid_generate_v4(), 'NZP_NOTIFICATION_TEMPLATES', current_timestamp, current_timestamp, 1, 'Sprava notifikacnych sablon', '') ;
INSERT INTO access_group (uuid, code, created_at , updated_at ,"version", name, description) values (uuid_generate_v4(), 'NZP_REPORTING_MANAGEMENT', current_timestamp, current_timestamp, 1, 'Sprava a spustanie reportov ', '') ;
INSERT INTO access_group (uuid, code, created_at , updated_at ,"version", name, description) values (uuid_generate_v4(), 'NZP_REPORTING', current_timestamp, current_timestamp, 1, 'Spustanie existujucich reportov', '') ;
INSERT INTO access_group (uuid, code, created_at , updated_at ,"version", name, description) values (uuid_generate_v4(), 'NZP_SUPER_ADMIN', current_timestamp, current_timestamp, 1, 'Super admin', '') ;
INSERT INTO access_group (uuid, code, created_at , updated_at ,"version", name, description) values (uuid_generate_v4(), 'NZP_PORTAL_ACCESS', current_timestamp, current_timestamp, 1, 'Portal access', '') ;

INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('CUSTOMERS_ACTIVATION', (select uuid from access_group where code = 'NZP_CUSTOMERS_ACTIVATION'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('CUSTOMERS_DEACTIVATION', (select uuid from access_group where code = 'NZP_CUSTOMERS_ACTIVATION'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('CUSTOMERS_VIEW', (select uuid from access_group where code = 'NZP_CUSTOMERS_VIEW'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('CUSTOMERS_DELETE_ACCOUNT', (select uuid from access_group where code = 'NZP_CUSTOMERS_DELETE'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('BUSINESS_PARTNERS_PAIRING', (select uuid from access_group where code = 'NZP_BUSINESS_PARTNERS_PAIRING'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('BUSINESS_PARTNERS_UNPAIRING', (select uuid from access_group where code = 'NZP_BUSINESS_PARTNERS_PAIRING'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('NOTIFICATION_TEMPLATES_VIEW', (select uuid from access_group where code = 'NZP_NOTIFICATION_TEMPLATES'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('NOTIFICATION_TEMPLATES_EDIT', (select uuid from access_group where code = 'NZP_NOTIFICATION_TEMPLATES'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('REPORTS_EDIT', (select uuid from access_group where code = 'NZP_REPORTING'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('REPORTS_EDIT', (select uuid from access_group where code = 'NZP_REPORTING_MANAGEMENT'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('REPORTS_VIEW', (select uuid from access_group where code = 'NZP_REPORTING'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('PORTAL_ACCESS', (select uuid from access_group where code = 'NZP_PORTAL_ACCESS'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('SSO_ADMIN', (select uuid from access_group where code = 'NZP_SUPER_ADMIN'), current_timestamp, 'GRANT', NULL);
