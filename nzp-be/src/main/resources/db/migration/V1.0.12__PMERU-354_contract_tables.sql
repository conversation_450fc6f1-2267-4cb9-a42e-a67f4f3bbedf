-----------
-- contract
-----------

CREATE TABLE contract
(
    uuid                     uuid                      NOT NULL,
    created_at               TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at               TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                  integer                   NOT NULL,
    
    external_id              CHARACTER VARYING(50)     NOT NULL,
    status                   CHARACTER VARYING(20)     NOT NULL,
    type                     CHARACTER VARYING(50)     NOT NULL,
    signature_at             DATE,
    effective_from           DATE,
    effective_to             DATE,
    binding_to               DATE,
    bill_cycle               CHARACTER VARYING(50),
    notice_period            CHARACTER VARYING(50),
    deal_amount              NUMERIC(19,2),
    maximum_daily_amount     NUMERIC(19,2),
    reserve_amount           NUMERIC(19,2),
    maximum_reserve_amount   NUMERIC(19,2),
    reading_period_type      CHARACTER VARYING(50),
    
    contract_account_id      uuid                      NOT NULL,

    CONSTRAINT pk_contract PRIMARY KEY (uuid),
    CONSTRAINT fk_contract_contract_account FOREIGN KEY (contract_account_id)
        REFERENCES contract_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_contract_external_id on contract(external_id);
CREATE INDEX idx_contract_contract_account_id on contract(contract_account_id);



-------------------
-- contract_product 
-------------------

CREATE TABLE contract_product
(
    uuid                  uuid                      NOT NULL,
    created_at            TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at            TIMESTAMP WITH TIME ZONE  NOT NULL,
    version               integer                   NOT NULL,
    
    contract_id   uuid                              NOT NULL,

    CONSTRAINT pk_contract_product PRIMARY KEY (uuid),
    CONSTRAINT fk_contract_product_contract FOREIGN KEY (contract_id)
        REFERENCES contract (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);