-- screen: "NZT", field: "NZT_ZOP_US_EE3"
INSERT INTO component_help (uuid, created_at, updated_at, version, screen, field, status, help_order)
VALUES (uuid_generate_v4(), now(), now(), 1, 'NZT', 'NZT_ZOP_US_EE3', 'ACTIVE', 571)
ON CONFLICT (screen, field) DO NOTHING;

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', '<p><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>, Videorady na mieru, Šetrime ďalej online, Personalizované online poradenstvo, výsadbu 2 stromov</p>
<p><span style="color: rgb(74, 74, 74); font-family: -apple-system, &quot;system-ui&quot;, &quot;Se<PERSON>e UI&quot;, <PERSON><PERSON>, <PERSON>xygen, Ubuntu, Cantarell, &quot;<PERSON>ra Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration: underline; float: none; display: inline !important; font-size: 16px;"><a href="https://moje.spp.sk/">Zistiť viac</a></span></p>', (
    SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOP_US_EE3'))
ON CONFLICT (locale, component_help_id) DO NOTHING;

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '<p>Recommendation package, Custom video advice, Personalised online consulting, plant 2 trees</p> <a href="https://moje.spp.sk/" target="_blank"></a><span style="color: rgb(74, 74, 74); font-family: -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, Cantarell, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration: underline; float: none; display: inline !important; font-size: 16px;"><a href="https://moje.spp.sk/" target="_blank">More information</a></span>', (
    SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOP_US_EE3'))
ON CONFLICT (locale, component_help_id) DO NOTHING;

-- screen: "NZT", field: "NZT_ZOP_US_ZP2"
INSERT INTO component_help (uuid, created_at, updated_at, version, screen, field, status, help_order)
VALUES (uuid_generate_v4(), now(), now(), 1, 'NZT', 'NZT_ZOP_US_ZP2', 'ACTIVE', 572)
ON CONFLICT (screen, field) DO NOTHING;

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', '<p>Štartovací balík rád, Videorady na mieru, Šetrime ďalej online, Personalizované online poradenstvo, výsadbu 5 stromov</p><a href="https://moje.spp.sk/" target="_blank"><span style="color: rgb(74, 74, 74); font-family: -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, Cantarell, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration: underline; float: none; display: inline !important; font-size: 16px;">Zistiť viac</span></a>', (
    SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOP_US_ZP2'))
ON CONFLICT (locale, component_help_id) DO NOTHING;

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '<p>Recommendation package, Custom video advice, Personalised online consulting, plant 5 trees</p> <a href="https://moje.spp.sk/" target="_blank"></a><span style="color: rgb(74, 74, 74); font-family: -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, Cantarell, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration: underline; float: none; display: inline !important; font-size: 16px;"><a href="https://moje.spp.sk/" target="_blank">More information</a></span>', (
    SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOP_US_ZP2'))
ON CONFLICT (locale, component_help_id) DO NOTHING;

-- screen: "NZT", field: "NZT_ZOP_US_ZP3"
INSERT INTO component_help (uuid, created_at, updated_at, version, screen, field, status, help_order)
VALUES (uuid_generate_v4(), now(), now(), 1, 'NZT', 'NZT_ZOP_US_ZP3', 'ACTIVE', 573)
ON CONFLICT (screen, field) DO NOTHING;

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', '<p>Štartovací balík rád, Videorady na mieru, Šetrime ďalej online, Personalizované online poradenstvo, výsadbu 13 stromov</p> <a href="https://moje.spp.sk/" target="_blank"></a><span style="color: rgb(74, 74, 74); font-family: -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, Cantarell, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration: underline; float: none; display: inline !important; font-size: 16px;"><a href="https://moje.spp.sk/" target="_blank">Zistiť viac</a></span>', (
    SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOP_US_ZP3'))
ON CONFLICT (locale, component_help_id) DO NOTHING;

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '<p>Recommendation package, Custom video advice, Personalised online consulting, plant 13 trees</p> <a href="https://moje.spp.sk/" target="_blank"></a><span style="color: rgb(74, 74, 74); font-family: -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, Cantarell, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration: underline; float: none; display: inline !important; font-size: 16px;"><a href="https://moje.spp.sk/" target="_blank">More information</a></span>', (
    SELECT uuid FROM component_help WHERE screen = 'NZT' AND field = 'NZT_ZOP_US_ZP3'))
ON CONFLICT (locale, component_help_id) DO NOTHING;