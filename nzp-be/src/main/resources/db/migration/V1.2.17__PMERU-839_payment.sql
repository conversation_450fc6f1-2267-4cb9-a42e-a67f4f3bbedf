alter table payment add column synchronization_log_uuid     uuid;
alter table payment add column synchronization_at           timestamp with time zone;
alter table payment add column vs                           text;


---------------------
-- Add indexes
---------------------
CREATE INDEX idx_payment_synchronization_log_uuid on business_partner(synchronization_log_uuid);

---------------------
-- Foreign keys
---------------------
alter table payment
ADD CONSTRAINT fk_payment_synchronization_log FOREIGN KEY (synchronization_log_uuid)
        REFERENCES synchronization_log (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;