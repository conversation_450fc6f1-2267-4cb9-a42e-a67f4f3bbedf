

update notification_template_i18n set email_body = null where notification_template_id in (
	select uuid from notification_template where code in ('CUSTOMER_PHONE_CHANGE_REQUEST','BUSINESS_PARTNER_PAIRING_CHALLENGE') and "type" = 'CUSTOMER_SYSTEM'
);

update notification_template_i18n set sms_body = null where notification_template_id in (
	select uuid from notification_template where code not in ('CUSTOMER_PHONE_CHANGE_REQUEST','BUSINESS_PARTNER_PAIRING_CHALLENGE') and "type" = 'CUSTOMER_SYSTEM'
);