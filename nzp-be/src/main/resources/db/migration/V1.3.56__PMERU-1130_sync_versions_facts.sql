
--remove duplicates

 DELETE FROM contract_version a USING (
      SELECT MIN(ctid) as ctid, contract_id, valid_from
        FROM contract_version
        GROUP BY contract_id, valid_from HAVING COUNT(*) > 1
      ) b
      WHERE a.contract_id = b.contract_id and a.valid_from = b.valid_from
      AND a.ctid <> b.ctid;

 DELETE FROM delivery_point_version a USING (
      SELECT MIN(ctid) as ctid, delivery_point_id, valid_from
        FROM delivery_point_version
        GROUP BY delivery_point_id, valid_from HAVING COUNT(*) > 1
      ) b
      WHERE a.delivery_point_id = b.delivery_point_id and a.valid_from = b.valid_from AND a.ctid <> b.ctid;

 DELETE FROM delivery_point_fact a USING (
      SELECT MIN(ctid) as ctid, delivery_point_id, valid_from, operand
        FROM delivery_point_fact
        GROUP BY delivery_point_id, valid_from, operand HAVING COUNT(*) > 1
      ) b
      WHERE a.delivery_point_id = b.delivery_point_id and a.valid_from = b.valid_from and a.operand = b.operand AND a.ctid <> b.ctid;

-- Delivery Point Version
delete from delivery_point_version where delivery_point_id is null or valid_from is null;
alter table delivery_point_version drop CONSTRAINT pk_delivery_point_version;
alter table delivery_point_version alter column uuid drop not null;
update delivery_point_version set uuid = null;
alter table delivery_point_version rename column uuid to id;
alter table delivery_point_version alter column id type  text;
update delivery_point_version set id = replace(replace(replace(encode(digest(CONCAT('Goe9AE2qd0O7UyX',delivery_point_id,'-',valid_from), 'sha1'), 'base64'),'+','-'),'/','_'),'=','');
alter table delivery_point_version ADD CONSTRAINT pk_delivery_point_version PRIMARY KEY (id);

-- Delivery Point Fact
delete from delivery_point_fact where delivery_point_id is null or valid_from is null or operand is null;
alter table delivery_point_fact drop CONSTRAINT pk_delivery_point_fact;
alter table delivery_point_fact alter column uuid drop not null;
update delivery_point_fact set uuid = null;
alter table delivery_point_fact rename column uuid to id;
alter table delivery_point_fact alter column id type  text;
update delivery_point_fact set id = replace(replace(replace(encode(digest(CONCAT('Goe9AE2qd0O7UyX',delivery_point_id,'-',operand,'-',valid_from), 'sha1'), 'base64'),'+','-'),'/','_'),'=','');
alter table delivery_point_fact ADD CONSTRAINT pk_delivery_point_fact PRIMARY KEY (id);

-- Contract Version
delete from contract_version where contract_id is null or valid_from is null;
alter table contract_version drop CONSTRAINT pk_contract_version;
alter table contract_version alter column uuid drop not null;
update contract_version set uuid = null;
alter table contract_version rename column uuid to id;
alter table contract_version alter column id type  text;
update contract_version set id = replace(replace(replace(encode(digest(CONCAT('Goe9AE2qd0O7UyX',contract_id,'-',valid_from), 'sha1'), 'base64'),'+','-'),'/','_'),'=','');
alter table contract_version ADD CONSTRAINT pk_contract_version PRIMARY KEY (id);

-- ADD not null constraints
alter table delivery_point_version alter column delivery_point_id set not null;
alter table delivery_point_version alter column valid_from set not null;

alter table delivery_point_fact alter column delivery_point_id set not null;
alter table delivery_point_fact alter column operand set not null;
alter table delivery_point_fact alter column valid_from set not null;

alter table contract_version alter column contract_id set not null;
alter table contract_version alter column valid_from set not null;
