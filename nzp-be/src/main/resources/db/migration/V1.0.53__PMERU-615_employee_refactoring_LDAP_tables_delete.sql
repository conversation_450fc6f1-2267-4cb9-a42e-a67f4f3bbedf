-----------------
-- audit_log change employee reference to string (login)
-----------------
alter table audit_log drop    constraint  fk_audit_log_employee_account;
alter table audit_log rename  column      employee_account_uuid                 to    employee_account_id;
alter table audit_log alter   column      employee_account_id                   type  CHARACTER VARYING(50) using employee_account_id::CHARACTER VARYING(50);

-----------------
-- drop tables (employee_account_access, employee_account)
-----------------
drop table employee_account_access;
drop table employee_account;

-----------------
-- access_group_right add columns (uuid, created_at, updated_at, version, operation, queue)
-- access_group_right drop primary key (access_group_code, access_right_code)
-- access_group_right add primary key (uuid)
-----------------
alter table access_group_right add column created_at    TIMESTAMP WITH TIME ZONE        NOT NULL;
alter table access_group_right add column operation     CHARACTER VARYING(50)           NOT NULL;
alter table access_group_right add column queue         CHARACTER VARYING(50);
