-- Add columns
ALTER TABLE customer_request_template       ADD COLUMN price_home           NUMERIC(19, 2);
ALTER TABLE customer_request_template       ADD COLUMN price_individual     NUMERIC(19, 2);

ALTER TABLE customer_request                ADD COLUMN price_home           NUMERIC(19, 2);
ALTER TABLE customer_request                ADD COLUMN price_individual     NUMERIC(19, 2);

-- Copy prices
UPDATE customer_request_template
SET price_home = price, price_individual = price;

UPDATE customer_request
SET price_home = price, price_individual = price;

-- Remove old price col
ALTER TABLE customer_request_template       DROP COLUMN price;
ALTER TABLE customer_request                DROP COLUMN price;

-- Set exceptions
UPDATE customer_request_template
SET price_individual = 0.0
WHERE code = 'ZOM_ZOPAOO_PDF';

UPDATE customer_request_template
SET price_individual = 0.0
WHERE code = 'ZOM_DOS';

UPDATE customer_request_template
SET price_individual = 0.0
WHERE code = 'ZOM_POVZ';

UPDATE customer_request_template
SET price_individual = 0.0
WHERE code = 'ZOM_POZV';

UPDATE customer_request_template
SET price_individual = 0.0
WHERE code = 'ZOM_ZOFMC';