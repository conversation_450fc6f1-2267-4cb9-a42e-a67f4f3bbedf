UPDATE generic_code_list SET type = 'METER_READING_REASON' where type = 'METER_READING_TYPE';


INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, '01', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '02', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '03', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '04', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '05', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '06', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '07', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '13', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '84', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '85', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '86', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '87', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '88', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '89', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '94', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '95', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '96', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '97', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '98', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, '99', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'A1', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'B1', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'B2', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'D1', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'D2', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'DS', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'E1', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'E2', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'E3', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'H1', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'H2', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'H3', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'H4', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'H5', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'H6', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'H7', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'HF', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'HM', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'HO', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'HS', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'HU', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'HV', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'K1', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'KL', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'KM', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'KP', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'L1', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'L2', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'M1', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'M2', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'M3', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'M4', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'ON', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'OS', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'S1', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'S2', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'S3', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'SO', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'SP', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'W1', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'W2', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'X1', 'METER_READING_KIND', null, null, null),
    (uuid_generate_v4(), now(), now(), 1, 'XX', 'METER_READING_KIND', null, null, null);

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Odpočet podnikom verejných služieb - SAP', null, (select uuid from generic_code_list where code like '01' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Odpočet zákazníkom - SAP', null, (select uuid from generic_code_list where code like '02' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Automatický odhad - SAP', null, (select uuid from generic_code_list where code like '03' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Odpočet odvodený - SAP', null, (select uuid from generic_code_list where code like '04' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Nový odhad po nadmernom odhade - SAP', null, (select uuid from generic_code_list where code like '05' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Odpočet internetu - SAP', null, (select uuid from generic_code_list where code like '06' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Výsledok odpočtu, upload, príjem faktúry', null, (select uuid from generic_code_list where code like '07' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Odhad odpočtov z DIS - jednotlivý prenos', null, (select uuid from generic_code_list where code like '13' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Odčítane bez čiarového kódu oprava', null, (select uuid from generic_code_list where code like '84' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Oprava odhadu z DIS - hromadný prenos', null, (select uuid from generic_code_list where code like '85' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Oprava odpočtu cez GMS', null, (select uuid from generic_code_list where code like '86' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Oprava odpočtový list - hromadné zadanie', null, (select uuid from generic_code_list where code like '87' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Oprava odhad.odpočtov - hromadný prenos', null, (select uuid from generic_code_list where code like '88' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Oprava odpočtov - PSION', null, (select uuid from generic_code_list where code like '89' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Odčítane bez čiarového kódu', null, (select uuid from generic_code_list where code like '94' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Odhad odpočtov z DIS - hromadný prenos', null, (select uuid from generic_code_list where code like '95' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Odpočet cez GMS', null, (select uuid from generic_code_list where code like '96' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Odpočtový list - hromadné zadávanie', null, (select uuid from generic_code_list where code like '97' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Odhad odpočtov - Hromadný prenos', null, (select uuid from generic_code_list where code like '98' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Hromadné prenosy - PSION,GMS 01,02,10,09', null, (select uuid from generic_code_list where code like '99' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Upravené po interpolácii', null, (select uuid from generic_code_list where code like 'A1' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'KOSK_SAM Samoodpočet', null, (select uuid from generic_code_list where code like 'B1' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'KOSK_SAK Kontrola samoodpočtu', null, (select uuid from generic_code_list where code like 'B2' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'ODZP_ENE Obnovenie dodávky po uhradení', null, (select uuid from generic_code_list where code like 'D1' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'ODZP_ENZ Obnovenie dodávky nový', null, (select uuid from generic_code_list where code like 'D2' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Doúčtovanie spotreby', null, (select uuid from generic_code_list where code like 'DS' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'PDZP_ENE Prerušenie', null, (select uuid from generic_code_list where code like 'E1' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'PDZP_ENP Neoprávnený odber ext.', null, (select uuid from generic_code_list where code like 'E2' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'PDZP_INP Neoprávnený odber int.', null, (select uuid from generic_code_list where code like 'E3' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'REME Iná reklamácia', null, (select uuid from generic_code_list where code like 'H1' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'REME_EXT kntrola merania pri REME', null, (select uuid from generic_code_list where code like 'H2' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'REME_EXT Rekl.skutočného dátumu odpočtu', null, (select uuid from generic_code_list where code like 'H3' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'REME_EXT Reklamácia fyzického odpočtu', null, (select uuid from generic_code_list where code like 'H4' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'REME_INT fyz. odp. z nasledujúceho odp.', null, (select uuid from generic_code_list where code like 'H5' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'REME_EXT  Reklamácia odhadu stavu', null, (select uuid from generic_code_list where code like 'H6' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'REME_EXT Dopočet spotreby - zasek. čís.', null, (select uuid from generic_code_list where code like 'H7' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'REME_INT fyz.odp. na základe deme', null, (select uuid from generic_code_list where code like 'HF' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Automat. odhad - mimoriadny dpočet', null, (select uuid from generic_code_list where code like 'HM' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'REME_INT odhad odp na základe deme', null, (select uuid from generic_code_list where code like 'HO' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'REME úprava spotr. po US', null, (select uuid from generic_code_list where code like 'HS' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'REME_INT oprava účt.DEME', null, (select uuid from generic_code_list where code like 'HU' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'REME_INT odhad odp. z nasledujúceho odp.', null, (select uuid from generic_code_list where code like 'HV' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'ZMPR_EKO Zmena odberateľa na odb. mieste', null, (select uuid from generic_code_list where code like 'K1' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Korešpodenčný lístok', null, (select uuid from generic_code_list where code like 'KL' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Kontrola merania', null, (select uuid from generic_code_list where code like 'KM' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Kontrola poklesov spotreby', null, (select uuid from generic_code_list where code like 'KP' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'PZOD_EUO Požiadavka zák.preruš. odberu', null, (select uuid from generic_code_list where code like 'L1' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'PZPD_EOO požiadavka zák.obnovenie odberu', null, (select uuid from generic_code_list where code like 'L2' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Migr. EE - odpočet z LCT-reálny INVOIC', null, (select uuid from generic_code_list where code like 'M1' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Migr. EE - odpočet z LCT-upravený INVOIC', null, (select uuid from generic_code_list where code like 'M2' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Migr. EE - odpočet z LCT-manuálny odp.', null, (select uuid from generic_code_list where code like 'M3' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Migr. EE - odpočet mimo LCT - INVOIC', null, (select uuid from generic_code_list where code like 'M4' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Zmena obchodníka NOVÝ OBCHODNÍK', null, (select uuid from generic_code_list where code like 'ON' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Zmena obchodníka STARÝ OBCHODNÍK', null, (select uuid from generic_code_list where code like 'OS' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Odhad odpočtu', null, (select uuid from generic_code_list where code like 'S1' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Oprava (potvrdenie) odhadu odpočtu', null, (select uuid from generic_code_list where code like 'S2' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Oprava odhadu odpočtu iným odpočtom', null, (select uuid from generic_code_list where code like 'S3' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Obnovenie statute', null, (select uuid from generic_code_list where code like 'SO' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Prerušenie statute', null, (select uuid from generic_code_list where code like 'SP' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Samoodpočet nahlásený cez Web-portál', null, (select uuid from generic_code_list where code like 'W1' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Oprava samoodpočtu Web', null, (select uuid from generic_code_list where code like 'W2' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Migrácia docasna', null, (select uuid from generic_code_list where code like 'X1' and type = 'METER_READING_KIND')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Migrácia', null, (select uuid from generic_code_list where code like 'XX' and type = 'METER_READING_KIND'));