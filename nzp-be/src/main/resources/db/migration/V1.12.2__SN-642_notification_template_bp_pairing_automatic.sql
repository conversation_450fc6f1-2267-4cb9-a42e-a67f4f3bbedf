delete from notification_template_variable ntv where ntv.variable = 'attributes.initializePairRequest';

-- new notification: BUSINESS_PARTNER_PAIRING_REQUEST_AUTOMATIC
INSERT INTO notification_template
(uuid, created_at, updated_at, version, code, status, type, name, priority, execution_type,
 description, attributes, template_group, default_email, default_sms)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_PAIRING_REQUEST_AUTOMATIC', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Automaticky iniciované párovanie obchodného partnera', 'LOW', 'AUTOMATIC',
 'Notifikácia ohľadom automaticky iniciovaného párovania Obchodného partnera', null, null, true, false);

INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, status, header, email_body,
 email_subject, sms_body,
 notification_template_id, locale)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'Automatické párovanie obchodného partnera',
 '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">párovanie obchodného partnera bolo automaticky iniciované.</@spp.notification_email_template>',
 'Automatické párovanie obchodného partnera',
 null, (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST_AUTOMATIC' and version = 1), 'SK');

INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, status, header, email_body,
 email_subject, sms_body,
 notification_template_id, locale)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ACTIVE', '[EN] Automatické párovanie obchodného partnera',
 '<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] párovanie obchodného partnera bolo automaticky iniciované.</@spp.notification_email_template>',
 '[EN] Automatické párovanie obchodného partnera',
 null, (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST_AUTOMATIC' and version = 1), 'EN');

insert into notification_template_variable select (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST_AUTOMATIC' and version = 1), now(), now(), 1, ntv.variable, ntv.name, ntv.description, ntv.type, ntv.notification_template_execution_type, uuid_generate_v4() from notification_template_variable ntv where ntv.notification_template_uuid = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1);



-- new notification: BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC
INSERT INTO notification_template
(uuid, created_at, updated_at, version, code, status, type, name, priority, execution_type,
 description, attributes, template_group, default_email, default_sms)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Dokončené automatické párovanie obchodného partnera', 'LOW', 'AUTOMATIC',
 'Notifikácia ohľadom úspešného automaticky iniciovaného párovania Obchodného partnera', null, null, true, false);

INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, status, header, email_body,
 email_subject, sms_body,
 notification_template_id, locale)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'Automatické párovanie obchodného partnera',
 '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">obchodný partner bol úspešne napárovaný automatickým procesom.</@spp.notification_email_template>',
 'Automatické párovanie obchodného partnera',
 null, (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC' and version = 1), 'SK');

INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, status, header, email_body,
 email_subject, sms_body,
 notification_template_id, locale)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ACTIVE', '[EN] Automatické párovanie obchodného partnera',
 '<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] obchodný partner bol úspešne napárovaný automatickým procesom.</@spp.notification_email_template>',
 '[EN] Automatické párovanie obchodného partnera',
 null, (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC' and version = 1), 'EN');


insert into notification_template_variable select (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC' and version = 1), now(), now(), 1, ntv.variable, ntv.name, ntv.description, ntv.type, ntv.notification_template_execution_type, uuid_generate_v4() from notification_template_variable ntv where ntv.notification_template_uuid = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1);

