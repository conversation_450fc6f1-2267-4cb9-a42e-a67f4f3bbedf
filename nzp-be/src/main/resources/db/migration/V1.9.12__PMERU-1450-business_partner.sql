
CREATE TABLE business_partner_tmp (
	id text NOT NULL,
	created_at timestamptz NOT NULL,
	updated_at timestamptz NOT NULL,
	version int4 NOT NULL,
	external_id varchar(50) NOT NULL,
	queue varchar(50) NULL,
	tax_id_number varchar(32) NULL,
	vat_registration_number varchar(32) NULL,
	company_registration_number varchar(32) NULL,
	name varchar(512) NULL,
	first_name varchar(100) NULL,
	last_name varchar(100) NULL,
	email varchar(64) NULL,
	phone varchar(32) NULL,
	synchronization_log_uuid uuid NOT NULL,
	synchronization_at timestamptz NOT NULL,
	category text NULL,
	type text NULL,
	primary_street varchar(64) NULL,
	primary_street_number varchar(32) NULL,
	primary_city varchar(64) NULL,
	primary_zip_code varchar(32) NULL,
	primary_country varchar(64) NULL,
	am_external_id varchar(50) NULL,
	am_first_name varchar(100) NULL,
	am_last_name varchar(100) NULL,
	am_email varchar(64) NULL,
	am_phone varchar(32) NULL,
	franche_fixation bool NULL,
	kind text NULL,
	am_name text NULL,
	name_searchable varchar(512) NULL
);

-- transfer and transform
INSERT INTO business_partner_tmp
(id, created_at, updated_at, version, external_id, queue, tax_id_number, vat_registration_number, company_registration_number, name, first_name, last_name,
email, phone, synchronization_log_uuid, synchronization_at, category, type, primary_street, primary_street_number, primary_city, primary_zip_code,
primary_country, am_external_id, am_first_name, am_last_name, am_email, am_phone, franche_fixation, kind, am_name, name_searchable)
SELECT
bp.id, bp.created_at, bp.updated_at, bp.version, bp.external_id, bp.queue, bp.tax_id_number, bp.vat_registration_number, bp.company_registration_number,
bp.name, bp.first_name, bp.last_name, bp.email, bp.phone, bp.synchronization_log_uuid, bp.synchronization_at, gclCategory.code, gclType.code, bp.primary_street,
bp.primary_street_number, bp.primary_city, bp.primary_zip_code, bp.primary_country, bp.am_external_id, bp.am_first_name, bp.am_last_name, bp.am_email, bp.am_phone,
bp.franche_fixation, gclKind.code , bp.am_name, bp.name_searchable
FROM business_partner bp
left outer join generic_code_list gclCategory on gclCategory.uuid = bp.gcl_category_uuid
left outer join generic_code_list gclType on gclType.uuid = bp.gcl_type_uuid
left outer join generic_code_list gclKind on gclKind.uuid = bp.gcl_kind_uuid ;

--drop foreign reference
alter table business_partner_pairing_request drop constraint fk_bppr_business_partner;
alter table business_partner_ownership drop constraint fk_business_partner_ownership_business_partner;
alter table customer_account_challenge_code drop constraint fk_customer_account_challenge_code_business_partner_id;
alter table customer_request drop constraint fk_customer_request_business_partner_id;
alter table customer_notification drop constraint fk_notification_business_partner_id;

-- drop old
drop table business_partner;
-- replace with new
ALTER TABLE business_partner_tmp RENAME TO business_partner;

-- create constraints and indexes

ALTER TABLE business_partner add CONSTRAINT pk_business_partner PRIMARY KEY (id);
ALTER TABLE business_partner add CONSTRAINT fk_business_partner_synchronization_log FOREIGN KEY (synchronization_log_uuid) REFERENCES synchronization_log(uuid);

CREATE INDEX idx_business_partner_queue ON business_partner(queue);
CREATE INDEX idx_business_partner_synchronization_log_uuid ON business_partner(synchronization_log_uuid);
CREATE UNIQUE INDEX unique_idx_business_partner_external_id ON business_partner(external_id);
CREATE INDEX idx_business_partner_category ON business_partner(category);
CREATE INDEX idx_business_partner_type ON business_partner(type);
CREATE INDEX idx_business_partner_kind ON business_partner(kind);

-- recreate foreign reference
alter table business_partner_pairing_request add constraint fk_bppr_business_partner FOREIGN KEY (business_partner_id) REFERENCES business_partner(id);
alter table business_partner_ownership add constraint fk_business_partner_ownership_business_partner FOREIGN KEY (business_partner_id) REFERENCES business_partner(id);
alter table customer_account_challenge_code add constraint fk_customer_account_challenge_code_business_partner_id FOREIGN KEY (business_partner_id) REFERENCES business_partner(id);
alter table customer_request add constraint fk_customer_request_business_partner_id FOREIGN KEY (business_partner_id) REFERENCES business_partner(id);

alter table customer_notification add constraint fk_notification_business_partner_id FOREIGN KEY (business_partner_id) REFERENCES business_partner(id);