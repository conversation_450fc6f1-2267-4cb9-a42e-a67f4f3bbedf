
INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms, execution_type, report_customer_column, report_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_INVITATION_ACTIVE', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Pozyvaci email na prihlasenie do NZP', 'HIGH', 'Notifikacia ohladom odoslania pozyvacieho emailu na prihlasenie do NZP', null, null, false, true, 'AUTOMATIC', null, null);


INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Odoslanie pozývacieho emailu', '<PERSON><PERSON><PERSON><PERSON><PERSON>az<PERSON>ík, vitajte na portáli SPP. Prihlásiť sa môžete na adrese: ${portalExternalUrl}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>', 'Odoslanie pozývacieho emailu', null, (select uuid from notification_template where code = 'CUSTOMER_INVITATION_ACTIVE' and version = 1), 'SK', 'ACTIVE');

INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Odoslanie pozývacieho emailu', '[EN] Vážený zákazník, vitajte na portáli SPP. Prihlásiť sa môžete na adrese: ${portalExternalUrl}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>', '[EN] Odoslanie pozývacieho emailu', null, (select uuid from notification_template where code = 'CUSTOMER_INVITATION_ACTIVE' and version = 1), 'EN', 'ACTIVE');



INSERT INTO notification_template_variable (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
SELECT nt.uuid ,now(), now(), 1, ntv.variable, ntv.name, ntv.description, ntv.type FROM notification_template_variable ntv
join notification_template nt on (nt.code = 'CUSTOMER_INVITATION_ACTIVE')
where notification_template_uuid = (select uuid from notification_template where code = 'CUSTOMER_INVITATION');