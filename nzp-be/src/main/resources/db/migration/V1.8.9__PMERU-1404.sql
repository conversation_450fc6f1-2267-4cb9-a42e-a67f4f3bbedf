
CREATE INDEX if not exists idx_invoice_raw_delivery_point_id on invoice_raw(delivery_point_id);
CREATE INDEX if not exists idx_invoice_raw_invoice_id on invoice_raw(invoice_id);

drop table if exists invoice_delivery_point;
drop table if exists invoice_ownership;

CREATE TABLE invoice_contract
(
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    invoice_id                   text                      NOT NULL,
    contract_id                  text                      NOT NULL,

    CONSTRAINT pk_invoice_contract PRIMARY KEY (invoice_id, contract_id)
)
WITH (
    OIDS = FALSE
);

CREATE INDEX if not exists idx_invoice_contract_invoice_id on invoice_contract(invoice_id);
CREATE INDEX if not exists idx_invoice_contract_contract_id on invoice_contract(contract_id);

insert into invoice_contract
(created_at, invoice_id, contract_id)
select now(), coalesce(invoice_id, invoice_fa_id), contract_id from invoice_raw where contract_id is not null  group by invoice_id,invoice_fa_id, contract_id;


CREATE TABLE invoice_ownership
(
    created_at                      TIMESTAMP WITH TIME ZONE    NOT NULL,
    uuid                            uuid                        NOT NULL,
    type                            CHARACTER VARYING(32)       NOT NULL,
    inherited                       boolean   			        NOT NULL,
    customer_account_uuid           uuid                        NOT NULL,
    grantor_customer_account_uuid   uuid                        NOT NULL,
    invoice_id                      text                        NOT NULL,
    united_delivery_point_uuid      uuid,

    CONSTRAINT pk_invoice_ownership PRIMARY KEY (uuid),
    CONSTRAINT fk_invoice_ownership_customer_account FOREIGN KEY (customer_account_uuid)
        REFERENCES customer_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk_invoice_ownership_grantor_customer_account FOREIGN KEY (grantor_customer_account_uuid)
        REFERENCES customer_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk_invoice_ownership_invoice FOREIGN KEY (invoice_id)
       REFERENCES invoice (id) MATCH SIMPLE
       ON UPDATE NO ACTION
       ON DELETE NO ACTION,
    CONSTRAINT fk_invoice_ownership_udp FOREIGN KEY (united_delivery_point_uuid)
      REFERENCES united_delivery_point (uuid) MATCH SIMPLE
      ON UPDATE NO ACTION
      ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

CREATE INDEX if not exists idx_invoice_ownership_invoice_id on invoice_ownership(invoice_id);
CREATE INDEX if not exists idx_invoice_ownership_united_delivery_point_uuid on invoice_ownership(united_delivery_point_uuid);
CREATE INDEX if not exists idx_invoice_ownership_grantor_customer_account_uuid on invoice_ownership(grantor_customer_account_uuid);
CREATE INDEX if not exists idx_invoice_ownership_customer_account_uuid on invoice_ownership(customer_account_uuid);

create index if not exists idx_contract_account_ownership_grantor_customer on contract_account_ownership(grantor_customer_account_uuid);
create index if not exists idx_contract_account_ownership_contract on contract_account_ownership(contract_account_id);

create index if not exists idx_business_partner_ownership_grantor_customer on business_partner_ownership(grantor_customer_account_uuid);
create index if not exists idx_business_partner_ownership_business_partner on business_partner_ownership(business_partner_id);

create index if not exists idx_united_delivery_point_ownership_grantor_customer on united_delivery_point_ownership(grantor_customer_account_uuid);
create index if not exists idx_united_delivery_point_ownership_udp on united_delivery_point_ownership(united_delivery_point_uuid);

--force recalculation
ALTER TABLE invoice ALTER COLUMN invalidated TYPE boolean USING true;
