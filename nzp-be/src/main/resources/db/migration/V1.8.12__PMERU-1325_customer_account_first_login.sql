alter table customer_account add column first_login_at timestamp with time zone default null;

alter table customer_account_challenge_code add column session_id varchar(500);

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'CUSTOMER_LOGIN_TOKEN_PHONE_ATTEMPT','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Pokus o získanie tokenu po prihlásení','Pokus o získanie tokenu po prihlásení - na nastavenie tel. čísla bez zadávania hesla', (select uuid from generic_code_list where code like 'CUSTOMER_LOGIN_TOKEN_PHONE_ATTEMPT' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Login token attempt','Login token attempt - for customer phone setting without need of password', (select uuid from generic_code_list where code like 'CUSTOMER_LOGIN_TOKEN_PHONE_ATTEMPT' and type ='AUDIT_LOG_CODE'));

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'LOGIN_PHONE_CHALLENGE_CODE_EXPIRED','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Verifikačný kód pre zmenu čísla po prihlásení expiroval','Verifikačný kód pre zmenu čísla po prihlásení expiroval', (select uuid from generic_code_list where code like 'LOGIN_PHONE_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Login phone challenge code expired','Login phone challenge code expired', (select uuid from generic_code_list where code like 'LOGIN_PHONE_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'PHONE_CHANGE_TOKEN_INVALID','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Verifikačný token pre prvé prihlásenie nie je valídny','Verifikačný token pre prvé prihlásenie nie je valídny', (select uuid from generic_code_list where code like 'PHONE_CHANGE_TOKEN_INVALID' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','First login challenge code invalid','First login challenge code invalid', (select uuid from generic_code_list where code like 'PHONE_CHANGE_TOKEN_INVALID' and type ='AUDIT_LOG_CODE'));

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'PHONE_CHANGE_TOKEN_VALID','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Verifikačný token pre prvé prihlásenie je valídny','Verifikačný token pre prvé prihlásenie je valídny', (select uuid from generic_code_list where code like 'PHONE_CHANGE_TOKEN_VALID' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','First login challenge code valid','First login challenge code valid', (select uuid from generic_code_list where code like 'PHONE_CHANGE_TOKEN_VALID' and type ='AUDIT_LOG_CODE'));