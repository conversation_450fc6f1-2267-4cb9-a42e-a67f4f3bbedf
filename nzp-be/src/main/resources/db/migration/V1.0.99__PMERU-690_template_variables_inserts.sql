
INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'logged.customer.email', 'Email zalogovaného p<PERSON>žívateľa', '', 'STRING');

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.email' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'logged.customer.phone', 'Telefónne číslo zalogovaného používateľa', '', 'STRING');

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.phone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'logged.customer.firstName', 'Meno zalogovaného používateľa', '', 'STRING');

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.firstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'logged.customer.lastName', 'Priezvisko zalogovaného používateľa', '', 'STRING');

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.customer.lastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'logged.employee.id', 'Id zalogovaného používateľa', '', 'STRING');

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.employee.id' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.employee.id' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'logged.employee.id' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'logged.employee.login', 'Login zalogovaného zamestnanca', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.login' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.login' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.login' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'logged.employee.email', 'Email zalogovaného zamestnanca', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.email' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'logged.employee.firstName', 'Meno zalogovaného zamestnanca', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.firstName' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'logged.employee.lastName', 'Priezvisko zalogovaného zamestnanca', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.lastName' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'logged.employee.accountExpires', 'Dátum expirácie účtu zalogovaného zamestnanca', '', 'TIMESTAMP');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.accountExpires' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.accountExpires' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.accountExpires' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------
INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'logged.employee.userAccountControl', 'Kontola účtu zalogovaného zamestnanca', '', 'NUMBER');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.userAccountControl' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.userAccountControl' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'logged.employee.userAccountControl' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity ', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DEACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DEACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------
INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DEACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácia cielenej entity', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DEACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', '', 'STRING');

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_ACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DEACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', '', 'STRING');

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_ACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DEACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', '', 'STRING');

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_ACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DEACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', '', 'STRING');

INSERT INTO public.template_notification_variable(
	template_variable_uuid, notification_template_uuid)
	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_ACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DEACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.employee.id', 'Id cieleného zamestnanca', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.id' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.id' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.id' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.employee.login', 'Login cieleného zamestnanca', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.login' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.login' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.login' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.employee.email', 'Email cieleného zamestnanca', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.email' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.employee.firstName', 'Meno cieleného zamestnanca', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.firstName' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.employee.lastName', 'Priezvisko cieleného zamestnanca', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.lastName' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.employee.accountExpires', 'Dátum expirácie účtu cieleného zamestnanca', '', 'TIMESTAMP');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.accountExpires' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.accountExpires' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.accountExpires' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------
INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'target.employee.userAccountControl', 'Kontrola účtu cieleného zamestnanca', '', 'NUMBER');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.userAccountControl' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.userAccountControl' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'target.employee.userAccountControl' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'locale', 'Lokalizácia', '', 'NUMBER');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.name', 'Názov obchodného partnera', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.name' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.firstName', 'Meno obchodného partnera', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.firstName' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.lastName', 'Priezvisko obchodného partnera', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.lastName' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.email', 'Email obchodného partnera', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.email' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.phone', 'Telefonné číslo obchodného partnera', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.phone' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.street', 'Ulica obchodného partnera', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.street' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.streetNumber', 'Číslo domu obchodného partnera', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.streetNumber' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.city', 'Obec obchodného partnera', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.city' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.zipCode', 'PSČ obchodného partnera', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.zipCode' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.country', 'Štát obchodného partnera', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.country' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.postStreet', 'Ulica obchodného partnera - korešpodenčná adresa', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreet' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.postStreetNumber', 'Číslo obchodného partnera - korešpodenčná adresa', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));;

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postStreetNumber' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

    INSERT INTO public.template_variable(
        uuid, created_at, updated_at, version, variable, name, description, type)
        VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.postCity', 'Obec obchodného partnera - korešpodenčná adresa ', '', 'STRING');

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

    INSERT INTO public.template_notification_variable(
                template_variable_uuid, notification_template_uuid)
                VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCity' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

    ------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.postZipCode', 'PSČ obchodného partnera - korešpodenčná adresa', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postZipCode' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.postCountry', 'Štát obchodného partnera - korešpodenčná adresa', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.postCountry' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.amFirstName', 'Obchodný partner - meno manažéra predaja', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amFirstName' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.amLastName', 'Obchodný partner - priezvisko manažéra predaja', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amLastName' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.amEmail', 'Obchodný partner - email manažéra predaja', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amEmail' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.amPhone', 'Obchodný partner - telefónne číslo manažéra predaja', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amPhone' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contract.effectiveFrom', 'Zmluva - účinnosť od', '', 'TIMESTAMP');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contract.effectiveTo', 'Zmluva - účinnosť do', '', 'TIMESTAMP');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contract.product', 'Produkt zmluvy', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contract.advancePayAmount', 'Zmluva - suma zálohy vopred', '', 'NUMBER');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contract.billCycle', 'Zmluva - fakturačný cyklus', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contract.eeTariff', 'Zmluva - eeTariff', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.email', 'Email zmlvného účtu', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.street', 'Zmlvný účet - ulica', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.streetNumber', 'Zmlvný účet - číslo', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.city', 'Zmlvný účet - obec', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.zipCode', 'Zmlvný účet - PSČ', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.country', 'Zmlvný účet - štát', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'customer.email', 'Email zákazníka', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
            VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_ACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DEACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_ACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DEACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'customer.firstName', 'Meno zákazníka', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_ACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DEACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_ACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DEACTIVATION_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'deliveryPoint.type', 'Typ odberného miesta', '', 'STRING');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.type' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.type' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.type' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'deliveryPoint.eic', 'Odberné miesto - eic', '', 'STRING');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.eic' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.eic' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.eic' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.eic' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.eic' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.eic' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.eic' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.eic' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'deliveryPoint.pod', 'Odberné miesto - pod', '', 'STRING');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.pod' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.pod' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.pod' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.pod' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.pod' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.pod' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.pod' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.pod' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'deliveryPoint.street', 'Ulica odberného miesta', '', 'STRING');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.street' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.street' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.street' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.street' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.street' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.street' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.street' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.street' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'deliveryPoint.streetNumber', 'Číslo odberného miesta', '', 'STRING');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.streetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.streetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.streetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.streetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.streetNumber' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.streetNumber' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.streetNumber' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.streetNumber' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'deliveryPoint.city', 'Obec odberného miesta', '', 'STRING');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.city' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.city' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.city' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.city' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.city' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.city' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.city' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.city' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'deliveryPoint.zipCode', 'PSČ odberného miesta', '', 'STRING');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.zipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.zipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.zipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.zipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.zipCode' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.zipCode' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.zipCode' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.zipCode' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'deliveryPoint.country', 'Štát odberného miesta', '', 'STRING');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.country' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.country' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.country' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.country' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.country' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.country' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.country' and version = 1), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'deliveryPoint.country' and version = 1), (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'employee.login', 'Login zamestnanca', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'employee.login' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'employee.login' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'employee.login' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'employee.email', 'Email zamestnanca', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'employee.email' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'employee.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'employee.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'employee.firstName', 'Meno zamestnanca', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'employee.firstName' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'employee.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'employee.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'employee.lastName', 'Priezvisko zamestnanca', '', 'STRING');

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'employee.lastName' and version = 1), (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'employee.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1));

INSERT INTO public.template_notification_variable(
    	template_variable_uuid, notification_template_uuid)
    	VALUES ( (select uuid from template_variable where variable = 'employee.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'invoice.status', 'Status zamestnanca', '', 'STRING');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.status' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.status' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.status' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.status' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.status' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'invoice.type', 'Typ faktúry', '', 'STRING');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'invoice.amount', 'Čiastka faktúry', '', 'NUMBER');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.amount' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.amount' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.amount' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.amount' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.amount' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'invoice.dueAt', 'Faktúra - splatné do', '', 'TIMESTAMP');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.dueAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.dueAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.dueAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.dueAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.dueAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'invoice.issueAt', 'Faktúra - vystavené', '', 'TIMESTAMP');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.issueAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.issueAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.issueAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.issueAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.issueAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'invoice.executeAt', 'Faktúra - dátum dodania', '', 'TIMESTAMP');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.executeAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.executeAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.executeAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.executeAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.executeAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'invoice.vs', 'Faktúra - vs', '', 'STRING');
	
INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.vs' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.vs' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.vs' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.vs' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'invoice.vs' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1));

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'sharing.sharedBy.email', 'Zdieľané použivateľom - email', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'sharing.sharedBy.phone', 'Zdieľané použivateľom - telefónne číslo', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'sharing.sharedBy.firstName', 'Zdieľané použivateľom - meno', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'sharing.sharedBy.lastName', 'Zdieľané použivateľom - priezvisko', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------
