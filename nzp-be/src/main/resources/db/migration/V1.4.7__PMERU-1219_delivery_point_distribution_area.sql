INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ZZS', 'DISTRIBUTION_AREA', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'ZSE - distribúcia', 'ZSE - distribúcia', (select uuid from generic_code_list where code like 'ZZS' and type = 'DISTRIBUTION_AREA'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] ZSE - distribúcia', '[EN] ZSE - distribúcia', (select uuid from generic_code_list where code like 'ZZS' and type = 'DISTRIBUTION_AREA'));


INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ZSS', 'DISTRIBUTION_AREA', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'SSE - distribúcia', 'SSE - distribúcia', (select uuid from generic_code_list where code like 'ZSS' and type = 'DISTRIBUTION_AREA'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] SSE - distribúcia', '[EN] SSE - distribúcia', (select uuid from generic_code_list where code like 'ZSS' and type = 'DISTRIBUTION_AREA'));


INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ZVS', 'DISTRIBUTION_AREA', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'VSE - distribúcia', 'VSE - distribúcia', (select uuid from generic_code_list where code like 'ZVS' and type = 'DISTRIBUTION_AREA'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] VSE - distribúcia', '[EN] VSE - distribúcia', (select uuid from generic_code_list where code like 'ZVS' and type = 'DISTRIBUTION_AREA'));
