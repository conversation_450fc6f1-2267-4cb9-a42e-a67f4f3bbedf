
alter table audit_log drop column employee_account_id;

alter table audit_log add column customer_account_email         text;
alter table audit_log add column customer_account_phone         CHARACTER VARYING(32);
alter table audit_log add column customer_account_first_name    CHARACTER VARYING(64);
alter table audit_log add column customer_account_last_name     CHARACTER VARYING(64);
alter table audit_log add column employee_login                 CHARACTER VARYING(64);
alter table audit_log add column employee_email                 text;
alter table audit_log add column employee_first_name            CHARACTER VARYING(64);
alter table audit_log add column employee_last_name             CHARACTER VARYING(64);
alter table audit_log add column business_partner_first_name    CHARACTER VARYING(64);
alter table audit_log add column business_partner_last_name     CHARACTER VARYING(64);
alter table audit_log add column business_partner_name          CHARACTER VARYING(64);
alter table audit_log add column user_agent                     text;