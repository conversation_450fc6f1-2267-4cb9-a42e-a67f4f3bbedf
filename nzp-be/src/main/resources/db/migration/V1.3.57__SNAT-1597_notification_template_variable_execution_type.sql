ALTER TABLE notification_template_variable ADD COLUMN notification_template_execution_type CHARACTER VARYING (50) default 'AUTOMATIC' NOT NULL;
ALTER TABLE notification_template_variable ADD COLUMN uuid UUID;

update notification_template_variable
    set uuid = uuid_generate_v4();


alter table notification_template_variable DROP CONSTRAINT pk_notification_template_variable;
ALTER TABLE notification_template_variable ADD CONSTRAINT pk_notification_template_variable PRIMARY KEY (uuid);

ALTER TABLE notification_template_variable ALTER COLUMN notification_template_uuid DROP NOT NULL;

CREATE INDEX idx_notification_template_variable_notification_template_uuid ON notification_template_variable (notification_template_uuid);