
CREATE TABLE meter_reading_tmp (
	id text NOT NULL,
	created_at timestamptz NOT NULL,
	updated_at timestamptz NOT NULL,
	version int4 NOT NULL,
	external_id varchar(50) NULL,
	category varchar(50) NOT NULL,
	value numeric(19,3) NOT NULL,
	value_high numeric(19,3) NULL,
	read_at timestamptz NOT NULL,
	delivery_point_id varchar(50) NOT NULL,
	reason text NULL,
	synchronization_log_uuid uuid NULL,
	synchronization_at timestamptz NULL,
	description text NULL,
	kind text NULL,
	register int4 NULL,
	device_number text NULL,
	register_kind text NULL
);

-- transfer and transform
INSERT INTO meter_reading_tmp
(id, created_at, updated_at, version, external_id, category, value, value_high, read_at, delivery_point_id, reason, synchronization_log_uuid, synchronization_at, description, kind, register, device_number, register_kind)
SELECT mr.id, mr.created_at, mr.updated_at, mr.version, mr.external_id, mr.category, mr.value, mr.value_high, mr.read_at, mr.delivery_point_id, reasonGcl.code , mr.synchronization_log_uuid, mr.synchronization_at, mr.description, kindGcl.code, mr.register, mr.device_number, registerKindGcl.code
FROM meter_reading mr 
left outer join generic_code_list reasonGcl on reasonGcl.uuid = mr.gcl_reason_uuid 
left outer join generic_code_list kindGcl on kindGcl.uuid = mr.gcl_kind_uuid 
left outer join generic_code_list registerKindGcl on registerKindGcl.uuid = mr.gcl_register_kind_uuid ;

-- drop old
drop table meter_reading;
-- replace with new
ALTER TABLE meter_reading_tmp RENAME TO meter_reading;

-- create constraints and indexes
ALTER table meter_reading add CONSTRAINT pk_meter_reading PRIMARY KEY (id);
ALTER table meter_reading add CONSTRAINT fk_meter_reading_synchronization_log FOREIGN KEY (synchronization_log_uuid) REFERENCES synchronization_log(uuid);
CREATE INDEX idx_meter_reading_delivery_point_external_id ON meter_reading(delivery_point_id);
CREATE INDEX idx_meter_reading_read_at ON meter_reading(read_at);
CREATE UNIQUE INDEX unique_idx_meter_reading_external_id ON meter_reading(external_id);

CREATE INDEX idx_meter_reading_kind ON meter_reading(kind);
CREATE INDEX idx_meter_reading_register_kind ON meter_reading(register_kind);
CREATE INDEX idx_meter_reading_reason ON meter_reading(reason);
