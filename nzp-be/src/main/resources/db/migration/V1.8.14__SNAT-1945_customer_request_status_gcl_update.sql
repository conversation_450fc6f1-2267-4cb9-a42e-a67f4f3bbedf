update generic_code_list_i18n gcli18n
set name = 'Rozpracovaná'
where gcli18n.code_list_uuid = (select gcl.uuid from generic_code_list gcl where gcl.type = 'CUSTOMER_REQUEST_STATUS' and gcl.code = 'PRE_CREATED')
  and gcli18n.locale in ('sk', 'SK');

update generic_code_list_i18n gcli18n
set name = '[EN] Rozpracovaná'
where gcli18n.code_list_uuid = (select gcl.uuid from generic_code_list gcl where gcl.type = 'CUSTOMER_REQUEST_STATUS' and gcl.code = 'PRE_CREATED')
  and gcli18n.locale in ('en', 'EN');

--------------------

update generic_code_list_i18n gcli18n
set name = 'Pripravená na tlač'
where gcli18n.code_list_uuid = (select gcl.uuid from generic_code_list gcl where gcl.type = 'CUSTOMER_REQUEST_STATUS' and gcl.code = 'GENERATED')
  and gcli18n.locale in ('sk', 'SK');

update generic_code_list_i18n gcli18n
set name = '[EN] Pripravená na tlač'
where gcli18n.code_list_uuid = (select gcl.uuid from generic_code_list gcl where gcl.type = 'CUSTOMER_REQUEST_STATUS' and gcl.code = 'GENERATED')
  and gcli18n.locale in ('en', 'EN');

--------------------

update generic_code_list_i18n gcli18n
set name = 'Pripravená na zaslanie do SPP'
where gcli18n.code_list_uuid = (select gcl.uuid from generic_code_list gcl where gcl.type = 'CUSTOMER_REQUEST_STATUS' and gcl.code = 'CREATED')
  and gcli18n.locale in ('sk', 'SK');

update generic_code_list_i18n gcli18n
set name = '[EN] Pripravená na zaslanie do SPP'
where gcli18n.code_list_uuid = (select gcl.uuid from generic_code_list gcl where gcl.type = 'CUSTOMER_REQUEST_STATUS' and gcl.code = 'CREATED')
  and gcli18n.locale in ('en', 'EN');

--------------------

update generic_code_list_i18n gcli18n
set name = 'Zrušená zákazníkom pred odoslaním do SPP'
where gcli18n.code_list_uuid = (select gcl.uuid from generic_code_list gcl where gcl.type = 'CUSTOMER_REQUEST_STATUS' and gcl.code = 'CANCELLED_BY_USER')
  and gcli18n.locale in ('sk', 'SK');

update generic_code_list_i18n gcli18n
set name = '[EN] Zrušená zákazníkom pred odoslaním do SPP'
where gcli18n.code_list_uuid = (select gcl.uuid from generic_code_list gcl where gcl.type = 'CUSTOMER_REQUEST_STATUS' and gcl.code = 'CANCELLED_BY_USER')
  and gcli18n.locale in ('en', 'EN');

--------------------

update generic_code_list_i18n gcli18n
set name = 'Zrušená zákazníkom v SPP'
where gcli18n.code_list_uuid = (select gcl.uuid from generic_code_list gcl where gcl.type = 'CUSTOMER_REQUEST_STATUS' and gcl.code = 'SAP_CANCELLED_BY_USER')
  and gcli18n.locale in ('sk', 'SK');

update generic_code_list_i18n gcli18n
set name = '[EN] Zrušená zákazníkom v SPP'
where gcli18n.code_list_uuid = (select gcl.uuid from generic_code_list gcl where gcl.type = 'CUSTOMER_REQUEST_STATUS' and gcl.code = 'SAP_CANCELLED_BY_USER')
  and gcli18n.locale in ('en', 'EN');