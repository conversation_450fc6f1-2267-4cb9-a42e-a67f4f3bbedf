
insert into generic_code_list_i18n
    (select uuid_generate_v4(), now(), now(), 1, 'en', '[EN] ' || gcli.name, gcli.description, gcli.code_list_uuid
     from generic_code_list_i18n gcli
     where gcli.locale in ('sk', 'SK')
       and gcli.code_list_uuid not in (select code_list_uuid from generic_code_list_i18n gcli2 where gcli2.locale in ('en', 'EN')));

insert into customer_request_template_i18n
    (select uuid_generate_v4(), now(), now(), 1, 'en', '[EN] ' || crti.name, '[EN] ' || crti.description, crti.customer_request_template_uuid
     from customer_request_template_i18n crti
     where crti.locale in ('sk', 'SK')
       and crti.customer_request_template_uuid not in (select customer_request_template_uuid from customer_request_template_i18n crti2 where crti2.locale in ('en', 'EN')));
