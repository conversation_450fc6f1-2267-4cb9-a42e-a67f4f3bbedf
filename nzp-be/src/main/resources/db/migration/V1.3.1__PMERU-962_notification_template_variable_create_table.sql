CREATE TABLE notification_template_variable
(
    notification_template_uuid            uuid   NOT NULL,
    created_at      TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at      TIMESTAMP WITH TIME ZONE  NOT NULL,
    version         integer                   NOT NULL,

    variable        CHARACTER VARYING(100)    NOT NULL,
    name            text                      NOT NULL,
    description     text,
    type            CHARACTER VARYING(32)     NOT NULL,

    CONSTRAINT pk_notification_template_variable PRIMARY KEY (notification_template_uuid, variable),
    CONSTRAINT fk_notification_template_variable_notification_template FOREIGN KEY (notification_template_uuid)
        REFERENCES notification_template (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);


drop table template_notification_variable;
drop table template_variable;
