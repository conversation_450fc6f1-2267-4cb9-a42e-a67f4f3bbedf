-- Add new columns
ALTER TABLE delivery_point          ADD COLUMN notification_rk_checked_at       TIMESTAMP WITH TIME ZONE;
ALTER TABLE delivery_point          ADD COLUMN notification_mrk_checked_at      TIMESTAMP WITH TIME ZONE;
ALTER TABLE delivery_point_fact     ADD COLUMN check_rk_locked_by               CHARACTER VARYING(50);
ALTER TABLE delivery_point_fact     ADD COLUMN check_mrk_locked_by              CHARACTER VARYING(50);
ALTER TABLE delivery_point_fact     ADD COLUMN check_rk_retry_count             INTEGER;
ALTER TABLE delivery_point_fact     ADD COLUMN check_mrk_retry_count            INTEGER;

-- Fill in values
UPDATE delivery_point SET
	notification_rk_checked_at = notification_rkmrk_checked_at,
	notification_mrk_checked_at = notification_rkmrk_checked_at
WHERE notification_rkmrk_checked_at IS NOT NULL;

UPDATE delivery_point_fact SET
	check_rk_locked_by = check_rkmrk_locked_by,
	check_mrk_locked_by = check_rkmrk_locked_by
WHERE check_rkmrk_locked_by IS NOT NULL;

UPDATE delivery_point_fact SET
	check_rk_retry_count = check_rkmrk_retry_count,
	check_mrk_retry_count = check_rkmrk_retry_count
WHERE check_rkmrk_retry_count IS NOT NULL;

-- Update view
DROP VIEW v_delivery_point;
CREATE VIEW v_delivery_point AS (
    SELECT
        dp.id,
        dp.created_at,
        dp.updated_at,
        dp."version",
        dp.external_id,
        dp."type",
        dp.pod,
        dp.eic,
        dp.street,
        dp.street_number,
        dp.city,
        dp.zip_code,
        dp.country,
        dp.synchronization_log_uuid,
        dp.synchronization_at,
        dp.valid_from,
        dp.valid_to,
        dp.device_number,
        dp.notification_rk_checked_at,
        dp.notification_mrk_checked_at,
        dp.notification_zm_checked_at,
        dp.notification_dmm_checked_at,
        c.id AS contract_id,
        c.business_partner_id AS business_partner_id,
        c.united_delivery_point_id AS united_delivery_point_id
    FROM delivery_point dp
    JOIN contract c ON c.delivery_point_id = dp.id
    AND c.effective_from < current_date
    AND c.effective_to > current_date);

-- Remove old columns
ALTER TABLE delivery_point          DROP COLUMN notification_rkmrk_checked_at;
ALTER TABLE delivery_point_fact     DROP COLUMN check_rkmrk_locked_by;
ALTER TABLE delivery_point_fact     DROP COLUMN check_rkmrk_retry_count;