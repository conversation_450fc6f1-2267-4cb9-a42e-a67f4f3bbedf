---------------------
-- Drop Tables
---------------------
DROP TABLE business_partner_address;


---------------------
-- add/modify columns
---------------------
alter table business_partner add column synchronization_log_uuid        uuid                        NOT NULL;
alter table business_partner add column synchronization_at              TIMESTAMP WITH TIME ZONE    NOT NULL;
alter table business_partner add column gcl_category_uuid   uuid;
alter table business_partner add column gcl_type_uuid       uuid;

alter table business_partner add column primary_street    CHARACTER VARYING(64);
alter table business_partner add column primary_street_number    CHARACTER VARYING(32);
alter table business_partner add column primary_city      CHARACTER VARYING(64);
alter table business_partner add column primary_zip_code  CHARACTER VARYING(32);
alter table business_partner add column primary_country   CHARACTER VARYING(64);

alter table business_partner add column post_street     CHARACTER VARYING(64);
alter table business_partner add column post_street_number     CHARACTER VARYING(32);
alter table business_partner add column post_city       CHARACTER VARYING(64);
alter table business_partner add column post_zip_code   CHARACTER VARYING(32);
alter table business_partner add column post_country    CHARACTER VARYING(64);

alter table business_partner add column am_external_id  CHARACTER VARYING(50);
alter table business_partner add column am_first_name   CHARACTER VARYING(100);
alter table business_partner add column am_last_name    CHARACTER VARYING(100);
alter table business_partner add column am_email        CHARACTER VARYING(64);
alter table business_partner add column am_phone        CHARACTER VARYING(32);

alter table business_partner alter column status DROP NOT NULL;
alter table business_partner alter column queue DROP NOT NULL;

---------------------
-- Drop columns
---------------------
alter table business_partner drop column type;
alter table business_partner drop column category;

---------------------
-- Add indexes
---------------------
CREATE INDEX idx_business_partner_synchronization_log_uuid on business_partner(synchronization_log_uuid);

---------------------
-- Foreign keys
---------------------
alter table business_partner
ADD CONSTRAINT fk_business_partner_synchronization_log FOREIGN KEY (synchronization_log_uuid)
        REFERENCES synchronization_log (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;
alter table business_partner
ADD CONSTRAINT fk_business_partner_gcl_category FOREIGN KEY (gcl_category_uuid)
        REFERENCES generic_code_list (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;
alter table business_partner
ADD CONSTRAINT fk_business_partner_gcl_type FOREIGN KEY (gcl_type_uuid)
        REFERENCES generic_code_list (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;