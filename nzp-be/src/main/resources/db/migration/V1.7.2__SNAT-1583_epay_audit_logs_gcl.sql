INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EPAY_TRANSACTION_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pokus o platbu', 'Pokus o platbu', (select uuid from generic_code_list where code like 'EPAY_TRANSACTION_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Payment attempt', 'Payment attempt', (select uuid from generic_code_list where code like 'EPAY_TRANSACTION_ATTEMPT' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EPAY_GATEWAY_ERROR', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zlyhala platba na platobnej bráne', 'Zlyhala platba na platobnej bráne', (select uuid from generic_code_list where code like 'EPAY_GATEWAY_ERROR' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Payment failed on payment gateway', 'Payment failed on payment gateway', (select uuid from generic_code_list where code like 'EPAY_GATEWAY_ERROR' and type = 'AUDIT_LOG_CODE'));
