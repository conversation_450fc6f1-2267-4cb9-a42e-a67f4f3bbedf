

ALTER TABLE business_partner_ownership ADD COLUMN grantor_customer_account_uuid uuid;
ALTER TABLE business_partner_ownership ADD CONSTRAINT fk_bpo_granted_customer_account FOREIGN KEY (grantor_customer_account_uuid) REFERENCES customer_account(uuid);

ALTER TABLE contract_account_ownership ADD COLUMN grantor_customer_account_uuid uuid;
ALTER TABLE contract_account_ownership ADD CONSTRAINT fk_cao_granted_customer_account FOREIGN KEY (grantor_customer_account_uuid) REFERENCES customer_account(uuid);

ALTER TABLE contract_ownership ADD COLUMN grantor_customer_account_uuid uuid;
ALTER TABLE contract_ownership ADD CONSTRAINT fk_co_granted_customer_account FOREIGN KEY (grantor_customer_account_uuid) REFERENCES customer_account(uuid);

ALTER TABLE united_delivery_point_ownership ADD COLUMN grantor_customer_account_uuid uuid;
ALTER TABLE united_delivery_point_ownership ADD CONSTRAINT fk_udpo_granted_customer_account FOREIGN KEY (grantor_customer_account_uuid) REFERENCES customer_account(uuid);


update business_partner_ownership ent1      set grantor_customer_account_uuid = (select customer_account_uuid from business_partner_ownership ent2      where "type" = 'OWNER' and ent2.business_partner_uuid = ent1.business_partner_uuid limit 1);
update contract_account_ownership ent1      set grantor_customer_account_uuid = (select customer_account_uuid from contract_account_ownership ent2      where "type" = 'OWNER' and ent2.contract_account_uuid = ent1.contract_account_uuid limit 1);
update contract_ownership ent1              set grantor_customer_account_uuid = (select customer_account_uuid from contract_ownership ent2              where "type" = 'OWNER' and ent2.contract_uuid = ent1.contract_uuid limit 1);
update united_delivery_point_ownership ent1 set grantor_customer_account_uuid = (select customer_account_uuid from united_delivery_point_ownership ent2 where "type" = 'OWNER' and ent2.united_delivery_point_uuid = ent1.united_delivery_point_uuid limit 1);

delete from business_partner_ownership where grantor_customer_account_uuid is null;
delete from contract_account_ownership where grantor_customer_account_uuid is null;
delete from contract_ownership where grantor_customer_account_uuid is null;
delete from united_delivery_point_ownership where grantor_customer_account_uuid is null;


ALTER TABLE business_partner_ownership ALTER COLUMN grantor_customer_account_uuid SET NOT NULL;
ALTER TABLE contract_account_ownership ALTER COLUMN grantor_customer_account_uuid SET NOT NULL;
ALTER TABLE contract_ownership ALTER COLUMN grantor_customer_account_uuid SET NOT NULL;
ALTER TABLE united_delivery_point_ownership ALTER COLUMN grantor_customer_account_uuid SET NOT NULL;


DROP VIEW v_sharing_summary;
CREATE VIEW v_sharing_summary AS
(select created_at, type, inherited, customer_account_uuid, target_uuid, business_partner_uuid, contract_account_uuid, contract_uuid, delivery_point_uuid, united_delivery_point_uuid, entity_type, grantor_customer_account_uuid
from (
         select created_at, type, inherited, customer_account_uuid, business_partner_uuid as target_uuid, business_partner_uuid, Null::UUID as contract_account_uuid, Null::UUID as contract_uuid, Null::UUID as delivery_point_uuid, Null::UUID as united_delivery_point_uuid, CAST('BUSINESS_PARTNER' as CHARACTER VARYING(32)) as entity_type, grantor_customer_account_uuid
         from business_partner_ownership
         union
         select created_at, type, inherited, customer_account_uuid, contract_account_uuid as target_uuid, Null::UUID as business_partner_uuid, contract_account_uuid, Null::UUID as contract_uuid, Null::UUID as delivery_point_uuid, Null::UUID as united_delivery_point_uuid, CAST('CONTRACT_ACCOUNT' as CHARACTER VARYING(32)) as entity_type, grantor_customer_account_uuid
         from contract_account_ownership
         union
         select created_at, type, inherited, customer_account_uuid, contract_uuid as target_uuid, Null::UUID as business_partner_uuid, Null::UUID as contract_account_uuid, contract_uuid, Null::UUID as delivery_point_uuid, Null::UUID as united_delivery_point_uuid, CAST('CONTRACT' as CHARACTER VARYING(32)) as entity_type, grantor_customer_account_uuid
         from contract_ownership
         union
         select created_at, type, inherited, customer_account_uuid, united_delivery_point_uuid as target_uuid, Null::UUID as business_partner_uuid, Null::UUID as contract_account_uuid, Null::UUID as contract_uuid,  Null::UUID as delivery_point_uuid, united_delivery_point_uuid, CAST('UNITED_DELIVERY_POINT' as CHARACTER VARYING(32)) as entity_type, grantor_customer_account_uuid
         from united_delivery_point_ownership
     ) as q
    );