-- SNVI-281 PS - Prepis - rendering problem for BP

update notification_template_i18n
set updated_at = now(), version = version + 1, email_body = '<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník <#if businessPartner.name?has_content>${businessPartner.name}<#else>${businessPartner.firstName} ${businessPartner.lastName}</#if> požiadal o prepis odberného miesta na Vás, pre dokončenie žiadosti online kliknite <a href="${attributes.requestCompletionUrl}">sem</a></@spp.notification_email_template>'
where notification_template_id in (select nt.uuid from notification_template nt where nt.code in ('CUSTOMER_REQUEST_COMPLETION_REMINDER', 'CUSTOMER_REQUEST_COMPLETION_REQUEST'));

update notification_template_i18n
set updated_at = now(), version = version + 1, email_body = '<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník <#if businessPartner.name?has_content>${businessPartner.name}<#else>${businessPartner.firstName} ${businessPartner.lastName}</#if> priradený ku Vám požiadal o zmenu rezervovanej kapacity typu ${attributes.reservedValueType} na odbernom mieste ${deliveryPoint.street} ${deliveryPoint.streetNumber}</@spp.notification_email_template>'
where notification_template_id in (select nt.uuid from notification_template nt where nt.code in ('CUSTOMER_REQUEST_RESERVED_VALUES'));

update notification_template_i18n
set updated_at = now(), version = version + 1, email_body = '<#import "spp.macros_sk.ftl" as spp>
     <@spp.notification_email_template oslovenie=""><p>
     		[EN] Od zákazníka ${target.customer.email} sme obdržali požiadavku na párovanie zákazníckeho účtu <a href="${portalExternalUrl}/admin/portal-accounts/customers/${attributes.customerId}/detail">${target.customer.email}</a> na obchodného partnera <#if businessPartner.name?has_content>${businessPartner.name}<#else>${businessPartner.firstName} ${businessPartner.lastName}</#if> (${businessPartner.externalId}).
     </p></@spp.notification_email_template>'
where notification_template_id in (select nt.uuid from notification_template nt where nt.code in ('BUSINESS_PARTNER_PAIRING_VERIFY')) and upper(locale) = 'EN';

update notification_template_i18n
set updated_at = now(), version = version + 1, email_body = '<#import "spp.macros_sk.ftl" as spp>
     <@spp.notification_email_template oslovenie=""><p>
     		Od zákazníka ${target.customer.email} sme obdržali požiadavku na párovanie zákazníckeho účtu <a href="${portalExternalUrl}/admin/portal-accounts/customers/${attributes.customerId}/detail">${target.customer.email}</a> na obchodného partnera <#if businessPartner.name?has_content>${businessPartner.name}<#else>${businessPartner.firstName} ${businessPartner.lastName}</#if> (${businessPartner.externalId}).
     </p></@spp.notification_email_template>'
where notification_template_id in (select nt.uuid from notification_template nt where nt.code in ('BUSINESS_PARTNER_PAIRING_VERIFY')) and upper(locale) = 'SK';

update notification_template_i18n
set updated_at = now(), version = version + 1, email_body = '<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozornenie, že zákazník <#if businessPartner.name?has_content>${businessPartner.name}<#else>${businessPartner.firstName} ${businessPartner.lastName}</#if>, číslo obchodného partnera ${businessPartner.externalId} prekročil maximálnu dennú spotrebu plynu na svojom odbernom mieste.</p></@spp.notification_email_template>'
where notification_template_id in (select nt.uuid from notification_template nt where nt.code in ('DELIVERY_POINT_AM_CHECK_DMM')) and upper(locale) = 'SK';

update notification_template_i18n
set updated_at = now(), version = version + 1, email_body = '<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozornenie, že zákazník <#if businessPartner.name?has_content>${businessPartner.name}<#else>${businessPartner.firstName} ${businessPartner.lastName}</#if>, číslo obchodného partnera ${businessPartner.externalId} prekročil maximálnu rezervovanú kapacitu pre spotrebu elektriny na svojom odbernom mieste.</p></@spp.notification_email_template>'
where notification_template_id in (select nt.uuid from notification_template nt where nt.code in ('DELIVERY_POINT_AM_CHECK_MRK')) and upper(locale) = 'SK';

update notification_template_i18n
set updated_at = now(), version = version + 1, email_body = '<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozornenie, že zákazník <#if businessPartner.name?has_content>${businessPartner.name}<#else>${businessPartner.firstName} ${businessPartner.lastName}</#if>, číslo obchodného partnera ${businessPartner.externalId} prekročil rezervovanú kapacitu pre spotrebu elektriny na svojom odbernom mieste.</p></@spp.notification_email_template>'
where notification_template_id in (select nt.uuid from notification_template nt where nt.code in ('DELIVERY_POINT_AM_CHECK_RK')) and upper(locale) = 'SK';
