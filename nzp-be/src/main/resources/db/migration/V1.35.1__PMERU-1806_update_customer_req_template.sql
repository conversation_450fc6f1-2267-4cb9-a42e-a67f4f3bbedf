update customer_request_template set status = 'INACTIVE', "version" = "version" + 1, updated_at = now() where code = 'ZOM_ZODE';

update customer_request_template_i18n set "name" = '[EN] Žiadosť odberné miesto - Žiadosť o dodávku elektriny (PDF)', "version" = "version" + 1, updated_at = now()
where customer_request_template_uuid = (
	select uuid from customer_request_template where code = 'ZOM_ZODE'
)  and locale = 'EN';

update customer_request_template_i18n set "name" = 'Žiadosť o dodávku elektriny (PDF)',  "version" = "version" + 1, updated_at = now()
where customer_request_template_uuid = (
	select uuid from customer_request_template  where code = 'ZOM_ZODE'
) and locale = 'SK';


update customer_request set "content" = jsonb_set(content::jsonb, '{type}', '"ZOM_ZODE_PDF"'),  "version" = "version" + 1, updated_at = now()
where customer_request_template_id  in (select uuid from customer_request_template  where code = 'ZOM_ZODE');

update customer_request_template set code = 'ZOM_ZODE_PDF',  "version" = "version" + 1, updated_at = now() where code = 'ZOM_ZODE';


insert into customer_request_template(
    uuid,
    created_at,
    updated_at,
    version,
    status,
    code,
    price,
    confirmation_required,
    type,
    link,
    confirmation_valid_days)
values (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZODE', null, false, 'DIGITAL', null, null);

insert into customer_request_template_i18n (
    uuid,
    created_at,
    updated_at,
    version,
    locale,
    name,
    description,
    customer_request_template_uuid
) VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť o dodávku elektriny', 'Žiadosť o dodávku elektiny od SPP.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZODE')),
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Žiadosť odberné miesto - Žiadosť o dodávku elektriny', '[EN] Žiadosť o dodávku elektiny od SPP.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZODE'));
