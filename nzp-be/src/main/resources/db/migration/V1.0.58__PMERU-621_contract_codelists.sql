INSERT INTO generic_code_list (
    uuid,
    created_at,
    updated_at,
    version,
    code,
    type,
    valid_from,
    valid_to,
    parent_uuid
)
VALUES
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'STANDARD_E',
    'CONTRACT_PRODUCT_TYPE',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'R1',
    'CONTRACT_BILL_CYCLE',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'R2',
    'CONTRACT_BILL_CYCLE',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'R3',
    'CONTRACT_BILL_CYCLE',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'R4',
    'CONTRACT_BILL_CYCLE',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'R5',
    'CONTRACT_BILL_CYCLE',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'R6',
    'CONTRACT_BILL_CYCLE',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'R7',
    'CONTRACT_BILL_CYCLE',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'R8',
    'CONTRACT_BILL_CYCLE',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'R9',
    'CONTRACT_BILL_CYCLE',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'R10',
    'CONTRACT_BILL_CYCLE',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'R11',
    'CONTRACT_BILL_CYCLE',
    null,
    null,
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'R12',
    'CONTRACT_BILL_CYCLE',
    null,
    null,
    null
);

INSERT INTO generic_code_list_i18n (
    uuid,
    created_at,
    updated_at,
    version,
    locale,
    name,
    description,
    code_list_uuid
)
VALUES
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Slovenská republika',
    null,
    (select uuid from generic_code_list where code like 'STANDARD_E' and type like 'CONTRACT_PRODUCT_TYPE')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Fakturačný cyklus - január',
    null,
    (select uuid from generic_code_list where code like 'R1' and type like 'CONTRACT_BILL_CYCLE')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Fakturačný cyklus - február',
    null,
    (select uuid from generic_code_list where code like 'R2' and type like 'CONTRACT_BILL_CYCLE')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Fakturačný cyklus - marec',
    null,
    (select uuid from generic_code_list where code like 'R3' and type like 'CONTRACT_BILL_CYCLE')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Fakturačný cyklus - apríl',
    null,
    (select uuid from generic_code_list where code like 'R4' and type like 'CONTRACT_BILL_CYCLE')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Fakturačný cyklus - máj',
    null,
    (select uuid from generic_code_list where code like 'R5' and type like 'CONTRACT_BILL_CYCLE')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Fakturačný cyklus - jún',
    null,
    (select uuid from generic_code_list where code like 'R6' and type like 'CONTRACT_BILL_CYCLE')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Fakturačný cyklus - júl',
    null,
    (select uuid from generic_code_list where code like 'R7' and type like 'CONTRACT_BILL_CYCLE')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Fakturačný cyklus - august',
    null,
    (select uuid from generic_code_list where code like 'R8' and type like 'CONTRACT_BILL_CYCLE')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Fakturačný cyklus - september',
    null,
    (select uuid from generic_code_list where code like 'R9' and type like 'CONTRACT_BILL_CYCLE')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Fakturačný cyklus - október',
    null,
    (select uuid from generic_code_list where code like 'R10' and type like 'CONTRACT_BILL_CYCLE')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Fakturačný cyklus - november',
    null,
    (select uuid from generic_code_list where code like 'R11' and type like 'CONTRACT_BILL_CYCLE')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'sk_SK',
    'Fakturačný cyklus - december',
    null,
    (select uuid from generic_code_list where code like 'R12' and type like 'CONTRACT_BILL_CYCLE')
);