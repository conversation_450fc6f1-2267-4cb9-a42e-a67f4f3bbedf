
-------------------------------------------------
-- Contract
-------------------------------------------------
-- Alter columns
ALTER TABLE contract drop column version_batch_timestamp;
-- Indexes
CREATE INDEX idx_contract_synchronization_log_uuid on contract(synchronization_log_uuid);
-- Foreign keys
alter table contract
ADD CONSTRAINT fk_contract_synchronization_log FOREIGN KEY (synchronization_log_uuid)
        REFERENCES synchronization_log (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;
-------------------------------------------------
-- Contract Version
-------------------------------------------------
-- Indexes
CREATE INDEX idx_contract_version_synchronization_log_uuid on contract(synchronization_log_uuid);
-- Foreign keys
alter table contract_version
ADD CONSTRAINT fk_contract_version_synchronization_log FOREIGN KEY (synchronization_log_uuid)
        REFERENCES synchronization_log (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;
-------------------------------------------------
-- Meter Reading
-------------------------------------------------
-- Indexes
CREATE INDEX idx_meter_reading_synchronization_log_uuid on contract(synchronization_log_uuid);
-- Foreign keys
alter table meter_reading
ADD CONSTRAINT fk_meter_reading_synchronization_log FOREIGN KEY (synchronization_log_uuid)
        REFERENCES synchronization_log (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;

-------------------------------------------------
-- Customer Request
-------------------------------------------------
-- Alter columns
ALTER TABLE customer_request drop column batch_timestamp;
-------------------------------------------------
-- Delivery Point
-------------------------------------------------
-- Alter columns
ALTER TABLE delivery_point drop column batch_timestamp;
ALTER TABLE delivery_point drop column version_batch_timestamp;
ALTER TABLE delivery_point drop column facts_batch_timestamp;

-------------------------------------------------
-- Invoice
-------------------------------------------------
-- Indexes
CREATE INDEX idx_invoice_synchronization_log_uuid on invoice(synchronization_log_uuid);
-- Foreign keys
alter table invoice
ADD CONSTRAINT fk_invoice_synchronization_log FOREIGN KEY (synchronization_log_uuid)
        REFERENCES synchronization_log (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;
-------------------------------------------------
-- Contract account
-------------------------------------------------
-- Indexes
CREATE INDEX idx_contract_account_synchronization_log_uuid on contract_account(synchronization_log_uuid);
-- Foreign keys
alter table contract_account
ADD CONSTRAINT fk_contract_account_synchronization_log FOREIGN KEY (synchronization_log_uuid)
        REFERENCES synchronization_log (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;

-------------------------------------------------
-- Synchronization log
-------------------------------------------------
alter table synchronization_log add column file_timestamp             TIMESTAMP WITH TIME ZONE;