alter table audit_log rename to audit_log_old;
alter table audit_log_old rename constraint pk_audit_log to pk_audit_log_old;

CREATE TABLE audit_log (
	uuid uuid NOT NULL,
	created_at TIMESTAMP WITH TIME ZONE NOT NULL,
	code varchar(50) NOT NULL,
	entity_type varchar(50) NULL,
	entity_id varchar(72) NULL,
	session_id varchar(100) NULL,
	request_id varchar(50) NULL,
	remote_address varchar(20) NULL,
	logged_customer_account_uuid uuid NULL,
	attributes text NULL,
	business_partner_id varchar(50) NULL,
	logged_customer_account_email text NULL,
	employee_login varchar(64) NULL,
	employee_email text NULL,
	user_agent text NULL,
	entity_reference varchar(64) NULL,
	related_customer_account_uuid uuid NULL,
	related_customer_account_email varchar(64) NULL,
	logged_customer_account_ft text NULL,
	logged_customer_account_name text NULL,
	related_customer_account_ft text NULL,
	related_customer_account_name text NULL,
	employee_name text NULL,
	employee_ft text NULL,
	business_partner_ft text NULL,
	business_partner_name text NULL,
	entity_item text NULL,
	business_partner_external_id text NULL,
	failover boolean null,
	CONSTRAINT pk_audit_log PRIMARY KEY (uuid, created_at)
) PARTITION BY RANGE (created_at);


DO
 $script$
     DECLARE
         partition_name TEXT;
     BEGIN
          FOR i IN 0..20 LOOP
              partition_name := format(
                     'create table if not exists audit_log_%s partition of audit_log for values from (''%s'') to (''%s'')',
                     to_char(TO_DATE('********','YYYYMMDD') + make_interval(years := i), 'YYYY_MM_DD'),
                     to_char(TO_DATE('********','YYYYMMDD') + make_interval(years := i), 'YYYY-MM-DD'),
                     to_char(TO_DATE('********','YYYYMMDD') + make_interval(years := i), 'YYYY-MM-DD')
                );
             EXECUTE partition_name;
 
              partition_name := format(
                     'create table if not exists audit_log_%s partition of audit_log for values from (''%s'') to (''%s'')',
                     to_char(TO_DATE('********','YYYYMMDD') + make_interval(years := i), 'YYYY_MM_DD'),
                     to_char(TO_DATE('********','YYYYMMDD') + make_interval(years := i), 'YYYY-MM-DD'),
                     to_char(TO_DATE('********','YYYYMMDD') + make_interval(years := i+1), 'YYYY-MM-DD')
                );
             EXECUTE partition_name;
 
         END LOOP;
     END
 $script$;

-- default partition to store unmatched
create table audit_log_default_partition PARTITION OF audit_log DEFAULT;

insert into audit_log select * from audit_log_old;
drop table audit_log_old;

alter table audit_log add CONSTRAINT fk_audit_log_logged_customer_account FOREIGN KEY (logged_customer_account_uuid) REFERENCES customer_account(uuid);
alter table audit_log add CONSTRAINT fk_audit_log_related_customer_account FOREIGN KEY (related_customer_account_uuid) REFERENCES customer_account(uuid);
CREATE INDEX idx_audit_code ON audit_log(code);
CREATE INDEX idx_audit_customer ON audit_log(related_customer_account_uuid);
CREATE INDEX idx_audit_entity_type_id ON audit_log(entity_type, entity_id);
CREATE INDEX idx_audit_log_business_partner_external_id ON audit_log(business_partner_external_id);
CREATE INDEX idx_audit_log_business_partner_ft ON audit_log(business_partner_ft);
CREATE INDEX idx_audit_log_created_at ON audit_log(created_at);
CREATE INDEX idx_audit_log_employee_ft ON audit_log(employee_ft);
CREATE INDEX idx_audit_log_logged_customer_ft ON audit_log(logged_customer_account_ft);
CREATE INDEX idx_audit_log_related_customer_ft ON audit_log(related_customer_account_ft);
CREATE INDEX idx_audit_logged_customer ON audit_log(logged_customer_account_uuid);
