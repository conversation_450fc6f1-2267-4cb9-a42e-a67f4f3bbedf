-- DROP VIEW
drop view if exists v_sap_registration_request;

-- CRAETE VIEW
create or replace view v_sap_registration_request as (
select ca.uuid as "uuid"
, ca.created_at as "created_at"
, ca.updated_at as "updated_at"
, ca.email as "email"
, ca.phone as "phone"
, ca.first_name as "first_name"
, ca.last_name as "last_name"
, ca."type" as "type"
, ca.status as "status"
, ca.activation_at as "activation_at"
, ca.registration_at as "registration_at"
, ca.pair_request_initialize as "pair_request_initialize"
, ca.deactivated_at as "deactivated_at"
, ca.deactivation_reason as "deactivation_reason"
, ca.deleted_at as "deleted_at"
, ca.delete_reason as "delete_reason"
, ca.queue_category as "queue_category"
, ca.first_login_at as "first_login_at"
, ca.login_success_at as "login_success_at"
, ca.login_unsuccess_at as "login_unsuccess_at"
, al."attributes"::json ->> 'sapLogin' as "sap_login"
, al."attributes"::json ->> 'businessPartnerExternalId' as "business_partner_external_id"
from audit_log al 
join customer_account ca on ca.uuid = al.related_customer_account_uuid 
where al.code = 'CUSTOMER_SAP_REGISTRATION_REQUEST');