-- Add columns
ALTE<PERSON> TABLE employee_account            ADD COLUMN admin            BOOLEAN;
ALTER TABLE access_right                ADD COLUMN admin            BOOLEAN;
ALTER TABLE access_right                ADD COLUMN option_queue      BOOLEAN;
ALTER TABLE employee_account_access     ADD COLUMN queue            CHARACTER VARYING(50);

-- Rename constraints
ALTER TABLE access_role                 RENAME CONSTRAINT pk_employee_account_role                          TO pk_employee_account_group;
ALTER TABLE access_role_right           RENAME CONSTRAINT pk_access_role_right                              TO pk_access_group_right;
ALTER TABLE access_role_right           RENAME CONSTRAINT fk_access_role_right_access_right_code            TO fk_access_group_right_access_right_code;
ALTER TABLE access_role_right           RENAME CONSTRAINT fk_access_role_right_access_role_code             TO fk_access_group_right_access_group_code;
ALTER TABLE employee_account_access     RENAME CONSTRAINT fk_employee_account_access_access_role_code       TO fk_employee_account_access_access_group_code;

-- Rename columns
ALTER TABLE employee_account_access     RENAME access_role_code     TO access_group_code;
ALTER TABLE access_role_right           RENAME access_role_code     TO access_group_code;

-- Rename tables
ALTER TABLE access_role                 RENAME      TO access_group;
ALTER TABLE access_role_right           RENAME      TO access_group_right;