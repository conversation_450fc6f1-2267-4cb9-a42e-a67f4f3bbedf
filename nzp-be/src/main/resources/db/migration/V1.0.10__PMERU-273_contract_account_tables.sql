----------------------
-- billing_arrangement
----------------------

CREATE TABLE billing_arrangement
(
    uuid          uuid                      NOT NULL,
    created_at    TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at    TIMESTAMP WITH TIME ZONE  NOT NULL,
    version       integer                   NOT NULL,
    
    external_id   CHARACTER VARYING(50)     NOT NULL,
    payment_type  CHARACTER VARYING(50)     NOT NULL,
    bill_media    CHARACTER VARYING(50)     NOT NULL,
    email         CHARACTER VARYING(64),
    iban          CHARACTER VARYING(64),
    bic_swift     CHARACTER VARYING(32),
    sipo          CHARACTER VARYING(64),

    CONSTRAINT pk_billing_arrangement PRIMARY KEY (uuid)
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_billing_arrangement_external_id on billing_arrangement(external_id);



---------------------------
-- contract_account
---------------------------

CREATE TABLE contract_account
(
    uuid                    uuid                      NOT NULL,
    created_at              TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at              TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                 integer                   NOT NULL,
    
    external_id             CHARACTER VARYING(50)     NOT NULL,
    status                  CHARACTER VARYING(50)     NOT NULL,
    e_invoice               boolean,
    balance                 NUMERIC(19,2),
    
    billing_arrangement_id  uuid,
    business_partner_id     uuid                      NOT NULL,

    CONSTRAINT pk_contract_account PRIMARY KEY (uuid),
    CONSTRAINT fk_contract_account_billing_arrangement FOREIGN KEY (billing_arrangement_id)
        REFERENCES billing_arrangement (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk_contract_account_business_partner FOREIGN KEY (business_partner_id)
        REFERENCES business_partner (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_contract_account_external_id on contract_account(external_id);
CREATE INDEX idx_contract_account_business_partner_id on contract_account(business_partner_id);