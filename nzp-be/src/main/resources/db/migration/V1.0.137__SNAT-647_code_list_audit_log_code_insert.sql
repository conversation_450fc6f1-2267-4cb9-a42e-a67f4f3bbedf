INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_MARKETING_APPROVAL_APPROVED', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk', 'Aktivovanie marketingového súhlasu', null, (select uuid from generic_code_list where code like 'CUSTOMER_MARKETING_APPROVAL_APPROVED' and type = 'AUDIT_LOG_CODE'));

-------------------------------------------------------------------------------------------------------------------------

INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_MARKETING_APPROVAL_DENIED', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk', 'Deaktivovanie marketingového súhlasu', null, (select uuid from generic_code_list where code like 'CUSTOMER_MARKETING_APPROVAL_DENIED' and type = 'AUDIT_LOG_CODE'));


-------------------------------------------------------------------------------------------------------------------------

INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_NAME_CHANGE', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk', 'Zmena mena zákazníka', null, (select uuid from generic_code_list where code like 'CUSTOMER_NAME_CHANGE' and type = 'AUDIT_LOG_CODE'));

-------------------------------------------------------------------------------------------------------------------------

INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'COMPONENT_HELP_CREATE', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk', 'Vytvorenie helpu', null, (select uuid from generic_code_list where code like 'COMPONENT_HELP_CREATE' and type = 'AUDIT_LOG_CODE'));

-------------------------------------------------------------------------------------------------------------------------

INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'COMPONENT_HELP_CHANGE', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk', 'Zmena helpu', null, (select uuid from generic_code_list where code like 'COMPONENT_HELP_CHANGE' and type = 'AUDIT_LOG_CODE'));