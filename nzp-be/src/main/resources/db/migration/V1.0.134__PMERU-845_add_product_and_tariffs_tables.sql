----------
-- product
----------

CREATE TABLE product
(
    uuid                         uuid                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      INTEGER                   NOT NULL,

    status                       CHARACTER VARYING(50)     NOT NULL,
    code                         CHARACTER VARYING(255)    NOT NULL,
    name                         CHARACTER VARYING(255),
    description                  CHARACTER VARYING(1024),
    zp                           BOOLEAN                   NOT NULL,
    ee                           BOOLEAN                   NOT NULL,
    not_commodity                BOOLEAN                   NOT NULL,

    CONSTRAINT pk_product PRIMARY KEY (uuid)
);

-- indexes
CREATE INDEX idx_product_status on product(status);


----------
-- tariff
----------

CREATE TABLE tariff
(
    uuid                         uuid                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      INTEGER                   NOT NULL,

    status                       CHARACTER VARYING(50)     NOT NULL,
    code                         CHARACTER VARYING(255)    NOT NULL,
    name                         CHARACTER VARYING(255),
    description                  CHARACTER VARYING(1024),
    type                         CHARACTER VARYING(50)     NOT NULL,

    CONSTRAINT pk_tariff PRIMARY KEY (uuid)
);

-- indexes
CREATE INDEX idx_tariff_status on tariff(status);
