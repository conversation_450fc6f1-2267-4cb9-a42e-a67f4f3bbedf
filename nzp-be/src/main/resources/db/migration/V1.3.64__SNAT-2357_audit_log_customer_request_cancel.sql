-- insert values for AUDIT_LOG_CODE
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REQUEST_CANCELLED', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', '<PERSON><PERSON><PERSON><PERSON><PERSON> žiados<PERSON>', 'Zákaznícka žiadosť bola zrušená', (select uuid from generic_code_list where code like 'CUSTOMER_REQUEST_CANCELLED' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Zrušenie žiados<PERSON>', '[EN] Zákaznícka žiadosť bola zrušená', (select uuid from generic_code_list where code like 'CUSTOMER_REQUEST_CANCELLED' and type = 'AUDIT_LOG_CODE'));
