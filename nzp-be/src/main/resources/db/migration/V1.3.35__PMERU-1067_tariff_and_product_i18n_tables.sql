CREATE TABLE tariff_i18n
(
    uuid                            uuid                      NOT NULL,
    created_at                      TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                      TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                         integer                   NOT NULL,
    locale                          CHARACTER VARYING(8)      NOT NULL,
    name                            <PERSON><PERSON><PERSON>TER VARYING(255)    NOT NULL,
    description                     text,
    tariff_uuid                     uuid                      NOT NULL,

    CONSTRAINT pk_tariff_i18n PRIMARY KEY (uuid),
    CONSTRAINT fk_tariff_i18n_tariff_uuid FOREIGN KEY (tariff_uuid)
        REFERENCES tariff (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

CREATE UNIQUE INDEX idx_tariff_i18n_locale on tariff_i18n(tariff_uuid, locale);

CREATE TABLE product_i18n
(
    uuid                            uuid                      NOT NULL,
    created_at                      TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                      TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                         integer                   NOT NULL,
    locale                          CHARACTER VARYING(8)      NOT NULL,
    name                            CHARACTER VARYING(255)    NOT NULL,
    description                     text,
    product_uuid                    uuid                      NOT NULL,

    CONSTRAINT pk_product_i18n PRIMARY KEY (uuid),
    CONSTRAINT fk_product_i18n_product_uuid FOREIGN KEY (product_uuid)
        REFERENCES product (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

CREATE UNIQUE INDEX idx_product_i18n_locale on product_i18n(product_uuid, locale);



