
INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOP_ZOUA', '<PERSON><PERSON><PERSON><PERSON> obchodný partner - <PERSON><PERSON><PERSON> osobných údajov a adresy', 'Žiados<PERSON> pre zmenu osobných údajov obchodného partnera a jeho trvalej (resp. sídelnej) adresy a korešpondenčnej adresy.', 'BUSINESS_PARTNER', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOP_ZP', 'Žiadosť obchodný partner - Zmena priezviska', 'Žiadosť pre zmenu priezviska obchodného partnera.', 'BUSINESS_PARTNER', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOP_ZBU', 'Žiadosť obchodný partner - Zmena bankových údajov', 'Žiadosť pre zmenu bankových údajov obchodného partnera.', 'BUSINESS_PARTNER', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_O', 'Žiadosť odberné miesto - Odpočet', 'Žiadosť pre zadanie odpočtu.', 'DELIVERY_POINT', null);

------------------------------------------------------------------------------------------------------------------------

DELETE FROM public.customer_request_template WHERE code LIKE 'ZOP_ZOUOP';