delete from invoice_delivery_point;
delete from invoice_raw;
delete from invoice;

alter table invoice_raw drop CONSTRAINT pk_invoice_raw;
alter table invoice_raw ADD CONSTRAINT pk_invoice_raw PRIMARY KEY (external_id, item1, item2, item3);

alter table invoice_raw add column balancing_reason text;
alter table invoice_raw add column balancing_reference text;
alter table invoice_raw add column invoice_fa_id text;
alter table invoice_raw alter column invoice_id DROP NOT NULL;

CREATE INDEX idx_invoice_raw_invoice_fa_id ON invoice_raw(invoice_fa_id);

alter table invoice add column invalidated boolean;
alter table invoice add column reference text NOT NULL;
alter table invoice add column sap_id text NOT NULL;

CREATE INDEX idx_invoice_sap_id ON invoice(sap_id);