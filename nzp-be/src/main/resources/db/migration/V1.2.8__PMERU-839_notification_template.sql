INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, execution_type, name, priority,
    description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_INVOICE_SAP_ISSUED', 'ACTIVE', 'CUSTOMER_SYSTEM', 'AUTOMATIC', 'Vystavená upomienka',
    'LOW', 'Notifikacia ohľadom odoslania upomienky', null, null, false, false);

INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, status, header, email_body,
    email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'ACTIVE',
    '<#switch attributes.notificationType!"">
  <#case "SMS">
     <#assign forma="formou SMS správy">
     <#break>
  <#case "EMAIL">
     <#assign forma="formou e-mailu">
     <#break>
  <#case "LIST">
     <#assign forma="formou listu">
     <#break>
  <#default>
     <#assign forma="">
</#switch>
Dňa ${attributes.notificationSend} Vám bola zaslaná upomienka ${forma}'
, null, null, null, (select uuid from notification_template where code = 'CUSTOMER_INVOICE_SAP_ISSUED' and version = 1), 'SK');
