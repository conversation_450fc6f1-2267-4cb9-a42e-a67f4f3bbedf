INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOP_ZOUOP', '<PERSON><PERSON><PERSON><PERSON> obchodný partner - <PERSON><PERSON><PERSON> p<PERSON>z<PERSON>', '<PERSON><PERSON>dos<PERSON> pre zmenu osobných údajov obchodného partnera a jeho trvalej respektíve sídelnej adresy a korešpondenčnej adresy.', 'BUSINESS_PARTNER', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZAOM', '<PERSON><PERSON><PERSON><PERSON> odberné miesto - Zmena adresy odberného miesta', 'Žiadosť o zmenu adresy odberného miesta.', 'DELIVERY_POINT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZSPAPPP', 'Žiadosť odberné miesto - Zmena spôsobu platby a periodicity preddavkovej platby', 'Žiadosť o zmenu spôsobu platby a periodicity preddavkovej platby.', 'CONTRACT_ACCOUNT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZVPP', 'Žiadosť odberné miesto - Zmena výšky preddavkovej platby', 'Žiadosť o zmenu výšky preddavkovej platby.', 'CONTRACT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZSE', 'Žiadosť odberné miesto - Zmena sadzby elektriny', 'Žiadosť o zmenu sadzby dodávky elektriny', 'DELIVERY_POINT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZTP', 'Žiadosť odberné miesto - Zmena tarify plynu', 'Žiadosť o zmenu sadzby dodávky zemného plynu.', 'DELIVERY_POINT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_RPVF', 'Žiadosť odberné miesto - Reklamácia preddavkov vo faktúre', 'Žiadosť o reklamáciu preddavkov vo faktúre za dodávku.', 'INVOICE', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_RF', 'Žiadosť odberné miesto - Reklamácia faktúry', 'Žiadosť o reklamáciu faktúry.', 'DELIVERY_POINT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOP_S', 'Žiadosť obchodný partner - Sťažnosť', 'Sťažnosť obchodného partnera.', 'BUSINESS_PARTNER', null); -- v conf. je k BUSINESS_PARTNER otaznik

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_P', 'Žiadosť odberné miesto - Prepis', 'Žiadosť o prepis odberného miesta.', 'DELIVERY_POINT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZODE', 'Žiadosť odberné miesto - Žiadosť o dodávku elektriny', 'Žiadosť o dodávku elektiny od SPP.', 'BUSINESS_PARTNER', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZODP', 'Žiadosť odberné miesto - Žiadosť o dodávku plynu', 'Žiadosť o dodávku zemného plynu od SPP.', 'DELIVERY_POINT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZOUZ', 'Žiadosť odberné miesto - Žiadosť o ukončenie zmluvy ZP a EE', 'Žiadosť o ukončenie zmluvy s SPP.', 'CONTRACT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZOPAOO', 'Žiadosť odberné miesto - Žiadosť o prerušenie a obnovu odberu ZP a EE', 'Žiadosť o prerušenie/obnovu odberu.', 'DELIVERY_POINT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZOUSM', 'Žiadosť odberné miesto - Žiadosť o úradnú skúšku meradla', 'Žiadosť o preskúšanie meradla.', 'DELIVERY_POINT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZOFMC', 'Žiadosť odberné miesto - Žiadosť o faktúru mimo cyklu', 'Žiadosť o faktúru mimo cyklu.', 'DELIVERY_POINT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZOZODZP', 'Žiadosť odberné miesto - Žiadosť o zrýchlené obnovenie dodávky zemného plynu', 'Žiadosť o zrýchlené obnovenie dodávky zemného plynu od SPP.', 'DELIVERY_POINT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_DOS', 'Žiadosť odberné miesto - Dohoda o splátkach', 'Žiadosť o dohodu o splátkach faktúry po splatnosti.', 'INVOICE', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_POVZ', 'Žiadosť odberné miesto - Potvrdenie o výške záväzkov', 'Žiadosť o potvrdenie o výške záväzkov voči SPP.', 'BUSINESS_PARTNER', null); -- v conf. je k BUSINESS_PARTNER otaznik

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_POZV', 'Žiadosť odberné miesto - Potvrdenie o zmluvnom vzťahu', 'Žiadosť o potvrdenie o zmluvnom vzťahu s SPP.', 'CONTRACT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZOVDF', 'Žiadosť odberné miesto - Žiadosť o vystavenie duplikátu faktúry', 'Žiadosť o vystavenie duplikátu faktúry.', 'INVOICE', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZODS', 'Žiadosť odberné miesto - Žiadosť o doplnkové služby', 'Žiadosť o doplnkové služby k odbernému miestu.', 'CONTRACT', null);

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOP_RS ', 'Žiadosť obhodný partner - Registrácia sprostredkovateľa', 'Žiadosť o registráciu sprostredkovateľa.', 'BUSINESS_PARTNER ', null);-- v conf. je k BUSINESS_PARTNER otaznik

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZZU_ADEF ', 'Žiadosť zmluvný účet - Aktivácia/deaktivácia eFaktúry', 'Žiadosť o aktiváciu/deaktiváciu eFaktúry.', 'CONTRACT_ACCOUNT ', null);

