INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZVAPPP', 'DIGITAL');

INSERT INTO customer_request_template_i18n (
    uuid,
    created_at,
    updated_at,
    version,
    locale,
    name,
    description,
    customer_request_template_uuid
) VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Zmena výšky preddavkovej platby', 'Žiadosť o zmenu výšky preddavkovej platby.', (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZVAPPP'));
