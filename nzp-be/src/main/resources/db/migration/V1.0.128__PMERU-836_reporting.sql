INSERT INTO
  public.data_model (uuid, created_at, valid_from, flyway_version, model)
  VALUES (
  uuid_generate_v4(),
  now(),
  '2020-12-09 00:00:00',
  '1.0.128',
  '{
     "version": "1",
     "dataEntities": [
       {
         "name": "AuditLog",
         "tableName": "audit_log",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "code",
             "column": "code",
             "type": "ENUM",
             "options": [
               "CUSTOMER_LOGIN",
               "CUSTOMER_LOGIN_UNSUCCESS",
               "CUSTOMER_LOGIN_INVALID_TOKEN",
               "CUSTOMER_LOGIN_TEMPORARY_BLOCKED",
               "CUSTOMER_LOGIN_IMPERSONIFICATION",
               "CUSTOMER_LOGOUT",
               "CUSTOMER_LOGOUT_TIMEOUT",
               "CUSTOMER_PASS_VERIFICATION",
               "EMPLOYEE_PASS_VERIFICATION",
               "EMPLOYEE_LOGIN",
               "EMPLOYEE_LOGIN_UNSUCCESS",
               "EMPLOYEE_LOGIN_TEMPORARY_BLOCKED",
               "EMPLOYEE_LOGOUT",
               "EMPLOYEE_LOGOUT_TIMEOUT",
               "CUSTOMER_LOCK",
               "CUSTOMER_REGISTRATION_REQUEST",
               "CUSTOMER_RESEND_REGISTRATION_REQUEST",
               "CUSTOMER_REGISTRATION_SUCCESS",
               "CUSTOMER_PASSWORD_RECOVERY_REQUEST",
               "CUSTOMER_PASSWORD_RECOVERY_SUCCESS",
               "CUSTOMER_PASSWORD_CHANGE_SUCCESS",
               "CUSTOMER_SHARING_OWNER_GRANT",
               "CUSTOMER_SHARING_CONSUMER_GRANT",
               "CUSTOMER_SHARING_OWNER_REVOKE",
               "CUSTOMER_SHARING_CONSUMER_REVOKE",
               "CUSTOMER_DELETE_REQUEST",
               "CUSTOMER_RESEND_DELETE_REQUEST",
               "CUSTOMER_DELETE_SUCCESS",
               "BUSINESS_PARTNER_PAIRING_APPROVAL_APPROVED",
               "BUSINESS_PARTNER_PAIRING_APPROVAL_DENIED",
               "BUSINESS_PARTNER_PAIRING_VERIFY",
               "BUSINESS_PARTNER_PAIRING_CHALLENGE",
               "BUSINESS_PARTNER_PAIRING_CHALLENGE_RESEND",
               "BUSINESS_PARTNER_PAIRING_SUCCESS",
               "BUSINESS_PARTNER_PAIRING_REQUEST",
               "BUSINESS_PARTNER_UNPAIRING_SUCCESS",
               "BUSINESS_PARTNER_UNPAIRING_REQUEST",
               "CUSTOMER_PHONE_CHANGE_REQUEST",
               "CUSTOMER_PHONE_CHANGE_REQUEST_RESEND",
               "CUSTOMER_PHONE_CHANGE_SUCCESS",
               "CUSTOMER_EMAIL_CHANGE_REQUEST",
               "CUSTOMER_RESEND_EMAIL_CHANGE_REQUEST",
               "CUSTOMER_EMAIL_CHANGE_SUCCESS",
               "CUSTOMER_ACTIVATION_SUCCESS",
               "CUSTOMER_DEACTIVATION_SUCCESS",
               "CUSTOMER_REQUEST_CREATE",
               "CUSTOMER_PUBLIC_REQUEST_CREATE",
               "CUSTOMER_REQUEST_STATUS_CHANGE",
               "EMPLOYEE_REGISTRATION_REQUEST",
               "NOTIFICATION_TEMPLATE_CHANGE",
               "CUSTOMER_INVITATION",
               "EMPLOYEE_REPORT_RUN",
               "ACCESS_GROUP_ADD",
               "ACCESS_GROUP_UPDATE",
               "ACCESS_GROUP_REMOVE"
             ]
           },
           {
             "name": "entityType",
             "column": "entity_type",
             "type": "ENUM",
             "options": [
               "CUSTOMER_ACCOUNT",
               "EMPLOYEE_ACCOUNT",
               "BUSINESS_PARTNER",
               "CONTRACT",
               "CONTRACT_ACCOUNT",
               "DELIVERY_POINT",
               "DELIVERY_POINT_VERSION",
               "DELIVERY_POINT_FACT",
               "UNITED_DELIVERY_POINT",
               "INVOICE",
               "PAYMENT",
               "METER_READING",
               "CUSTOMER_REQUEST",
               "CUSTOMER_NOTIFICATION",
               "CUSTOMER_NOTIFICATION_TEMPLATE",
               "REPORT",
               "ACCESS_GROUP_ENTITY",
               "CONTRACT_VERSION",
               "CUSTOMER_ACCOUNT_CHALLENGE_CODE"
             ]
           },
           {
             "name": "entityId",
             "column": "entity_id",
             "type": "STRING"
           },
           {
             "name": "sessionId",
             "column": "session_id",
             "type": "STRING"
           },
           {
             "name": "requestId",
             "column": "request_id",
             "type": "STRING"
           },
           {
             "name": "remoteAddress",
             "column": "remote_address",
             "type": "STRING"
           },
           {
             "name": "customer_account",
             "type": "OBJECT",
             "column": "customer_account_uuid",
             "refEntity": "CustomerAccount"
           },
           {
             "name": "businessPartner",
             "type": "OBJECT",
             "column": "business_partner_external_id",
             "refEntity": "BusinessPartner",
             "referencedColumnName": "externalId"
           },
           {
             "name": "customerAccountEmail",
             "column": "customer_account_email",
             "type": "STRING"
           },
           {
             "name": "customerAccountPhone",
             "column": "customer_account_phone",
             "type": "STRING"
           },
           {
             "name": "customerAccountFirstName",
             "column": "customer_account_first_name",
             "type": "STRING"
           },
           {
             "name": "customerAccountLastName",
             "column": "customer_account_last_name",
             "type": "STRING"
           },
           {
             "name": "employeeLogin",
             "column": "employee_login",
             "type": "STRING"
           },
           {
             "name": "employeeEmail",
             "column": "employee_email",
             "type": "STRING"
           },
           {
             "name": "employeeFirstName",
             "column": "employee_first_name",
             "type": "STRING"
           },
           {
             "name": "employeeLastName",
             "column": "employee_last_name",
             "type": "STRING"
           },
           {
             "name": "userAgent",
             "column": "user_agent",
             "type": "STRING"
           },
           {
             "name": "entityFt",
             "column": "entity_ft",
             "type": "STRING"
           },
           {
             "name": "entityName",
             "column": "entity_name",
             "type": "STRING"
           },
           {
             "name": "entityReference",
             "column": "entity_reference",
             "type": "STRING"
           }
         ]
       },
       {
         "name": "CustomerAccount",
         "tableName": "customer_account",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "email",
             "column": "email",
             "type": "STRING"
           },
           {
             "name": "externalId",
             "column": "external_id",
             "type": "STRING"
           },
           {
             "name": "type",
             "column": "type",
             "type": "ENUM",
             "options": ["NEWSLETTER", "CUSTOMER", "BUSINESS_PARTNER"]
           },
           {
             "name": "status",
             "column": "status",
             "type": "ENUM",
             "options": [
               "ACTIVE",
               "INACTIVE",
               "UNVERIFIED",
               "PRE_REGISTRATION",
               "DELETED"
             ]
           },
           {
             "name": "activationAt",
             "column": "activation_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "registrationAt",
             "column": "registration_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "lockUntil",
             "column": "lock_until",
             "type": "TIMESTAMP"
           },
           {
             "name": "loginSuccessAt",
             "column": "login_success_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "loginUnsuccessAt",
             "column": "login_unsuccess_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "passwordUpdatedAt",
             "column": "password_updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "deactivationReason",
             "column": "deactivation_reason",
             "type": "STRING"
           },
           {
             "name": "deactivatedAt",
             "column": "deactivated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "deleteReason",
             "column": "delete_reason",
             "type": "STRING"
           },
           {
             "name": "deletedAt",
             "column": "deleted_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "locale",
             "column": "locale",
             "type": "STRING"
           },
           {
             "name": "phone",
             "column": "phone",
             "type": "STRING"
           },
           {
             "name": "firstName",
             "column": "first_name",
             "type": "STRING"
           },
           {
             "name": "lastName",
             "column": "last_name",
             "type": "STRING"
           },
           {
             "name": "queueCategory",
             "column": "queue_category",
             "type": "ENUM",
             "options": ["COLLECTIVE", "INDIVIDUAL", "ALL"]
           },
           {
             "name": "businessPartnerOwnerships",
             "type": "LIST",
             "refEntity": "BusinessPartnerOwnership",
             "joinColumn": "customer_account_uuid"
           },
           {
             "name": "contractAccountOwnerships",
             "type": "LIST",
             "refEntity": "ContractAccountOwnership",
             "joinColumn": "customer_account_uuid"
           },
           {
             "name": "contractOwnerships",
             "type": "LIST",
             "refEntity": "ContractOwnership",
             "joinColumn": "customer_account_uuid"
           },
           {
             "name": "unitedDeliveryPointOwnerships",
             "type": "LIST",
             "refEntity": "UnitedDeliveryPointOwnership",
             "joinColumn": "customer_account_uuid"
           },
           {
             "name": "approvals",
             "type": "LIST",
             "refEntity": "CustomerApproval",
             "joinColumn": "customer_id"
           }
         ]
       },
       {
         "name": "CustomerApproval",
         "tableName": "customer_approval",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "type",
             "column": "type",
             "type": "ENUM",
             "options": [
               "CONTRACT_CONDITIONS",
               "PROTECTION_PERSONAL_DATA",
               "MARKETING"
             ]
           },
           {
             "name": "approval",
             "column": "approval",
             "type": "BOOLEAN"
           },
           {
             "name": "customer",
             "type": "OBJECT",
             "column": "customer_id",
             "refEntity": "CustomerAccount"
           }
         ]
       },
       {
         "name": "IntervalMeterReadingZp",
         "tableName": "interval_meter_reading_zp",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "externalId",
             "column": "external_id",
             "type": "STRING"
           },
           {
             "name": "value",
             "column": "value",
             "type": "NUMBER"
           },
           {
             "name": "readAt",
             "column": "read_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "deliveryPoint",
             "type": "OBJECT",
             "column": "delivery_point_external_id",
             "refEntity": "DeliveryPoint",
             "referencedColumnName": "externalId"
           }
         ]
       },
       {
         "name": "MeterReading",
         "tableName": "meter_reading",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "externalId",
             "column": "external_id",
             "type": "STRING"
           },
           {
             "name": "category",
             "column": "category",
             "type": "ENUM",
             "options": ["SAP", "INFO"]
           },
           {
             "name": "value",
             "column": "value",
             "type": "NUMBER"
           },
           {
             "name": "valueHigh",
             "column": "value_high",
             "type": "NUMBER"
           },
           {
             "name": "readAt",
             "column": "read_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "description",
             "column": "description",
             "type": "STRING"
           },
           {
             "name": "register",
             "column": "register",
             "type": "INTEGER"
           },
           {
             "name": "deliveryPoint",
             "type": "OBJECT",
             "column": "delivery_point_external_id",
             "refEntity": "DeliveryPoint",
             "referencedColumnName": "externalId"
           }
         ]
       },
       {
         "name": "BusinessPartnerApproval",
         "tableName": "business_partner_approval",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "type",
             "column": "type",
             "type": "ENUM",
             "options": ["MARKETING"]
           },
           {
             "name": "approval",
             "column": "approval",
             "type": "BOOLEAN"
           },
           {
             "name": "businessPartner",
             "type": "OBJECT",
             "column": "business_partner_external_id",
             "refEntity": "BusinessPartner",
             "referencedColumnName": "externalId"
           }
         ]
       },
       {
         "name": "BusinessPartner",
         "tableName": "business_partner",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "externalId",
             "column": "external_id",
             "type": "STRING"
           },
           {
             "name": "status",
             "column": "STATUS",
             "type": "ENUM",
             "options": ["ACTIVE", "INACTIVE"]
           },
           {
             "name": "queue",
             "column": "queue",
             "type": "ENUM",
             "options": ["INDIVIDUAL", "COLLECTIVE"]
           },
           {
             "name": "taxIdNumber",
             "column": "tax_id_number",
             "type": "STRING"
           },
           {
             "name": "vatRegistrationNumber",
             "column": "vat_registration_number",
             "type": "STRING"
           },
           {
             "name": "companyRegistrationNumber",
             "column": "company_registration_number",
             "type": "STRING"
           },
           {
             "name": "name",
             "column": "name",
             "type": "STRING"
           },
           {
             "name": "firstName",
             "column": "first_name",
             "type": "STRING"
           },
           {
             "name": "lastName",
             "column": "last_name",
             "type": "STRING"
           },
           {
             "name": "birthNumber",
             "column": "birth_number",
             "type": "STRING"
           },
           {
             "name": "email",
             "column": "email",
             "type": "STRING"
           },
           {
             "name": "phone",
             "column": "phone",
             "type": "STRING"
           },
           {
             "name": "primaryStreet",
             "column": "primary_street",
             "type": "STRING"
           },
           {
             "name": "primaryStreetNumber",
             "column": "primary_street_number",
             "type": "STRING"
           },
           {
             "name": "primaryCity",
             "column": "primary_city",
             "type": "STRING"
           },
           {
             "name": "primaryZipCode",
             "column": "primary_zip_code",
             "type": "STRING"
           },
           {
             "name": "primaryCountry",
             "column": "primary_country",
             "type": "STRING"
           },
           {
             "name": "postStreet",
             "column": "post_street",
             "type": "STRING"
           },
           {
             "name": "postStreetNumber",
             "column": "post_street_number",
             "type": "STRING"
           },
           {
             "name": "postCity",
             "column": "post_city",
             "type": "STRING"
           },
           {
             "name": "postZipCode",
             "column": "post_zip_code",
             "type": "STRING"
           },
           {
             "name": "postCountry",
             "column": "post_country",
             "type": "STRING"
           },
           {
             "name": "amfirstName",
             "column": "am_first_name",
             "type": "STRING"
           },
           {
             "name": "amLastName",
             "column": "am_last_name",
             "type": "STRING"
           },
           {
             "name": "amEmail",
             "column": "am_email",
             "type": "STRING"
           },
           {
             "name": "amPhone",
             "column": "am_phone",
             "type": "STRING"
           },
           {
             "name": "francheFixation",
             "column": "franche_fixation",
             "type": "BOOLEAN"
           },
           {
             "name": "approvals",
             "type": "LIST",
             "refEntity": "BusinessPartnerApproval",
             "joinColumn": "business_partner_id"
           },
           {
             "name": "businessPartnerOwnerships",
             "type": "LIST",
             "refEntity": "BusinessPartnerOwnership",
             "joinColumn": "business_partner_uuid"
           }
         ]
       },
       {
         "name": "ContractAccount",
         "tableName": "contract_account",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "externalId",
             "column": "external_id",
             "type": "STRING"
           },
           {
             "name": "status",
             "column": "status",
             "type": "ENUM",
             "options": ["ACTIVE", "INACTIVE"]
           },
           {
             "name": "eInvoice",
             "column": "e_invoice",
             "type": "BOOLEAN"
           },
           {
             "name": "email",
             "column": "email",
             "type": "STRING"
           },
           {
             "name": "street",
             "column": "street",
             "type": "STRING"
           },
           {
             "name": "streetNumber",
             "column": "street_number",
             "type": "STRING"
           },
           {
             "name": "city",
             "column": "city",
             "type": "STRING"
           },
           {
             "name": "zipCode",
             "column": "zip_code",
             "type": "STRING"
           },
           {
             "name": "country",
             "column": "country",
             "type": "STRING"
           },
           {
             "name": "iban",
             "column": "iban",
             "type": "STRING"
           },
           {
             "name": "sipo",
             "column": "sipo",
             "type": "STRING"
           },
           {
             "name": "vs",
             "column": "vs",
             "type": "STRING"
           },
           {
             "name": "businessPartner",
             "type": "OBJECT",
             "column": "business_partner_external_id",
             "refEntity": "BusinessPartner",
             "referencedColumnName": "externalId"
           },
           {
             "name": "contractAccountOwnerships",
             "type": "LIST",
             "refEntity": "ContractAccountOwnership",
             "joinColumn": "contract_account_uuid"
           }
         ]
       },
       {
         "name": "Contract",
         "tableName": "contract",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "externalId",
             "column": "external_id",
             "type": "STRING"
           },
           {
             "name": "signatureAt",
             "column": "signature_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "effectiveFrom",
             "column": "effective_from",
             "type": "TIMESTAMP"
           },
           {
             "name": "effectiveTo",
             "column": "effective_to",
             "type": "TIMESTAMP"
           },
           {
             "name": "contractAccount",
             "type": "OBJECT",
             "column": "contract_account_external_id",
             "refEntity": "ContractAccount",
             "referencedColumnName": "externalId"
           },
           {
             "name": "contractOwnerships",
             "type": "LIST",
             "refEntity": "ContractOwnership",
             "joinColumn": "contract_uuid"
           },
           {
             "name": "versions",
             "type": "LIST",
             "refEntity": "ContractVersion",
             "joinColumn": "contract_external_id",
             "referencedColumnName": "externalId"
           }
         ]
       },
       {
         "name": "ContractVersion",
         "tableName": "contract_version",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "item",
             "column": "item",
             "type": "INTEGER"
           },
           {
             "name": "product",
             "column": "product",
             "type": "STRING"
           },
           {
             "name": "validFrom",
             "column": "valid_from",
             "type": "TIMESTAMP"
           },
           {
             "name": "validTo",
             "column": "valid_to",
             "type": "TIMESTAMP"
           },
           {
             "name": "bindingPeriod",
             "column": "binding_period",
             "type": "INTEGER"
           },
           {
             "name": "bindingTo",
             "column": "binding_to",
             "type": "TIMESTAMP"
           },
           {
             "name": "bindingProlong1",
             "column": "binding_prolong_1",
             "type": "TIMESTAMP"
           },
           {
             "name": "bindingProlong2",
             "column": "binding_prolong_2",
             "type": "TIMESTAMP"
           },
           {
             "name": "noticePeriod",
             "column": "notice_period",
             "type": "INTEGER"
           },
           {
             "name": "advancePayAmount",
             "column": "advance_pay_amount",
             "type": "NUMBER"
           },
           {
             "name": "eeTariffCount",
             "column": "ee_tariff_count",
             "type": "ENUM",
             "options": ["T1", "T2"]
           },
           {
             "name": "contract",
             "type": "OBJECT",
             "column": "contract_external_id",
             "refEntity": "Contract",
             "referencedColumnName": "externalId"
           }
         ]
       },
       {
         "name": "DeliveryPoint",
         "tableName": "delivery_point",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "externalId",
             "column": "external_id",
             "type": "STRING"
           },
           {
             "name": "validFrom",
             "column": "valid_from",
             "type": "TIMESTAMP"
           },
           {
             "name": "validTo",
             "column": "valid_to",
             "type": "TIMESTAMP"
           },
           {
             "name": "type",
             "column": "type",
             "type": "ENUM",
             "options": ["EE", "ZP"]
           },
           {
             "name": "street",
             "column": "street",
             "type": "STRING"
           },
           {
             "name": "streetNumber",
             "column": "street_number",
             "type": "STRING"
           },
           {
             "name": "city",
             "column": "city",
             "type": "STRING"
           },
           {
             "name": "zipCode",
             "column": "zip_code",
             "type": "STRING"
           },
           {
             "name": "country",
             "column": "country",
             "type": "STRING"
           },
           {
             "name": "pod",
             "column": "pod",
             "type": "STRING"
           },
           {
             "name": "eic",
             "column": "eic",
             "type": "NUMBER"
           },
           {
             "name": "contract",
             "type": "OBJECT",
             "column": "contract_external_id",
             "refEntity": "Contract",
             "referencedColumnName": "externalId"
           },
           {
             "name": "businessPartner",
             "type": "OBJECT",
             "column": "business_partner_external_id",
             "refEntity": "BusinessPartner",
             "referencedColumnName": "externalId"
           },
           {
             "name": "deviceNumber",
             "column": "device_number",
             "type": "STRING"
           },
           {
             "name": "unitedDeliveryPoint",
             "type": "OBJECT",
             "column": "united_delivery_point_id",
             "refEntity": "UnitedDeliveryPoint"
           },
           {
             "name": "deliveryPointVersions",
             "type": "LIST",
             "refEntity": "DeliveryPointVersion",
             "joinColumn": "deliver_point_external_id",
             "referencedColumnName": "externalId"
           },
           {
             "name": "deliveryPointFacts",
             "type": "LIST",
             "refEntity": "DeliveryPointFact",
             "joinColumn": "deliver_point_external_id",
             "referencedColumnName": "externalId"
           }
         ]
       },
       {
         "name": "DeliveryPointFact",
         "tableName": "delivery_point_fact",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "validFrom",
             "column": "valid_from",
             "type": "TIMESTAMP"
           },
           {
             "name": "validTo",
             "column": "valid_to",
             "type": "TIMESTAMP"
           },
           {
             "name": "deliveryPoint",
             "type": "OBJECT",
             "column": "deliver_point_external_id",
             "refEntity": "DeliveryPoint",
             "referencedColumnName": "externalId"
           },
           {
             "name": "operand",
             "column": "operand",
             "type": "ENUM",
             "options": [
               "EDITARIFID",
               "EISTIC",
               "EMRK",
               "ERKA",
               "ETYPMERAN",
               "PDMM_M3",
               "PZJEDRMMWH"
             ]
           },
           {
             "name": "value",
             "column": "operand_value",
             "type": "STRING"
           },
           {
             "name": "value2",
             "column": "operand_value2",
             "type": "STRING"
           },
           {
             "name": "tarifart",
             "column": "tarifart",
             "type": "STRING"
           }
         ]
       },
       {
         "name": "DeliveryPointVersion",
         "tableName": "delivery_point_version",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "validFrom",
             "column": "valid_from",
             "type": "TIMESTAMP"
           },
           {
             "name": "validTo",
             "column": "valid_to",
             "type": "TIMESTAMP"
           },
           {
             "name": "deliveryPoint",
             "type": "OBJECT",
             "column": "deliver_point_external_id",
             "refEntity": "DeliveryPoint",
             "referencedColumnName": "externalId"
           },
           {
             "name": "tariffRate",
             "column": "tariff_rate",
             "type": "STRING"
           },
           {
             "name": "readingCycleValue",
             "column": "reading_cycle_value",
             "type": "INTEGER"
           },
           {
             "name": "readingCycle",
             "column": "reading_cycle",
             "type": "STRING"
           }
         ]
       },
       {
         "name": "Invoice",
         "tableName": "invoice",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "externalId",
             "column": "external_id",
             "type": "STRING"
           },
           {
             "name": "status",
             "column": "status",
             "type": "ENUM",
             "options": ["PAID", "UNPAID"]
           },
           {
             "name": "type",
             "column": "type",
             "type": "ENUM",
             "options": ["INVOICE", "CREDIT", "ADVANCE_INVOICE"]
           },
           {
             "name": "amount",
             "column": "amount",
             "type": "NUMBER"
           },
           {
             "name": "dueAt",
             "column": "due_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "issueAt",
             "column": "issue_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "executeAt",
             "column": "execute_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "notificationCreatedAt",
             "column": "notification_created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "notificationDueDateAt",
             "column": "notification_duedate_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "lockedBy",
             "column": "locked_by",
             "type": "STRING"
           },
           {
             "name": "retryCount",
             "column": "retry_count",
             "type": "INTEGER"
           },
           {
             "name": "vs",
             "column": "vs",
             "type": "STRING"
           },
           {
             "name": "deliveryPoint",
             "type": "OBJECT",
             "column": "delivery_point_external_id",
             "refEntity": "DeliveryPoint",
             "referencedColumnName": "externalId"
           },
           {
             "name": "payments",
             "type": "LIST",
             "refEntity": "Payment",
             "joinColumn": "invoice_external_id",
             "referencedColumnName": "externalId"
           }
         ]
       },
       {
         "name": "Payment",
         "tableName": "payment",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "externalId",
             "column": "external_id",
             "type": "STRING"
           },
           {
             "name": "status",
             "column": "status",
             "type": "ENUM",
             "options": ["PAID", "UNPAID"]
           },
           {
             "name": "type",
             "column": "type",
             "type": "ENUM",
             "options": ["DIRECT_DEBIT", "MANDATE", "SIPO"]
           },
           {
             "name": "amount",
             "column": "amount",
             "type": "NUMBER"
           },
           {
             "name": "executeDate",
             "column": "execute_date",
             "type": "TIMESTAMP"
           },
           {
             "name": "invoice",
             "type": "OBJECT",
             "refEntity": "Invoice",
             "joinColumn": "invoice_external_id",
             "referencedColumnName": "externalId"
           }
         ]
       },
       {
         "name": "UnitedDeliveryPoint",
         "tableName": "united_delivery_point",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "street",
             "column": "street",
             "type": "STRING"
           },
           {
             "name": "streetNumber",
             "column": "street_number",
             "type": "STRING"
           },
           {
             "name": "city",
             "column": "city",
             "type": "STRING"
           },
           {
             "name": "zipCode",
             "column": "zip_code",
             "type": "STRING"
           },
           {
             "name": "country",
             "column": "country",
             "type": "STRING"
           },
           {
             "name": "hidden",
             "column": "hidden",
             "type": "BOOLEAN"
           },
           {
             "name": "businessPartner",
             "type": "OBJECT",
             "column": "business_partner_external_id",
             "refEntity": "BusinessPartner",
             "referencedColumnName": "externalId"
           },
           {
             "name": "deliveryPoints",
             "type": "LIST",
             "refEntity": "DeliveryPoint",
             "joinColumn": "united_delivery_point_id"
           },
           {
             "name": "unitedDeliveryPointOwnerships",
             "type": "LIST",
             "refEntity": "UnitedDeliveryPointOwnership",
             "joinColumn": "united_delivery_point_uuid"
           },
           {
             "name": "types",
             "column": "types",
             "type": "STRING"
           },
           {
             "name": "pairingStatus",
             "column": "pairing_status",
             "type": "ENUM",
             "options": ["IN_PROGRESS", "DONE"]
           }
         ]
       },
       {
         "name": "CustomerRequest",
         "tableName": "customer_request",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "external_id",
             "column": "externalId",
             "type": "STRING"
           },
           {
             "name": "status",
             "column": "status",
             "type": "ENUM",
             "options": [
               "CREATED",
               "REGISTERED",
               "CANCELLATION",
               "CANCELLED",
               "FINISHED"
             ]
           },
           {
             "name": "request_cancel",
             "column": "requestCancel",
             "type": "BOOLEAN"
           },
           {
             "name": "issuedAt",
             "column": "issued_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "statusUpdatedAt",
             "column": "status_updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "notificationStatusUpdatedAt",
             "column": "notification_status_updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "retryCount",
             "column": "retry_count",
             "type": "INTEGER"
           },
           {
             "name": "lockedBy",
             "column": "locked_by",
             "type": "STRING"
           },
           {
             "name": "customer",
             "type": "OBJECT",
             "column": "customer_id",
             "refEntity": "CustomerAccount"
           },
           {
             "name": "businessPartner",
             "type": "OBJECT",
             "column": "business_partner_id",
             "refEntity": "BusinessPartner"
           },
           {
             "name": "deliveryPoint",
             "type": "OBJECT",
             "column": "delivery_point_id",
             "refEntity": "DeliveryPoint"
           }
         ]
       },
       {
         "name": "CustomerNotification",
         "tableName": "customer_notification",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "source",
             "column": "source",
             "type": "ENUM",
             "options": ["NZP", "EXTERNAL"]
           },
           {
             "name": "externalId",
             "column": "external_id",
             "type": "STRING"
           },
           {
             "name": "status",
             "column": "status",
             "type": "ENUM",
             "options": ["CREATED", "RENDERED"]
           },
           {
             "name": "header",
             "column": "header",
             "type": "STRING"
           },
           {
             "name": "emailBody",
             "column": "email_body",
             "type": "STRING"
           },
           {
             "name": "emailSubject",
             "column": "email_subject",
             "type": "STRING"
           },
           {
             "name": "smsBody",
             "column": "sms_body",
             "type": "STRING"
           },
           {
             "name": "entityType",
             "column": "entity_type",
             "type": "ENUM",
             "options": [
               "CUSTOMER_ACCOUNT",
               "EMPLOYEE_ACCOUNT",
               "BUSINESS_PARTNER",
               "CONTRACT",
               "CONTRACT_ACCOUNT",
               "CONTRACT_PRODUCT",
               "DELIVERY_POINT",
               "DELIVERY_POINT_VERSION",
               "DELIVERY_POINT_FACT",
               "UNITED_DELIVERY_POINT",
               "INVOICE",
               "PAYMENT",
               "METER_READING",
               "CUSTOMER_REQUEST",
               "CUSTOMER_NOTIFICATION",
               "CUSTOMER_NOTIFICATION_TEMPLATE",
               "REPORT",
               "ACCESS_GROUP_ENTITY",
               "CONTRACT_VERSION",
               "CUSTOMER_ACCOUNT_CHALLENGE_CODE"
             ]
           },
           {
             "name": "entityId",
             "column": "entity_id",
             "type": "STRING"
           },
           {
             "name": "phone",
             "column": "phone",
             "type": "STRING"
           },
           {
             "name": "email",
             "column": "email",
             "type": "STRING"
           },
           {
             "name": "locale",
             "column": "locale",
             "type": "STRING"
           },
           {
             "name": "employeeLogin",
             "column": "employee_login",
             "type": "STRING"
           },
           {
             "name": "customerAccount",
             "type": "OBJECT",
             "column": "customer_account_id",
             "refEntity": "CustomerAccount"
           },
           {
             "name": "shareFrom",
             "type": "OBJECT",
             "column": "share_from_id",
             "refEntity": "CustomerAccount"
           },
           {
             "name": "readAt",
             "column": "read_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "read",
             "column": "read",
             "type": "BOOLEAN"
           },
           {
             "name": "businessPartner",
             "type": "OBJECT",
             "column": "business_partner_id",
             "refEntity": "BusinessPartner"
           },
           {
             "name": "deliveryPoint",
             "type": "OBJECT",
             "column": "delivery_point_id",
             "refEntity": "DeliveryPoint"
           },
           {
             "name": "renderStatuses",
             "type": "LIST",
             "refEntity": "CustomerNotificationRenderStatus",
             "joinColumn": "customer_notification_id"
           },
           {
             "name": "sendStatuses",
             "type": "LIST",
             "refEntity": "CustomerNotificationSendStatus",
             "joinColumn": "customer_notification_id"
           }
         ]
       },
       {
         "name": "CustomerNotificationSendStatus",
         "tableName": "customer_notification_send_status",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "priority",
             "column": "priority",
             "type": "ENUM",
             "options": ["LOW", "HIGH"]
           },
           {
             "name": "channel",
             "column": "channel",
             "type": "ENUM",
             "options": ["EMAIL", "SMS"]
           },
           {
             "name": "email",
             "column": "email",
             "type": "STRING"
           },
           {
             "name": "phone",
             "column": "phone",
             "type": "STRING"
           },
           {
             "name": "retryCount",
             "column": "retry_count",
             "type": "INTEGER"
           },
           {
             "name": "lockedBy",
             "column": "locked_by",
             "type": "STRING"
           },
           {
             "name": "customerNotification",
             "type": "OBJECT",
             "column": "customer_notification_id",
             "refEntity": "CustomerNotification"
           }
         ]
       },
       {
         "name": "CustomerNotificationRenderStatus",
         "tableName": "customer_notification_render_status",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "priority",
             "column": "priority",
             "type": "ENUM",
             "options": ["LOW", "HIGH"]
           },
           {
             "name": "lockedBy",
             "column": "locked_by",
             "type": "STRING"
           },
           {
             "name": "retryCount",
             "column": "retry_count",
             "type": "INTEGER"
           },
           {
             "name": "renderAt",
             "column": "render_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "customerNotification",
             "type": "OBJECT",
             "column": "customer_notification_id",
             "refEntity": "CustomerNotification"
           }
         ]
       },
       {
         "name": "CustomerNotificationSetting",
         "tableName": "customer_notification_setting",
         "properties": [
           {
             "name": "id",
             "column": "uuid",
             "primaryKey": true,
             "type": "STRING"
           },
           {
             "name": "createdAt",
             "column": "created_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "updatedAt",
             "column": "updated_at",
             "type": "TIMESTAMP"
           },
           {
             "name": "version",
             "column": "version",
             "type": "INTEGER"
           },
           {
             "name": "unitedDeliveryPoint",
             "type": "OBJECT",
             "column": "united_delivery_point_id",
             "refEntity": "UnitedDeliveryPoint"
           },
           {
             "name": "customerAccount",
             "type": "OBJECT",
             "column": "customer_account_id",
             "refEntity": "CustomerAccount"
           },
           {
             "name": "email",
             "column": "email",
             "type": "STRING"
           },
           {
             "name": "sms",
             "column": "sms",
             "type": "STRING"
           }
         ]
       },
       {
         "name": "BusinessPartnerOwnership",
         "tableName": "business_partner_ownership",
         "properties": [
           {
             "name": "businessPartner",
             "type": "OBJECT",
             "column": "business_partner_uuid",
             "refEntity": "BusinessPartner"
           },
           {
             "name": "customerAccount",
             "type": "OBJECT",
             "column": "customer_account_uuid",
             "refEntity": "CustomerAccount"
           },
           {
             "name": "type",
             "type": "ENUM",
             "column": "type",
             "options": ["OWNER", "SHARING"]
           },
           {
             "name": "inherited",
             "column": "inherited",
             "type": "BOOLEAN"
           }
         ]
       },
       {
         "name": "ContractAccountOwnership",
         "tableName": "contract_account_ownership",
         "properties": [
           {
             "name": "contractAccount",
             "type": "OBJECT",
             "column": "contract_account_uuid",
             "refEntity": "ContractAccount"
           },
           {
             "name": "customerAccount",
             "type": "OBJECT",
             "column": "customer_account_uuid",
             "refEntity": "CustomerAccount"
           },
           {
             "name": "type",
             "type": "ENUM",
             "column": "type",
             "options": ["OWNER", "SHARING"]
           },
           {
             "name": "inherited",
             "column": "inherited",
             "type": "BOOLEAN"
           }
         ]
       },
       {
         "name": "ContractOwnership",
         "tableName": "contract_ownership",
         "properties": [
           {
             "name": "contract",
             "type": "OBJECT",
             "column": "contract_uuid",
             "refEntity": "Contract"
           },
           {
             "name": "customerAccount",
             "type": "OBJECT",
             "column": "customer_account_uuid",
             "refEntity": "CustomerAccount"
           },
           {
             "name": "type",
             "type": "ENUM",
             "column": "type",
             "options": ["OWNER", "SHARING"]
           },
           {
             "name": "inherited",
             "column": "inherited",
             "type": "BOOLEAN"
           }
         ]
       },
       {
         "name": "UnitedDeliveryPointOwnership",
         "tableName": "united_delivery_point_ownership",
         "properties": [
           {
             "name": "unitedDeliveryPoint",
             "type": "OBJECT",
             "column": "united_delivery_point_uuid",
             "refEntity": "UnitedDeliveryPoint"
           },
           {
             "name": "customerAccount",
             "type": "OBJECT",
             "column": "customer_account_uuid",
             "refEntity": "CustomerAccount"
           },
           {
             "name": "type",
             "type": "ENUM",
             "column": "type",
             "options": ["OWNER", "SHARING"]
           },
           {
             "name": "inherited",
             "column": "inherited",
             "type": "BOOLEAN"
           }
         ]
       }
     ]
   }
');