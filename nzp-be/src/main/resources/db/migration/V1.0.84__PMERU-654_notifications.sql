-----------------
-- Notification Template
-----------------
ALTER TABLE notification_template ADD COLUMN execution_type CHARACTER VARYING(50);
UPDATE notification_template SET execution_type = 'AUTOMATIC';
ALTER TABLE notification_template ALTER COLUMN execution_type SET NOT NULL;

ALTER TABLE notification_template ADD COLUMN report_customer_column text;
ALTER TABLE notification_template ADD COLUMN report_uuid UUID;

alter table notification_template
ADD CONSTRAINT fk_notification_template_report_uuid FOREIGN KEY (report_uuid)
        REFERENCES report (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;
-----------------
-- Notification Template I18N
-----------------
ALTER TABLE notification_template_i18n ADD COLUMN status CHARACTER VARYING(50);
UPDATE notification_template_i18n SET status = 'ACTIVE';
ALTER TABLE notification_template_i18n ALTER COLUMN status SET NOT NULL;


-----------------
-- Customer Notification
-----------------
ALTER TABLE customer_notification ADD COLUMN employee_login text;
-----------------
-- Notification Schedule
-----------------
CREATE TABLE notification_schedule
(
    uuid                        uuid                        NOT NULL,
    created_at                  TIMESTAMP WITH TIME ZONE    NOT NULL,
    updated_at                  TIMESTAMP WITH TIME ZONE    NOT NULL,
    version                     integer                     NOT NULL,

    time_from                   TIME,
    time_to                     TIME,
    work_weak                   boolean,
    batch_size                  integer,
    batch_delay                 integer,
    status                      CHARACTER VARYING(50)       NOT NULL,

    notification_template_id    uuid                        NOT NULL,
    locked_by                   CHARACTER VARYING(50),
    retry_count                 integer,
    employee_login              text,

    CONSTRAINT pk_notification_schedule PRIMARY KEY (uuid),
    CONSTRAINT fk_notification_schedule_notification_template FOREIGN KEY (notification_template_id)
        REFERENCES notification_template (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_notification_schedule_notification_template_id on notification_schedule(notification_template_id);