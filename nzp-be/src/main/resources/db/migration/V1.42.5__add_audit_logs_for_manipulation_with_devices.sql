INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_DEVICE_ADDED', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Pridanie zariadenia k používateľskému účtu.', null, (select uuid from generic_code_list where code like 'CUSTOMER_DEVICE_ADDED' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', 'Adding device to customer account', null, (select uuid from generic_code_list where code like 'CUSTOMER_DEVICE_ADDED' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_DEVICE_REMOVED_MANUAL', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Manuálne odobratie zariadenia z používateľského účtu.', null, (select uuid from generic_code_list where code like 'CUSTOMER_DEVICE_REMOVED_MANUAL' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', 'Manual device removal from customer account.', null, (select uuid from generic_code_list where code like 'CUSTOMER_DEVICE_REMOVED_MANUAL' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_DEVICE_REMOVED_AUTO', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Automatické odobratie zariadenia z používateľského účtu.', null, (select uuid from generic_code_list where code like 'CUSTOMER_DEVICE_REMOVED_AUTO' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', 'Automatic device removal from customer account.', null, (select uuid from generic_code_list where code like 'CUSTOMER_DEVICE_REMOVED_AUTO' and type = 'AUDIT_LOG_CODE'));
