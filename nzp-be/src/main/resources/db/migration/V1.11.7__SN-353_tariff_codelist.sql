INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'C9', 'EE', 'RETAIL', 'ZSS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'C9', 'C9', (select uuid from tariff where code = 'C9'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]C9', '[EN]C9', (select uuid from tariff where code = 'C9'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'C9A', 'EE', 'RETAIL', 'ZSS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'C9A', 'C9A', (select uuid from tariff where code = 'C9A'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]C9A', '[EN]C9A', (select uuid from tariff where code = 'C9A'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'C9B', 'EE', 'RETAIL', 'ZSS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'C9B', 'C9B', (select uuid from tariff where code = 'C9B'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]C9B', '[EN]C9B', (select uuid from tariff where code = 'C9B'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'C10', 'EE', 'RETAIL', 'ZSS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'C10', 'C10', (select uuid from tariff where code = 'C10'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]C10', '[EN]C10', (select uuid from tariff where code = 'C10'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'C11', 'EE', 'RETAIL', 'ZSS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'C11', 'C11', (select uuid from tariff where code = 'C11'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]C11', '[EN]C11', (select uuid from tariff where code = 'C11'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'NN', 'EE', 'RETAIL', 'ZZS,ZSS,ZVS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'NN', 'NN', (select uuid from tariff where code = 'NN'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]NN', '[EN]NN', (select uuid from tariff where code = 'NN'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'VN', 'EE', 'RETAIL', 'ZZS,ZSS,ZVS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'VN', 'VN', (select uuid from tariff where code = 'VN'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]VN', '[EN]VN', (select uuid from tariff where code = 'VN'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'VVN', 'EE', 'RETAIL', 'ZZS,ZSS,ZVS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'VVN', 'VVN', (select uuid from tariff where code = 'VVN'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]VVN', '[EN]VVN', (select uuid from tariff where code = 'VVN'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'X1', 'EE', 'RETAIL', 'ZZS,ZSS,ZVS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'X1', 'X1', (select uuid from tariff where code = 'X1'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]X1', '[EN]X1', (select uuid from tariff where code = 'X1'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'X2', 'EE', 'RETAIL', 'ZZS,ZSS,ZVS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'X2', 'X2', (select uuid from tariff where code = 'X2'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]X2', '[EN]X2', (select uuid from tariff where code = 'X2'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'X2-D', 'EE', 'RETAIL', 'ZZS,ZSS,ZVS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'X2-D', 'X2-D', (select uuid from tariff where code = 'X2-D'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]X2-D', '[EN]X2-D', (select uuid from tariff where code = 'X2-D'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'X2-S', 'EE', 'RETAIL', 'ZZS,ZSS,ZVS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'X2-S', 'X2-S', (select uuid from tariff where code = 'X2-S'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]X2-S', '[EN]X2-S', (select uuid from tariff where code = 'X2-S'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'X2_ADAPT', 'EE', 'RETAIL', 'ZZS,ZSS,ZVS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'X2_ADAPT', 'X2_ADAPT', (select uuid from tariff where code = 'X2_ADAPT'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]X2_ADAPT', '[EN]X2_ADAPT', (select uuid from tariff where code = 'X2_ADAPT'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'X3-C11', 'EE', 'RETAIL', 'ZZS,ZSS,ZVS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'X3-C11', 'X3-C11', (select uuid from tariff where code = 'X3-C11'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]X3-C11', '[EN]X3-C11', (select uuid from tariff where code = 'X3-C11'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'X3-C9A', 'EE', 'RETAIL', 'ZZS,ZSS,ZVS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'X3-C9A', 'X3-C9A', (select uuid from tariff where code = 'X3-C9A'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]X3-C9A', '[EN]X3-C9A', (select uuid from tariff where code = 'X3-C9A'));


INSERT INTO tariff (uuid, created_at, updated_at, version, status, code, type, category, distribution_area)
VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'X3-C9B', 'EE', 'RETAIL', 'ZZS,ZSS,ZVS');
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'X3-C9B', 'X3-C9B', (select uuid from tariff where code = 'X3-C9B'));
INSERT INTO tariff_i18n (uuid, created_at, updated_at, version, locale, name, description, tariff_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN]X3-C9B', '[EN]X3-C9B', (select uuid from tariff where code = 'X3-C9B'));

