



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SERVICE_CALL_REGISTER_USER', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Požiadavka na registráciu', 'Odoslanie požiadavky na registráciu účtu', (select uuid from generic_code_list where code like 'SERVICE_CALL_REGISTER_USER' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Service call registerUser', '[EN] Service call registerUser', (select uuid from generic_code_list where code like 'SERVICE_CALL_REGISTER_USER' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SERVICE_CALL_PAIRING', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Požiadavka na párovanie', 'Odoslanie požiadavky na párovanie obchodného partnera', (select uuid from generic_code_list where code like 'SERVICE_CALL_PAIRING' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Service call pairing', '[EN] Service call pairing', (select uuid from generic_code_list where code like 'SERVICE_CALL_PAIRING' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SERVICE_CALL_UNPAIRING', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Požiadavka na odpárovanie', 'Odoslanie požiadavky na odpárovanie obchodného partnera', (select uuid from generic_code_list where code like 'SERVICE_CALL_UNPAIRING' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Service call unpairing', '[EN] Service call unpairing', (select uuid from generic_code_list where code like 'SERVICE_CALL_UNPAIRING' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SERVICE_CALL_CUSTOMER_REQUEST', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Odoslanie žiadosti', 'Odoslanie zákaznickej žiadosti', (select uuid from generic_code_list where code like 'SERVICE_CALL_CUSTOMER_REQUEST' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Service call customer request', '[EN] Service call customer request', (select uuid from generic_code_list where code like 'SERVICE_CALL_CUSTOMER_REQUEST' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EPAY_TRANSACTION_CREATE', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Vytvorenie transakcie', 'Vytvorenie platobnej transakcie ', (select uuid from generic_code_list where code like 'EPAY_TRANSACTION_CREATE' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Epay transaction was forwarded and created', '[EN] Epay transaction was forwarded and created', (select uuid from generic_code_list where code like 'EPAY_TRANSACTION_CREATE' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EPAY_TRANSACTION_STATUS_CHANGE', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmena stavu transakcie', 'Zmena stavu platobnej transakcie', (select uuid from generic_code_list where code like 'EPAY_TRANSACTION_STATUS_CHANGE' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Status update of Transaction', '[EN] Status update of Transaction', (select uuid from generic_code_list where code like 'EPAY_TRANSACTION_STATUS_CHANGE' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'PHONE_CHALLENGE_CODE_VALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmena telefónneho čísla- platný kód', 'Zmena telefónneho čísla- platný kód', (select uuid from generic_code_list where code like 'PHONE_CHALLENGE_CODE_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Valid code send for phone change', '[EN] Valid code send for phone change', (select uuid from generic_code_list where code like 'PHONE_CHALLENGE_CODE_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'PHONE_CHALLENGE_CODE_INVALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmena telefónneho čísla - neplatný kód', 'Zmena telefónneho čísla - neplatný kód', (select uuid from generic_code_list where code like 'PHONE_CHALLENGE_CODE_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Invalid code send for phone change', '[EN] Invalid code send for phone change', (select uuid from generic_code_list where code like 'PHONE_CHALLENGE_CODE_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EMAIL_CHALLENGE_CODE_VALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmena emailu - platný kód', 'Zmena emailu - platný kód', (select uuid from generic_code_list where code like 'EMAIL_CHALLENGE_CODE_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Valid code send for email change', '[EN] Valid code send for email change', (select uuid from generic_code_list where code like 'EMAIL_CHALLENGE_CODE_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EMAIL_CHALLENGE_CODE_INVALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmena emailu - neplatný kód', 'Zmena emailu - neplatný kód', (select uuid from generic_code_list where code like 'EMAIL_CHALLENGE_CODE_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Invalid code send for email change', '[EN] Invalid code send for email change', (select uuid from generic_code_list where code like 'EMAIL_CHALLENGE_CODE_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELETE_CUSTOMER_CHALLENGE_CODE_VALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmazanie zákazníka - platný kód', 'Zmazanie zákazníka - platný kód', (select uuid from generic_code_list where code like 'DELETE_CUSTOMER_CHALLENGE_CODE_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Valid code send for delete customer', '[EN] Valid code send for delete customer', (select uuid from generic_code_list where code like 'DELETE_CUSTOMER_CHALLENGE_CODE_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELETE_CUSTOMER_CHALLENGE_CODE_INVALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmazanie zákazníka - neplatný kód', 'Zmazanie zákazníka - neplatný kód', (select uuid from generic_code_list where code like 'DELETE_CUSTOMER_CHALLENGE_CODE_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Invalid code send for delete customer', '[EN] Invalid code send for delete customer', (select uuid from generic_code_list where code like 'DELETE_CUSTOMER_CHALLENGE_CODE_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REGISTRATION_CHALLENGE_CODE_VALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Registrácia - platný kód', 'Registrácia - platný kód', (select uuid from generic_code_list where code like 'REGISTRATION_CHALLENGE_CODE_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Valid code send for registration', '[EN] Valid code send for registration', (select uuid from generic_code_list where code like 'REGISTRATION_CHALLENGE_CODE_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REGISTRATION_CHALLENGE_CODE_INVALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Registrácia - neplatný kód', 'Registrácia - neplatný kód', (select uuid from generic_code_list where code like 'REGISTRATION_CHALLENGE_CODE_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Invalid code send for registration', '[EN] Invalid code send for registration', (select uuid from generic_code_list where code like 'REGISTRATION_CHALLENGE_CODE_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'PASSWORD_RECOVERY_CHALLENGE_CODE_VALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Obnova hesla - platný kód', 'Obnova hesla - platný kód', (select uuid from generic_code_list where code like 'PASSWORD_RECOVERY_CHALLENGE_CODE_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Valid code send for password recovery', '[EN] Valid code send for password recovery', (select uuid from generic_code_list where code like 'PASSWORD_RECOVERY_CHALLENGE_CODE_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'PASSWORD_RECOVERY_CHALLENGE_CODE_INVALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Obnova hesla - neplatný kód', 'Obnova hesla - neplatný kód', (select uuid from generic_code_list where code like 'PASSWORD_RECOVERY_CHALLENGE_CODE_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Invalid code send for password recovery', '[EN] Invalid code send for password recovery', (select uuid from generic_code_list where code like 'PASSWORD_RECOVERY_CHALLENGE_CODE_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_PAIRING_CHALLENGE_CODE_VALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Párovanie - platný kód', 'Párovanie - platný kód', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_CHALLENGE_CODE_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Valid code send for business partner pairing', '[EN] Valid code send for business partner pairing', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_CHALLENGE_CODE_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_PAIRING_CHALLENGE_CODE_INVALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Párovanie - neplatný kód', 'Párovanie - neplatný kód', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_CHALLENGE_CODE_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Invalid code send for business partner pairing', '[EN] Invalid code send for business partner pairing', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_CHALLENGE_CODE_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CHANGE_PASSWORD_PASSWORD_INVALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmena hesla - neplatné heslo', 'Zmena hesla - neplatné heslo', (select uuid from generic_code_list where code like 'CHANGE_PASSWORD_PASSWORD_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Invalid password provided for change password', '[EN] Invalid password provided for change password', (select uuid from generic_code_list where code like 'CHANGE_PASSWORD_PASSWORD_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CHANGE_PASSWORD_PASSWORD_VALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmena hesla - platné heslo', 'Zmena hesla - platné heslo', (select uuid from generic_code_list where code like 'CHANGE_PASSWORD_PASSWORD_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Valid password provided for change password', '[EN] Valid password provided for change password', (select uuid from generic_code_list where code like 'CHANGE_PASSWORD_PASSWORD_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'IDM_AUTHENTICATE_PASSWORD_INVALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'IDM autentifikácia - neplatné heslo', 'IDM autentifikácia - neplatné heslo', (select uuid from generic_code_list where code like 'IDM_AUTHENTICATE_PASSWORD_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Invalid password provided for idm authentication', '[EN] Invalid password provided for idm authentication', (select uuid from generic_code_list where code like 'IDM_AUTHENTICATE_PASSWORD_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'IDM_AUTHENTICATE_PASSWORD_VALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'IDM autentifikácia - platné heslo', 'IDM autentifikácia - platné heslo', (select uuid from generic_code_list where code like 'IDM_AUTHENTICATE_PASSWORD_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Valid password provided for idm authentication', '[EN] Valid password provided for idm authentication', (select uuid from generic_code_list where code like 'IDM_AUTHENTICATE_PASSWORD_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'PHONE_CHANGE_PASSWORD_INVALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmena telefónneho čísla - neplatné heslo', 'Zmena telefónneho čísla - neplatné heslo', (select uuid from generic_code_list where code like 'PHONE_CHANGE_PASSWORD_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Invalid password provided for phone change', '[EN] Invalid password provided for phone change', (select uuid from generic_code_list where code like 'PHONE_CHANGE_PASSWORD_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'PHONE_CHANGE_PASSWORD_VALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmena telefónneho čísla - platné heslo', 'Zmena telefónneho čísla - platné heslo', (select uuid from generic_code_list where code like 'PHONE_CHANGE_PASSWORD_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Valid password provided for phone change', '[EN] Valid password provided for phone change', (select uuid from generic_code_list where code like 'PHONE_CHANGE_PASSWORD_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EMAIL_CHANGE_PASSWORD_INVALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmena emailu - neplatné heslo', 'Zmena emailu - neplatné heslo', (select uuid from generic_code_list where code like 'EMAIL_CHANGE_PASSWORD_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Invalid password provided for email change', '[EN] Invalid password provided for email change', (select uuid from generic_code_list where code like 'EMAIL_CHANGE_PASSWORD_INVALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EMAIL_CHANGE_PASSWORD_VALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Zmena emailu - platné heslo', 'Zmena emailu - platné heslo', (select uuid from generic_code_list where code like 'EMAIL_CHANGE_PASSWORD_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Valid password provided for email change', '[EN] Valid password provided for email change', (select uuid from generic_code_list where code like 'EMAIL_CHANGE_PASSWORD_VALID' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SHARING_INVITATION', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Odoslanie pozvánky - zdielanie ', 'Odoslanie pozvánky pre zdielanie odberného miesta', (select uuid from generic_code_list where code like 'CUSTOMER_SHARING_INVITATION' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Customer invitation for sharing', '[EN] Customer invitation for sharing', (select uuid from generic_code_list where code like 'CUSTOMER_SHARING_INVITATION' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_LOGOUT_IMPERSONIFICATION', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Odhlásenie impersonifikácie zákazníka', 'Odhlásenie impersonifikácie zákazníka', (select uuid from generic_code_list where code like 'CUSTOMER_LOGOUT_IMPERSONIFICATION' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Customer impersonification by employee - logout.', '[EN] Customer impersonification by employee - logout.', (select uuid from generic_code_list where code like 'CUSTOMER_LOGOUT_IMPERSONIFICATION' and type = 'AUDIT_LOG_CODE'));
