UPDATE notification_template_i18n
    SET email_body = '<PERSON><PERSON><PERSON><PERSON><PERSON>, pre potvrdenie deaktivácie účtu kliknite na túto <a href="${portalExternalUrl}/confirm-account-removal?challengeCodeUuid=${attributes.challengeCodeUuid}&challengeCode=${attributes.challengeCode}">URL</a>. Platnosť kódu je do ${attributes.challengeCodeValidTo}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>'
WHERE notification_template_id = (SELECT uuid FROM notification_template nt WHERE nt.code = 'CUSTOMER_DELETE_REQUEST');