
INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BB', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ES', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FA', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FD', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FE', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FF', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FO', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'FT', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'M1', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'M2', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'N1', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'N2', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'N3', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'N4', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'N5', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'N6', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'N7', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'N8', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'N9', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NO', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NS', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'NT', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'OD', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'P1', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'P2', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'P3', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'P4', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'P5', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'P6', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'P7', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'P8', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'P9', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PE', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PK', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PL', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PO', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PP', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PR', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PS', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'PU', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'RE', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SA', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SB', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SC', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SD', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SE', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SL', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SN', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'SP', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ST', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'TX', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'UH', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'UO', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'US', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VF', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VH', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'VM', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ZB', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ZD', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ZE', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ZP', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'ZV', 'INVOICE_TYPE', null, null, null),
(uuid_generate_v4(), now(), now(), 1, 'BA', 'INVOICE_TYPE', null, null, null);



INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Rozpis záloh', null, (select uuid from generic_code_list where code like 'BB' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Storno platby', null, (select uuid from generic_code_list where code like 'ES' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Faktúra', null, (select uuid from generic_code_list where code like 'FA' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Storno', null, (select uuid from generic_code_list where code like 'FD' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Zaúčt.pohľ.na OP man', null, (select uuid from generic_code_list where code like 'FE' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Preúčtovanie', null, (select uuid from generic_code_list where code like 'FF' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Opravná faktúra- man', null, (select uuid from generic_code_list where code like 'FO' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Viacnásobné storno', null, (select uuid from generic_code_list where code like 'FT' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Balík AS2+Lekár+IT', null, (select uuid from generic_code_list where code like 'M1' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Uhlíková stopka', null, (select uuid from generic_code_list where code like 'M2' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Poplat.-upom.MU25a26', null, (select uuid from generic_code_list where code like 'N1' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Prekroc_Mnozs_DOaMO', null, (select uuid from generic_code_list where code like 'N2' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Kompenz_Platby', null, (select uuid from generic_code_list where code like 'N3' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Kompenz_Platby EE', null, (select uuid from generic_code_list where code like 'N4' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Dohoda o urovnaní EE', null, (select uuid from generic_code_list where code like 'N5' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Jednorazova zlava DO', null, (select uuid from generic_code_list where code like 'N6' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Popl. znovuPripoj OM', null, (select uuid from generic_code_list where code like 'N7' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Asistenčné služby', null, (select uuid from generic_code_list where code like 'N8' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Asistenčné služby2', null, (select uuid from generic_code_list where code like 'N9' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Neoprávnený odber', null, (select uuid from generic_code_list where code like 'NO' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Náhrada škody za NO', null, (select uuid from generic_code_list where code like 'NS' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Obchodná zmena', null, (select uuid from generic_code_list where code like 'NT' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Odpis pohľ. v podsúv', null, (select uuid from generic_code_list where code like 'OD' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Poplatky - upomienky', null, (select uuid from generic_code_list where code like 'P1' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Výst.fa mimo cyklu', null, (select uuid from generic_code_list where code like 'P2' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Vystavenie DoS', null, (select uuid from generic_code_list where code like 'P3' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Zasl.duplikátu fa', null, (select uuid from generic_code_list where code like 'P4' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Rúzná potvrdení', null, (select uuid from generic_code_list where code like 'P5' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Zrušenie DoS', null, (select uuid from generic_code_list where code like 'P6' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Zmluvná pokuta', null, (select uuid from generic_code_list where code like 'P7' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Zmluvná sankcia', null, (select uuid from generic_code_list where code like 'P8' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Služba rezerv. plynu', null, (select uuid from generic_code_list where code like 'P9' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Pohľ_platba Od inyOP', null, (select uuid from generic_code_list where code like 'PE' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Opravná položka', null, (select uuid from generic_code_list where code like 'PK' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Platby ostatné', null, (select uuid from generic_code_list where code like 'PL' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Pohľadávka- odpis', null, (select uuid from generic_code_list where code like 'PO' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Postúpenie pohľadávo', null, (select uuid from generic_code_list where code like 'PP' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'FA zvysenia ceny', null, (select uuid from generic_code_list where code like 'PR' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Platba SIPO', null, (select uuid from generic_code_list where code like 'PS' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Poplatok SIPO a PPPU', null, (select uuid from generic_code_list where code like 'PU' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Storno return,refund', null, (select uuid from generic_code_list where code like 'RE' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Sankcie - zm. pokuty', null, (select uuid from generic_code_list where code like 'SA' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Vrátenia za Dist_DOM', null, (select uuid from generic_code_list where code like 'SB' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Vrátenia za Dist_MO', null, (select uuid from generic_code_list where code like 'SC' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Faktúra SD - man.', null, (select uuid from generic_code_list where code like 'SD' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Zúčt.záv.na ZU posty', null, (select uuid from generic_code_list where code like 'SE' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Služba', null, (select uuid from generic_code_list where code like 'SL' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Súdy- paušálne náhr.', null, (select uuid from generic_code_list where code like 'SN' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Súdne poplatky', null, (select uuid from generic_code_list where code like 'SP' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Storno', null, (select uuid from generic_code_list where code like 'ST' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Migrované doklady', null, (select uuid from generic_code_list where code like 'TX' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Úhrada/platba z BV', null, (select uuid from generic_code_list where code like 'UH' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Úroky z omeškania', null, (select uuid from generic_code_list where code like 'UO' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Dohoda o splátkach', null, (select uuid from generic_code_list where code like 'US' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Vyrovnanie pri fakt.', null, (select uuid from generic_code_list where code like 'VF' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Vyrovnanie hromadné', null, (select uuid from generic_code_list where code like 'VH' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Vyrovnanie manuálne', null, (select uuid from generic_code_list where code like 'VM' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Záruky (zábezpeka)', null, (select uuid from generic_code_list where code like 'ZB' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Zálohy daňové', null, (select uuid from generic_code_list where code like 'ZD' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Záväz_platba iný OP', null, (select uuid from generic_code_list where code like 'ZE' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Zápočet pohľadávky', null, (select uuid from generic_code_list where code like 'ZP' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Zálohy VO - predpis', null, (select uuid from generic_code_list where code like 'ZV' and type = 'INVOICE_TYPE')),
(uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Platba', null, (select uuid from generic_code_list where code like 'BA' and type = 'INVOICE_TYPE'));