-- PMERU-2015

-- UPDATE notification_template records that were updated after 04.03.2025 on SPP PROD DB
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=true, enable_email=true, enable_sms=true, enable_portal=true, active_account_only=false, default_push=true, enable_push=false WHERE code='EPAY_TRANSACTION_FINISHED';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_REQUEST_RESERVED_VALUES';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=false, default_push=true, enable_push=true WHERE code='CUSTOMER_REQUEST_STATUS_CHANGE';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_REQUEST_CANCELLED_CUSTOMER';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=false, default_sms=true, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_EMAIL_CHANGE_REQUEST';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=true, enable_portal=true, active_account_only=false, default_push=true, enable_push=true WHERE code='ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=false, default_sms=true, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_INVITATION_ACTIVE';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_EMAIL_ALREADY_REGISTERED';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_PASSWORD_RECOVERY_SUCCESS';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_BATCH_REQUEST_REGISTRATION';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_REQUEST_ANONYMOUS_COMPLETION_REQUEST';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_REQUEST_ANONYMOUS_COMPLETION_REMINDER';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_REQUEST_ANONYMOUS_CONFIRM_REQUEST';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=true, default_push=true, enable_push=true WHERE code='CUSTOMER_INVOICE_BEFORE_DUE';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=false, default_push=true, enable_push=true WHERE code='CUSTOMER_SHARING_OWNER_GRANT';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=true, default_push=true, enable_push=true WHERE code='CUSTOMER_INVOICE_CREDIT_ISSUED';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='BUSINESS_PARTNER_CONSENT_EXPIRING';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_SHARING_INVITATION';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_BATCH_INVITATION_REMINDER';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_SAP_REGISTRATION_REQUEST';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=false, default_sms=true, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_REGISTRATION_REQUEST';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_PASSWORD_RECOVERY_REQUEST';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=false, default_sms=true, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_REGISTRATION_SUCCESS';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=false, enable_sms=true, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_PHONE_CHANGE_REQUEST';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=false, enable_sms=true, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_INVITATION_PASSWORD';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='BUSINESS_PARTNER_UNPAIRING_REQUEST';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='BUSINESS_PARTNER_UNPAIRING_SUCCESS';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_SHARING_OWNER_REVOKE';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_ACTIVATION_SUCCESS';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_PASSWORD_CHANGE_SUCCESS';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_SHARING_CONSUMER_REVOKE';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_INVITATION';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_DEACTIVATION_SUCCESS';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=true, default_push=true, enable_push=false WHERE code='BUSINESS_PARTNER_PAIRING_REQUEST';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=true, default_push=true, enable_push=false WHERE code='CUSTOMER_INVOICE_ADVANCE_ISSUED';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=true, default_push=true, enable_push=false WHERE code='DELIVERY_POINT_AM_CHECK_DMM';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=true, default_push=true, enable_push=false WHERE code='CUSTOMER_PHONE_CHANGE_SUCCESS';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_DELETE_SUCCESS';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=false, default_sms=true, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_EMAIL_CHANGE_SUCCESS';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='BUSINESS_PARTNER_PAIRING_VERIFY';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=false, enable_sms=true, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='BUSINESS_PARTNER_PAIRING_CHALLENGE';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_DELETE_REQUEST';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=false, default_sms=true, enable_email=false, enable_sms=true, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_LOGIN_PHONE';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_REGISTRATION_ALREADY_REGISTERED';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=false, default_sms=true, enable_email=false, enable_sms=true, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_PHONE_ALREADY_REGISTERED';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='SUSPICIOUS_ACTIVITY';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=false, default_push=true, enable_push=false WHERE code='BUSINESS_PARTNER_VERIFY_REVOKE';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_REQUEST_CANCEL_REQUEST';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_REQUEST_CONFIRM_REQUEST';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_REQUEST_EXPIRED_REQUEST';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_REQUEST_CONFIRM_REMINDER';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=true, default_push=true, enable_push=false WHERE code='BUSINESS_PARTNER_PAIRING_SUCCESS';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=true, default_push=true, enable_push=false WHERE code='BUSINESS_PARTNER_PAIRING_REQUEST_AUTOMATIC';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=true, default_push=true, enable_push=false WHERE code='CUSTOMER_INVOICE_ISSUED';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=true, default_push=true, enable_push=false WHERE code='DELIVERY_POINT_AM_CHECK_RK';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=true, default_push=true, enable_push=false WHERE code='DELIVERY_POINT_AM_CHECK_MRK';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=false, active_account_only=false, default_push=true, enable_push=false WHERE code='CUSTOMER_INVOICE_SAP_ISSUED';
UPDATE notification_template SET updated_at=now(), version=version + 1, default_email=true, default_sms=false, enable_email=true, enable_sms=false, enable_portal=true, active_account_only=true, default_push=true, enable_push=true WHERE code='CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE';


-- UPDATE notification_template_i18n records that were updated after 04.03.2025 on SPP PROD DB
UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='[EN] Automatické párovanie obchodného partnera', email_body='<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] obchodný partner bol úspešne napárovaný automatickým procesom.</@spp.notification_email_template>', email_subject='[EN] Automatické párovanie obchodného partnera', sms_body=NULL, header_url=NULL, push_title='Assigning supply points to account', push_body='Supply points have been assigned to your account', push_text='From this point on, you can track your details and make online requests.', push_redirection='/delivery-points'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník ${attributes.requestCompletionEmailTo} odmietol žiadosť o prepis odberného miesta.</@spp.notification_email_template>', email_subject='[EN] Odmietnutie žiadosti o prepis', sms_body=NULL, header_url=NULL, push_title='Supply point transfer request', push_body='Transfer request refused', push_text='The new owner refused to transfer the supply point.', push_redirection='/customer-requests/${target.entity.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_REJECT_REQUEST') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Blížiaca sa splatnosť faktúry', email_body='<#setting locale="en"><#import "spp.macros_en.ftl" as spp><@spp.notification_email_template oslovenie="">[EN] pre zákazníka s číslom ${businessPartner.externalId} sa blíži splatnosť faktúry s číslom ${invoice.reference} vo výške ${invoice.amount?string(",##0.00")} EUR. Úhradu môžete zrealizovať aj prostredníctvom nášho portálu.<br><a href="${portalExternalUrl}/invoice/${invoice.id}">Prejsť na faktúru</a></@spp.notification_email_template>', email_subject='[EN] Upozornenie na blížiacu sa splatnosť faktúry', sms_body='', header_url=NULL, push_title='The invoice due date is approaching.', push_body='Don''t forget about the invoice due date.', push_text='The due date of your invoice is approaching. Check its status in the Moje SPP app.', push_redirection=''
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_INVOICE_BEFORE_DUE') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník ${attributes.requestCompletionEmailTo} nedokončil Vašu ziadosť o prepis odberného miesta vo vyhradenom čase</@spp.notification_email_template>', email_subject='Platnosť žiadosti o prepis vypršala', sms_body=NULL, header_url=NULL, push_title='Požiadavka na prepis odberného miesta', push_body='Vypršala platnosť žiadosti o prepis', push_text='Nový majiteľ nedokončil žiadosť o prepis odberného miesta v danom termíne.', push_redirection='/customer-requests/${target.entity.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_COMPLETION_EXPIRED') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='<p>Potvrdenie o uhrade</p>', email_body='<p><#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""></p><p>ďakujeme za úhradu vo výške ${customerTransaction.amount} eur. Platba sa aktuálne spracováva.<br><br><strong>Po spracovaní ju uvidíte v časti Faktúry vo Vašom účte.</strong></p><br><p><a href="${portalExternalUrl}/invoices">Prejsť na stránku zobrazenie úhrad</a></p><p></@spp.notification_email_template></p>', email_subject='Potvrdenie o prijatí platby', sms_body='Dobry den, dakujeme za uhradu vo vyske ${customerTransaction.amount} eur. Platba sa aktualne spracovava. Vase SPP', header_url=NULL, push_title='Poďakovanie za úhradu.', push_body='Vaša úhrada sa aktuálne spracováva.', push_text='Ďakujeme za úhradu Vašej platby vo výške ${customerTransaction.amount} eur. Aktuálne sa spracováva.', push_redirection=''
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'EPAY_TRANSACTION_FINISHED') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Zadajte samoodpočet spotreby na odbernom mieste typu ${deliveryPoints[0].typeName.name}', email_body='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>v týchto dňoch máte možnosť nahlásiť samoodpočet spotreby na odbernom mieste typu ${deliveryPoints[0].typeName.name} s adresou ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}.</p><p><p>V zmysle platnej legislatívy je stanovený postup pre realizáciu odpočtu nasledovne:</p><ol><li>fyzicky prevádzkovateľom distribučnej siete alebo ním poverenou osobou</li><li>    samoodpočtom</li><li>odhadom stavu meracieho zariadenia prevádzkovateľom distribučnej siete k dátumu odpočtu </li></ol><p>Vašou povinnosťou je však sprístupniť meradlo odpočtárovi na odčítanie jeho stavu aj v prípade, ak ste nám stav meradla už&nbsp;nahlásili.</p></p><p><a href="${portalExternalUrl}/delivery-points/${unitedDeliveryPoint.id}/self-read">Prejsť na zadanie samoodpočtu</a></p></@spp.notification_email_template>', email_subject='Upozornenie na vystavenie faktúry za spotrebu na odbernom mieste typu ${deliveryPoints[0].typeName.name}', sms_body=NULL, header_url=NULL, push_title='Možnosť vykonať samoodpočet', push_body='Odčítajte hodnoty z meradla', push_text='Odčítané hodnoty zadajte v portáli Moje SPP.', push_redirection='/delivery-points/${unitedDeliveryPoint.id}/self-read'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'METER_READING_CUSTOMER') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='[EN] Upozornenie na blížiacu sa splatnosť preddavku', email_body='<#setting locale="en"><#import "spp.macros_en.ftl" as spp><@spp.notification_email_template oslovenie="">[EN] pre zákazníka s číslom ${businessPartner.externalId} sa blíži splatnosť preddavku s číslom ${invoice.reference} vo výške ${invoice.amount?string(",##0.00")} EUR. Úhradu môžete zrealizovať aj prostredníctvom nášho portálu.<br><a href="${portalExternalUrl}/invoice/${invoice.id}">Prejsť na faktúru</a></@spp.notification_email_template>', email_subject='[EN] Upozornenie na blížiacu sa splatnosť preddavku', sms_body='', header_url=NULL, push_title='The advance payment due date is approaching.', push_body='Don''t forget about the advance payment due date.', push_text='The due date of your advance payment is approaching. Check its status in the Moje SPP app.', push_redirection=''
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='<p>Úspešné nastavenie zdieľania odberných miest</p>', email_body='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>úspešne ste nastavili zdieľanie svojich odberných miest zákazníkovi ${attributes.sharingEmail}.</p><p>Od tohto okamihu vidí údaje k Vašim zdieľaným odberným miestam.</p></@spp.notification_email_template>', email_subject='Úspešné nastavenie zdieľania odberných miest', sms_body='', header_url=NULL, push_title='Zdieľanie odberných miest nastavené', push_body='Zdieľanie odberných miest je úspešné nastavené', push_text='Vaša požiadavka na zdieľanie odberných miest bola úspešná. Skontrolujte detaily vo Vašom účte.', push_redirection='/delivery-points'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_SHARING_OWNER_GRANT') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Prekročenie maximálnej rezervovanej kapacity (EE)', email_body='<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] prekročili ste Vašu maximálnu rezervovanú kapacitu pre spotrebu elektrickej energie.</@spp.notification_email_template>', email_subject='[EN] Prekročenie rezervovanej kapacity (EE)', sms_body=NULL, header_url=NULL, push_title='Reserved electricity capacity exceeded', push_body='You have exceeded the maximum electricity capacity', push_text='Please note that you have exceeded your maximum reserved capacity for electricity consumption at your supply point.', push_redirection=NULL
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'DELIVERY_POINT_CHECK_MRK') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>radi by sme Vám pripomenuli, že objaviť nový zákaznícky portál Moje SPP je teraz oveľa jednoduchšie.</p><p>Majte prehľad o Vašich službách z pohodlia Vášho domova - stačí jeden klik ...&nbsp;<a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName?url}&amp;lastName=${customer.lastName?url}&amp;type=pre_registration">Objaviť Moje SPP teraz</a></p><p></@spp.notification_email_template></p>', email_subject='Objavte jedným klikom nový zákaznícky portál', sms_body=NULL, header_url=NULL, push_title=NULL, push_body=NULL, push_text=NULL, push_redirection=NULL
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_BATCH_INVITATION_REMINDER') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Prekročenie maximálnej dennej spotreby plynu (ZP)', email_body='<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] prekročili ste Vašu maximálnu dennú spotrebu plynu.</@spp.notification_email_template>', email_subject='[EN] Prekročenie rezervovanej kapacity (EE)', sms_body=NULL, header_url=NULL, push_title='Daily gas consumption limit exceeded', push_body='You have exceeded the daily gas consumption limit', push_text='Please note that you have exceeded your daily gas consumption limit at your supply point.', push_redirection=NULL
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'DELIVERY_POINT_CHECK_DMM') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Pridanie poznámky k žiadosti ${customerRequest.name}', email_body='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>pracujeme na riešení Vašej požiadavky ${customerRequest.name}.</p><p>K zadanej žiadosti sme pridali poznámku ${attributes.customerRequestNote?no_esc}.</p><p><a href="${portalExternalUrl}/customer-requests/${customerRequest.uuid}">Prejsť na detail</a></p></@spp.notification_email_template>', email_subject='Pridanie poznámky k žiadosti ${customerRequest.name}', sms_body=NULL, header_url=NULL, push_title='Zmena stavu požiadavky', push_body='Pri riešení Vašej požiadavky došlo k zmene stavu', push_text='Pozrite si stav riešenia Vašej požiadavky.', push_redirection='/customer-requests/${customerRequest.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_NOTE_CREATE') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Zmena stavu žiadosti - ${customerRequest.externalId} (${customerRequest.status.name})', email_body='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>Vaša požiadavka s názvom ${customerRequest.name} zmenila stav na ${customerRequest.status.name}.</p></@spp.notification_email_template>', email_subject='Zmena stavu žiadosti', sms_body='Vazeny zakaznik, ziadost s nazvom ${customerRequest.name} zmenila stav na: ${customerRequest.status.name}. Vase SPP', header_url=NULL, push_title='Stav žiadosti bol zmenený', push_body='Stav žiadosti sa zmenil na: ${customerRequest.status.name}.', push_text='Žiadosť s názvom ${customerRequest.name} zmenila stav na: ${customerRequest.status.name}.', push_redirection='/customer-requests/${customerRequest.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Zmena stavu žiadosti - ${customerRequest.externalId} (${customerRequest.status.name})', email_body='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>Vaša požiadavka s názvom ${customerRequest.name} zmenila stav na ${customerRequest.status.name}.</p></@spp.notification_email_template>', email_subject='Zmena stavu žiadosti', sms_body=NULL, header_url=NULL, push_title='Stav žiadosti bol zmenený', push_body='Stav žiadosti sa zmenil na: ${customerRequest.status.name}.', push_text='Žiadosť s názvom ${customerRequest.name} zmenila stav na: ${customerRequest.status.name}.', push_redirection='/customer-requests/${customerRequest.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_STATUS_CHANGE') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Prekročenie rezervovanej kapacity (EE)', email_body='<#import "spp.macros_en.ftl" as spp><@spp.notification_email_template oslovenie="">[EN] prekročili ste Vašu rezervovanú kapacitu pre spotrebu elektrickej energie.</@spp.notification_email_template>', email_subject='[EN] Prekročenie rezervovanej kapacity (EE)', sms_body=NULL, header_url=NULL, push_title='Reserved electricity capacity exceeded', push_body='You have exceeded the reserved electricity capacity', push_text='Please note that you have exceeded your reserved capacity for electricity consumption at your supply point.', push_redirection=NULL
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'DELIVERY_POINT_CHECK_RK') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Pridanie poznámky k žiadosti', email_body='<#import "spp.macros_en.ftl" as spp><@spp.notification_email_template oslovenie="">[EN] k žiadosti ${customerRequest.name} bola pridaná poznámka ${attributes.customerRequestNote?no_esc}.</@spp.notification_email_template>', email_subject='[EN] Pridanie poznámky k žiadosti', sms_body=NULL, header_url=NULL, push_title='Request status change', push_body='The status of your request has changed', push_text='Check the status of your request.', push_redirection='/customer-requests/${customerRequest.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_NOTE_CREATE') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='[EN] Zmena stavu žiadosti - ${customerRequest.externalId} (${customerRequest.status.name})', email_body='<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] žiadosť s názvom ${customerRequest.name} zmenila stav na: ${customerRequest.status.name}.</@spp.notification_email_template>', email_subject='[EN] Zmena stavu žiadosti', sms_body=NULL, header_url=NULL, push_title='The status of the request has changed.', push_body='The status of the request has changed to: ${customerRequest.status.name}.', push_text='The request named ${customerRequest.name} has changed status to: ${customerRequest.status.name}.', push_redirection='/customer-requests/${customerRequest.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Nastavenie zdieľania', email_body='<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] práve Vám bolo nastavené zdieľanie od zákazníka ${attributes.sharingEmail}.</@spp.notification_email_template>', email_subject='[EN] Nastavenie zdieľania', sms_body=NULL, header_url=NULL, push_title='New access to supply point data', push_body='You have obtained new access to supply point data', push_text='Your account has been granted new access to the supply point data. See those data in your account.', push_redirection='/delivery-points'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_SHARING_CONSUMER_GRANT') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Nastavenie zdieľania', email_body='<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] práve ste nastavili zdieľanie zákazníkovi ${attributes.sharingEmail}.</@spp.notification_email_template>', email_subject='[EN] Nastavenie zdieľania', sms_body=NULL, header_url=NULL, push_title='', push_body='', push_text='', push_redirection=''
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_SHARING_OWNER_GRANT') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník <#if businessPartner.name?has_content>${businessPartner.name}<#else>${businessPartner.firstName} ${businessPartner.lastName}</#if> požiadal o prepis odberného miesta na Vás, pre dokončenie žiadosti online kliknite <a href="${attributes.requestCompletionUrl}">sem</a>. Odkaz je platný najneskôr do ${attributes.requestCompletionValidTo}.</@spp.notification_email_template>', email_subject='Pripomenutie dokončenia žiadosti o prepis', sms_body=NULL, header_url=NULL, push_title='Požiadavka na prepis odberného miesta', push_body='Máte nedokončenú požiadavku na prepis', push_text='Pre dokončenie žiadosti a bližšie informácie o prepise odberného miesta kliknite.', push_redirection='/customer-requests/${target.entity.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_COMPLETION_REMINDER') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Udelenie zdieľania odberných miest', email_body='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>zákazník ${attributes.sharingEmail} Vám dal zdieľať údaje zo svojho portálového účtu.</p><p>Pre prístup k týmto zdieľaným údajom musíte mať aktívny účet na portáli Moje SPP.</p><p><a href="${portalExternalUrl}">Prejsť na prihlásenie</a></p></@spp.notification_email_template>', email_subject='Udelenie zdieľania odberných miest', sms_body=NULL, header_url=NULL, push_title='Nový prístup k údajom odberných miest', push_body='Získali ste nový prístup k údajom odberného miesta', push_text='Do Vášho účtu bol pridelený nový prístup k údajom odberného miesta. Pozrite si tieto údaje vo svojom účte.', push_redirection='/delivery-points'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_SHARING_CONSUMER_GRANT') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník ${attributes.requestCompletionEmailTo} odmietol žiadosť o prepis odberného miesta.</@spp.notification_email_template>', email_subject='Odmietnutie žiadosti o prepis', sms_body=NULL, header_url=NULL, push_title='Požiadavka na prepis odberného miesta', push_body='Odmietnutie žiadosti o prepis', push_text='Nový majiteľ odmietol žiadosť o prepis odberného miesta.', push_redirection='/customer-requests/${target.entity.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_REJECT_REQUEST') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='[EN] Zmena stavu žiadosti - ${customerRequest.externalId} (${customerRequest.status.name})', email_body='<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] žiadosť s názvom ${customerRequest.name} zmenila stav na: ${customerRequest.status.name}.</@spp.notification_email_template>', email_subject='[EN] Zmena stavu žiadosti', sms_body=NULL, header_url=NULL, push_title='The status of the request has changed.', push_body='The status of the request has changed to: ${customerRequest.status.name}.', push_text='The request named ${customerRequest.name} has changed status to: ${customerRequest.status.name}."', push_redirection='/customer-requests/${customerRequest.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_STATUS_CHANGE') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='<p><span data-teams="true"><span class="ui-provider a b c d e f g h i j k l m n o p q r s t u v w x y z ab ac ae af ag ah ai aj ak" dir="ltr">Vyrovnanie preplatku za spotrebu na odbernom mieste <#if deliveryPoints?has_content>typu ${deliveryPoints[0].typeName.name}</#if></span></span></p>', email_body='<p><#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""></p><p>po vyúčtovaní spotreby <#if deliveryPoints?has_content> <#if deliveryPoints?size == 1>na odbernom mieste s adresou ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}.<#else>na odberných miestach s adresami</p><ul><#list deliveryPoints as dp><li>${dp.street} ${dp.streetNumber}, ${dp.city}</li></#list></ul><p></#if></#if></p><p>Vám vznikol preplatok.</p><p>V prípade, ak uhrádzate platby inkasom alebo prevodným príkazom, preplatok Vám <strong>odošleme ku dňu splatnosti</strong> na Váš bankový účet.</p><p>Ak využívate SIPO, výška preplatku bude zahrnutá do položiek inkasného lístka (peňažný doklad SIPO).</p><p>Všetky svoje úhrady si môžete pozrieť v <a href="${portalExternalUrl}/invoices">portálovom účte</a>.</p><p></@spp.notification_email_template></p>', email_subject='Odoslanie preplatku po vyúčtovaní spotreby', sms_body='', header_url=NULL, push_title='Odoslali sme Vám preplatok', push_body='Odoslali sme Vám preplatok po vyúčtovaní spotreby', push_text='Váš preplatok po vyúčtovaní spotreby bol odoslaný. Skontrolujte svoj účet pre bližšie detaily.', push_redirection=''
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_INVOICE_CREDIT_ISSUED') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>radi by sme Vám pripomenuli, že objaviť nový zákaznícky portál Moje SPP je teraz oveľa jednoduchšie.</p><p>Majte prehľad o Vašich službách z pohodlia Vášho domova - stačí jeden klik ...&nbsp;<a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName?url}&amp;lastName=${customer.lastName?url}&amp;type=pre_registration">Objaviť Moje SPP teraz</a></p><p></@spp.notification_email_template></p>', email_subject='Objavte jedným klikom nový zákaznícky portál', sms_body=NULL, header_url=NULL, push_title=NULL, push_body=NULL, push_text=NULL, push_redirection=NULL
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_BATCH_INVITATION_REMINDER') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník ${attributes.requestCompletionCompletorName} dokončil Vašu ziadosť o prepis odberného miesta</@spp.notification_email_template>', email_subject='Dokončenie žiadosti o prepis novým odberateľom', sms_body=NULL, header_url=NULL, push_title='Požiadavka na prepis odberného miesta', push_body='Dokončenie žiadosti o prepis odberného miesta', push_text='Nový majiteľ dokončil žiadosť o prepis odberného miesta', push_redirection='/customer-requests/${target.entity.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_COMPLETION_DONE') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Upozornenie na bližiaci sa termín splatnosti preddavku ', email_body='<p><#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""></p><p>blíži sa dátum splatnosti Vašej preddavkovej platby za spotrebu <#if deliveryPoints?has_content> <#if deliveryPoints?size == 1> na odbernom mieste s adresou ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}. <#else> na odberných miestach s adresami </p><ul> <#list deliveryPoints as dp> <li>${dp.street} ${dp.streetNumber}, ${dp.city}</li> </#list> </ul><p> </#if></#if></p><p><br></p><p>Platbu vo výške ${invoice.amount} EUR môžete zadať priamo v portáli Moje SPP. Variabilný symbol použite ${invoice.vs}.</p><p>Ak ste už uvedenú čiastku zaplatili, alebo máte nastavený trvalý príkaz na úhradu, považujte, prosím, email za informatívny.</p><p><a href="${portalExternalUrl}/invoice/${invoice.id}">Prejsť na úhradu preddavkovej platby</a></p><p></@spp.notification_email_template></p>', email_subject='Upozornenie na bližiaci sa termín splatnosti preddavku ', sms_body='', header_url=NULL, push_title='Blíži sa termín splatnosti preddavku', push_body='Nezabudnite na splatnosť preddavku', push_text='Termín splatnosti Vášho preddavku sa blíži. Skontrolujte si jeho stav v aplikácii Moje SPP.', push_redirection=''
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Úspešné priradenie odberných miest k portálovému účtu', email_body='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>k Vášmu portálovému účtu boli priradené Vaše odberné miesta.</p><p>Od tohto okamihu si vo Vašom účte môžete pozrieť k nim detailné údaje.</p><p><a href="${portalExternalUrl}/delivery-points">Prejsť na odberné miesta</a></p></@spp.notification_email_template>', email_subject='Úspešné priradenie odberných miest k portálovému účtu', sms_body=NULL, header_url=NULL, push_title='Priradenie odberných miest k účtu', push_body='K Vášmu účtu boli priradené odberné miesta', push_text='Od tohto okamihu si môžete sledovať detailné údaje a  zadávať online požiadavky.', push_redirection='/delivery-points'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>at SPP, we do everything we can to make it easy and fast for you to solve your needs.</p><p>The Moje SPP customer portal is a proof of our efforts. It enables you to enter your requirements easily.<br>You will also find:</p><ul>    <li>an overview of information about your distribution points,</li>    <li>your invoices,</li>    <li>your payment and consumption history.</li></ul><p>Discover the Moje SPP portal in one click and save time today and in the future.</p><p><a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName?url}&amp;lastName=${customer.lastName?url}&amp;type=pre_registration">Discover Moje SPP now</a></p><p></@spp.notification_email_template></p>', email_subject='Discover the Moje SPP portal in one click', sms_body=NULL, header_url=NULL, push_title=NULL, push_body=NULL, push_text=NULL, push_redirection=NULL
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_BATCH_REQUEST_REGISTRATION') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>v SPP robíme všetko preto, aby ste Vaše potreby mohli riešiť jednoducho a rýchlo.</p><p>Dôkazom našej snahy je aj zákaznícky portál Moje SPP.<br>Prináša Vám možnosť jednoducho zadať svoje požiadavky. Ďalej v ňom nájdete:</p><ul>    <li>prehľad informácií o svojich odberných miestach,</li>    <li>zobrazenie svojich faktúr,</li>    <li>históriu platieb a spotreby.</li></ul><p>Objavte portál Moje SPP jediným klikom a ušetrite čas dnes aj nabudúce.</p><p><a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName?url}&amp;lastName=${customer.lastName?url}&amp;type=pre_registration">Dokončiť registráciu teraz</a></p><p></@spp.notification_email_template></p>', email_subject='Dokončenie registrácie do zákazníckeho portálu', sms_body=NULL, header_url=NULL, push_title=NULL, push_body=NULL, push_text=NULL, push_redirection=NULL
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_BATCH_REQUEST_REGISTRATION') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>at SPP, we do everything we can to make it easy and fast for you to solve your needs.</p><p>The Moje SPP customer portal is a proof of our efforts. It enables you to enter your requirements easily.<br>You will also find:</p><ul>    <li>an overview of information about your distribution points,</li>    <li>your invoices,</li>    <li>your payment and consumption history.</li></ul><p>Discover the Moje SPP portal in one click and save time today and in the future.</p><p><a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName?url}&amp;lastName=${customer.lastName?url}&amp;type=pre_registration">Discover Moje SPP now</a></p><p></@spp.notification_email_template></p>', email_subject='Discover the Moje SPP portal in one click', sms_body=NULL, header_url=NULL, push_title=NULL, push_body=NULL, push_text=NULL, push_redirection=NULL
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_SAP_REGISTRATION_REQUEST') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>v SPP robíme všetko preto, aby ste Vaše potreby mohli riešiť jednoducho a rýchlo.</p><p>Dôkazom našej snahy je aj zákaznícky portál Moje SPP.<br>Prináša Vám možnosť jednoducho zadať svoje požiadavky. Ďalej v ňom nájdete:</p><ul>    <li>prehľad informácií o svojich odberných miestach,</li>    <li>zobrazenie svojich faktúr,</li>    <li>históriu platieb a spotreby.</li></ul><p>Objavte portál Moje SPP jediným klikom a ušetrite čas dnes aj nabudúce.</p><p><a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName?url}&amp;lastName=${customer.lastName?url}&amp;type=pre_registration">Dokončiť registráciu teraz</a></p><p></@spp.notification_email_template></p>', email_subject='Dokončenie registrácie do zákazníckeho portálu', sms_body=NULL, header_url=NULL, push_title=NULL, push_body=NULL, push_text=NULL, push_redirection=NULL
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_SAP_REGISTRATION_REQUEST') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Upozornenie o prekročení rezervovanej kapacity elektriny', email_body='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozorňujeme Vás, že na odbernom mieste na adrese  ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city} ste prekročili Vašu maximálnu rezervovanú kapacitu pre spotrebu elektriny. V prípade potreby, kontaktujte svojho manažéra predaja.</p></@spp.notification_email_template>', email_subject='Upozornenie o prekročení rezervovanej kapacity elektriny', sms_body='', header_url=NULL, push_title='Prekročenie rezervovanej kapacity elektriny', push_body='Prekročili ste maximálnu kapacitu elektriny', push_text='Upozorňujeme Vás, že na odbernom mieste ste prekročili Vašu maximálnu rezervovanú kapacitu pre spotrebu elektriny.', push_redirection=NULL
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'DELIVERY_POINT_CHECK_MRK') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník ${attributes.requestCompletionCompletorName} dokončil Vašu ziadosť o prepis odberného miesta</@spp.notification_email_template>', email_subject='[EN] Dokončenie žiadosti o prepis novým odberateľom', sms_body=NULL, header_url=NULL, push_title='Supply point transfer request', push_body='Supply point transfer request completion', push_text='The new owner completed the supply point transfer request.', push_redirection='/customer-requests/${target.entity.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_COMPLETION_DONE') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník ${attributes.requestCompletionEmailTo} nedokončil Vašu ziadosť o prepis odberného miesta vo vyhradenom čase</@spp.notification_email_template>', email_subject='[EN] Platnosť žiadosti o prepis vypršala', sms_body=NULL, header_url=NULL, push_title='Supply point transfer request', push_body='Transfer request expired', push_text='The new owner did not complete the supply point transfer request by the deadline.', push_redirection='/customer-requests/${target.entity.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_COMPLETION_EXPIRED') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Upozornenie o prekročení rezervovanej kapacity elektriny', email_body='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozorňujeme Vás, že na odbernom mieste na adrese  ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city} ste prekročili Vašu rezervovanú kapacitu pre spotrebu elektriny. V prípade potreby, kontaktujte svojho manažéra predaja.</p></@spp.notification_email_template>', email_subject='Upozornenie o prekročení rezervovanej kapacity elektriny', sms_body='', header_url=NULL, push_title='Prekročenie rezervovanej kapacity elektriny', push_body='Prekročili ste rezervovanú kapacitu elektriny', push_text='Upozorňujeme Vás, že na odbernom mieste ste prekročili Vašu rezervovanú kapacitu pre spotrebu elektriny.', push_redirection=NULL
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'DELIVERY_POINT_CHECK_RK') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník <#if businessPartner.name?has_content>${businessPartner.name}<#else>${businessPartner.firstName} ${businessPartner.lastName}</#if> požiadal o prepis odberného miesta na Vás, pre dokončenie žiadosti online kliknite <a href="${attributes.requestCompletionUrl}">sem</a>. Odkaz je platný najneskôr do ${attributes.requestCompletionValidTo}.</@spp.notification_email_template>', email_subject='Dokončenie žiadosti o prepis', sms_body=NULL, header_url=NULL, push_title='Požiadavka na prepis odberného miesta', push_body='Dokončite žiadosť o prepis odberného miesta', push_text='Pre dokončenie žiadosti a bližšie informácie o prepise odberného miesta kliknite.', push_redirection='/customer-requests/${target.entity.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_COMPLETION_REQUEST') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Upozornenie o prekročení maximálnej dennej spotreby plynu', email_body='<#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""><p>upozorňujeme Vás, že na odbernom mieste na adrese  ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city} ste prekročili Vašu maximálnu dennú spotrebu plynu. V prípade potreby, kontaktujte svojho manažéra predaja.</p></@spp.notification_email_template>', email_subject='Upozornenie o prekročení maximálnej dennej spotreby plynu', sms_body='', header_url=NULL, push_title='Prekročenie dennej spotreby plynu', push_body='Prekročili ste dennú spotrebu plynu', push_text='Upozorňujeme Vás, že na odbernom mieste ste prekročili dennú spotrebu plynu.', push_redirection=NULL
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'DELIVERY_POINT_CHECK_DMM') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník <#if businessPartner.name?has_content>${businessPartner.name}<#else>${businessPartner.firstName} ${businessPartner.lastName}</#if> požiadal o prepis odberného miesta na Vás, pre dokončenie žiadosti online kliknite <a href="${attributes.requestCompletionUrl}">sem</a>. Odkaz je platný najneskôr do ${attributes.requestCompletionValidTo}.</@spp.notification_email_template>', email_subject='[EN] Dokončenie žiadosti o prepis', sms_body=NULL, header_url=NULL, push_title='Supply point transfer request', push_body='Complete the supply point transfer request', push_text='Click to complete the request and get more information about the supply point transfer.', push_redirection='/customer-requests/${target.entity.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_COMPLETION_REQUEST') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header=NULL, email_body='<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník <#if businessPartner.name?has_content>${businessPartner.name}<#else>${businessPartner.firstName} ${businessPartner.lastName}</#if> požiadal o prepis odberného miesta na Vás, pre dokončenie žiadosti online kliknite <a href="${attributes.requestCompletionUrl}">sem</a>. Odkaz je platný najneskôr do ${attributes.requestCompletionValidTo}.</@spp.notification_email_template>', email_subject='[EN] Pripomenutie dokončenia žiadosti o prepis', sms_body=NULL, header_url=NULL, push_title='Supply point transfer request', push_body='You have an unfinished transfer request', push_text='Click to complete the request and get more information about the supply point transfer.', push_redirection='/customer-requests/${target.entity.uuid}'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_COMPLETION_REMINDER') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Certifikované meranie', email_body='<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] v týchto dňoch máte možnosť nahlásiť samoodpočet spotreby na odbernom mieste typu ${deliveryPoints[0].typeName.name} s adresou ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}.<br>Vykonanie samoodpočtu online je jednoduché a má veľkú výhodu — nemusíte čakať na odpočtára.<br>Odčítajte, prosím, stav na svojom meradle a zadajte ho do portálového účtu.<br><a href="${portalExternalUrl}/delivery-points/${unitedDeliveryPoint.id}/self-read">Nahlásiť stav meradla</a></@spp.notification_email_template>', email_subject='[EN] Certifikované meranie', sms_body=NULL, header_url=NULL, push_title='Self-reading option', push_body='Read the meter', push_text='Enter the readings in the My SPP portal.', push_redirection='/delivery-points/${unitedDeliveryPoint.id}/self-read'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'METER_READING_CUSTOMER') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='Upozornenie na bližiaci sa termín splatnosti faktúry', email_body='<p><#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""></p><p>blíži sa dátum splatnosti Vašej faktúry za spotrebu <#if deliveryPoints?has_content> <#if deliveryPoints?size == 1> na odbernom mieste s adresou ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}. <#else> na odberných miestach s adresami </p><ul> <#list deliveryPoints as dp> <li>${dp.street} ${dp.streetNumber}, ${dp.city}</li>    </#list>    </ul><p>  </#if></#if></p><p><br></p><p>Ak platíte prevodom, platbu môžete zrealizovať priamo vo svojom <a href="${portalExternalUrl}/invoice/${invoice.id}">portálovom účte</a> vo výške ${invoice.amount} eur s variabilným symbolom ${invoice.vs}.</p><p>Ak máte zriadené inkaso, úhrada prebehne automaticky k dátumu splatnosti faktúry.</p><p>Spôsob platby SIPO, vyžaduje úhradu cez Slovenskú poštu.</p><p>V prípade preplatku Vám peniaze zašleme na Vás bankový účet.</p><p><br></p><p><a href="${portalExternalUrl}/invoice/${invoice.id}">Prejsť na úhradu faktúry</a></p><p></@spp.notification_email_template></p>', email_subject='Upozornenie na blížiaci sa termín splatnosti faktúry ', sms_body='', header_url=NULL, push_title='Blíži sa termín splatnosti faktúry', push_body='Nezabudnite na splatnosť faktúry', push_text='Termín splatnosti Vašej faktúry sa blíži. Skontrolujte si jej stav v aplikácii Moje SPP.', push_redirection=''
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_INVOICE_BEFORE_DUE') AND upper(locale)='SK';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='<p><span data-teams="true"><span class="ui-provider a b c d e f g h i j k l m n o p q r s t u v w x y z ab ac ae af ag ah ai aj ak" dir="ltr">Settlement of overpayment for consumption at the point of delivery <#if deliveryPoints?has_content>typu ${deliveryPoints[0].typeName.name}</#if></span></span></p>', email_body='<p><#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""></p><p>after the billing of consumption at the supply point <#if deliveryPoints?has_content> <#if deliveryPoints?size == 1>with the address ${deliveryPoints[0].street} ${deliveryPoints[0].streetNumber}, ${deliveryPoints[0].city}.<#else> at the supply points with addresses</p><ul><#list deliveryPoints as dp><li>${dp.street} ${dp.streetNumber}, ${dp.city}</li></#list></ul><p></#if></#if></p><p>you have an overpayment.</p><p>If you make payments by direct debit or transfer order, the overpayment will be sent to your bank account <strong>by the due date</strong>.</p><p>If you use SIPO, the amount of the overpayment will be included in the items of the collection slip (SIPO payment document).</p><p>You can view all your payments in the <a href="${portalExternalUrl}/invoices">portal account</a>.</p><p></@spp.notification_email_template></p>', email_subject='Settlement of overpayment for consumption', sms_body='', header_url=NULL, push_title='We have sent you a refund.', push_body='We have sent you a refund after the consumption settlement', push_text=' Your refund after the consumption settlement has been sent. Please check your account for more details', push_redirection=''
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'CUSTOMER_INVOICE_CREDIT_ISSUED') AND upper(locale)='EN';

UPDATE notification_template_i18n
SET updated_at=now(), version=version + 1, header='[EN]&nbsp;Potvrdenie o prijatí platby', email_body='<#import "spp.macros_en.ftl" as spp><@spp.notification_email_template oslovenie="">[EN] prijali sme od Vás platbu vo výške ${customerTransaction.amount} eur.</@spp.notification_email_template>', email_subject='[EN] Prijatie platby', sms_body='[EN] Dobry den, prijali sme od Vas platbu vo vyske ${customerTransaction.amount} eur. Dakujeme za uhradu. Vase SPP', header_url=NULL, push_title='Thank you for your payment. ', push_body='Your payment is currently being processed.', push_text='Thank you for your payment of ${customerTransaction.amount} euros. It is currently being processed.', push_redirection=''
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'EPAY_TRANSACTION_FINISHED') AND upper(locale)='EN';
