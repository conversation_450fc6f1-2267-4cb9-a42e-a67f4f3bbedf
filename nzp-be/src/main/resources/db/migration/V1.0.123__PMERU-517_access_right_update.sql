INSERT INTO access_group (uuid, code, created_at , updated_at ,"version", name, description) values (uuid_generate_v4(), 'NZP_ENTITIES_VIEW', current_timestamp, current_timestamp, 1, 'Pristup k entitam zakaznickeho uctu', '');

INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue)
values ('ENTITY_BUSINESS_PARTNERS_VIEW', current_timestamp, current_timestamp, 1, 'Pristup k zakaznickej entite OP', '', false, true);
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue)
values ('ENTITY_DELIVERY_POINTS_VIEW', current_timestamp, current_timestamp, 1, 'Pristup k zakaznickej entite OM', '', false, true);
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue)
values ('ENTITY_INVOICES_VIEW', current_timestamp, current_timestamp, 1, 'Pristup k zakaznickej entite FA', '', false, true);
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue)
values ('ENTITY_REQUESTS_VIEW', current_timestamp, current_timestamp, 1, 'Pristup k zakaznickej entite Ziadosti', '', false, true);

INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('ENTITY_BUSINESS_PARTNERS_VIEW', (select uuid from access_group where code = 'NZP_ENTITIES_VIEW'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('ENTITY_DELIVERY_POINTS_VIEW', (select uuid from access_group where code = 'NZP_ENTITIES_VIEW'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('ENTITY_INVOICES_VIEW', (select uuid from access_group where code = 'NZP_ENTITIES_VIEW'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('ENTITY_REQUESTS_VIEW', (select uuid from access_group where code = 'NZP_ENTITIES_VIEW'), current_timestamp, 'GRANT', NULL);

