
---------------------
-- Columns
---------------------
alter table customer_request add column synchronization_log_uuid    uuid;
alter table customer_request add column synchronization_at          TIMESTAMP WITH TIME ZONE;
alter table customer_request add column batch_timestamp             TIMESTAMP WITH TIME ZONE;


---------------------
-- Add indexes
---------------------
CREATE INDEX idx_customer_request_synchronization_log_uuid on customer_request(synchronization_log_uuid);

---------------------
-- Foreign keys
---------------------
alter table customer_request
ADD CONSTRAINT fk_customer_request_synchronization_log FOREIGN KEY (synchronization_log_uuid)
        REFERENCES synchronization_log (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;