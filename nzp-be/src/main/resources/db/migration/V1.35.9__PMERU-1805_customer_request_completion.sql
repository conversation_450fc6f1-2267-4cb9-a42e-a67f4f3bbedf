-- CREATE REMINDER TEMPLATE
INSERT INTO notification_template (
    uuid, created_at, updated_at, version, code, status, type, execution_type, name, priority, description, attributes, template_group, default_email, default_sms, enable_email, enable_sms, enable_portal)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REQUEST_COMPLETION_REMINDER', 'ACTIVE', 'CUSTOMER_SYSTEM', 'AUTOMATIC', 'Pripomienka dokončenia žiadosti o prepis', 'HIGH', 'Notifikácia o pripomenutí dokončenia žiadosti o prepis', null, null, true, false, true, false, false);

-- CREATE REMINDER TEMPLATE I18Ns
INSERT INTO notification_template_i18n (
    uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status, header_url)
VALUES (
    uuid_generate_v4(), now(), now(), 1, NULL,
    '<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník ${businessPartner.name} požiadal o prepis odberného miesta na Vás, pre dokončenie žiadosti online kliknite <a href="${attributes.requestCompletionUrl}">sem</a></@spp.notification_email_template>',
    'Pripomenutie dokončenia žiadosti o prepis',
    NULL,
    (SELECT uuid FROM notification_template
        WHERE code = 'CUSTOMER_REQUEST_COMPLETION_REMINDER'),
    'SK', 'ACTIVE', NULL), (

    uuid_generate_v4(), now(), now(), 1, NULL,
    '<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="">Zákazník ${businessPartner.name} požiadal o prepis odberného miesta na Vás, pre dokončenie žiadosti online kliknite <a href="${attributes.requestCompletionUrl}">sem</a></@spp.notification_email_template>',
    '[EN] Pripomenutie dokončenia žiadosti o prepis',
    NULL,
    (SELECT uuid FROM notification_template
        WHERE code = 'CUSTOMER_REQUEST_COMPLETION_REMINDER'),
    'EN', 'ACTIVE', NULL);

-- CREATE REMINDER VARIABLES
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.email', 'Email zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.firstName', 'Meno zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'logged.customer.email', 'Email zalogovaného používateľa', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'logged.customer.firstName', 'Meno zalogovaného používateľa', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'logged.customer.lastName', 'Priezvisko zalogovaného používateľa', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'logged.customer.phone', 'Telefónne číslo zalogovaného používateľa', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'notificationTemplateCode', 'Kód notifikácie', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácie cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', 'Týka sa iba SAP entít.', 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'locale', 'Lokalizácia', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.name', 'Názov obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.firstName', 'Meno obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.lastName', 'Priezvisko obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.email', 'Email obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.phone', 'Telefonné číslo obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.street', 'Ulica obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.streetNumber', 'Číslo domu obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.city', 'Obec obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.zipCode', 'PSČ obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.country', 'Štát obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amFirstName', 'Obchodný partner - meno manažéra predaja', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amLastName', 'Obchodný partner - priezvisko manažéra predaja', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amEmail', 'Obchodný partner - email manažéra predaja', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amPhone', 'Obchodný partner - telefónne číslo manažéra predaja', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amName', 'Obchodný partner - meno pobočky', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.externalId', 'SAP ID obchodného partnera', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.kindCode', 'Kód druhu obchodného partnera', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.externalId', 'SAP ID žiadosti', 'Dostupné iba ak bola žiadosť zaevidovaná v SAPe', 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.name', 'Názov žiadosti', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.status.code', 'Kód stavu žiadosti', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.status.name', 'Stav žiadosti', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.registeredAt', 'Dátum registrácie v SAPe', 'Dátum odoslania žiadosti do SAPu', 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.externalId', 'SAP ID žiadosti', 'Dostupné iba ak bola žiadosť zaevidovaná v SAPe', 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customerRequest.confirmedAt', 'Dátum a čas potvrdenia žiadosti', 'Dostupné iba ak bola žiadosť potvrdená zákazníkom', 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'attributes.requestCompletionUrl', 'Url na dokončenie žiadosti', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'attributes.requestCompletionValidTo', 'Dátum validity url na dokončenie žiadosti', NULL, 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'attributes.requestCompletionEmailFrom', 'Email zákazníka, ktorý založil žiadosť', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('CUSTOMER_REQUEST_COMPLETION_REMINDER');