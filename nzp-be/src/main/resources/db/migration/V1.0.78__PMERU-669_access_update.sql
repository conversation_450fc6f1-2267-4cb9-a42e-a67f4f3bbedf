DELETE FROM access_group_right;
DELETE FROM access_group;

INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('NZP_CUSTOMERS_ACTIVATION', current_timestamp, current_timestamp, 1, 'Aktivacia a Deaktivacia Zakaznikov', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('NZP_CUSTOMERS_VIEW', current_timestamp, current_timestamp, 1, '<PERSON><PERSON><PERSON> z<PERSON> uctov', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('NZP_CUSTOMERS_DELETE', current_timestamp, current_timestamp, 1, '<PERSON><PERSON><PERSON> z<PERSON> uctov', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('NZP_BUSINESS_PARTNERS_PAIRING', current_timestamp, current_timestamp, 1, '<PERSON>rovanie ob<PERSON><PERSON> partnerov', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('NZP_NOTIFICATION_TEMPLATES', current_timestamp, current_timestamp, 1, 'Sprava notifikacnych sablon', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('NZP_REPORTING_MANAGEMENT', current_timestamp, current_timestamp, 1, 'Sprava a spustanie reportov ', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('NZP_REPORTING', current_timestamp, current_timestamp, 1, 'Spustanie existujucich reportov', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('NZP_SUPER_ADMIN', current_timestamp, current_timestamp, 1, 'Super admin', '') ;
INSERT INTO access_group (code, created_at , updated_at ,"version", name, description) values ('NZP_PORTAL_ACCESS', current_timestamp, current_timestamp, 1, 'Portal access', '') ;

INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('CUSTOMERS_ACTIVATION', 'NZP_CUSTOMERS_ACTIVATION', current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('CUSTOMERS_DEACTIVATION', 'NZP_CUSTOMERS_ACTIVATION', current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('CUSTOMERS_VIEW', 'NZP_CUSTOMERS_VIEW', current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('CUSTOMERS_DELETE_ACCOUNT', 'NZP_CUSTOMERS_DELETE', current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('BUSINESS_PARTNERS_PAIRING', 'NZP_BUSINESS_PARTNERS_PAIRING', current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('BUSINESS_PARTNERS_UNPAIRING', 'NZP_BUSINESS_PARTNERS_PAIRING', current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('NOTIFICATION_TEMPLATES_VIEW', 'NZP_NOTIFICATION_TEMPLATES', current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('NOTIFICATION_TEMPLATES_EDIT', 'NZP_NOTIFICATION_TEMPLATES', current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('REPORTS_EDIT', 'NZP_REPORTING', current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('REPORTS_EDIT', 'NZP_REPORTING_MANAGEMENT', current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('REPORTS_VIEW', 'NZP_REPORTING', current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('PORTAL_ACCESS', 'NZP_PORTAL_ACCESS', current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_code, created_at, operation, queue) values ('SSO_ADMIN', 'NZP_SUPER_ADMIN', current_timestamp, 'GRANT', NULL);