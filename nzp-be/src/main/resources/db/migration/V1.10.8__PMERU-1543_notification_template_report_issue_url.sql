-- remove obsolete link to report suspicious issue, as the link is contained in the freemarker template macro
update notification_template_i18n
set email_body = replace(email_body, ' Vaše SPP <br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>', '')
where email_body like '%${portalExternalUrl}%';

update notification_template_i18n
set email_body = replace(email_body, ' Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>', '')
where email_body like '%${portalExternalUrl}%';

update notification_template_i18n
set email_body = replace(email_body, '<br/><br/>Va<PERSON>e SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>', '')
where email_body like '%${portalExternalUrl}%';
