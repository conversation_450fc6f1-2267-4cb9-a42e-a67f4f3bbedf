-------------------------------------------------------------------------------------
---- PMERU-1535: ADD component_help records
-------------------------------------------------------------------------------------

INSERT INTO component_help (uuid, created_at, updated_at, version, screen, field, status, help_order) VALUES (uuid_generate_v4(), now(), now(), 1, 'FAK', 'FAK_BANK_ACCOUNTS_EE_RETAIL', 'ACTIVE', 365);
INSERT INTO component_help (uuid, created_at, updated_at, version, screen, field, status, help_order) VALUES (uuid_generate_v4(), now(), now(), 1, 'FAK', 'FAK_BANK_ACCOUNTS_EE_HOME', 'ACTIVE', 366);
INSERT INTO component_help (uuid, created_at, updated_at, version, screen, field, status, help_order) VALUES (uuid_generate_v4(), now(), now(), 1, 'FAK', 'FAK_BANK_ACCOUNTS_EE_WHOLESALE', 'ACTIVE', 367);

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id) VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '<p><strong>Tatra banka, a.s.</strong><br><span style="font-size: 14px;">SK49 1100 0000 0026 2022 5605</span></p>
<p><strong>Všeobecná úverová banka, a.s.</strong><br><span style="font-size: 14px;">SK90 0200 0000 0010 0101 8151</span></p>
<p><strong>UniCredit Bank Czech Republic and Slovakia, a.s.</strong><br><span style="font-size: 14px;">SK20 1111 0000 0000 0513 0277</span></p>
<p><strong>Československá obchodná banka, a.s.</strong><br><span style="font-size: 14px;">SK74 ************** 2511 5813</span></p>', (SELECT uuid FROM component_help WHERE screen = 'FAK' AND field = 'FAK_BANK_ACCOUNTS_EE_RETAIL'));
INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id) VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', '<p><strong>Tatra banka, a.s.</strong><br><span style="font-size: 14px;">SK49 1100 0000 0026 2022 5605</span></p>
<p><strong>Všeobecná úverová banka, a.s.</strong><br><span style="font-size: 14px;">SK90 0200 0000 0010 0101 8151</span></p>
<p><strong>UniCredit Bank Czech Republic and Slovakia, a.s.</strong><br><span style="font-size: 14px;">SK20 1111 0000 0000 0513 0277</span></p>
<p><strong>Československá obchodná banka, a.s.</strong><br><span style="font-size: 14px;">SK74 ************** 2511 5813</span></p>', (SELECT uuid FROM component_help WHERE screen = 'FAK' AND field = 'FAK_BANK_ACCOUNTS_EE_RETAIL'));
INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id) VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '<p><strong>Tatra banka, a.s.</strong><br><span style="font-size: 14px;">SK49 1100 0000 0026 2022 5605</span></p>
<p><strong>Slovenská sporiteľňa, a.s.</strong><br><span style="font-size: 14px;">SK05 0900 0000 0001 7358 4614</span></p>
<p><strong>Všeobecná úverová banka, a.s.</strong><br><span style="font-size: 14px;">SK54 0200 0000 0010 0202 9151</span></p>
<p><strong>UniCredit Bank Czech Republic and Slovakia, a.s.</strong><br><span style="font-size: 14px;">SK20 1111 0000 0000 0513 0277</span></p>
<p><strong>Československá obchodná banka, a.s.</strong><br><span style="font-size: 14px;">SK74 ************** 2511 5813</span></p>', (SELECT uuid FROM component_help WHERE screen = 'FAK' AND field = 'FAK_BANK_ACCOUNTS_EE_HOME'));
INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id) VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', '<p><strong>Tatra banka, a.s.</strong><br><span style="font-size: 14px;">SK49 1100 0000 0026 2022 5605</span></p>
<p><strong>Slovenská sporiteľňa, a.s.</strong><br><span style="font-size: 14px;">SK05 0900 0000 0001 7358 4614</span></p>
<p><strong>Všeobecná úverová banka, a.s.</strong><br><span style="font-size: 14px;">SK54 0200 0000 0010 0202 9151</span></p>
<p><strong>UniCredit Bank Czech Republic and Slovakia, a.s.</strong><br><span style="font-size: 14px;">SK20 1111 0000 0000 0513 0277</span></p>
<p><strong>Československá obchodná banka, a.s.</strong><br><span style="font-size: 14px;">SK74 ************** 2511 5813</span></p>', (SELECT uuid FROM component_help WHERE screen = 'FAK' AND field = 'FAK_BANK_ACCOUNTS_EE_HOME'));
INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id) VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '<p><strong>Tatra banka, a.s.</strong><br><span style="font-size: 14px;">SK49 1100 0000 0026 2022 5605</span></p>
<p><strong>Všeobecná úverová banka, a.s.</strong><br><span style="font-size: 14px;">SK27 0200 0000 0010 3331 1555</span></p>
<p><strong>UniCredit Bank Czech Republic and Slovakia, a.s.</strong><br><span style="font-size: 14px;">SK20 1111 0000 0000 0513 0277</span></p>
<p><strong>Československá obchodná banka, a.s.</strong><br><span style="font-size: 14px;">SK74 ************** 2511 5813</span></p>', (SELECT uuid FROM component_help WHERE screen = 'FAK' AND field = 'FAK_BANK_ACCOUNTS_EE_WHOLESALE'));
INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, content, component_help_id) VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', '<p><strong>Tatra banka, a.s.</strong><br><span style="font-size: 14px;">SK49 1100 0000 0026 2022 5605</span></p>
<p><strong>Všeobecná úverová banka, a.s.</strong><br><span style="font-size: 14px;">SK27 0200 0000 0010 3331 1555</span></p>
<p><strong>UniCredit Bank Czech Republic and Slovakia, a.s.</strong><br><span style="font-size: 14px;">SK20 1111 0000 0000 0513 0277</span></p>
<p><strong>Československá obchodná banka, a.s.</strong><br><span style="font-size: 14px;">SK74 ************** 2511 5813</span></p>', (SELECT uuid FROM component_help WHERE screen = 'FAK' AND field = 'FAK_BANK_ACCOUNTS_EE_WHOLESALE'));
