

-- Invoice.type [->] Invoice.typeGroup
UPDATE template_variable SET variable = 'invoice.typeGroup' where variable = 'invoice.type';

-- [+]Contract.postAddress
------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.name', 'Zmluvný účet - názov organizácie', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.lastName', 'Zmluvný účet - priezvisko', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.firstName', 'Zmluvný účet - meno', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.postCountry', 'Zmluvný účet - korešpondenčná adresa - štát', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.postZipCode', 'Zmluvný účet - korešpondenčná adresa -  PSČ', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.postCity', 'Zmluvný účet - korešpondenčná adresa -  mesto', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.postStreetNumber', 'Zmluvný účet - korešpondenčná adresa - popisné číslo', '', 'STRING');

------------------------------------------------------------------------------------------------------------------------

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'contractAccount.postStreet', 'Zmluvný účet - korešpondenčná adresa -  ulica', '', 'STRING');


-- [-]Contract.advancePayAmount
delete from template_variable where variable = 'contract.advancePayAmount';

-- [-]BP.post
delete from template_notification_variable where template_variable_uuid = (select uuid from template_variable where variable = 'businessPartner.postStreet');
delete from template_notification_variable where template_variable_uuid = (select uuid from template_variable where variable = 'businessPartner.postStreetNumber');
delete from template_notification_variable where template_variable_uuid = (select uuid from template_variable where variable = 'businessPartner.postCity');
delete from template_notification_variable where template_variable_uuid = (select uuid from template_variable where variable = 'businessPartner.postZipCode');
delete from template_notification_variable where template_variable_uuid = (select uuid from template_variable where variable = 'businessPartner.postCountry');

delete from template_variable where variable = 'businessPartner.postStreet';
delete from template_variable where variable = 'businessPartner.postStreetNumber';
delete from template_variable where variable = 'businessPartner.postCity';
delete from template_variable where variable = 'businessPartner.postZipCode';
delete from template_variable where variable = 'businessPartner.postCountry';

-- [+]BP.amName

INSERT INTO public.template_variable(
	uuid, created_at, updated_at, version, variable, name, description, type)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'businessPartner.amName', 'Obchodný partner - meno pobočky', '', 'STRING');

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST'));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS'));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST'));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS'));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE'));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY'));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED'));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED'));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED'));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE'));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE'));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY'));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY'));

INSERT INTO public.template_notification_variable(
        	template_variable_uuid, notification_template_uuid)
        	VALUES ( (select uuid from template_variable where variable = 'businessPartner.amName'), (select uuid from notification_template where code = 'METER_READING_CUSTOMER'));
