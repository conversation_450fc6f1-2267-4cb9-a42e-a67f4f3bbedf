-- Create new
ALTER TABLE delivery_point          ADD COLUMN notification_limit_checked_at        TIMESTAMP WITH TIME ZONE;
ALTER TABLE delivery_point          ADD COLUMN locked_by                            CHARACTER VARYING(50);
ALTER TABLE delivery_point          ADD COLUMN check_limit_retry_count              INTEGER;

-- Initialize
UPDATE delivery_point SET notification_limit_checked_at =
    greatest(notification_rk_checked_at, notification_mrk_checked_at, notification_dmm_checked_at);

-- Update view
DROP VIEW v_delivery_point;
CREATE VIEW v_delivery_point AS (
    SELECT
        dp.id,
        dp.created_at,
        dp.updated_at,
        dp."version",
        dp.external_id,
        dp."type",
        dp.pod,
        dp.eic,
        dp.street,
        dp.street_number,
        dp.city,
        dp.zip_code,
        dp.country,
        dp.synchronization_log_uuid,
        dp.synchronization_at,
        dp.valid_from,
        dp.valid_to,
        dp.device_number,
        dp.notification_limit_checked_at,
        c.id AS contract_id,
        c.business_partner_id AS business_partner_id,
        c.united_delivery_point_id AS united_delivery_point_id
    FROM delivery_point dp
    JOIN contract c ON c.delivery_point_id = dp.id
    AND c.effective_from < CURRENT_DATE
    AND c.effective_to > CURRENT_DATE);

-- Drop old
ALTER TABLE delivery_point_fact DROP COLUMN check_rk_locked_by;
ALTER TABLE delivery_point_fact DROP COLUMN check_rk_retry_count;
ALTER TABLE delivery_point_fact DROP COLUMN check_mrk_locked_by;
ALTER TABLE delivery_point_fact DROP COLUMN check_mrk_retry_count;
ALTER TABLE delivery_point_fact DROP COLUMN check_zm_locked_by;
ALTER TABLE delivery_point_fact DROP COLUMN check_zm_retry_count;
ALTER TABLE delivery_point_fact DROP COLUMN check_dmm_locked_by;
ALTER TABLE delivery_point_fact DROP COLUMN check_dmm_retry_count;

ALTER TABLE delivery_point DROP COLUMN notification_rk_checked_at;
ALTER TABLE delivery_point DROP COLUMN notification_mrk_checked_at;
ALTER TABLE delivery_point DROP COLUMN notification_zm_checked_at;
ALTER TABLE delivery_point DROP COLUMN notification_dmm_checked_at;