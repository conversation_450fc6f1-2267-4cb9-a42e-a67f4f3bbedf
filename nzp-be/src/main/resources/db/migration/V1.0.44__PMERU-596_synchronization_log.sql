
-------------------
-- synchronization_log
-------------------

CREATE TABLE synchronization_log
(
    uuid                        uuid                        NOT NULL,
    created_at                  TIMESTAMP WITH TIME ZONE    NOT NULL,
    updated_at                  TIMESTAMP WITH TIME ZONE    NOT NULL,
    version                     integer                     NOT NULL,

    status                      CHARACTER VARYING(50)       NOT NULL,
    entity_type                 CHARACTER VARYING(50)       NOT NULL,
    file_name                   text                        NOT NULL,
    success_entry_count         integer                     NOT NULL,
    failed_entry_count          integer                     NOT NULL,

    CONSTRAINT pk_synchronization_log PRIMARY KEY (uuid)
)
WITH (
    OIDS = FALSE
);