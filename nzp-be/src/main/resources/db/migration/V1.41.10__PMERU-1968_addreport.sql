insert into report (
    uuid,
    created_at,
    updated_at,
    version,
    name,
    definition,
    access_groups,
    errors,
    created_by,
    updated_by)
 values (
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'Registácia portálových ú<PERSON> zo systému SAP',
    '{"dataEntity":"SapRegistrationCustomerAccount","alias":"ROOT","format":null,"joins":[],"outputProps":["ROOT.email","ROOT.type","ROOT.status","ROOT.activationAt","ROOT.registrationAt","ROOT.deleteReason","ROOT.deletedAt","ROOT.phone","ROOT.firstName","ROOT.lastName","ROOT.sapLogin","ROOT.businessPartnerExternalId","ROOT.loginSuccessAt","ROOT.loginUnsuccessAt"],"filterConditions":[]}',
    null,
    null,
    'system',
    'system'
    );