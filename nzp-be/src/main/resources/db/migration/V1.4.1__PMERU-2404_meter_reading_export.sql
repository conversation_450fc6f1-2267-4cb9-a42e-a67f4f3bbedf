
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ID', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', '<PERSON><PERSON>lo odpočtu', '<PERSON><PERSON>lo odpočtu', (select uuid from generic_code_list where code like 'ID' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] <PERSON><PERSON>lo odpočtu', '[EN] <PERSON><PERSON><PERSON> odpo<PERSON>', (select uuid from generic_code_list where code like 'ID' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DEVICE_NUMBER', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Číslo merača', 'Číslo merača', (select uuid from generic_code_list where code like 'DEVICE_NUMBER' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Číslo merača', '[EN] Číslo merača', (select uuid from generic_code_list where code like 'DEVICE_NUMBER' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REGISTER_KIND_CODE', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Druh registra (kód)', 'Druh registra (kód)', (select uuid from generic_code_list where code like 'REGISTER_KIND_CODE' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Druh registra (kód)', '[EN] Druh registra (kód)', (select uuid from generic_code_list where code like 'REGISTER_KIND_CODE' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REGISTER_KIND_NAME', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Druh registra', 'Druh registra', (select uuid from generic_code_list where code like 'REGISTER_KIND_NAME' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Druh registra', '[EN] Druh registra', (select uuid from generic_code_list where code like 'REGISTER_KIND_NAME' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REASON_CODE', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dôvod odpočtu (kód)', 'Dôvod odpočtu (kód)', (select uuid from generic_code_list where code like 'REASON_CODE' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Dôvod odpočtu (kód)', '[EN] Dôvod odpočtu (kód)', (select uuid from generic_code_list where code like 'REASON_CODE' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REASON_NAME', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dôvod odpočtu', 'Dôvod odpočtu', (select uuid from generic_code_list where code like 'REASON_NAME' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Dôvod odpočtu', '[EN] Dôvod odpočtu', (select uuid from generic_code_list where code like 'REASON_NAME' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'KIND_CODE', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Druh odpočtu (kód)', 'Druh odpočtu (kód)', (select uuid from generic_code_list where code like 'KIND_CODE' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Druh odpočtu (kód)', '[EN] Druh odpočtu (kód)', (select uuid from generic_code_list where code like 'KIND_CODE' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'KIND_NAME', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Druh odpočtu', 'Druh odpočtu', (select uuid from generic_code_list where code like 'KIND_NAME' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Druh odpočtu', '[EN] Druh odpočtu', (select uuid from generic_code_list where code like 'KIND_NAME' and type = 'METER_READING_EXPORT_COLUMN'));

