create table title (
    id                              CHARACTER VARYING(100) NOT NULL,
    created_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                        INTEGER                   NOT NULL,
    external_id                    CHARACTER VARYING(50),
    type                           CHARACTER VARYING(50),
    name                           CHARACTER VARYING(20),
    synchronization_log_uuid       uuid                      NOT NULL,
    synchronization_at             TIMESTAMP WITH TIME ZONE,
    constraint pk_title primary key (id),
    constraint fk_title_synchronization_log foreign key (synchronization_log_uuid) references synchronization_log
)
WITH (
    OIDS = FALSE
);

create index idx_title_synchronization_log_uuid
    on title (synchronization_log_uuid);

ALTER TABLE business_partner ADD COLUMN title_front CHARACTER VARYING(100)
CONSTRAINT fk_title_front REFERENCES title(id)
ON UPDATE CASCADE;

ALTER TABLE business_partner ADD COLUMN title_behind CHARACTER VARYING(100)
CONSTRAINT fk_title_behind REFERENCES title(id)
ON UPDATE CASCADE;