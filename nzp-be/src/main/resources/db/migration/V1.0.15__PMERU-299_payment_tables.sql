----------
-- payment
----------

CREATE TABLE payment
(
    uuid                         uuid                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      integer                   NOT NULL,
    
    external_id                  CHARACTER VARYING(50)     NOT NULL,
    status                       CHARACTER VARYING(20)     NOT NULL,
    type                         CHARACTER VARYING(50),
    amount                       NUMERIC(19,2)             NOT NULL,
    currency                     CHARACTER VARYING(50)     NOT NULL,
    execute_date                 DATE,
    
    invoice_id            uuid,

    CONSTRAINT pk_payment PRIMARY KEY (uuid),
    CONSTRAINT fk_payment_invoice FOREIGN KEY (invoice_id)
        REFERENCES invoice (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_payment_external_id on payment(external_id);
CREATE INDEX idx_payment_invoice_id on payment(invoice_id);