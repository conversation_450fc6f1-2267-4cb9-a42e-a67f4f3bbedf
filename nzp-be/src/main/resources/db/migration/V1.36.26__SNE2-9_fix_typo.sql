UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, "name"='Odpočet spotreby zemného plynu/ elektriny'
    WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOM_O') and LOWER(locale) = 'sk';

UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, "name"='Prerušenie/obnovenie dodávky zemného plynu/ elektriny z dôvodu rekonštrukcie'
    WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOM_ZOPAOO') and LOWER(locale) = 'sk';

UPDATE customer_request_template_i18n SET updated_at=now(), "version"= "version"+1, "name"='Ukončenie zmluvy na dodávku zemného plynu/ elektriny'
    WHERE customer_request_template_uuid = (select uuid from customer_request_template where code = 'ZOM_ZOUZ') and LOWER(locale) = 'sk';