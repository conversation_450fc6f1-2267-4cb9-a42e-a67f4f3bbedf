-- Agreement Rules
INSERT INTO component_help (uuid, created_at, updated_at, version, screen, field, status, help_order)
VALUES (uuid_generate_v4(), now(), now(), 1, 'NZD', 'NZD_AGREEMENT_RULES_OF_PRIVATE_DATA_PROCESSING', 'ACTIVE', 1);

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, component_help_id, content) VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', (SELECT uuid FROM component_help WHERE screen = 'NZD' AND field = 'NZD_AGREEMENT_RULES_OF_PRIVATE_DATA_PROCESSING'),
'Beriem na vedomie, že SPP spracúva moje osobné údaje uvedené v tejto elektronickej žiadosti za účelom výkonu činností SPP podľa udeleného povolenia na podnikanie v oblasti energetiky, a to najmä na účely uzatvorenia zmluvy a vykonanie opatrení pred uzatvorením zmluvy, a to po dobu nevyhnutne potrebnú pre zabezpečenie výkonu práv a povinností vyplývajúcich z predzmluvného vzťahu v nevyhnutnom rozsahu na základe článku 6 ods. 1 písm. b) Nariadenia (EÚ) 2016/679 o ochrane fyzických osôb pri spracúvaní osobných údajov a o voľnom pohybe takýchto údajov (všeobecné nariadenie o ochrane údajov), resp. § 13 ods. 1 písm. b) zákona č. 18/2018 Z. z. o ochrane osobných údajov v platnom znení. Beriem na vedomie, že na uzatvorenie zmluvy je nevyhnutné, aby SPP spracúval údaje v rozsahu meno a priezvisko, adresa trvalého bydliska, adresa pre poštový styk, dátum narodenia a číslo bankového účtu odberateľa, pričom spracúvanie emailovej adresy a/alebo telefónneho čísla je nevyhnutným na plnenie zmluvy v prípade, ak odberateľ a SPP tieto údaje budú využívať na vzájomnú komunikáciu, alebo odberateľ bude využívať služby predpokladajúce emailovú alebo telefonickú komunikáciu alebo si zvolí elektronické zasielanie faktúr.');

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, component_help_id, content) VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', (SELECT uuid FROM component_help WHERE screen = 'NZD' AND field = 'NZD_AGREEMENT_RULES_OF_PRIVATE_DATA_PROCESSING'),
'[EN] Beriem na vedomie, že SPP spracúva moje osobné údaje uvedené v tejto elektronickej žiadosti za účelom výkonu činností SPP podľa udeleného povolenia na podnikanie v oblasti energetiky, a to najmä na účely uzatvorenia zmluvy a vykonanie opatrení pred uzatvorením zmluvy, a to po dobu nevyhnutne potrebnú pre zabezpečenie výkonu práv a povinností vyplývajúcich z predzmluvného vzťahu v nevyhnutnom rozsahu na základe článku 6 ods. 1 písm. b) Nariadenia (EÚ) 2016/679 o ochrane fyzických osôb pri spracúvaní osobných údajov a o voľnom pohybe takýchto údajov (všeobecné nariadenie o ochrane údajov), resp. § 13 ods. 1 písm. b) zákona č. 18/2018 Z. z. o ochrane osobných údajov v platnom znení. Beriem na vedomie, že na uzatvorenie zmluvy je nevyhnutné, aby SPP spracúval údaje v rozsahu meno a priezvisko, adresa trvalého bydliska, adresa pre poštový styk, dátum narodenia a číslo bankového účtu odberateľa, pričom spracúvanie emailovej adresy a/alebo telefónneho čísla je nevyhnutným na plnenie zmluvy v prípade, ak odberateľ a SPP tieto údaje budú využívať na vzájomnú komunikáciu, alebo odberateľ bude využívať služby predpokladajúce emailovú alebo telefonickú komunikáciu alebo si zvolí elektronické zasielanie faktúr.');

-- Confirmation rules
INSERT INTO component_help (uuid, created_at, updated_at, version, screen, field, status, help_order)
VALUES (uuid_generate_v4(), now(), now(), 1, 'NZD', 'NZD_CONFIRMATION_RULES_OF_PRIVATE_DATA_PROCESSING', 'ACTIVE', 1);

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, component_help_id, content) VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', (SELECT uuid FROM component_help WHERE screen = 'NZD' AND field = 'NZD_CONFIRMATION_RULES_OF_PRIVATE_DATA_PROCESSING'),
'Potvrdzujem, že SPP ma informoval o podrobnostiach o spracúvaní osobných údajov vrátane poučenia o právach odberateľa ako dotknutej osoby. Podrobné informácie o spracúvaní osobných údajov odberateľa vyžadované platnými právnymi predpismi sú dostupné na webovej stránke SPP na podstránke <a href="https://www.spp.sk/sk/vsetky-segmenty/ochrana-udajov/" target="_blank">Ochrana súkromia a spracúvanie osobných údajov</a> a na ktoromkoľvek Zákazníckom centre SPP.');

INSERT INTO component_help_i18n (uuid, created_at, updated_at, version, locale, component_help_id, content) VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', (SELECT uuid FROM component_help WHERE screen = 'NZD' AND field = 'NZD_CONFIRMATION_RULES_OF_PRIVATE_DATA_PROCESSING'),
'[EN] Potvrdzujem, že SPP ma informoval o podrobnostiach o spracúvaní osobných údajov vrátane poučenia o právach odberateľa ako dotknutej osoby. Podrobné informácie o spracúvaní osobných údajov odberateľa vyžadované platnými právnymi predpismi sú dostupné na webovej stránke SPP na podstránke <a href="https://www.spp.sk/sk/vsetky-segmenty/ochrana-udajov/" target="_blank">Ochrana súkromia a spracúvanie osobných údajov</a> a na ktoromkoľvek Zákazníckom centre SPP.');


