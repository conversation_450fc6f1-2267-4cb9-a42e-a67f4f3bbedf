-- ALTER 'customerAccount'
ALTER TABLE customer_account ADD registration_source CHARACTER VARYING(50);

-- add reference to registration
ALTER TABLE customer_account ADD registration_batch_request_id uuid;

-- FK
ALTER TABLE customer_account ADD CONSTRAINT fk_customer_account_reg_batch FOREIGN KEY (registration_batch_request_id)
        REFERENCES registration_batch_request (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;

-- UPDATE all customers to
UPDATE customer_account SET registration_source = 'CUSTOMER' where registration_source is null;
