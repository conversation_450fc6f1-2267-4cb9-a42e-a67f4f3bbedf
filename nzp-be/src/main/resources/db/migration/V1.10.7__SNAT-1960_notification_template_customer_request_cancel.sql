
-- new notification: CUSTOMER_REQUEST_CANCELLED_CUSTOMER
INSERT INTO notification_template
(uuid, created_at, updated_at, version, code, status, type, name, priority, execution_type,
 description, attributes, template_group, default_email, default_sms)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REQUEST_CANCELLED_CUSTOMER', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Zrušenie žiadosti zákazníka', 'LOW', 'AUTOMATIC',
 'Notifikácia o zrušení žiadosti pre zákazníka', null, null, true, false);

INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, status, header, email_body,
 email_subject, sms_body,
 notification_template_id, locale)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'Zrušenie žiadosti zákazníka',
 '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">žia<PERSON>ť s názvom ${customerRequest.name} bola zrušená. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a></@spp.notification_email_template>',
 'Zrušenie žiadosti zákazníka',
 null, (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCELLED_CUSTOMER' and version = 1), 'SK');

INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, status, header, email_body,
 email_subject, sms_body,
 notification_template_id, locale)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ACTIVE', '[EN] Zrušenie žiadosti zákazníka',
 '<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] žiadosť s názvom ${customerRequest.name} bola zrušená. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a></@spp.notification_email_template>',
 '[EN] Zrušenie žiadosti zákazníka',
 null, (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCELLED_CUSTOMER' and version = 1), 'EN');


insert into notification_template_variable select (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCELLED_CUSTOMER' and version = 1), now(), now(), 1, ntv.variable, ntv.name, ntv.description, ntv.type, ntv.notification_template_execution_type, uuid_generate_v4() from notification_template_variable ntv where ntv.notification_template_uuid = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER' and version = 1);


-- update of notification CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER
update notification_template
set name = 'Vyžiadanie zrušenia žiadosti'
where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER';

update notification_template_i18n
set header = 'Vyžiadanie zrušenia žiadosti',
    email_body = '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">žiadosť s názvom ${customerRequest.name} bola vyžiadaná na zrušenie. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a></@spp.notification_email_template>',
    email_subject = 'Vyžiadanie zrušenia žiadosti'
where notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER' and version = 1)
  and lower(locale) = 'sk' ;

update notification_template_i18n
set header = '[EN] Vyžiadanie zrušenia žiadosti',
    email_body = '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] žiadosť s názvom ${customerRequest.name} bola vyžiadaná na zrušenie. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a></@spp.notification_email_template>',
    email_subject = '[EN] Vyžiadanie zrušenia žiadosti'
where notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER' and version = 1)
  and lower(locale) = 'en' ;