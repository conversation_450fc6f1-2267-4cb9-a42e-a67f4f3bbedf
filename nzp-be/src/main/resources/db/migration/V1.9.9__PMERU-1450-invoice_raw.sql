
CREATE TABLE invoice_raw_tmp (
	invoice_id text NULL,
	created_at timestamptz NOT NULL,
	updated_at timestamptz NOT NULL,
	version int4 NOT NULL,
	item1 int4 NOT NULL,
	item2 int4 NOT NULL,
	item3 int4 NOT NULL,
	type text NULL,
	amount numeric(19,2) NULL,
	execute_at date NULL,
	status varchar(50) NOT NULL,
	payment_req_invoice_id text NULL,
	delivery_point_id text NULL,
	contract_id text NULL,
	repayment_plan_id text NULL,
	contract_account_id text NULL,
	payment_req_notif_sent bool NULL,
	file_archive_id text NULL,
	due_at date NULL,
	issue_at date NULL,
	vs text NULL,
	synchronization_log_uuid uuid NULL,
	synchronization_at timestamptz NOT NULL,
	payment_vs text NULL,
	payment_external_id text NULL,
	external_id text NOT NULL,
	payment_type varchar(50) NULL,
	payment_amount numeric(19,2) NULL,
	balancing_reason text NULL,
	balancing_reference text NULL,
	invoice_fa_id text NULL
);

-- transfer and transform
INSERT INTO invoice_raw_tmp
(invoice_id, created_at, updated_at, version, item1, item2, item3, type, amount, execute_at, status, payment_req_invoice_id, delivery_point_id, contract_id, repayment_plan_id, contract_account_id, payment_req_notif_sent, file_archive_id, due_at, issue_at, vs, synchronization_log_uuid, synchronization_at, payment_vs, payment_external_id, external_id, payment_type, payment_amount, balancing_reason, balancing_reference, invoice_fa_id)
SELECT ir.invoice_id, ir.created_at, ir.updated_at, ir.version, ir.item1, ir.item2, ir.item3, typegcl.code, ir.amount, ir.execute_at, ir.status, ir.payment_req_invoice_id, ir.delivery_point_id, ir.contract_id, ir.repayment_plan_id, ir.contract_account_id, ir.payment_req_notif_sent, ir.file_archive_id, ir.due_at, ir.issue_at, ir.vs, ir.synchronization_log_uuid, ir.synchronization_at, ir.payment_vs, ir.payment_external_id, ir.external_id, ir.payment_type, ir.payment_amount, ir.balancing_reason, ir.balancing_reference, ir.invoice_fa_id
FROM invoice_raw ir
left outer join generic_code_list typegcl on typegcl.uuid = ir.gcl_type_uuid ;

-- drop old
drop table invoice_raw;
-- replace with new
ALTER TABLE invoice_raw_tmp RENAME TO invoice_raw;

-- create constraints and indexes
ALTER table invoice_raw add CONSTRAINT pk_invoice_raw PRIMARY KEY (external_id, item1, item2, item3);
ALTER table invoice_raw add CONSTRAINT fk_invoice_raw_synchronization_log FOREIGN KEY (synchronization_log_uuid) REFERENCES synchronization_log(uuid);

CREATE INDEX idx_invoice_raw_delivery_point_id ON invoice_raw(delivery_point_id);
CREATE INDEX idx_invoice_raw_external_id ON invoice_raw(external_id);
CREATE INDEX idx_invoice_raw_invoice_fa_id ON invoice_raw(invoice_fa_id);
CREATE INDEX idx_invoice_raw_invoice_id ON invoice_raw(invoice_id);
CREATE INDEX idx_invoice_raw_payment_external_id ON invoice_raw(payment_external_id);
CREATE INDEX idx_invoice_raw_repayment_plan_id ON invoice_raw(repayment_plan_id);
CREATE INDEX idx_invoice_raw_synchronization_log_uuid ON invoice_raw(synchronization_log_uuid);
