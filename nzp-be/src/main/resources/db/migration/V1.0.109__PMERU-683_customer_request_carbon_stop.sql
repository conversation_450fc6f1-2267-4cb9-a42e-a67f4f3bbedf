-- ZOP_US CUSTOMER REQUEST TEMPLATE

INSERT INTO public.customer_request_template(
	uuid, created_at, updated_at, version, status, code, name, description, target, price)
	VALUES (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOP_US', 'iado<PERSON><PERSON> obchodný partner - <PERSON><PERSON><PERSON><PERSON> stopka', 'Žiadosť pre zazmluvnenie produktu Uhlíková stopka.', 'BUSINESS_PARTNER', null);

-- CARBON_STOP_PLEVEL_EE

INSERT INTO
    generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'EE1', 'CARBON_STOP_PLEVEL_EE'),
    (uuid_generate_v4(), now(), now(), 1, 'EE2', 'CARBON_STOP_PLEVEL_EE'),
    (uuid_generate_v4(), now(), now(), 1, 'EE3', 'CARBON_STOP_PLEVEL_EE');

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Uhlíková stopka - EE1', null, (select uuid from generic_code_list where code like 'EE1' and type = 'CARBON_STOP_PLEVEL_EE')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Uhlíková stopka - EE2', null, (select uuid from generic_code_list where code like 'EE2' and type = 'CARBON_STOP_PLEVEL_EE')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Uhlíková stopka - EE3', null, (select uuid from generic_code_list where code like 'EE3' and type = 'CARBON_STOP_PLEVEL_EE'));

-- CARBON_STOP_PLEVEL_ZP

INSERT INTO
    generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'ZP1', 'CARBON_STOP_PLEVEL_ZP'),
    (uuid_generate_v4(), now(), now(), 1, 'ZP2', 'CARBON_STOP_PLEVEL_ZP'),
    (uuid_generate_v4(), now(), now(), 1, 'ZP3', 'CARBON_STOP_PLEVEL_ZP');

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Uhlíková stopka - ZP1', null, (select uuid from generic_code_list where code like 'ZP1' and type = 'CARBON_STOP_PLEVEL_ZP')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Uhlíková stopka - ZP2', null, (select uuid from generic_code_list where code like 'ZP2' and type = 'CARBON_STOP_PLEVEL_ZP')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Uhlíková stopka - ZP3', null, (select uuid from generic_code_list where code like 'ZP3' and type = 'CARBON_STOP_PLEVEL_ZP'));