-- DeliveryPointConsumption
INSERT INTO notification_template_variable
    SELECT nt.uuid, now(), now(), 1, 'deliveryPointConsumption.limit', 'Maximálny odber', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4()
    FROM notification_template nt
    WHERE nt.code IN (
        'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_RK',
        'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_AM_CHECK_MRK',
        'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_DMM');

INSERT INTO notification_template_variable
    SELECT nt.uuid, now(), now(), 1, 'deliveryPointConsumption.exceeded.value', '<PERSON><PERSON><PERSON> hodnota', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4()
    FROM notification_template nt
    WHERE nt.code IN (
        'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_RK',
        'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_AM_CHECK_MRK',
        'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_DMM');

INSERT INTO notification_template_variable
    SELECT nt.uuid, now(), now(), 1, 'deliveryPointConsumption.exceeded.period.from', 'Interval nameranej hodnoty od', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4()
    FROM notification_template nt
    WHERE nt.code IN (
        'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_RK',
        'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_AM_CHECK_MRK',
        'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_DMM');

INSERT INTO notification_template_variable
    SELECT nt.uuid, now(), now(), 1, 'deliveryPointConsumption.exceeded.period.to', 'Interval nameranej hodnoty do', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4()
    FROM notification_template nt
    WHERE nt.code IN (
        'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_RK',
        'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_AM_CHECK_MRK',
        'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_DMM');