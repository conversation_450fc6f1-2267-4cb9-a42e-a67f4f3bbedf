INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SERVICE_CALL_CONSENT_REQUEST','AUDIT_LOG_CODE', null, null, null);


INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Odoslanie marketingového súhlasu', '<PERSON>doslanie marketingového súhlasu',
(select uuid from generic_code_list where code = 'SERVICE_CALL_CONSENT_REQUEST' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Sending marketing consent', 'Sending marketing consent',
(select uuid from generic_code_list where code = 'SERVICE_CALL_CONSENT_REQUEST' and type = 'AUDIT_LOG_CODE'));