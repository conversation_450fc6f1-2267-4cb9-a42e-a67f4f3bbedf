

update notification_template_i18n 
set email_body = email_body || '<br/><br/><a href="https://meru-nzp-dev.isdd.sk/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>' 
where email_body is not null;

update notification_template_i18n 
set email_body = 'Bola nahlásená podozrivá aktivita uuid=${attributes.notificationUuid}. Túto aktivitu nahlasil používateľ ${attributes.firstName} ${attributes.lastName}. Email používateľa: ${attributes.email}, telefónne číslo ${attributes.phone}. Popis podozrivej aktivity: ${attributes.description}.' 
where notification_template_id = (select uuid from notification_template nt where nt.code = 'SUSPICIOUS_ACTIVITY');




insert into template_variable (uuid, created_at, updated_at , "name" , "type" , variable , "version" ) values (uuid_generate_v4(), now(), now(), 'Uuid notifikácie', 'STRING', 'notificationUuid', 1);
insert into template_variable (uuid, created_at, updated_at , "name" , "type" , variable , "version" ) values (uuid_generate_v4(), now(), now(), 'Kód notifikácie', 'STRING', 'notificationTemplateCode', 1);


insert into template_notification_variable (notification_template_uuid , template_variable_uuid )
select nt.uuid as ntId, tv.uuid as tvId from notification_template nt join template_variable tv on (tv.variable = 'notificationUuid');

insert into template_notification_variable (notification_template_uuid , template_variable_uuid )
select nt.uuid as ntId, tv.uuid as tvId from notification_template nt join template_variable tv on (tv.variable = 'notificationTemplateCode');




insert into template_variable (uuid, created_at, updated_at , "name" , "type" , variable , "version" ) values (uuid_generate_v4(), now(), now(), 'Uuid pôvodnej notifikácie', 'STRING', 'attributes.notificationUuid', 1);
insert into template_variable (uuid, created_at, updated_at , "name" , "type" , variable , "version" ) values (uuid_generate_v4(), now(), now(), 'Zdroj', 'STRING', 'attributes.source', 1);
insert into template_variable (uuid, created_at, updated_at , "name" , "type" , variable , "version" ) values (uuid_generate_v4(), now(), now(), 'Meno', 'STRING', 'attributes.firstName', 1);
insert into template_variable (uuid, created_at, updated_at , "name" , "type" , variable , "version" ) values (uuid_generate_v4(), now(), now(), 'Priezvisko', 'STRING', 'attributes.lastName', 1);
insert into template_variable (uuid, created_at, updated_at , "name" , "type" , variable , "version" ) values (uuid_generate_v4(), now(), now(), 'email', 'STRING', 'attributes.email', 1);
insert into template_variable (uuid, created_at, updated_at , "name" , "type" , variable , "version" ) values (uuid_generate_v4(), now(), now(), 'Tel. číslo', 'STRING', 'attributes.phone', 1);
insert into template_variable (uuid, created_at, updated_at , "name" , "type" , variable , "version" ) values (uuid_generate_v4(), now(), now(), 'Popis', 'STRING', 'attributes.description', 1);

insert into template_notification_variable (notification_template_uuid , template_variable_uuid )
select nt.uuid as ntId, tv.uuid as tvId from notification_template nt join template_variable tv on (tv.variable = 'attributes.notificationUuid')
where nt.code = 'SUSPICIOUS_ACTIVITY';

insert into template_notification_variable (notification_template_uuid , template_variable_uuid )
select nt.uuid as ntId, tv.uuid as tvId from notification_template nt join template_variable tv on (tv.variable = 'attributes.source')
where nt.code = 'SUSPICIOUS_ACTIVITY';

insert into template_notification_variable (notification_template_uuid , template_variable_uuid )
select nt.uuid as ntId, tv.uuid as tvId from notification_template nt join template_variable tv on (tv.variable = 'attributes.firstName')
where nt.code = 'SUSPICIOUS_ACTIVITY';

insert into template_notification_variable (notification_template_uuid , template_variable_uuid )
select nt.uuid as ntId, tv.uuid as tvId from notification_template nt join template_variable tv on (tv.variable = 'attributes.lastName')
where nt.code = 'SUSPICIOUS_ACTIVITY';

insert into template_notification_variable (notification_template_uuid , template_variable_uuid )
select nt.uuid as ntId, tv.uuid as tvId from notification_template nt join template_variable tv on (tv.variable = 'attributes.phone')
where nt.code = 'SUSPICIOUS_ACTIVITY';

insert into template_notification_variable (notification_template_uuid , template_variable_uuid )
select nt.uuid as ntId, tv.uuid as tvId from notification_template nt join template_variable tv on (tv.variable = 'attributes.description')
where nt.code = 'SUSPICIOUS_ACTIVITY';

insert into template_notification_variable (notification_template_uuid , template_variable_uuid )
select nt.uuid as ntId, tv.uuid as tvId from notification_template nt join template_variable tv on (tv.variable = 'attributes.email')
where nt.code = 'SUSPICIOUS_ACTIVITY';

