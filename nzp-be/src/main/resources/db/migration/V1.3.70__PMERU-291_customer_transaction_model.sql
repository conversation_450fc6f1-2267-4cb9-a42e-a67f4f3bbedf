
CREATE TABLE customer_transaction
(
    uuid                         uuid                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      integer                   NOT NULL,

    status                       CHARACTER VARYING(50),
    last_status_changed_at       TIMESTAMP WITH TIME ZONE,
    started_at                   TIMESTAMP WITH TIME ZONE,
    finished_at                  TIMESTAMP WITH TIME ZONE,

    bulk                         boolean,
    finished                     boolean,

    vs                           text                      NOT NULL,

    amount                       NUMERIC(19,2),
    customer_uuid                uuid                      NOT NULL,
    business_partner_id          text,
    delivery_point_id            text,

    CONSTRAINT pk_customer_transaction PRIMARY KEY (uuid)
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_customer_uuid on customer_transaction(customer_uuid);
CREATE INDEX idx_business_partner_id on customer_transaction(business_partner_id);
CREATE INDEX idx_delivery_point_id on customer_transaction(delivery_point_id);



CREATE TABLE customer_transaction_invoice
(
    created_at                               TIMESTAMP WITH TIME ZONE  NOT NULL,
    customer_transaction_uuid                uuid                      NOT NULL,
    invoice_id                               text                      NOT NULL,

    CONSTRAINT pk_customer_transaction_invoice PRIMARY KEY (invoice_id, customer_transaction_uuid)
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_customer_transaction_uuid on customer_transaction_invoice(customer_transaction_uuid);