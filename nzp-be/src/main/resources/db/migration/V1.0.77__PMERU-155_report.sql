CREATE TABLE report
(
    uuid            uuid                      NOT NULL,
    created_at      TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at      TIMESTAMP WITH TIME ZONE  NOT NULL,
    version         integer                   NOT NULL,

    created_by_uuid uuid                      NOT NULL,
    updated_by_uuid uuid,
    name            CHARACTER VARYING(255)    NOT NULL,
    definition      text,
    access_groups   text,
    errors   text,

    CONSTRAINT pk_report PRIMARY KEY (uuid)
);

CREATE UNIQUE INDEX idx_report_name_unq on report (name);

CREATE TABLE data_model
(
    uuid            uuid                      NOT NULL,
    created_at      TIMESTAMP WITH TIME ZONE  NOT NULL,
    valid_from      TIMESTAMP WITH TIME ZONE  NOT NULL,
    model           text                      NOT NULL,
    flyway_version  CHARACTER VARYING(32),

    CONSTRAINT pk_data_model PRIMARY KEY (uuid)
);