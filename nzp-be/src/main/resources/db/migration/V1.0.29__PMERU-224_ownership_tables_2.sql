CREATE TABLE united_delivery_point_ownership
(
    created_at                     TIMESTAMP WITH TIME ZONE    NOT NULL,

    type                           CHARACTER VARYING(32)       NOT NULL,
    inherited                      boolean                     NOT NULL,
    customer_account_uuid          uuid                        NOT NULL,

    united_delivery_point_uuid     uuid,

    CONSTRAINT pk_udp_ownership PRIMARY KEY (customer_account_uuid, united_delivery_point_uuid),
    CONSTRAINT fk_udp_ownership_customer_account FOREIGN KEY (customer_account_uuid)
        REFERENCES customer_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk_udp_ownership_united_delivery_point FOREIGN KEY (united_delivery_point_uuid)
        REFERENCES united_delivery_point (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);



DROP VIEW v_sharing_summary;
CREATE VIEW v_sharing_summary AS
(select created_at, type, inherited, customer_account_uuid, target_uuid, business_partner_uuid, contract_account_uuid, contract_uuid, contract_product_uuid, delivery_point_uuid, united_delivery_point_uuid, entity_type
from (
         select created_at, type, inherited, customer_account_uuid, business_partner_uuid as target_uuid, business_partner_uuid, Null::UUID as contract_account_uuid, Null::UUID as contract_uuid, Null::UUID as contract_product_uuid, Null::UUID as delivery_point_uuid, Null::UUID as united_delivery_point_uuid, CAST('BUSINESS_PARTNER' as CHARACTER VARYING(32)) as entity_type
         from business_partner_ownership
         union
         select created_at, type, inherited, customer_account_uuid, contract_account_uuid as target_uuid, Null::UUID as business_partner_uuid, contract_account_uuid, Null::UUID as contract_uuid, Null::UUID as contract_product_uuid, Null::UUID as delivery_point_uuid, Null::UUID as united_delivery_point_uuid, CAST('CONTRACT_ACCOUNT' as CHARACTER VARYING(32)) as entity_type
         from contract_account_ownership
         union
         select created_at, type, inherited, customer_account_uuid, contract_uuid as target_uuid, Null::UUID as business_partner_uuid, Null::UUID as contract_account_uuid, contract_uuid, Null::UUID as contract_product_uuid, Null::UUID as delivery_point_uuid, Null::UUID as united_delivery_point_uuid, CAST('CONTRACT' as CHARACTER VARYING(32)) as entity_type
         from contract_ownership
         union
         select created_at, type, inherited, customer_account_uuid, contract_product_uuid as target_uuid, Null::UUID as business_partner_uuid, Null::UUID as contract_account_uuid, Null::UUID as contract_uuid, contract_product_uuid, Null::UUID as delivery_point_uuid, Null::UUID as united_delivery_point_uuid, CAST('CONTRACT_PRODUCT' as CHARACTER VARYING(32)) as entity_type
         from contract_product_ownership
         union
         select created_at, type, inherited, customer_account_uuid, delivery_point_uuid as target_uuid, Null::UUID as business_partner_uuid, Null::UUID as contract_account_uuid, Null::UUID as contract_uuid, Null::UUID as contract_product_uuid, delivery_point_uuid, Null::UUID as united_delivery_point_uuid, CAST('DELIVERY_POINT' as CHARACTER VARYING(32)) as entity_type
         from delivery_point_ownership
         union
         select created_at, type, inherited, customer_account_uuid, united_delivery_point_uuid as target_uuid, Null::UUID as business_partner_uuid, Null::UUID as contract_account_uuid, Null::UUID as contract_uuid, Null::UUID as contract_product_uuid,  Null::UUID as delivery_point_uuid, united_delivery_point_uuid, CAST('UNITED_DELIVERY_POINT' as CHARACTER VARYING(32)) as entity_type
         from united_delivery_point_ownership
     ) as q
    );