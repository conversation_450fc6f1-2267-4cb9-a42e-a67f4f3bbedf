-- ADVANCE_PAYMENT_PERIOD

INSERT INTO
    generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'MONTHLY', 'ADVANCE_PAYMENT_PERIOD'),
    (uuid_generate_v4(), now(), now(), 1, 'QUARTERLY', 'ADVANCE_PAYMENT_PERIOD'),
    (uuid_generate_v4(), now(), now(), 1, 'HALF_YEARLY', 'ADVANCE_PAYMENT_PERIOD'),
    (uuid_generate_v4(), now(), now(), 1, 'YEARLY', 'ADVANCE_PAYMENT_PERIOD');

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'mesačne', null, (select uuid from generic_code_list where code like 'MONTHLY' and type = 'ADVANCE_PAYMENT_PERIOD')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'štvrťročne', null, (select uuid from generic_code_list where code like 'QUARTERLY' and type = 'ADVANCE_PAYMENT_PERIOD')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'polročne', null, (select uuid from generic_code_list where code like 'HALF_YEARLY' and type = 'ADVANCE_PAYMENT_PERIOD')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'ročne', null, (select uuid from generic_code_list where code like 'YEARLY' and type = 'ADVANCE_PAYMENT_PERIOD'));

-- COMPLAINED_INVOICE_PROBLEM

INSERT INTO
    generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'WRONG_METER_READING', 'COMPLAINED_INVOICE_PROBLEM'),
    (uuid_generate_v4(), now(), now(), 1, 'WRONG_TARIFF', 'COMPLAINED_INVOICE_PROBLEM'),
    (uuid_generate_v4(), now(), now(), 1, 'WRONG_CUSTOMER_DATA', 'COMPLAINED_INVOICE_PROBLEM'),
    (uuid_generate_v4(), now(), now(), 1, 'OTHER', 'COMPLAINED_INVOICE_PROBLEM');

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Nesprávne uvedený odpočet stavu meradla', null, (select uuid from generic_code_list where code like 'WRONG_METER_READING' and type = 'COMPLAINED_INVOICE_PROBLEM')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Nesprávna cenová tarifa alebo sadzba', null, (select uuid from generic_code_list where code like 'WRONG_TARIFF' and type = 'COMPLAINED_INVOICE_PROBLEM')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Chybné údaje zákazníka', null, (select uuid from generic_code_list where code like 'WRONG_CUSTOMER_DATA' and type = 'COMPLAINED_INVOICE_PROBLEM')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Iný dôvod', null, (select uuid from generic_code_list where code like 'OTHER' and type = 'COMPLAINED_INVOICE_PROBLEM'));

-- OVERPAYMENT_SETTLEMENT_TYPE

INSERT INTO
    generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'AFTER_DUE_DATE_SETTLEMENT', 'OVERPAYMENT_SETTLEMENT_TYPE'),
    (uuid_generate_v4(), now(), now(), 1, 'BANK_ACCOUNT', 'OVERPAYMENT_SETTLEMENT_TYPE'),
    (uuid_generate_v4(), now(), now(), 1, 'ADDRESS', 'OVERPAYMENT_SETTLEMENT_TYPE');

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Vyrovnanie pohľadávky po splatnosti', null, (select uuid from generic_code_list where code like 'AFTER_DUE_DATE_SETTLEMENT' and type = 'OVERPAYMENT_SETTLEMENT_TYPE')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Zaslať na bankový účet', null, (select uuid from generic_code_list where code like 'BANK_ACCOUNT' and type = 'OVERPAYMENT_SETTLEMENT_TYPE')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Zaslať na poštovú adresu', null, (select uuid from generic_code_list where code like 'ADDRESS' and type = 'OVERPAYMENT_SETTLEMENT_TYPE'));

-- ZOM_ZOPAOO_REASON

INSERT INTO
    generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'SUSPEND', 'ZOM_ZOPAOO_REASON'),
    (uuid_generate_v4(), now(), now(), 1, 'RESTORE', 'ZOM_ZOPAOO_REASON');

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Prerušenie odberu', null, (select uuid from generic_code_list where code like 'SUSPEND' and type = 'ZOM_ZOPAOO_REASON')),
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Obnovenie odberu (z dôvodu rekonštrukcie)', null, (select uuid from generic_code_list where code like 'RESTORE' and type = 'ZOM_ZOPAOO_REASON'));



