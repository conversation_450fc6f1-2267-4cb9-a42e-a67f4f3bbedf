

INSERT INTO notification_template
(uuid, created_at, updated_at, version, code, status, type, name, priority, execution_type,
 description, attributes, template_group, default_email, default_sms, customer_visibility)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE', 'ACTIVE', 'CUSTOMER_NOTICE', 'Zmena stavu žiadosti', 'LOW', 'AUTOMATIC',
 'Notifikacia ohladom zmeny stavu pre ziadost', null, 'CUSTOMER_REQUEST', true, true, true);

INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, status, header, email_body,
 email_subject, sms_body,
 notification_template_id, locale)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'Zmena stavu žiadosti',
 '<PERSON><PERSON><PERSON><PERSON><PERSON>, žia<PERSON>ť s názvom ${customerRequest.name} zmenila stav na: ${customerRequest.status.name}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>',
 'Zmena stavu žiadosti',
 'Vazeny zakaznik, ziadost s nazvom ${customerRequest.name} zmenila stav na: ${customerRequest.status.name}. Vase SPP',
 (select uuid from notification_template where code = 'ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1), 'SK'); 
 
INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, status, header, email_body,
 email_subject, sms_body,
 notification_template_id, locale)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'Zmena stavu žiadosti',
 '[EN] Vážený zákazník, žiadosť s názvom ${customerRequest.name} zmenila stav na: ${customerRequest.status.name}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>',
 '[EN] Zmena stavu žiadosti',
  null,
 (select uuid from notification_template where code = 'ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1), 'EN'); 



insert into notification_template_variable
 (uuid, created_at, updated_at, "version", variable, "name", description, "type", notification_template_execution_type, notification_template_uuid)
select uuid_generate_v4(), now(), now(), 1, ntv.variable, ntv."name", ntv.description, ntv."type", ntv.notification_template_execution_type, nt.uuid 
from notification_template_variable ntv
join notification_template nt on (nt.code = 'ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE')
where ntv.notification_template_uuid = (select uuid from notification_template nt where code = 'CUSTOMER_REQUEST_STATUS_CHANGE') 
and 
ntv.variable not like 'target%'
and
ntv.variable not like 'businessPartner.%'
and
ntv.variable not like 'logged.%'
and
ntv.variable not like 'customer.%';

insert into notification_template_variable
 (uuid, created_at, updated_at, "version", variable, "name", description, "type", notification_template_execution_type, notification_template_uuid )
values
 (uuid_generate_v4(), now(), now(), 1, 'email', 'Email', null, 'STRING', 'AUTOMATIC', (select uuid from notification_template nt where code = 'ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE'));

insert into notification_template_variable
 (uuid, created_at, updated_at, "version", variable, "name", description, "type", notification_template_execution_type, notification_template_uuid )
values
 (uuid_generate_v4(), now(), now(), 1, 'phone', 'Telefónne číslo', null, 'STRING', 'AUTOMATIC', (select uuid from notification_template nt where code = 'ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE'));
