
create view v_united_deliver_point_commodities as
select 
	uuid,
	created_at,
	updated_at,
	version,
	street,
	street_number,
	city,
	zip_code,
	country,
	business_partner_id,
	pairing_status,
	locked_by ,
	retry_count ,
	address_searchable,
	address_key,
	synchronization_at,
	synchronization_log_uuid,
	types,
	(
	select
	        string_agg(dpType,'')
	    from
	        (
		        select dp.type as dpType from contract c 
				join delivery_point dp on (dp.id = c.delivery_point_id)
				where c.united_delivery_point_id = united_delivery_point.uuid and c.effective_from < current_date and effective_to > current_date
				group by dp.type order by dp.type
			) a
	) as active_commodities,
	(
	select
	        string_agg(dpType,'')
	    from
	        (
		        select dp.type as dpType from contract c 
				join delivery_point dp on (dp.id = c.delivery_point_id)
				where c.united_delivery_point_id = united_delivery_point.uuid 
				group by dp.type order by dp.type
			) a
	) as all_commodities
from united_delivery_point;