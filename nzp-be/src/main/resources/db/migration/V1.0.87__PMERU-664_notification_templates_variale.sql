CREATE TABLE template_variable
(
    uuid            uuid                      NOT NULL,
    created_at      TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at      TIMESTAMP WITH TIME ZONE  NOT NULL,
    version         integer                   NOT NULL,

    variable        CHARACTER VARYING(100)    NOT NULL,
    name            text                      NOT NULL,
    description     text,
    type            <PERSON><PERSON><PERSON>TER VARYING(32)     NOT NULL,

    CONSTRAINT pk_template_variable PRIMARY KEY (uuid)
);

CREATE UNIQUE INDEX idx_template_variable_name_unq on template_variable (name);

CREATE TABLE template_notification_variable
(
    template_variable_uuid        uuid          NOT NULL,
    notification_template_uuid    uuid          NOT NULL,

    CONSTRAINT pk_template_notification_variable PRIMARY KEY (template_variable_uuid, notification_template_uuid),
    CONSTRAINT fk_template_notification_id foreign key (notification_template_uuid) references notification_template (uuid),
    CONSTRAINT fk_template_variable_id foreign key (template_variable_uuid) references template_variable (uuid)
);
