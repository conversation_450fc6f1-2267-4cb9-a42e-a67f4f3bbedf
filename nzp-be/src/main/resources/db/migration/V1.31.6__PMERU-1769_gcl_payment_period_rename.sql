-- 00
UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'bez preddavkov'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '00')
AND LOWER(locale) = 'sk';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'without advance payments'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '00')
AND LOWER(locale) = 'en';

-- 01
UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'mesačne'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '01')
AND LOWER(locale) = 'sk';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'monthly'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '01')
AND LOWER(locale) = 'en';

-- 02
UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'raz za 2 mesiace'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '02')
AND LOWER(locale) = 'sk';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'once every 2 months'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '02')
AND LOWER(locale) = 'en';

-- 03
UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'štvrťročne'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '03')
AND LOWER(locale) = 'sk';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'quarterly'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '03')
AND LOWER(locale) = 'en';

-- 04
UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'raz za 4 mesiace'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '04')
AND LOWER(locale) = 'sk';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'once every 4 months'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '04')
AND LOWER(locale) = 'en';

-- 06
UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'polročne'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '06')
AND LOWER(locale) = 'sk';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'half-yearly'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '06')
AND LOWER(locale) = 'en';

-- 12
UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'raz ročne'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '12')
AND LOWER(locale) = 'sk';

UPDATE generic_code_list_i18n
SET updated_at = now(), version = (version + 1), name = 'yearly'
WHERE code_list_uuid = (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '12')
AND LOWER(locale) = 'en';
