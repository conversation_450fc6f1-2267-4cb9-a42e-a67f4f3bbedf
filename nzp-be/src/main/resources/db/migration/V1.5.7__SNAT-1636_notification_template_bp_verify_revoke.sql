
-- new notification: BUSINESS_PARTNER_VERIFY_REVOKE
INSERT INTO notification_template
(uuid, created_at, updated_at, version, code, status, type, name, priority, execution_type,
 description, attributes, template_group, default_email, default_sms)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_VERIFY_REVOKE', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Zamietnutie párovania obchodného partnera', 'LOW', 'AUTOMATIC',
 'Notifikácia o zamietnutí párovania obchodného partnera', null, null, true, false);

INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, status, header, email_body,
 email_subject, sms_body,
 notification_template_id, locale)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'Zamietnutie párovania obchodného partnera',
 '<PERSON><PERSON><PERSON><PERSON><PERSON>, párovanie obchodného partnera s číslom ${target.entity.externalId} bolo zamietnuté. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>',
 'Zamietnutie párovania obchodného partnera',
 null, (select uuid from notification_template where code = 'BUSINESS_PARTNER_VERIFY_REVOKE' and version = 1), 'SK');

INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, status, header, email_body,
 email_subject, sms_body,
 notification_template_id, locale)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ACTIVE', '[EN] Zamietnutie párovania obchodného partnera',
 '[EN] Vážený zákazník, párovanie obchodného partnera s číslom ${target.entity.externalId} bolo zamietnuté. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>',
 '[EN] Zamietnutie párovania obchodného partnera',
 null, (select uuid from notification_template where code = 'BUSINESS_PARTNER_VERIFY_REVOKE' and version = 1), 'EN');


-- common fields that are always present
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.email', 'Email zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.firstName', 'Meno zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.email', 'Email zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.firstName', 'Meno zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.lastName', 'Priezvisko zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.phone', 'Telefónne číslo zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationTemplateCode', 'Kód notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácie cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', 'Týka sa iba SAP entít.', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'locale', 'Lokalizácia', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');


-- target: BusinessPartner
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.name', 'Názov obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.firstName', 'Meno obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.lastName', 'Priezvisko obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.email', 'Email obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.phone', 'Telefonné číslo obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.street', 'Ulica obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.streetNumber', 'Číslo domu obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.city', 'Obec obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.zipCode', 'PSČ obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.country', 'Štát obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amFirstName', 'Obchodný partner - meno manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amLastName', 'Obchodný partner - priezvisko manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amEmail', 'Obchodný partner - email manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amPhone', 'Obchodný partner - telefónne číslo manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amName', 'Obchodný partner - meno pobočky', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('BUSINESS_PARTNER_VERIFY_REVOKE');
