create index idx_business_partner_ownership_customer on business_partner_ownership(customer_account_uuid);



create or replace view v_business_partner_extended_ownerships as (

    select bpo.business_partner_id as business_partner_id, bpo.customer_account_uuid as customer_account_uuid, bpo."type" as type
    from business_partner_ownership bpo
	
    union
	
	select udp.business_partner_id as business_partner_id, udpo.customer_account_uuid as customer_account_uuid, null as type
	from united_delivery_point_ownership udpo 
	join united_delivery_point udp on (udp.uuid = udpo.united_delivery_point_uuid)
	
	union
	
	select bppr.business_partner_id as business_partner_id, bppr.customer_account_id as customer_account_uuid, null as type
	from business_partner_pairing_request bppr where status = 'VERIFY'
);