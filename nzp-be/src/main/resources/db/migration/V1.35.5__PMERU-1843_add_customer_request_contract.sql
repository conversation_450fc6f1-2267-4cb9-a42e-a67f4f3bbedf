-------------------------------
-- customer_request_contract --
-------------------------------

create table customer_request_contract (

    uuid uuid not null,
    created_at timestamp with time zone not null,
    updated_at timestamp with time zone not null,
    version integer not null,

    customer_request_id uuid not null,
    contract_account_id text,
    contract_id character varying (50),
    delivery_point_id text,

    constraint pk_customer_request_contract
    primary key (uuid),

    constraint fk_customer_request_contract_customer_request_id
    foreign key (customer_request_id) references customer_request(uuid)
    on update no action
    on delete no action,

    constraint fk_customer_request_contract_delivery_point_id
    foreign key (delivery_point_id) references delivery_point(id)
    on update no action
    on delete no action

);