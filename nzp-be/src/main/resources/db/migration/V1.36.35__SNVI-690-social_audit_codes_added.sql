INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SOCIAL_UNPAIR_SUCCESS', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Úspešné odpárovanie účtu socálnej siete', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_UNPAIR_SUCCESS' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Úspešné odp<PERSON>rovanie ú<PERSON> socálnej siete', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_UNPAIR_SUCCESS' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SOCIAL_PAIRING_SUCCESS', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Úspešné napárovanie účtu socálnej siete', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_PAIRING_SUCCESS' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Úspešné napárovanie účtu socálnej siete', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_PAIRING_SUCCESS' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SOCIAL_FB_LOGIN_ENABLED', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Povolenie prihlasovania prostredníctvom Facebook účtu', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_FB_LOGIN_ENABLED' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Povolenie prihlasovania prostredníctvom Facebook účtu', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_FB_LOGIN_ENABLED' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SOCIAL_FB_LOGIN_DISABLED', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Zakázanie prihlasovania prostredníctvom Facebook účtu', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_FB_LOGIN_DISABLED' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Zakázanie prihlasovania prostredníctvom Facebook účtu', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_FB_LOGIN_DISABLED' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SOCIAL_GOOGLE_LOGIN_ENABLED', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Povolenie prihlasovania prostredníctvom Google účtu', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_GOOGLE_LOGIN_ENABLED' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Povolenie prihlasovania prostredníctvom Google účtu', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_GOOGLE_LOGIN_ENABLED' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SOCIAL_GOOGLE_LOGIN_DISABLED', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Zakázanie prihlasovania prostredníctvom Google účtu', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_GOOGLE_LOGIN_DISABLED' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Zakázanie prihlasovania prostredníctvom Google účtu', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_GOOGLE_LOGIN_DISABLED' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SOCIAL_DISABLE_INVALID_NO_OTHER_LOGIN_OPT', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Odmietnutie zakázania prihlasovania prostredníctvom účtu na sociálnej sieti, kvôli chýbajúcej alternatívnej metóde prihlasovania', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_DISABLE_INVALID_NO_OTHER_LOGIN_OPT' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Odmietnutie zakázania prihlasovania prostredníctvom účtu na sociálnej sieti, kvôli chýbajúcej alternatívnej metóde prihlasovania', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_DISABLE_INVALID_NO_OTHER_LOGIN_OPT' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SOCIAL_UNPAIRING_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Pokus o odpárovanie účtu socálnej siete', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_UNPAIRING_ATTEMPT' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Pokus o odpárovanie účtu socálnej siete', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_UNPAIRING_ATTEMPT' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SOCIAL_UNPAIR_INVALID_NO_OTHER_LOGIN_OPT', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Odmietnutie odpárovania účtu na sociálnej sieti, kvôli chýbajúcej alternatívnej metóde prihlasovania', null, (select uuid from generic_code_list where code like 'SOCIAL_UNPAIR_INVALID_NO_OTHER_LOGIN_OPT' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Odmietnutie odpárovania účtu na sociálnej sieti, kvôli chýbajúcej alternatívnej metóde prihlasovania', null, (select uuid from generic_code_list where code like 'SOCIAL_UNPAIR_INVALID_NO_OTHER_LOGIN_OPT' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SOCIAL_PAIRING_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'sk', 'Pokus o napárovanie účtu socálnej siete', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_PAIRING_ATTEMPT' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Pokus o napárovanie účtu socálnej siete', null, (select uuid from generic_code_list where code like 'CUSTOMER_SOCIAL_PAIRING_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
