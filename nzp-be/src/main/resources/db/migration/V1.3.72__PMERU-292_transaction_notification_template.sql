INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms, execution_type, report_customer_column, report_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'EPAY_TRANSACTION_FINISHED', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Potvrdenie platby', 'HIGH', 'Potvrdenie banky o zaplatení platby', null, null, false, true, 'AUTOMATIC', null, null);


INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status)
VALUES
    (uuid_generate_v4(), now(), now(), 1, '<PERSON><PERSON><PERSON> platba bola akceptovaná bankou.', '<PERSON><PERSON><PERSON><PERSON> deň, vaša platba na ${customerTransaction.amount} Eur bola akceptovaná bankou.', '<PERSON><PERSON><PERSON><PERSON> platby', '<PERSON><PERSON><PERSON> den, vasa platba na ${customerTransaction.amount} Eur bola akceptovana bankou', (select uuid from notification_template where code = 'EPAY_TRANSACTION_FINISHED' and version = 1), 'SK', 'ACTIVE');

INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status)
VALUES
(uuid_generate_v4(), now(), now(), 1, '[EN] Vaša platba bola akceptovaná bankou.', '[EN] Dobrý deň, vaša platba na ${customerTransaction.amount} Eur bola akceptovaná bankou.', '[EN] Prijatie platby', '[EN] Dobry den, vasa platba na ${customerTransaction.amount} Eur bola akceptovana bankou', (select uuid from notification_template where code = 'EPAY_TRANSACTION_FINISHED' and version = 1), 'EN', 'ACTIVE');

-- Customer Transaction
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerTransaction.transactionId', 'Identifikátor transakcie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerTransaction.amount', 'Čiastka transakcie', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerTransaction.vs', 'Transakcia - variabilný symbol', null, 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerTransaction.status', 'Transakcia - status', null, 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerTransaction.finishedAt', 'Transakcia - dátum akceptovania', null, 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');

-- BP
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.name', 'Názov obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.firstName', 'Meno obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.lastName', 'Priezvisko obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.email', 'Email obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.phone', 'Telefonné číslo obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.street', 'Ulica obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.streetNumber', 'Číslo domu obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.city', 'Obec obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.zipCode', 'PSČ obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.country', 'Štát obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amFirstName', 'Obchodný partner - meno manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amLastName', 'Obchodný partner - priezvisko manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amEmail', 'Obchodný partner - email manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amPhone', 'Obchodný partner - telefónne číslo manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amName', 'Obchodný partner - meno pobočky', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
-- DP
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.type', 'Typ odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.eic', 'Odberné miesto - eic', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.pod', 'Odberné miesto - pod', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.street', 'Ulica odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.streetNumber', 'Číslo odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.city', 'Obec odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.zipCode', 'PSČ odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'deliveryPoint.country', 'Štát odberného miesta', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
-- Invoice
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.status', 'Status faktúry', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.amount', 'Čiastka faktúry', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.dueAt', 'Faktúra - splatné do', null, 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.issueAt', 'Faktúra - vystavené', null, 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.executeAt', 'Faktúra - dátum dodania', null, 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.vs', 'Faktúra - vs', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'invoice.typeGroup', 'Typ faktúry', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =  ('EPAY_TRANSACTION_FINISHED');

-- common fields that are always present
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.email', 'Email zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.firstName', 'Meno zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationTemplateCode', 'Kód notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácie cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', 'Týka sa iba SAP entít.', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'locale', 'Lokalizácia', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code =('EPAY_TRANSACTION_FINISHED');
