INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms, execution_type, report_customer_column, report_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_PHONE_ALREADY_REGISTERED', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Telefónne číslo už je zaregistrované', 'HIGH', 'Notifikácia Telefónne číslo už je zaregistrované', null, null, false, true, 'AUTOMATIC', null, null);


INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Telefónne číslo už je zaregistrované', null, null, 'Poziadavka na zmenu telefonneho cisla zamietnuta. Telefonne cislo je uz registrovane pre emailovu adresu ${attributes.customerEmail}', (select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), 'SK', 'ACTIVE');


--common fields that are always present
INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'customer.email', 'Email zákazníka', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'customer.firstName', 'Meno zákazníka', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'logged.customer.email', 'Email zalogovaného používateľa', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'logged.customer.firstName', 'Meno zalogovaného používateľa', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'logged.customer.lastName', 'Priezvisko zalogovaného používateľa', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'logged.customer.phone', 'Telefónne číslo zalogovaného používateľa', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'notificationTemplateCode', 'Kód notifikácie', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácie cielenej entity', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity', null, 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', 'Týka sa iba SAP entít.', 'STRING');

INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'locale', 'Lokalizácia', null, 'NUMBER');

--CUSTOMER_PHONE_ALREADY_REGISTERED
INSERT INTO notification_template_variable
    (notification_template_uuid, created_at, updated_at, version, variable, name, description, type)
VALUES
    ((select uuid from notification_template where code = 'CUSTOMER_PHONE_ALREADY_REGISTERED' and version = 1), now(), now(), 1, 'attributes.customerEmail', 'Email zákazníka, ktorému patrí tel. č.', null, 'STRING');