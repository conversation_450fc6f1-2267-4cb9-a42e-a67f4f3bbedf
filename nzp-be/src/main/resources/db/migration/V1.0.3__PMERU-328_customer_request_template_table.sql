CREATE TABLE customer_request_template
(
    uuid            uuid                      NOT NULL,
    created_at      TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at      TIMESTAMP WITH TIME ZONE  NOT NULL,
    version         integer                    NOT NULL,
    status          CHARACTER VARYING(16)     NOT NULL,
    code            CHARACTER VARYING(32)     NOT NULL,
    name            CHARACTER VARYING(255)    NOT NULL,
    description     text,
    target          CHARACTER VARYING(32),

    CONSTRAINT pk_customer_request_template PRIMARY KEY (uuid)
)
WITH (
    OIDS = FALSE
);

-- indexes
create unique index idx_customer_request_template_code_unq on customer_request_template (code);