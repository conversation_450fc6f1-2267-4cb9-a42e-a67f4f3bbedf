INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, execution_type,
    description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Registrácia už existujúcej emailovej adresy', 'HIGH', 'AUTOMATIC',
    'Notifikacia ohľadom už registrovaného emailového účtu', null, null, true, false);

INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, status,
    header,
    email_body,
    email_subject,
    sms_body,
    notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'ACTIVE',
    'Registrácia emailovej adresy. Existujúci účet',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>azník, účet s Vašou emailovou adresou už bol vytvorený. Ak potrebujete zmeniť heslo, kliknite <a href="${portalExternalUrl}/login-issues">na túto linku</a>. Vaše SPP <br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>',
    'Registrácia emailovej adresy. Existujúci účet',
    null,
    (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and version = 1), 'sk');

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.entity.uuid' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.entity.type' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.entity.createdAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.entity.updatedAt' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'target.customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'customer.email' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'customer.phone' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'customer.firstName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and version = 1));

INSERT INTO public.template_notification_variable(template_variable_uuid, notification_template_uuid)
    VALUES ((select uuid from template_variable where variable = 'customer.lastName' and version = 1), (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_ALREADY_REGISTERED' and version = 1));