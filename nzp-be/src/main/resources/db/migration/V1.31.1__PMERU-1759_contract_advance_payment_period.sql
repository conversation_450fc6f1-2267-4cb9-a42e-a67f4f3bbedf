-- Add "advance_payment_period" to contract
ALTER TABLE contract        ADD COLUMN advance_payment_period     CHARACTER VARYING(50);

-- Add advance payment types to GCL
INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES (uuid_generate_v4(), now(), now(), 1, '00', 'CONTRACT_ADVANCE_PAYMENT_PERIOD');

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Bez platby preddavkov', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '00'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Bez platby preddavkov', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '00'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES (uuid_generate_v4(), now(), now(), 1, '01', 'CONTRACT_ADVANCE_PAYMENT_PERIOD');

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Platba preddavkov mesačne', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '01'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Platba preddavkov mesačne', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '01'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES (uuid_generate_v4(), now(), now(), 1, '02', 'CONTRACT_ADVANCE_PAYMENT_PERIOD');

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Platba preddavkov raz za 2 mesiace', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '02'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Platba preddavkov raz za 2 mesiace', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '02'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES (uuid_generate_v4(), now(), now(), 1, '03', 'CONTRACT_ADVANCE_PAYMENT_PERIOD');

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Platba preddavkov štvrťročne', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '03'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Platba preddavkov štvrťročne', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '03'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES (uuid_generate_v4(), now(), now(), 1, '04', 'CONTRACT_ADVANCE_PAYMENT_PERIOD');

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Platba preddavkov raz za 4 mesiace', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '04'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Platba preddavkov raz za 4 mesiace', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '04'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES (uuid_generate_v4(), now(), now(), 1, '06', 'CONTRACT_ADVANCE_PAYMENT_PERIOD');

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Platba preddavkov polročne', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '06'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Platba preddavkov polročne', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '06'));


INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type)
VALUES (uuid_generate_v4(), now(), now(), 1, '12', 'CONTRACT_ADVANCE_PAYMENT_PERIOD');

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Platba preddavkov raz ročne', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '12'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Platba preddavkov raz ročne', (SELECT uuid FROM generic_code_list WHERE type = 'CONTRACT_ADVANCE_PAYMENT_PERIOD' AND code = '12'));