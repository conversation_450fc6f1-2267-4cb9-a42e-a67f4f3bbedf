------------------------------
-- consent_request
------------------------------

create table consent_request
(

    uuid                            uuid                        NOT NULL,
    created_at                      TIMESTAMP WITH TIME ZONE    NOT NULL,
    updated_at                      TIMESTAMP WITH TIME ZONE    NOT NULL,
    version                         INTEGER                     NOT NULL,

    session_id                      varchar(128),
    request_id                      varchar(128),
    remote_address                  varchar(60),
    action                          varchar(50)                 NOT NULL,
    type                            varchar(50)                 NOT NULL,
    content                         text,
    source                          varchar(50)                 NOT NULL,
    attributes                      text,
    registered_at                   timestamp with time zone,
    retry_count                     integer,
    locked_by                       varchar(128),
    external_id                     varchar(128),
    external_guid                   varchar(128),

    business_partner_id             text                      NOT NULL,
    customer_account_id             uuid                      NOT NULL,


    constraint pk_consent_request             primary key (uuid),
    constraint fk_consent_request_customer_account_id foreign key (customer_account_id)
        references customer_account (uuid) MATCH SIMPLE
        on update no ACTION
        on delete no ACTION
);

-- indexes
CREATE INDEX idx_consent_request_external_id on consent_request(external_id);