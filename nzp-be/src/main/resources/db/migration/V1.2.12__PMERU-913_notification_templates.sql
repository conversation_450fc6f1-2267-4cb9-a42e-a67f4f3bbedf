UPDATE notification_template
	SET version=(version + 1), priority = 'HIGH'
	WHERE code = 'CUSTOMER_DELETE_REQUEST';


UPDATE notification_template_i18n
	SET version=(version + 1), header = '<PERSON><PERSON><PERSON><PERSON>eni<PERSON> zákazníka pomocou telefónu'
	WHERE notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_LOGIN_PHONE');


ALTER TABLE customer_notification ALTER COLUMN header DROP NOT NULL;


UPDATE notification_template_i18n
	SET version=(version + 1), email_body = null, email_subject = null
	WHERE notification_template_id in
	(select uuid from notification_template where code in ('BUSINESS_PARTNER_PAIRING_CHALLENGE','CUSTOMER_PHONE_CHANGE_REQUEST', 'CUSTOMER_LOGIN_PHONE', 'CUSTOMER_INVITATION_PASSWORD'));


UPDATE notification_template_i18n
	SET version=(version + 1), sms_body = null
	WHERE notification_template_id in
	(select uuid from notification_template where code not in ('BUSINESS_PARTNER_PAIRING_CHALLENGE','CUSTOMER_PHONE_CHANGE_REQUEST', 'CUSTOMER_LOGIN_PHONE', 'CUSTOMER_INVITATION_PASSWORD'));