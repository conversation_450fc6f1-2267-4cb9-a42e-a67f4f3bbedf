-- confirmation link valid days for customer_request_template

alter table customer_request_template
add column confirmation_valid_days integer;

update customer_request_template
set confirmation_valid_days = 50
where code like 'ZOP_US';


-- confirmation code valid to for customer_request

alter table customer_request
add column confirmation_valid_to date;


-- add AVAYA email contacts for email requests

insert into config_parameter (created_at, updated_at, version, id, value, target, type) values
(now(), now(), 1, 'customer.request.confirmOnlineRequestEmail.home', '<EMAIL>', 'UI', 'STRING'),
(now(), now(), 1, 'customer.request.confirmOnlineRequestEmail.organisation', '<EMAIL>', 'UI', 'STRING');


-- notification about customer request confirmation

insert into notification_template (
    uuid,
    created_at,
    updated_at,
    version,
    code,
    status,
    type,
    execution_type,
    name,
    priority,
    description,
    attributes,
    template_group,
    default_email,
    default_sms,
    enable_email,
    enable_sms,
    enable_portal
) values (
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'CUSTOMER_REQUEST_CONFIRM_REQUEST',
    'ACTIVE',
    'CUSTOMER_SYSTEM',
    'AUTOMATIC',
    'Potvrdenie žiados<PERSON> z<PERSON>azníkom',
    'LOW',
    'Notifikácia o potvrdení žiadosti zákazníkom',
    null,
    null,
    true,
    false,
    true,
    false,
    false
);

-- common fields that are always present
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.email', 'Email zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.firstName', 'Meno zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.email', 'Email zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.firstName', 'Meno zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.lastName', 'Priezvisko zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.phone', 'Telefónne číslo zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationTemplateCode', 'Kód notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácie cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', 'Týka sa iba SAP entít.', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'locale', 'Lokalizácia', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');

-- target: BusinessPartner
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.name', 'Názov obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.firstName', 'Meno obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.lastName', 'Priezvisko obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.email', 'Email obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.phone', 'Telefonné číslo obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.street', 'Ulica obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.streetNumber', 'Číslo domu obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.city', 'Obec obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.zipCode', 'PSČ obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.country', 'Štát obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amFirstName', 'Obchodný partner - meno manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amLastName', 'Obchodný partner - priezvisko manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amEmail', 'Obchodný partner - email manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amPhone', 'Obchodný partner - telefónne číslo manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amName', 'Obchodný partner - meno pobočky', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.externalId', 'SAP ID obchodného partnera', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.kindCode', 'Kód druhu obchodného partnera', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');

-- target: CustomerRequest
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.externalId', 'SAP ID žiadosti', 'Dostupné iba ak bola žiadosť zaevidovaná v SAPe', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.name', 'Názov žiadosti', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.status.code', 'Kód stavu žiadosti', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.status.name', 'Stav žiadosti', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.registeredAt', 'Dátum registrácie v SAPe', 'Dátum odoslania žiadosti do SAPu', 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.externalId', 'SAP ID žiadosti', 'Dostupné iba ak bola žiadosť zaevidovaná v SAPe', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.confirmedAt', 'Dátum a čas potvrdenia žiadosti', 'Dostupné iba ak bola žiadosť potvrdená zákazníkom', 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');

insert into notification_template_variable select nt.uuid, now(), now(), 1, 'attributes.customerName', 'Meno zákazníka, ktorý uzavrel zmluvu', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REQUEST');

insert into notification_template_i18n (
    uuid,
    created_at,
    updated_at,
    version,
    header,
    email_body,
    email_subject,
    sms_body,
    notification_template_id,
    locale,
    status,
    header_url
)
values
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    null,
    '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">Žiadosť ${customerRequest.name} so SAP ID: ${customerRequest.externalId} uzavretá zákazníkom ${attributes.customerName} bola potvrdená dňa ${customerRequest.confirmedAt}.</@spp.notification_email_template>',
    '${customerRequest.externalId}/${customerRequest.status.code}/on-line podpis/${businessPartner.externalId}/${businessPartner.kindCode}',
    null,
    (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CONFIRM_REQUEST'),
    'SK',
    'ACTIVE',
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    null,
    '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">Žiadosť ${customerRequest.name} so SAP ID: ${customerRequest.externalId} uzavretá zákazníkom ${attributes.customerName} bola potvrdená dňa ${customerRequest.confirmedAt}.</@spp.notification_email_template>',
    '${customerRequest.externalId}/${customerRequest.status.code}/on-line podpis/${businessPartner.externalId}/${businessPartner.kindCode}',
    null,
    (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CONFIRM_REQUEST'),
    'EN',
    'ACTIVE',
    null
);


-- confirmation expired notification

alter table customer_request
add column notification_confirmation_expired_at timestamp with time zone;


-- confirmation reminder notification

alter table customer_request
add column notification_confirm_reminder_at timestamp with time zone;

-- notification about invalidation of online customer request contract

insert into notification_template (
    uuid,
    created_at,
    updated_at,
    version,
    code,
    status,
    type,
    execution_type,
    name,
    priority,
    description,
    attributes,
    template_group,
    default_email,
    default_sms,
    enable_email,
    enable_sms,
    enable_portal
) values (
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'CUSTOMER_REQUEST_EXPIRED_REQUEST',
    'ACTIVE',
    'CUSTOMER_SYSTEM',
    'AUTOMATIC',
    'Vypršanie platnosti online žiadosti',
    'LOW',
    'Notifikácia o vypršaní platnosti online žiadosti',
    null,
    null,
    true,
    false,
    true,
    false,
    false
);

-- common fields that are always present
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.email', 'Email zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.firstName', 'Meno zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.email', 'Email zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.firstName', 'Meno zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.lastName', 'Priezvisko zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.phone', 'Telefónne číslo zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationTemplateCode', 'Kód notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácie cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', 'Týka sa iba SAP entít.', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'locale', 'Lokalizácia', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');

-- target: BusinessPartner
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.name', 'Názov obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.firstName', 'Meno obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.lastName', 'Priezvisko obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.email', 'Email obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.phone', 'Telefonné číslo obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.street', 'Ulica obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.streetNumber', 'Číslo domu obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.city', 'Obec obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.zipCode', 'PSČ obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.country', 'Štát obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amFirstName', 'Obchodný partner - meno manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amLastName', 'Obchodný partner - priezvisko manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amEmail', 'Obchodný partner - email manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amPhone', 'Obchodný partner - telefónne číslo manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amName', 'Obchodný partner - meno pobočky', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.externalId', 'SAP ID obchodného partnera', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.kindCode', 'Kód druhu obchodného partnera', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');

-- target: CustomerRequest
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.externalId', 'SAP ID žiadosti', 'Dostupné iba ak bola žiadosť zaevidovaná v SAPe', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.name', 'Názov žiadosti', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.status.code', 'Kód stavu žiadosti', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.status.name', 'Stav žiadosti', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.registeredAt', 'Dátum registrácie v SAPe', 'Dátum odoslania žiadosti do SAPu', 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.externalId', 'SAP ID žiadosti', 'Dostupné iba ak bola žiadosť zaevidovaná v SAPe', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.confirmedAt', 'Dátum a čas potvrdenia žiadosti', 'Dostupné iba ak bola žiadosť potvrdená zákazníkom', 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_EXPIRED_REQUEST');

insert into notification_template_i18n (
    uuid,
    created_at,
    updated_at,
    version,
    header,
    email_body,
    email_subject,
    sms_body,
    notification_template_id,
    locale,
    status,
    header_url
)
values
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    null,
    '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">Platnosť ponuky pre žiadosť ${customerRequest.name} so SAP ID: ${customerRequest.externalId} skončila.</@spp.notification_email_template>',
    '${customerRequest.externalId}/${customerRequest.status.code}/platnosť ponuky skončila/${businessPartner.externalId}/${businessPartner.kindCode}',
    null,
    (select uuid from notification_template where code = 'CUSTOMER_REQUEST_EXPIRED_REQUEST'),
    'SK',
    'ACTIVE',
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    null,
    '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">Platnosť ponuky pre žiadosť ${customerRequest.name} so SAP ID: ${customerRequest.externalId} skončila.</@spp.notification_email_template>',
    '${customerRequest.externalId}/${customerRequest.status.code}/platnosť ponuky skončila/${businessPartner.externalId}/${businessPartner.kindCode}',
    null,
    (select uuid from notification_template where code = 'CUSTOMER_REQUEST_EXPIRED_REQUEST'),
    'EN',
    'ACTIVE',
    null
);


-- notification about invalidation of online customer request contract

insert into notification_template (
    uuid,
    created_at,
    updated_at,
    version,
    code,
    status,
    type,
    execution_type,
    name,
    priority,
    description,
    attributes,
    template_group,
    default_email,
    default_sms,
    enable_email,
    enable_sms,
    enable_portal
) values (
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'CUSTOMER_REQUEST_CONFIRM_REMINDER',
    'ACTIVE',
    'CUSTOMER_SYSTEM',
    'AUTOMATIC',
    'Pripomienka potvrdenia žiadosti',
    'LOW',
    'Notifikácia o pripomenutí potvrdenia zákazníckej žiadosti',
    null,
    null,
    true,
    false,
    true,
    false,
    true
);

-- common fields that are always present
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.email', 'Email zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.firstName', 'Meno zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.email', 'Email zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.firstName', 'Meno zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.lastName', 'Priezvisko zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.phone', 'Telefónne číslo zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationTemplateCode', 'Kód notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácie cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', 'Týka sa iba SAP entít.', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'locale', 'Lokalizácia', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');

-- target: BusinessPartner
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.name', 'Názov obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.firstName', 'Meno obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.lastName', 'Priezvisko obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.email', 'Email obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.phone', 'Telefonné číslo obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.street', 'Ulica obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.streetNumber', 'Číslo domu obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.city', 'Obec obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.zipCode', 'PSČ obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.country', 'Štát obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amFirstName', 'Obchodný partner - meno manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amLastName', 'Obchodný partner - priezvisko manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amEmail', 'Obchodný partner - email manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amPhone', 'Obchodný partner - telefónne číslo manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amName', 'Obchodný partner - meno pobočky', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.externalId', 'SAP ID obchodného partnera', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.kindCode', 'Kód druhu obchodného partnera', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');

-- target: CustomerRequest
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.externalId', 'SAP ID žiadosti', 'Dostupné iba ak bola žiadosť zaevidovaná v SAPe', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.name', 'Názov žiadosti', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.status.code', 'Kód stavu žiadosti', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.status.name', 'Stav žiadosti', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.registeredAt', 'Dátum registrácie v SAPe', 'Dátum odoslania žiadosti do SAPu', 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.externalId', 'SAP ID žiadosti', 'Dostupné iba ak bola žiadosť zaevidovaná v SAPe', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.confirmedAt', 'Dátum a čas potvrdenia žiadosti', 'Dostupné iba ak bola žiadosť potvrdená zákazníkom', 'TIMESTAMP', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CONFIRM_REMINDER');

insert into notification_template_i18n (
    uuid,
    created_at,
    updated_at,
    version,
    header,
    email_body,
    email_subject,
    sms_body,
    notification_template_id,
    locale,
    status,
    header_url
)
values
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    null,
    '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">Sme radi, že Vás zaujal produkt ${customerRequest.name}. K uzavretiu zmluvy chýba už len krok. Uzavrite zmluvu online kliknutím na odkaz, ktorý ste dostali v e-maili od SPP. Je to jednoduché a rýchle.</@spp.notification_email_template>',
    'Dokončenie uzavretia zmluvy na produkt ${customerRequest.name}',
    null,
    (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CONFIRM_REMINDER'),
    'SK',
    'ACTIVE',
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    null,
    '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">Sme radi, že Vás zaujal produkt ${customerRequest.name}. K uzavretiu zmluvy chýba už len krok. Uzavrite zmluvu online kliknutím na odkaz, ktorý ste dostali v e-maili od SPP. Je to jednoduché a rýchle.</@spp.notification_email_template>',
    'Dokončenie uzavretia zmluvy na produkt ${customerRequest.name}',
    null,
    (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CONFIRM_REMINDER'),
    'EN',
    'ACTIVE',
    null
);

INSERT INTO notification_template_variable
SELECT nt.uuid, now(), now(), 1, 'attributes.requestConfirmationUrl', 'Url na podpis/potvrdenie žiadosti', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4()
FROM notification_template nt
WHERE nt.code = 'CUSTOMER_REQUEST_CONFIRM_REMINDER';

update  notification_template_i18n  set email_body = '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">Sme radi, že Vás zaujal produkt ${customerRequest.name}. K uzavretiu zmluvy chýba už len krok. Uzavrite zmluvu online kliknutím na odkaz ${attributes.requestConfirmationUrl}, ktorý ste dostali aj v e-maili od SPP. Je to jednoduché a rýchle.</@spp.notification_email_template>'
where notification_template_id =(select b.uuid from notification_template b where b.code = 'CUSTOMER_REQUEST_CONFIRM_REMINDER')

