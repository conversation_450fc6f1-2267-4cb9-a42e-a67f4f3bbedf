ALTER TABLE delivery_point              DROP COLUMN address_searchable;
ALTER TABLE united_delivery_point       ADD COLUMN address_searchable        CHARACTER VARYING(512);

UPDATE united_delivery_point            SET address_searchable =
		NULLIF(TRANSLATE(LOWER(TRIM(BOTH FROM REPLACE(REPLACE(REPLACE(CONCAT(
				street, ' ', street_number, ' ', city, ' ', zip_code, ' ', country), ' ', '<>'), '><', ''), '<>', ' '))),
						'áäčďéíĺľňóôŕšťúýž', 'aacdeillnoorstuyz'), '');