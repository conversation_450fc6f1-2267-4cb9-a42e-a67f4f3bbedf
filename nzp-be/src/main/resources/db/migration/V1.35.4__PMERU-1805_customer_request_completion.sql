-- ADD CUSTOMER REQUEST COMPLETION COLUMNS
ALTER TABLE customer_request    ADD COLUMN completion_customer_id               UUID;
ALTER TABLE customer_request    ADD COLUMN completion_customer_email            VARCHAR(128);
ALTER TABLE customer_request    ADD COLUMN completion_result                    VARCHAR(128);
ALTER TABLE customer_request    ADD COLUMN completion_result_at                 TIMESTAMP;
ALTER TABLE customer_request    ADD COLUMN completion_reject_reason             VARCHAR(128);
ALTER TABLE customer_request    ADD COLUMN completion_sent_at                   TIMESTAMP;
ALTER TABLE customer_request    ADD COLUMN completion_notification_reminder_at  TIMESTAMP;
ALTER TABLE customer_request    ADD COLUMN completion_code                      VARCHAR(128);
ALTER TABLE customer_request    ADD COLUMN completion_valid_to                  TIMESTAMP;

ALTER TABLE customer_request    ADD CONSTRAINT fk_customer_request_completion_customer_account
    FOREIGN KEY (completion_customer_id) REFERENCES customer_account (uuid);

CREATE INDEX idx_customer_request_completion_customer_id ON customer_request(completion_customer_id);
CREATE INDEX idx_customer_request_completion_customer_email ON customer_request(completion_customer_email);

-- ADD CUSTOMER REQUEST TEMPLATE COMPLETION COLUMNS
ALTER TABLE customer_request_template   ADD COLUMN completion_required      BOOLEAN     DEFAULT FALSE NOT NULL;
ALTER TABLE customer_request_template   ADD COLUMN completion_valid_days    INTEGER;

-- UPDATE & DEACTIVATE CURRENT TEMPLATE
UPDATE customer_request_template SET
    code = 'ZOM_P_PDF',
    status = 'INACTIVE',
    updated_at = now(),
    "version" = "version" + 1
WHERE code = 'ZOM_P';

-- CREATE NEW TEMPLATE
INSERT INTO customer_request_template(
	uuid, created_at, updated_at, version, status, code, price, type, link, confirmation_required, confirmation_valid_days)
VALUES (
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ZOM_P',
    null,
    'DIGITAL',
    null,
    false,
    null);

-- CREATE NEW TEMPLATE I18Ns
INSERT INTO customer_request_template_i18n (
    uuid, created_at, updated_at, version, locale, name, description, customer_request_template_uuid)
VALUES (
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'SK', (
    SELECT name FROM customer_request_template_i18n
    WHERE customer_request_template_uuid = (
        SELECT uuid FROM customer_request_template
        WHERE code = 'ZOM_P_PDF')
    AND LOWER(locale) = 'sk'), (
    SELECT description FROM customer_request_template_i18n
    WHERE customer_request_template_uuid = (
        SELECT uuid FROM customer_request_template
        WHERE code = 'ZOM_P_PDF')
    AND LOWER(locale) = 'sk'), (
    SELECT uuid FROM customer_request_template
    WHERE code = 'ZOM_P'));

INSERT INTO customer_request_template_i18n (
    uuid, created_at, updated_at, version, locale, name, description, customer_request_template_uuid)
VALUES (
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'EN', (
    SELECT name FROM customer_request_template_i18n
    WHERE customer_request_template_uuid = (
        SELECT uuid FROM customer_request_template
        WHERE code = 'ZOM_P_PDF')
    AND LOWER(locale) = 'en'), (
    SELECT description FROM customer_request_template_i18n
    WHERE customer_request_template_uuid = (
        SELECT uuid FROM customer_request_template
        WHERE code = 'ZOM_P_PDF')
    AND LOWER(locale) = 'en'), (
    SELECT uuid FROM customer_request_template crt
    WHERE crt.code = 'ZOM_P'));

-- UPDATE OLD TEMPLATE I18Ns
UPDATE customer_request_template_i18n SET
    "name" = CONCAT(name, ' (PDF)'),
    description = CONCAT(description, ' (PDF)'),
    updated_at = now(),
    "version" = "version" + 1
WHERE customer_request_template_uuid = (
    SELECT uuid FROM customer_request_template
    WHERE code = 'ZOM_P_PDF');