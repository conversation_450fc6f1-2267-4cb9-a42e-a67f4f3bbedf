-----------------------------------------------------------------------------------------------
---------------- ADD AuditLogCodes - login temporary blocked
-----------------------------------------------------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_LOGIN_TEMPORARY_BLOCKED_PHONE', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Prihlasovanie: Dočasné zablokovanie prihlásenia cez tel. číslo', 'Prihlasovanie: Dočasné zablokovanie prihlásenia cez tel. číslo', (select uuid from generic_code_list where code like 'CUSTOMER_LOGIN_TEMPORARY_BLOCKED_PHONE' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Prihlasovanie: Dočasné zablokovanie prihlásenia cez tel. číslo', '[EN] Prihlasovanie: Dočasné zablokovanie prihlásenia cez tel. číslo', (select uuid from generic_code_list where code like 'CUSTOMER_LOGIN_TEMPORARY_BLOCKED_PHONE' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_LOGIN_TEMPORARY_BLOCKED_EMAIL', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Prihlasovanie: Dočasné zablokovanie prihlásenia cez kombináciu email / heslo', 'Prihlasovanie: Dočasné zablokovanie prihlásenia cez kombináciu email / heslo', (select uuid from generic_code_list where code like 'CUSTOMER_LOGIN_TEMPORARY_BLOCKED_EMAIL' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Prihlasovanie: Dočasné zablokovanie prihlásenia cez kombináciu email / heslo', '[EN] Prihlasovanie: Dočasné zablokovanie prihlásenia cez kombináciu email / heslo', (select uuid from generic_code_list where code like 'CUSTOMER_LOGIN_TEMPORARY_BLOCKED_EMAIL' and type = 'AUDIT_LOG_CODE'));
