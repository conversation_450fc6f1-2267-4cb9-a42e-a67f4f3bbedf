insert into notification_template (
    uuid,
    created_at,
    updated_at,
    version,
    code,
    status,
    type,
    execution_type,
    name,
    priority,
    description,
    attributes,
    template_group,
    default_email,
    default_sms,
    enable_email,
    enable_sms,
    enable_portal
) values (
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'BUSINESS_PARTNER_CONSENT_EXPIRING',
    'ACTIVE',
    'CUSTOMER_SYSTEM',
    'AUTOMATIC',
    'Vyprša<PERSON> platnosti marketingových súhlasov',
    'LOW',
    'Notifikácia o vypršani platnosti marketingových súhlasov',
    null,
    null,
    true,
    false,
    true,
    false,
    false
);

insert into notification_template_i18n (
    uuid,
    created_at,
    updated_at,
    version,
    header,
    email_body,
    email_subject,
    sms_body,
    notification_template_id,
    locale,
    status,
    header_url
)
values
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    null,
    '<#import "spp.macros_sk.ftl" as spp>
     <@spp.notification_email_template oslovenie="">Consent to receive news from  <#if businessPartner.name??>${businessPartner.name}<#else>${businessPartner.firstName} ${businessPartner.lastName}</#if>  (${businessPartner.externalId}) will expire soon.</@spp.notification_email_template>',
    'Consent to receive news',
    null,
    (select uuid from notification_template where code = 'BUSINESS_PARTNER_CONSENT_EXPIRING'),
    'EN',
    'ACTIVE',
    null
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    null,
    '<#import "spp.macros_sk.ftl" as spp>
     <@spp.notification_email_template oslovenie="">Súhlas na odber noviniek od  <#if businessPartner.name??>${businessPartner.name}<#else>${businessPartner.firstName} ${businessPartner.lastName}</#if>  (${businessPartner.externalId}) čoskoro vyprší.</@spp.notification_email_template>',
    'Súhlas na odber noviniek',
    null,
    (select uuid from notification_template where code = 'BUSINESS_PARTNER_CONSENT_EXPIRING'),
    'SK',
    'ACTIVE',
    null
);

-- CREATE VARIABLES
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.email', 'Email zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.firstName', 'Meno zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'logged.customer.email', 'Email zalogovaného používateľa', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'logged.customer.firstName', 'Meno zalogovaného používateľa', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'logged.customer.lastName', 'Priezvisko zalogovaného používateľa', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'logged.customer.phone', 'Telefónne číslo zalogovaného používateľa', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'notificationTemplateCode', 'Kód notifikácie', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácie cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', 'Týka sa iba SAP entít.', 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'locale', 'Lokalizácia', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');

INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.name', 'Názov obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.firstName', 'Meno obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.lastName', 'Priezvisko obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.email', 'Email obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.phone', 'Telefonné číslo obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.street', 'Ulica obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.streetNumber', 'Číslo domu obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.city', 'Obec obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.zipCode', 'PSČ obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.country', 'Štát obchodného partnera', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amFirstName', 'Obchodný partner - meno manažéra predaja', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amLastName', 'Obchodný partner - priezvisko manažéra predaja', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amEmail', 'Obchodný partner - email manažéra predaja', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amPhone', 'Obchodný partner - telefónne číslo manažéra predaja', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.amName', 'Obchodný partner - meno pobočky', NULL, 'STRING', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.externalId', 'SAP ID obchodného partnera', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');
INSERT INTO notification_template_variable SELECT nt.uuid, now(), now(), 1, 'businessPartner.kindCode', 'Kód druhu obchodného partnera', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() FROM notification_template nt WHERE nt.code IN ('BUSINESS_PARTNER_CONSENT_EXPIRING');