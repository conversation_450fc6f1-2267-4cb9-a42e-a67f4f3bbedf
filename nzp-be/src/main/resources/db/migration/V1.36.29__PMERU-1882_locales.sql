CREATE TABLE locale_entry (
    uuid        UUID                        NOT NULL,

    created_at  TIMESTAMP WITH TIME ZONE    NOT NULL,
    updated_at  TIMESTAMP WITH TIME ZONE    NOT NULL,
    version     INTEGER                     NOT NULL,

    locale      CHARACTER VARYING(10)       NOT NULL,
    name        text,
    parent_uuid uuid,
    content     text,

    CONSTRAINT pk_locale_entry  PRIMARY KEY (uuid),
    CONSTRAINT fk_parent_uuid   FOREIGN KEY (parent_uuid)   REFERENCES locale_entry (uuid)
);

CREATE TABLE locale_history_entry (
    uuid                                UUID                        NOT NULL,

    created_at                          TIMESTAMP WITH TIME ZONE    NOT NULL,

    locale                              CHARACTER VARYING(10)       NOT NULL,
    affected_entry_uuid                 uuid                        NOT NULL,
    logged_in_customer_account_uuid     uuid,
    changed_content_key                 text                        NOT NULL,
    previous_value                      text,

    CONSTRAINT pk_locale_history_entry              PRIMARY KEY (uuid),
    CONSTRAINT fk_affected_entry_uuid               FOREIGN KEY (affected_entry_uuid)               REFERENCES locale_entry (uuid),
    CONSTRAINT fk_logged_in_customer_account_uuid   FOREIGN KEY (logged_in_customer_account_uuid)   REFERENCES customer_account (uuid)
);

INSERT INTO access_group (uuid, created_at , updated_at ,"version", code, name, description)
VALUES (uuid_generate_v4(), now(), now(), 1, 'NZP_LOCALE_MANAGEMENT', 'Správa textácie NZP', '');

INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue)
VALUES ('LOCALE_FULL_IMPORT', now(), now(), 1, 'Inicializovanie textácie', 'Právo kompletne vymazať aktuálnu textáciu v rátane jej histórie a nahratiť ju inou', false, false);
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue)
VALUES ('LOCALE_HISTORY_IMPORT', now(), now(), 1, 'Menežovanie histórie zmien textácie', 'Právo importovať a exportovať históriu textácie', false, false);
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue)
VALUES ('LOCALE_EDIT', now(), now(), 1, 'Úprava textácie', 'Právo upravovať textáciu', false, false);

INSERT INTO access_group_right (created_at, access_group_uuid, access_right_code, operation, queue)
VALUES (now(), (SELECT uuid FROM access_group WHERE code = 'NZP_LOCALE_MANAGEMENT'), 'LOCALE_FULL_IMPORT', 'GRANT', NULL);
INSERT INTO access_group_right (created_at, access_group_uuid, access_right_code, operation, queue)
VALUES (now(), (SELECT uuid FROM access_group WHERE code = 'NZP_LOCALE_MANAGEMENT'), 'LOCALE_HISTORY_IMPORT', 'GRANT', NULL);
INSERT INTO access_group_right (created_at, access_group_uuid, access_right_code, operation, queue)
VALUES (now(), (SELECT uuid FROM access_group WHERE code = 'NZP_LOCALE_MANAGEMENT'), 'LOCALE_EDIT', 'GRANT', NULL);