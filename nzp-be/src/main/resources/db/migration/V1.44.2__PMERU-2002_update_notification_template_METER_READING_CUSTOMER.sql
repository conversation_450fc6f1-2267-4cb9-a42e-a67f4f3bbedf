-- PMERU-2002 - update notification template METER_READING_CUSTOMER

UPDATE notification_template_i18n SET updated_at=now(), version=version + 1, header = '<p>Zadajte samoodpočet spotreby na odbernom mieste typu ${deliveryPoint.typeName.name}</p>', email_body = '<style type="text/css">p.p1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 13.0px ''Helvetica Neue''}p.p2 {margin: 0.0px 0.0px 0.0px 0.0px; font: 13.0px ''Helvetica Neue''; min-height: 15.0px}li.li1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 13.0px ''Helvetica Neue''}span.s1 {text-decoration: underline}ol.ol1 {list-style-type: decimal}</style><p class="p1"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;"><#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""></span></p><p class="p2"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;"><br></span></p><p class="p1"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;">v týchto dňoch máte možnosť nahlásiť samoodpočet spotreby na odbernom mieste typu ${deliveryPoint.typeName.name} s adresou ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city}.</span></p><p class="p2"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;"><br></span></p><p class="p1"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;"><#if deliveryPoint.tariffRate=="D1"></span></p><p class="p1"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;">Zadajte v&nbsp;portáli údaj odčítaný z&nbsp;meradla a&nbsp;my ho zašleme distribútorovi.</span></p><p class="p1"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;"><#else></span></p><p class="p1"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;">V zmysle platnej legislatívy je stanovený postup pre realizáciu odpočtu nasledovne:</span></p><p class="p1"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;">Zadajte v&nbsp;portáli údaj odčítaný z&nbsp;meradla a&nbsp;my ho zašleme distribútorovi.</span></p><ol class="ol1"><li class="li1"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;">fyzicky prevádzkovateľom distribučnej siete alebo ním poverenou osobou</span></li><li class="li1"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;">samoodpočtom</span></li><li class="li1"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;">odhadom stavu meracieho zariadenia prevádzkovateľom distribučnej siete</span></li></ol><p class="p1"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;">V každom prípade je však Vašou povinnosťou sprístupniť meradlo odpočtárovi na odčítanie jeho stavu.</span></p><p class="p1"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;"></#if></span></p><p class="p1"><span class="s1"><a href="${portalExternalUrl}/delivery-points/${unitedDeliveryPoint.id}/self-read" style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;">Prejsť na zadanie samoodpočtu</a></span></p><p class="p1"><span style="font-size: 16px; font-family: Arial, Helvetica, sans-serif;"></@spp.notification_email_template></span></p>', email_subject = 'Certifikované meranie', push_title = 'Možnosť vykonať samoodpočet', push_text = e'<#if deliveryPoint.tariffRate="D1">
Zadajte v portáli údaj odčítaný z meradla a my ho zašleme distribútorovi.
<#else>
V zmysle platnej legislatívy je stanovený postup pre realizáciu odpočtu nasledovne:<br>
<ul>
<li>fyzicky prevádzkovateľom distribučnej siete alebo ním poverenou osobou</li>
<li>samoodpočtom</li>
<li>odhadom stavu meracieho zariadenia prevádzkovateľom distribučnej siete</li>
</ul>
</#if>
V každom prípade je však Vašou povinnosťou sprístupniť meradlo odpočtárovi na odčítanie jeho stavu.', push_body = 'Odčítajte hodnoty z meradla', push_redirection = '/delivery-points/${unitedDeliveryPoint.id}/self-read'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'METER_READING_CUSTOMER') AND upper(locale)='SK';

UPDATE notification_template_i18n SET updated_at=now(), version=version + 1, header = 'Certifikované meranie', email_body = '<p class="p1"><span style="font-family: Arial, Helvetica, sans-serif; font-size: 16px;"><#import "spp.macros_sk.ftl" as spp><@spp.notification_email_template oslovenie=""></span></p><p class="p1"><span style="font-family: Arial, Helvetica, sans-serif; font-size: 16px;"><span lang="EN-GB" style="line-height: 107%; font-family: Arial, Helvetica, sans-serif; font-size: 16px;">These days you can report the self meter reading ofthe consumption at the point of consumption located at</span> ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city}.</span></p><p class="p1"><span style="font-family: Arial, Helvetica, sans-serif; font-size: 16px;"><#if deliveryPoint.tariffRate=="D1"></span></p><p class="MsoNormal" style="margin: 0in; font-family: Calibri, sans-serif; line-height: normal; font-size: 15px;"><span lang="EN-GB" style="font-family: Arial, Helvetica, sans-serif; font-size: 16px;">Enter the meter reading in the portal and wewill send it to the distributor.</span></p><p class="p1"><span style="font-family: Arial, Helvetica, sans-serif; font-size: 16px;"><#else></span></p><p class="MsoNormal" style="margin: 0in; font-family: Calibri, sans-serif; line-height: normal; font-size: 15px;"><span lang="EN-GB" style="font-family: Arial, Helvetica, sans-serif; font-size: 16px;">According to the applicable legislation, thefollowing reading methods have been defined:</span></p><p class="MsoNormal" style="margin: 0in; font-family: Arial, Helvetica, sans-serif; line-height: normal; font-size: 16px;"><span lang="EN-GB" style="font-family: &quot;Times New Roman&quot;, serif;">&nbsp;</span><br></p><ol style="margin-bottom: 0in; margin-top: 0in;" start="1" type="1"> <li class="MsoNormal" style="margin: 0in; font-family: Calibri, sans-serif; line-height: normal; font-size: 15px;"><span lang="EN-GB" style="font-family: Arial, Helvetica, sans-serif; font-size: 16px;">physically by a     distribution system operator or a person authorised by it</span></li> <li class="MsoNormal" style="margin: 0in; font-family: Calibri, sans-serif; line-height: normal; font-size: 15px;"><span lang="EN-GB" style="font-family: Arial, Helvetica, sans-serif; font-size: 16px;">by self meter     reading</span></li> <li class="MsoNormal" style="margin: 0in; font-family: Calibri, sans-serif; line-height: normal; font-size: 15px;"><span lang="EN-GB" style="font-family: Arial, Helvetica, sans-serif; font-size: 16px;">by estimating the     meter reading by a distribution system operator </span></li></ol><p class="MsoNormal" style="margin: 0in; font-family: Arial, Helvetica, sans-serif; line-height: normal; font-size: 16px;"><span lang="EN-GB" style="font-family: &quot;Times New Roman&quot;, serif;">&nbsp;</span><br></p><p><span lang="EN-GB" style="line-height: 107%; font-family: Arial, Helvetica, sans-serif; font-size: 16px;">Inany case, it is your responsibility to make the meter available to a meterreader for reading.</span></p><p class="p1"><span style="font-family: Arial, Helvetica, sans-serif; font-size: 16px;"></#if></span></p><p class="p1"><span class="s1"><a href="${portalExternalUrl}/delivery-points/${unitedDeliveryPoint.id}/self-read" style="font-family: Arial, Helvetica, sans-serif; font-size: 16px;">Proceed to enter the self meter reading</a></span></p><p class="p1"><span style="font-family: Arial, Helvetica, sans-serif; font-size: 16px;"></@spp.notification_email_template></span></p>', email_subject = '[EN] Self meter reading', push_title = 'Self-reading option', push_text = e'<#if deliveryPoint.tariffRate=="D1">
Enter the meter reading in the portal and we will send it to the distributor. <#else>
According to the applicable legislation, the following reading methods have been defined: <br>
<ul>
<li>physically by a distribution system operator or a person authorised by it</li>
<li>by self meter reading</li>
<li>by estimating the meter reading by a distribution system operator</li>
</ul>
 In any case, it is your responsibility to make the meter available to a meterreader for reading. </#if>', push_body = 'Read the meter', push_redirection = '/delivery-points/${unitedDeliveryPoint.id}/self-read'
WHERE notification_template_id=(select uuid from notification_template nt where nt.code = 'METER_READING_CUSTOMER') AND upper(locale)='EN';

