UPDATE notification_template_i18n SET email_body = '<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>v SPP robíme všetko preto, aby ste Vaše potreby mohli riešiť jednoducho a rýchlo.</p><p>Dôkazom našej snahy je aj zákaznícky portál Moje SPP.<br><PERSON>rináša Vám možnosť jednoducho zadať svoje požiadavky. Ďalej v ňom nájdete:</p><ul>    <li>prehľad informácií o svojich odberných miestach,</li>    <li>zobrazenie svojich faktúr,</li>    <li>históriu platieb a spotreby.</li></ul><p>Objavte portál Moje SPP jediným klikom a ušetrite čas dnes aj nabudúce.</p><p><a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName}&amp;lastName=${customer.lastName}&amp;type=pre_registration">Objaviť Moje SPP teraz</a></p><p></@spp.notification_email_template></p>'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_BATCH_REQUEST_REGISTRATION') and locale = 'SK';

UPDATE notification_template_i18n SET email_subject = 'Objavte jedným klikom nový zákaznícky portál', email_body = '<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>v SPP robíme všetko preto, aby ste Vaše potreby mohli riešiť jednoducho a rýchlo.</p><p>Dôkazom našej snahy je aj zákaznícky portál Moje SPP.<br>Prináša Vám možnosť jednoducho zadať svoje požiadavky. Ďalej v ňom nájdete:</p><ul>    <li>prehľad informácií o svojich odberných miestach,</li>    <li>zobrazenie svojich faktúr,</li>    <li>históriu platieb a spotreby.</li></ul><p>Objavte portál Moje SPP jediným klikom a ušetrite čas dnes aj nabudúce.</p><p><a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName}&amp;lastName=${customer.lastName}&amp;type=pre_registration">Objaviť Moje SPP teraz</a></p><p></@spp.notification_email_template></p>'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_BATCH_REQUEST_REGISTRATION') and locale = 'EN';

UPDATE notification_template_i18n SET email_body = '<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>radi by sme Vám pripomenuli, že objaviť nový zákaznícky portál Moje SPP je teraz oveľa jednoduchšie.</p><p>Majte prehľad o Vašich službách z pohodlia Vášho domova - stačí jeden klik ...&nbsp;<a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName}&amp;lastName=${customer.lastName}&amp;type=pre_registration">Objaviť Moje SPP teraz</a></p><p></@spp.notification_email_template></p>'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_BATCH_INVITATION_REMINDER') and locale = 'SK';

UPDATE notification_template_i18n SET email_subject = 'Objavte jedným klikom nový zákaznícky portál', email_body = '<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>radi by sme Vám pripomenuli, že objaviť nový zákaznícky portál Moje SPP je teraz oveľa jednoduchšie.</p><p>Majte prehľad o Vašich službách z pohodlia Vášho domova - stačí jeden klik ...&nbsp;<a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName}&amp;lastName=${customer.lastName}&amp;type=pre_registration">Objaviť Moje SPP teraz</a></p><p></@spp.notification_email_template></p>'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_BATCH_INVITATION_REMINDER') and locale = 'EN';

UPDATE notification_template_i18n SET email_body = '<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>v SPP robíme všetko preto, aby ste Vaše potreby mohli riešiť jednoducho a rýchlo.</p><p>Dôkazom našej snahy je aj zákaznícky portál Moje SPP.<br>Prináša Vám možnosť jednoducho zadať svoje požiadavky. Ďalej v ňom nájdete:</p><ul>    <li>prehľad informácií o svojich odberných miestach,</li>    <li>zobrazenie svojich faktúr,</li>    <li>históriu platieb a spotreby.</li></ul><p>Objavte portál Moje SPP jediným klikom a ušetrite čas dnes aj nabudúce.</p><p><a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName}&amp;lastName=${customer.lastName}&amp;type=pre_registration">Objaviť Moje SPP teraz</a></p><p></@spp.notification_email_template></p>'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_SAP_REGISTRATION_REQUEST') and locale = 'SK';

UPDATE notification_template_i18n SET email_subject = 'Objavte jedným klikom nový zákaznícky portál', email_body = '<p><#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"></p><p>v SPP robíme všetko preto, aby ste Vaše potreby mohli riešiť jednoducho a rýchlo.</p><p>Dôkazom našej snahy je aj zákaznícky portál Moje SPP.<br>Prináša Vám možnosť jednoducho zadať svoje požiadavky. Ďalej v ňom nájdete:</p><ul>    <li>prehľad informácií o svojich odberných miestach,</li>    <li>zobrazenie svojich faktúr,</li>    <li>históriu platieb a spotreby.</li></ul><p>Objavte portál Moje SPP jediným klikom a ušetrite čas dnes aj nabudúce.</p><p><a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName}&amp;lastName=${customer.lastName}&amp;type=pre_registration">Objaviť Moje SPP teraz</a></p><p></@spp.notification_email_template></p>'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_SAP_REGISTRATION_REQUEST') and locale = 'EN';
