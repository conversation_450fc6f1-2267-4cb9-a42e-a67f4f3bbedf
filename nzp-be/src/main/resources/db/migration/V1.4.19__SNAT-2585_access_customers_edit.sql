

INSERT INTO access_group (uuid, code, created_at , updated_at ,"version", name, description) 
values (uuid_generate_v4(), 'NZP_CUSTOMERS_EDIT', current_timestamp, current_timestamp, 1, 'Moznost editovat data portaloveho uctu', '') ;

INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) 
values ('CUSTOMERS_EDIT', current_timestamp, current_timestamp, 1, 'Možnosť editovať dáta portálového účtu', '', false, false) ;


INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) 
values ('CUSTOMERS_EDIT', (select uuid from access_group where code = 'NZP_CUSTOMERS_EDIT'), current_timestamp, 'GRANT', NULL);
