-- it's neccessary remove all
delete from united_delivery_point_ownership;
delete from customer_notification_setting;
delete from united_delivery_point;

-- alter synchronization
alter table synchronization_log add column type character varying(50);

-- update value in existing
update synchronization_log set type = 'FILE_IMPORT' where type is null;

-- add not null contraints
alter table synchronization_log alter column type set not null;
alter table synchronization_log alter column file_name drop not null;

--
alter table contract add column invalidated boolean;
alter table delivery_point add column address_key character varying(50);
alter table united_delivery_point drop column udp_key;
alter table united_delivery_point add column address_key character varying(50);

-- indexes
create index idx_delivery_point_address_key on delivery_point(address_key);
create unique index idx_united_delivery_point_address_key_bpid on united_delivery_point(address_key, business_partner_id);
