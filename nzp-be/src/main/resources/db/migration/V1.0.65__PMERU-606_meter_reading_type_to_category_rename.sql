ALTER TABLE meter_reading
RENAME COLUMN type TO category;

alter table meter_reading alter column value_low type numeric(19, 3);
alter table meter_reading alter column value_high type numeric(19, 3);

alter table meter_reading add column gcl_type_uuid                      uuid;
alter table meter_reading add column synchronization_log_uuid           uuid;
alter table meter_reading add column synchronization_at                 timestamp with time zone;

alter table meter_reading
ADD CONSTRAINT fk_meter_reading_gcl_type_uuid FOREIGN KEY (gcl_type_uuid)
        REFERENCES generic_code_list (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION;
