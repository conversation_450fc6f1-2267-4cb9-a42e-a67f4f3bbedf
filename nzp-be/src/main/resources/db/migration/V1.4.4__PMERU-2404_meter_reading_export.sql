delete from generic_code_list_i18n where code_list_uuid in (select uuid from generic_code_list gcl where "type" = 'METER_READING_EXPORT_COLUMN');
delete from generic_code_list gcl where "type" = 'METER_READING_EXPORT_COLUMN';


INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'ID', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Číslo odpočtu', 'Č<PERSON>lo odpočtu', (select uuid from generic_code_list where code like 'ID' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Číslo odpočtu', '[EN] Číslo odpočtu', (select uuid from generic_code_list where code like 'ID' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DEVICE_NUMBER', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Číslo merača', 'Číslo merača', (select uuid from generic_code_list where code like 'DEVICE_NUMBER' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Číslo merača', '[EN] Číslo merača', (select uuid from generic_code_list where code like 'DEVICE_NUMBER' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REGISTER', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Číslo registra', 'Číslo registra', (select uuid from generic_code_list where code like 'REGISTER' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Číslo registra', '[EN] Číslo registra', (select uuid from generic_code_list where code like 'REGISTER' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REGISTER_KIND_CODE', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Druh registra (kód)', 'Druh registra (kód)', (select uuid from generic_code_list where code like 'REGISTER_KIND_CODE' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Druh registra (kód)', '[EN] Druh registra (kód)', (select uuid from generic_code_list where code like 'REGISTER_KIND_CODE' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REGISTER_KIND_NAME', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Druh registra', 'Druh registra', (select uuid from generic_code_list where code like 'REGISTER_KIND_NAME' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Druh registra', '[EN] Druh registra', (select uuid from generic_code_list where code like 'REGISTER_KIND_NAME' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REASON_CODE', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dôvod odpočtu (kód)', 'Dôvod odpočtu (kód)', (select uuid from generic_code_list where code like 'REASON_CODE' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Dôvod odpočtu (kód)', '[EN] Dôvod odpočtu (kód)', (select uuid from generic_code_list where code like 'REASON_CODE' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'REASON_NAME', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dôvod odpočtu', 'Dôvod odpočtu', (select uuid from generic_code_list where code like 'REASON_NAME' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Dôvod odpočtu', '[EN] Dôvod odpočtu', (select uuid from generic_code_list where code like 'REASON_NAME' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'KIND_CODE', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Druh odpočtu (kód)', 'Druh odpočtu (kód)', (select uuid from generic_code_list where code like 'KIND_CODE' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Druh odpočtu (kód)', '[EN] Druh odpočtu (kód)', (select uuid from generic_code_list where code like 'KIND_CODE' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'KIND_NAME', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Druh odpočtu', 'Druh odpočtu', (select uuid from generic_code_list where code like 'KIND_NAME' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Druh odpočtu', '[EN] Druh odpočtu', (select uuid from generic_code_list where code like 'KIND_NAME' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'READ_AT', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dátum odpočtu', 'Dátum odpočtu', (select uuid from generic_code_list where code like 'READ_AT' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Dátum odpočtu', '[EN] Dátum odpočtu', (select uuid from generic_code_list where code like 'READ_AT' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'VALUE', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Hodnota odpočtu', 'Hodnota odpočtu', (select uuid from generic_code_list where code like 'VALUE' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Hodnota odpočtu', '[EN] Hodnota odpočtu', (select uuid from generic_code_list where code like 'VALUE' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'VALUE_HIGH', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Hodnota odpočtu (vysoká)', 'Hodnota odpočtu (vysoká)', (select uuid from generic_code_list where code like 'VALUE_HIGH' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Hodnota odpočtu (vysoká)', '[EN] Hodnota odpočtu (vysoká)', (select uuid from generic_code_list where code like 'VALUE_HIGH' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'UNITS', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Jednotky', 'Jednotky', (select uuid from generic_code_list where code like 'UNITS' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Jednotky', '[EN] Jednotky', (select uuid from generic_code_list where code like 'UNITS' and type = 'METER_READING_EXPORT_COLUMN'));



INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CATEGORY', 'METER_READING_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Kategória', 'Kategória', (select uuid from generic_code_list where code like 'CATEGORY' and type = 'METER_READING_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Kategória', '[EN] Kategória', (select uuid from generic_code_list where code like 'CATEGORY' and type = 'METER_READING_EXPORT_COLUMN'));

