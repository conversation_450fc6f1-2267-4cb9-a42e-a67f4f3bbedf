INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'VALUE', 'CONSUMPTION_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'DATE', 'CONSUMPTION_EXPORT_COLUMN', null, null, null);

INSERT INTO generic_code_list (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'TIME_FROM', 'CONSUMPTION_EXPORT_COLUMN', null, null, null);


INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Hodnota', 'Hodnota', (select uuid from generic_code_list where code like 'VALUE' and type = 'CONSUMPTION_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Hodnota', '[EN] Hodnota', (select uuid from generic_code_list where code like 'VALUE' and type = 'CONSUMPTION_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Dátum', 'Dátum', (select uuid from generic_code_list where code like 'DATE' and type = 'CONSUMPTION_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Dátum', '[EN] Dátum', (select uuid from generic_code_list where code like 'DATE' and type = 'CONSUMPTION_EXPORT_COLUMN'));

INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'SK', 'Čas od', 'Čas od', (select uuid from generic_code_list where code like 'TIME_FROM' and type = 'CONSUMPTION_EXPORT_COLUMN'));
INSERT INTO generic_code_list_i18n (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES (uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Čas od', '[EN] Čas od', (select uuid from generic_code_list where code like 'TIME_FROM' and type = 'CONSUMPTION_EXPORT_COLUMN'));

