INSERT INTO
    data_model (uuid, created_at, valid_from, model)
VALUES (
           uuid_generate_v4(),
           now(),
           now(),
           '{
       "version": "17",
       "dataEntities": [
           {
               "name": "AuditLog",
               "tableName": "audit_log",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "code",
                        "column": "code",
                        "type": "ENUM",
                        "options": [
                            "CUSTOMER_LOGIN",
                            "CUSTOMER_LOGIN_UNSUCCESS",
                            "CUSTOMER_LOGIN_INVALID_TOKEN",
                            "CUSTOMER_LOGIN_TEMPORARY_BLOCKED",
                            "CUSTOMER_LOGIN_IMPERSONIFICATION",
                            "CUSTOMER_LOGOUT_IMPERSONIFICATION",
                            "CUSTOMER_LOGOUT",
                            "CUSTOMER_LOGOUT_TIMEOUT",
                            "CUSTOMER_PASS_VERIFICATION",
                            "EMPLOYEE_PASS_VERIFICATION",
                            "EMPLOYEE_LOGIN",
                            "EMPLOYEE_LOGIN_UNSUCCESS",
                            "EMPLOYEE_LOGIN_TEMPORARY_BLOCKED",
                            "EMPLOYEE_LOGOUT",
                            "EMPLOYEE_LOGOUT_TIMEOUT",
                            "CUSTOMER_LOCK",
                            "CUSTOMER_REGISTRATION_REQUEST",
                            "CUSTOMER_REGISTRATION_ALREADY_REGISTERED",
                            "CUSTOMER_RESEND_REGISTRATION_REQUEST",
                            "CUSTOMER_REGISTRATION_SUCCESS",
                            "CUSTOMER_PASSWORD_RECOVERY_REQUEST",
                            "CUSTOMER_PASSWORD_RECOVERY_SUCCESS",
                            "CUSTOMER_PASSWORD_CHANGE_SUCCESS",
                            "CUSTOMER_SHARING_OWNER_GRANT",
                            "CUSTOMER_SHARING_CONSUMER_GRANT",
                            "CUSTOMER_SHARING_OWNER_REVOKE",
                            "CUSTOMER_SHARING_CONSUMER_REVOKE",
                            "CUSTOMER_DELETE_REQUEST",
                            "CUSTOMER_RESEND_DELETE_REQUEST",
                            "CUSTOMER_DELETE_SUCCESS",
                            "CUSTOMER_MARKETING_APPROVAL_APPROVED",
                            "CUSTOMER_MARKETING_APPROVAL_DENIED",
                            "BUSINESS_PARTNER_PAIRING_APPROVAL_APPROVED",
                            "BUSINESS_PARTNER_PAIRING_APPROVAL_DENIED",
                            "BUSINESS_PARTNER_PAIRING_VERIFY",
                            "BUSINESS_PARTNER_PAIRING_CHALLENGE",
                            "BUSINESS_PARTNER_PAIRING_CHALLENGE_RESEND",
                            "BUSINESS_PARTNER_PAIRING_SUCCESS",
                            "BUSINESS_PARTNER_PAIRING_REQUEST",
                            "BUSINESS_PARTNER_UNPAIRING_SUCCESS",
                            "BUSINESS_PARTNER_UNPAIRING_REQUEST",
                            "CUSTOMER_PHONE_CHANGE_REQUEST",
                            "CUSTOMER_PHONE_CHANGE_REQUEST_RESEND",
                            "CUSTOMER_PHONE_CHANGE_SUCCESS",
                            "CUSTOMER_EMAIL_CHANGE_REQUEST",
                            "CUSTOMER_RESEND_EMAIL_CHANGE_REQUEST",
                            "CUSTOMER_EMAIL_CHANGE_SUCCESS",
                            "CUSTOMER_ACTIVATION_SUCCESS",
                            "CUSTOMER_DEACTIVATION_SUCCESS",
                            "CUSTOMER_REQUEST_CREATE",
                            "CUSTOMER_PUBLIC_REQUEST_CREATE",
                            "CUSTOMER_REQUEST_STATUS_CHANGE",
                            "CUSTOMER_REQUEST_CONFIRMED",
                            "CUSTOMER_REQUEST_CANCELLED",
                            "CUSTOMER_NAME_CHANGE",
                            "EMPLOYEE_REGISTRATION_REQUEST",
                            "NOTIFICATION_TEMPLATE_CHANGE",
                            "NOTIFICATION_TEMPLATE_CREATE",
                            "CUSTOMER_INVITATION",
                            "EMPLOYEE_REPORT_RUN",
                            "ACCESS_GROUP_ADD",
                            "ACCESS_GROUP_UPDATE",
                            "ACCESS_GROUP_REMOVE",
                            "CUSTOMER_SHARING_INVITATION",
                            "COMPONENT_HELP_CREATE",
                            "COMPONENT_HELP_CHANGE",
                            "CUSTOMER_NOTIFICATION_SETTINGS_CHANGE",
                            "REPORT_CREATE",
                            "REPORT_CHANGE",
                            "SERVICE_CALL_REGISTER_USER",
                            "SERVICE_CALL_PAIRING",
                            "EXTERNAL_CALL_UNPAIRING",
                            "SERVICE_CALL_CUSTOMER_REQUEST",
                            "EPAY_TRANSACTION_CREATE",
                            "EPAY_TRANSACTION_STATUS_CHANGE",
                            "PHONE_CHALLENGE_CODE_VALID",
                            "PHONE_CHALLENGE_CODE_INVALID",
                            "EMAIL_CHALLENGE_CODE_VALID",
                            "EMAIL_CHALLENGE_CODE_INVALID",
                            "DELETE_CUSTOMER_CHALLENGE_CODE_VALID",
                            "DELETE_CUSTOMER_CHALLENGE_CODE_INVALID",
                            "REGISTRATION_CHALLENGE_CODE_VALID",
                            "REGISTRATION_CHALLENGE_CODE_INVALID",
                            "PASSWORD_RECOVERY_CHALLENGE_CODE_VALID",
                            "PASSWORD_RECOVERY_CHALLENGE_CODE_INVALID",
                            "BUSINESS_PARTNER_PAIRING_CHALLENGE_CODE_VALID",
                            "BUSINESS_PARTNER_PAIRING_CHALLENGE_CODE_INVALID",
                            "CHANGE_PASSWORD_PASSWORD_INVALID",
                            "CHANGE_PASSWORD_PASSWORD_VALID",
                            "IDM_AUTHENTICATE_PASSWORD_INVALID",
                            "IDM_AUTHENTICATE_PASSWORD_VALID",
                            "PHONE_CHANGE_PASSWORD_INVALID",
                            "PHONE_CHANGE_PASSWORD_VALID",
                            "EMAIL_CHANGE_PASSWORD_INVALID",
                            "EMAIL_CHANGE_PASSWORD_VALID",
                            "CUSTOMER_NOTFOUND_ID_LOGIN_ATTEMPT",
                            "CUSTOMER_NOTFOUND_EMAIL_LOGIN_ATTEMPT",
                            "CUSTOMER_NOTFOUND_PHONE_LOGIN_ATTEMPT",
                            "CUSTOMER_NOTFOUND_FACEBOOK_LOGIN_ATTEMPT",
                            "CUSTOMER_NOTFOUND_GOOGLE_LOGIN_ATTEMPT",
                            "EMPLOYEE_NOTFOUND_LOGIN_ATTEMPT",
							"SUSPICIOUS_ACTIVITY",
							"CUSTOMER_EXPORT",
							"METER_READING_EXPORT",
							"DELIVERY_POINT_METER_READING_INFO"
                        ]
                   },
                   {
                       "name": "entityType",
                       "column": "entity_type",
                       "type": "ENUM",
                       "options": [
                           "CUSTOMER_ACCOUNT",
                           "EMPLOYEE_ACCOUNT",
                           "BUSINESS_PARTNER",
                           "CONTRACT",
                           "CONTRACT_ACCOUNT",
                           "DELIVERY_POINT",
                           "DELIVERY_POINT_VERSION",
                           "DELIVERY_POINT_FACT",
                           "UNITED_DELIVERY_POINT",
                           "INVOICE",
                           "PAYMENT",
                           "METER_READING",
                           "CUSTOMER_REQUEST",
                           "SAP_NOTIFICATION",
                           "CUSTOMER_NOTIFICATION",
                           "CUSTOMER_NOTIFICATION_TEMPLATE",
                           "REPORT",
                           "ACCESS_GROUP_ENTITY",
                           "CONTRACT_VERSION",
                           "CUSTOMER_ACCOUNT_CHALLENGE_CODE",
                           "COMPONENT_HELP",
                           "CUSTOMER_NOTIFICATION_SETTINGS",
                           "CONTRACT_PRODUCT",
                           "CUSTOMER_TRANSACTION",
						   "AUDIT_LOG"
                       ]
                   },
                   {
                       "name": "entityId",
                       "column": "entity_id",
                       "type": "STRING"
                   },
                   {
                       "name": "sessionId",
                       "column": "session_id",
                       "type": "STRING"
                   },
                   {
                       "name": "requestId",
                       "column": "request_id",
                       "type": "STRING"
                   },
                   {
                       "name": "remoteAddress",
                       "column": "remote_address",
                       "type": "STRING"
                   },
                   {
                       "name": "businessPartner",
                       "type": "OBJECT",
                       "column": "business_partner_id",
                       "refEntity": "BusinessPartner"
                   },
                   {
                       "name": "loggedCustomerAccount",
                       "type": "OBJECT",
                       "column": "logged_customer_account_uuid",
                       "refEntity": "CustomerAccount"
                   },
                   {
                       "name": "loggedCustomerAccountEmail",
                       "column": "logged_customer_account_email",
                       "type": "STRING"
                   },
                   {
                       "name": "loggedCustomerAccountFt",
                       "column": "logged_customer_account_ft",
                       "type": "STRING"
                   },
                   {
                       "name": "loggedCustomerAccountName",
                       "column": "logged_customer_account_name",
                       "type": "STRING"
                   },
                   {
                       "name": "relatedCustomerAccount",
                       "type": "OBJECT",
                       "column": "related_customer_account_uuid",
                       "refEntity": "CustomerAccount"
                   },
                   {
                       "name": "relatedCustomerAccountEmail",
                       "column": "related_customer_account_email",
                       "type": "STRING"
                   },
                   {
                       "name": "relatedCustomerAccountFt",
                       "column": "related_customer_account_ft",
                       "type": "STRING"
                   },
                   {
                       "name": "relatedCustomerAccountName",
                       "column": "related_customer_account_name",
                       "type": "STRING"
                   },
                   {
                       "name": "employeeLogin",
                       "column": "employee_login",
                       "type": "STRING"
                   },
                   {
                       "name": "employeeEmail",
                       "column": "employee_email",
                       "type": "STRING"
                   },
                   {
                       "name": "employeeFt",
                       "column": "employee_ft",
                       "type": "STRING"
                   },
                   {
                       "name": "employeeName",
                       "column": "employee_name",
                       "type": "STRING"
                   },
                   {
                       "name": "businessPartnerFt",
                       "column": "business_partner_ft",
                       "type": "STRING"
                   },
                   {
                       "name": "businessPartnerName",
                       "column": "business_partner_name",
                       "type": "STRING"
                   },
                   {
                       "name": "businessPartnerExternalId",
                       "column": "business_partner_external_id",
                       "type": "STRING"
                   },
                   {
                       "name": "entityReference",
                       "column": "entity_reference",
                       "type": "STRING"
                   },
                   {
                       "name": "entityItem",
                       "column": "entity_item",
                       "type": "STRING"
                   },
                   {
                       "name": "userAgent",
                       "column": "user_agent",
                       "type": "STRING"
                   }
               ]
           },
           {
               "name": "CustomerAccount",
               "tableName": "customer_account",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "email",
                       "column": "email",
                       "type": "STRING"
                   },
                   {
                       "name": "externalId",
                       "column": "external_id",
                       "type": "STRING"
                   },
                   {
                       "name": "type",
                       "column": "type",
                       "type": "ENUM",
                       "options": [
                           "NEWSLETTER",
                           "CUSTOMER",
                           "BUSINESS_PARTNER"
                       ]
                   },
                   {
                       "name": "status",
                       "column": "status",
                       "type": "ENUM",
                       "options": [
                           "ACTIVE",
                           "INACTIVE",
                           "UNVERIFIED",
                           "PRE_REGISTRATION",
                           "DELETED"
                       ]
                   },
                   {
                       "name": "activationAt",
                       "column": "activation_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "registrationAt",
                       "column": "registration_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "lockUntil",
                       "column": "lock_until",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "loginSuccessAt",
                       "column": "login_success_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "loginUnsuccessAt",
                       "column": "login_unsuccess_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "passwordUpdatedAt",
                       "column": "password_updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "deactivationReason",
                       "column": "deactivation_reason",
                       "type": "STRING"
                   },
                   {
                       "name": "deactivatedAt",
                       "column": "deactivated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "deleteReason",
                       "column": "delete_reason",
                       "type": "STRING"
                   },
                   {
                       "name": "deletedAt",
                       "column": "deleted_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "locale",
                       "column": "locale",
                       "type": "STRING"
                   },
                   {
                       "name": "phone",
                       "column": "phone",
                       "type": "STRING"
                   },
                   {
                       "name": "firstName",
                       "column": "first_name",
                       "type": "STRING"
                   },
                   {
                       "name": "lastName",
                       "column": "last_name",
                       "type": "STRING"
                   },
                   {
                       "name": "fullName",
                       "column": "full_name",
                       "type": "STRING"
                   },
                   {
                       "name": "otp",
                       "column": "otp",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "queueCategory",
                       "column": "queue_category",
                       "type": "ENUM",
                       "options": [
                           "COLLECTIVE",
                           "INDIVIDUAL",
                           "ALL"
                       ]
                   },
                   {
                       "name": "pairRequestInitialize",
                       "column": "pair_request_initialize",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "businessPartnerOwnerships",
                       "type": "LIST",
                       "refEntity": "BusinessPartnerOwnership",
                       "joinColumn": "customer_account_uuid"
                   },
                   {
                       "name": "contractAccountOwnerships",
                       "type": "LIST",
                       "refEntity": "ContractAccountOwnership",
                       "joinColumn": "customer_account_uuid"
                   },
                   {
                       "name": "contractOwnerships",
                       "type": "LIST",
                       "refEntity": "ContractOwnership",
                       "joinColumn": "customer_account_uuid"
                   },
                   {
                       "name": "unitedDeliveryPointOwnerships",
                       "type": "LIST",
                       "refEntity": "UnitedDeliveryPointOwnership",
                       "joinColumn": "customer_account_uuid"
                   },
                   {
                       "name": "approvals",
                       "type": "LIST",
                       "refEntity": "CustomerApproval",
                       "joinColumn": "customer_id"
                   },
                   {
                       "name": "auditLogsRelatedAccounts",
                       "type": "LIST",
                       "refEntity": "AuditLog",
                       "joinColumn": "related_customer_account_uuid"
                   },
                   {
                       "name": "auditLogsLoggedAccounts",
                       "type": "LIST",
                       "refEntity": "AuditLog",
                       "joinColumn": "logged_customer_account_uuid"
                   },
                   {
                       "name": "notifications",
                       "type": "LIST",
                       "refEntity": "CustomerNotification",
                       "joinColumn": "customer_account_id"
                   },
                   {
                       "name": "notificationsSharedFrom",
                       "type": "LIST",
                       "refEntity": "CustomerNotification",
                       "joinColumn": "share_from_id"
                   },
                   {
                       "name": "notificationsSettings",
                       "type": "LIST",
                       "refEntity": "CustomerNotificationSetting",
                       "joinColumn": "customer_account_id"
                   },
                   {
                       "name": "customerRequests",
                       "type": "LIST",
                       "refEntity": "CustomerRequest",
                       "joinColumn": "customer_id"
                   }
               ]
           },
           {
               "name": "CustomerApproval",
               "tableName": "customer_approval",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "type",
                       "column": "type",
                       "type": "ENUM",
                       "options": [
                           "CONTRACT_CONDITIONS",
                           "PROTECTION_PERSONAL_DATA",
                           "MARKETING"
                       ]
                   },
                   {
                       "name": "approval",
                       "column": "approval",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "customer",
                       "type": "OBJECT",
                       "column": "customer_id",
                       "refEntity": "CustomerAccount"
                   }
               ]
           },
           {
               "name": "IntervalMeterReadingZp",
               "tableName": "interval_meter_reading_zp",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "externalId",
                       "column": "external_id",
                       "type": "STRING"
                   },
                   {
                       "name": "value",
                       "column": "value",
                       "type": "NUMBER"
                   },
                   {
                       "name": "readAt",
                       "column": "read_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "deliveryPoint",
                       "type": "OBJECT",
                       "column": "delivery_point_id",
                       "refEntity": "DeliveryPoint"
                   }
               ]
           },
           {
               "name": "MeterReading",
               "tableName": "meter_reading",
               "properties": [
                   {
                       "name": "id",
                       "column": "id",
                       "primaryKey": true,
                       "type": "STRING"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "externalId",
                       "column": "external_id",
                       "type": "STRING"
                   },
                   {
                       "name": "category",
                       "column": "category",
                       "type": "ENUM",
                       "options": [
                           "SAP",
                           "INFO"
                       ]
                   },
                   {
                       "name": "value",
                       "column": "value",
                       "type": "NUMBER"
                   },
                   {
                       "name": "valueHigh",
                       "column": "value_high",
                       "type": "NUMBER"
                   },
                   {
                       "name": "readAt",
                       "column": "read_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "deliveryPoint",
                       "type": "OBJECT",
                       "column": "delivery_point_id",
                       "refEntity": "DeliveryPoint"
                   },
                   {
                       "name": "description",
                       "column": "description",
                       "type": "STRING"
                   },
                   {
                       "name": "deviceNumber",
                       "column": "device_number",
                       "type": "STRING"
                   },
                   {
                       "name": "register",
                       "column": "register",
                       "type": "INTEGER"
                   }
               ]
           },
           {
               "name": "BusinessPartnerApproval",
               "tableName": "business_partner_approval",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "type",
                       "column": "type",
                       "type": "ENUM",
                       "options": [
                           "MARKETING"
                       ]
                   },
                   {
                       "name": "approval",
                       "column": "approval",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "businessPartner",
                       "type": "OBJECT",
                       "column": "business_partner_id",
                       "refEntity": "BusinessPartner"
                   }
               ]
           },
           {
               "name": "BusinessPartner",
               "tableName": "business_partner",
               "properties": [
                   {
                       "name": "id",
                       "column": "id",
                       "primaryKey": true,
                       "type": "STRING"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "externalId",
                       "column": "external_id",
                       "type": "STRING"
                   },
                   {
                       "name": "queue",
                       "column": "queue",
                       "type": "ENUM",
                       "options": [
                           "INDIVIDUAL",
                           "COLLECTIVE"
                       ]
                   },
                   {
                       "name": "taxIdNumber",
                       "column": "tax_id_number",
                       "type": "STRING"
                   },
                   {
                       "name": "vatRegistrationNumber",
                       "column": "vat_registration_number",
                       "type": "STRING"
                   },
                   {
                       "name": "companyRegistrationNumber",
                       "column": "company_registration_number",
                       "type": "STRING"
                   },
                   {
                       "name": "name",
                       "column": "name",
                       "type": "STRING"
                   },
                   {
                       "name": "firstName",
                       "column": "first_name",
                       "type": "STRING"
                   },
                   {
                       "name": "lastName",
                       "column": "last_name",
                       "type": "STRING"
                   },
                   {
                       "name": "email",
                       "column": "email",
                       "type": "STRING"
                   },
                   {
                       "name": "phone",
                       "column": "phone",
                       "type": "STRING"
                   },
                   {
                       "name": "primaryStreet",
                       "column": "primary_street",
                       "type": "STRING"
                   },
                   {
                       "name": "primaryStreetNumber",
                       "column": "primary_street_number",
                       "type": "STRING"
                   },
                   {
                       "name": "primaryCity",
                       "column": "primary_city",
                       "type": "STRING"
                   },
                   {
                       "name": "primaryZipCode",
                       "column": "primary_zip_code",
                       "type": "STRING"
                   },
                   {
                       "name": "primaryCountry",
                       "column": "primary_country",
                       "type": "STRING"
                   },
                   {
                       "name": "amExternalId",
                       "column": "am_external_id",
                       "type": "STRING"
                   },
                   {
                       "name": "amfirstName",
                       "column": "am_first_name",
                       "type": "STRING"
                   },
                   {
                       "name": "amLastName",
                       "column": "am_last_name",
                       "type": "STRING"
                   },
                   {
                       "name": "amName",
                       "column": "am_name",
                       "type": "STRING"
                   },
                   {
                       "name": "amEmail",
                       "column": "am_email",
                       "type": "STRING"
                   },
                   {
                       "name": "amPhone",
                       "column": "am_phone",
                       "type": "STRING"
                   },
                   {
                       "name": "francheFixation",
                       "column": "franche_fixation",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "approvals",
                       "type": "LIST",
                       "refEntity": "BusinessPartnerApproval",
                       "joinColumn": "business_partner_id"
                   },
                   {
                       "name": "businessPartnerOwnerships",
                       "type": "LIST",
                       "refEntity": "BusinessPartnerOwnership",
                       "joinColumn": "business_partner_id"
                   },
                   {
                       "name": "contractAccounts",
                       "type": "LIST",
                       "refEntity": "ContractAccount",
                       "joinColumn": "business_partner_id"
                   },
                   {
                       "name": "auditLogs",
                       "type": "LIST",
                       "refEntity": "AuditLog",
                       "joinColumn": "business_partner_id"
                   },
                   {
                       "name": "notifications",
                       "type": "LIST",
                       "refEntity": "CustomerNotification",
                       "joinColumn": "business_partner_id"
                   },
                   {
                       "name": "customerRequests",
                       "type": "LIST",
                       "refEntity": "CustomerRequest",
                       "joinColumn": "business_partner_id"
                   },
                   {
                       "name": "deliveryPoints",
                       "type": "LIST",
                       "refEntity": "DeliveryPoint",
                       "joinColumn": "business_partner_id"
                   },
                   {
                       "name": "unitedDeliveryPoints",
                       "type": "LIST",
                       "refEntity": "DeliveryPoint",
                       "joinColumn": "business_partner_id"
                   }
               ]
           },
           {
               "name": "ContractAccount",
               "tableName": "contract_account",
               "properties": [
                   {
                       "name": "id",
                       "column": "id",
                       "primaryKey": true,
                       "type": "STRING"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "externalId",
                       "column": "external_id",
                       "type": "STRING"
                   },
                   {
                       "name": "status",
                       "column": "status",
                       "type": "ENUM",
                       "options": [
                           "ACTIVE",
                           "INACTIVE"
                       ]
                   },
                   {
                       "name": "eInvoice",
                       "column": "e_invoice",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "email",
                       "column": "email",
                       "type": "STRING"
                   },
                   {
                       "name": "postStreet",
                       "column": "post_street",
                       "type": "STRING"
                   },
                   {
                       "name": "postStreetNumber",
                       "column": "post_street_number",
                       "type": "STRING"
                   },
                   {
                       "name": "postCity",
                       "column": "post_city",
                       "type": "STRING"
                   },
                   {
                       "name": "postZipCode",
                       "column": "post_zip_code",
                       "type": "STRING"
                   },
                   {
                       "name": "postCountry",
                       "column": "post_country",
                       "type": "STRING"
                   },
                   {
                       "name": "firstName",
                       "column": "first_name",
                       "type": "STRING"
                   },
                   {
                       "name": "lastName",
                       "column": "last_name",
                       "type": "STRING"
                   },
                   {
                       "name": "name",
                       "column": "name",
                       "type": "STRING"
                   },
                   {
                       "name": "billingStreet",
                       "column": "street",
                       "type": "STRING"
                   },
                   {
                       "name": "billingStreetNumber",
                       "column": "street_number",
                       "type": "STRING"
                   },
                   {
                       "name": "billingCity",
                       "column": "city",
                       "type": "STRING"
                   },
                   {
                       "name": "billingZipCode",
                       "column": "zip_code",
                       "type": "STRING"
                   },
                   {
                       "name": "billingCountry",
                       "column": "country",
                       "type": "STRING"
                   },
                   {
                       "name": "iban",
                       "column": "iban",
                       "type": "STRING"
                   },
                   {
                       "name": "sipo",
                       "column": "sipo",
                       "type": "STRING"
                   },
                   {
                       "name": "businessPartner",
                       "type": "OBJECT",
                       "column": "business_partner_id",
                       "refEntity": "BusinessPartner"
                   },
                   {
                       "name": "contractAccountOwnerships",
                       "type": "LIST",
                       "refEntity": "ContractAccountOwnership",
                       "joinColumn": "contract_account_id"
                   },
                   {
                       "name": "contracts",
                       "type": "LIST",
                       "refEntity": "Contract",
                       "joinColumn": "contract_account_id"
                   },
                   {
                       "name": "notifications",
                       "type": "LIST",
                       "refEntity": "CustomerNotification",
                       "joinColumn": "contract_account_id"
                   },
                   {
                       "name": "customerRequests",
                       "type": "LIST",
                       "refEntity": "CustomerRequest",
                       "joinColumn": "contract_account_id"
                   },
                   {
                       "name": "invoices",
                       "type": "LIST",
                       "refEntity": "Invoice",
                       "joinColumn": "contract_account_id"
                   }
               ]
           },
           {
               "name": "Contract",
               "tableName": "contract",
               "properties": [
                   {
                       "name": "id",
                       "column": "id",
                       "primaryKey": true,
                       "type": "STRING"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "externalId",
                       "column": "external_id",
                       "type": "STRING"
                   },
                   {
                       "name": "signatureAt",
                       "column": "signature_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "effectiveFrom",
                       "column": "effective_from",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "effectiveTo",
                       "column": "effective_to",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "contractAccount",
                       "type": "OBJECT",
                       "column": "contract_account_id",
                       "refEntity": "ContractAccount"
                   },
                   {
                       "name": "contractOwnerships",
                       "type": "LIST",
                       "refEntity": "ContractOwnership",
                       "joinColumn": "contract_id"
                   },
                   {
                       "name": "versions",
                       "type": "LIST",
                       "refEntity": "ContractVersion",
                       "joinColumn": "contract_id"
                   },
                   {
                       "name": "deliveryPoints",
                       "type": "LIST",
                       "refEntity": "DeliveryPoint",
                       "joinColumn": "contract_id"
                   }
               ]
           },
           {
               "name": "ContractVersion",
               "tableName": "contract_version",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "item",
                       "column": "item",
                       "type": "INTEGER"
                   },
                   {
                       "name": "product",
                       "column": "product",
                       "type": "STRING"
                   },
                   {
                       "name": "validFrom",
                       "column": "valid_from",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "validTo",
                       "column": "valid_to",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "bindingPeriod",
                       "column": "binding_period",
                       "type": "INTEGER"
                   },
                   {
                       "name": "bindingTo",
                       "column": "binding_to",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "bindingProlong1",
                       "column": "binding_prolong_1",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "bindingProlong2",
                       "column": "binding_prolong_2",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "noticePeriod",
                       "column": "notice_period",
                       "type": "INTEGER"
                   },
                   {
                       "name": "eeTariffCount",
                       "column": "ee_tariff_count",
                       "type": "ENUM",
                       "options": [
                           "T1",
                           "T2"
                       ]
                   },
                   {
                       "name": "contract",
                       "type": "OBJECT",
                       "column": "contract_id",
                       "refEntity": "Contract"
                   }
               ]
           },
           {
               "name": "DeliveryPoint",
               "tableName": "delivery_point",
               "properties": [
                   {
                       "name": "id",
                       "column": "id",
                       "primaryKey": true,
                       "type": "STRING"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "externalId",
                       "column": "external_id",
                       "type": "STRING"
                   },
                   {
                       "name": "validFrom",
                       "column": "valid_from",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "validTo",
                       "column": "valid_to",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "type",
                       "column": "type",
                       "type": "ENUM",
                       "options": [
                           "EE",
                           "ZP"
                       ]
                   },
                   {
                       "name": "street",
                       "column": "street",
                       "type": "STRING"
                   },
                   {
                       "name": "streetNumber",
                       "column": "street_number",
                       "type": "STRING"
                   },
                   {
                       "name": "city",
                       "column": "city",
                       "type": "STRING"
                   },
                   {
                       "name": "zipCode",
                       "column": "zip_code",
                       "type": "STRING"
                   },
                   {
                       "name": "country",
                       "column": "country",
                       "type": "STRING"
                   },
                   {
                       "name": "eic",
                       "column": "eic",
                       "type": "NUMBER"
                   },
                   {
                       "name": "pod",
                       "column": "pod",
                       "type": "STRING"
                   },
                   {
                       "name": "contract",
                       "type": "OBJECT",
                       "column": "contract_id",
                       "refEntity": "Contract"
                   },
                   {
                       "name": "businessPartner",
                       "type": "OBJECT",
                       "column": "business_partner_id",
                       "refEntity": "BusinessPartner"
                   },
                   {
                       "name": "unitedDeliveryPoint",
                       "type": "OBJECT",
                       "column": "united_delivery_point_id",
                       "refEntity": "UnitedDeliveryPoint"
                   },
                   {
                       "name": "deviceNumber",
                       "column": "device_number",
                       "type": "STRING"
                   },
                   {
                       "name": "notificationRKMRKCheckedAt",
                       "column": "notification_rkmrk_checked_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "notificationZMCheckedAt",
                       "column": "notification_zm_checked_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "notificationDMMCheckedAt",
                       "column": "notification_dmm_checked_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "deliveryPointVersions",
                       "type": "LIST",
                       "refEntity": "DeliveryPointVersion",
                       "joinColumn": "delivery_point_id"
                   },
                   {
                       "name": "deliveryPointFacts",
                       "type": "LIST",
                       "refEntity": "DeliveryPointFact",
                       "joinColumn": "delivery_point_id"
                   },
                   {
                       "name": "notifications",
                       "type": "LIST",
                       "refEntity": "CustomerNotification",
                       "joinColumn": "delivery_point_id"
                   },
                   {
                       "name": "customerRequests",
                       "type": "LIST",
                       "refEntity": "CustomerRequest",
                       "joinColumn": "delivery_point_id"
                   },
                   {
                       "name": "intervalMeterReadingsZp",
                       "type": "LIST",
                       "refEntity": "IntervalMeterReadingZp",
                       "joinColumn": "delivery_point_id"
                   },
                   {
                       "name": "meterReadings",
                       "type": "LIST",
                       "refEntity": "MeterReading",
                       "joinColumn": "delivery_point_id"
                   }
               ]
           },
           {
               "name": "DeliveryPointFact",
               "tableName": "delivery_point_fact",
               "properties": [
                   {
                       "name": "id",
                       "column": "id",
                       "primaryKey": true,
                       "type": "STRING"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "validFrom",
                       "column": "valid_from",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "validTo",
                       "column": "valid_to",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "deliveryPoint",
                       "type": "OBJECT",
                       "column": "delivery_point_id",
                       "refEntity": "DeliveryPoint"
                   },
                   {
                       "name": "operand",
                       "column": "operand",
                       "type": "ENUM",
                       "options": [
                           "EDITARIFID",
                           "EISTIC",
                           "EMRK",
                           "ERKA",
                           "ETYPMERAN",
                           "PDMM_M3",
                           "PZJEDRMMWH",
                           "EDISSADZBA",
                           "PDITARIFID"
                       ]
                   },
                   {
                       "name": "value",
                       "column": "operand_value",
                       "type": "STRING"
                   },
                   {
                       "name": "value2",
                       "column": "operand_value2",
                       "type": "STRING"
                   },
                   {
                       "name": "tarifart",
                       "column": "tarifart",
                       "type": "STRING"
                   }
               ]
           },
           {
               "name": "DeliveryPointVersion",
               "tableName": "delivery_point_version",
               "properties": [
                   {
                       "name": "id",
                       "column": "id",
                       "primaryKey": true,
                       "type": "STRING"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "validFrom",
                       "column": "valid_from",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "validTo",
                       "column": "valid_to",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "deliveryPoint",
                       "type": "OBJECT",
                       "column": "delivery_point_id",
                       "refEntity": "DeliveryPoint"
                   },
                   {
                       "name": "category",
                       "column": "category",
                       "type": "STRING"
                   },
                   {
                       "name": "readingCycleValue",
                       "column": "reading_cycle_value",
                       "type": "INTEGER"
                   },
                   {
                       "name": "readingCycle",
                       "column": "reading_cycle",
                       "type": "ENUM",
                       "options": [
                           "SEDDOR01",
                           "SEDDOR02",
                           "SEDDOR03",
                           "SEDDOR04",
                           "SEDDOR05",
                           "SEDDOR06",
                           "SEDDOR07",
                           "SEDDOR08",
                           "SEDDOR09",
                           "SEDDOR10",
                           "SEDDOR11",
                           "SEDDOR12",
                           "SEDDPR00",
                           "SEDMOR12",
                           "SEDMPR00",
                           "SEDVKM00",
                           "SEDVOM00",
                           "SEDVPM00",
                           "SEODOR01",
                           "SEODOR02",
                           "SEODOR03",
                           "SEODOR04",
                           "SEODOR05",
                           "SEODOR06",
                           "SEODOR07",
                           "SEODOR08",
                           "SEODOR09",
                           "SEODOR10",
                           "SEODOR11",
                           "SEODOR12",
                           "SEOMOR12",
                           "SPDDKR01",
                           "SPDDKR02",
                           "SPDDKR03",
                           "SPDDKR04",
                           "SPDDKR05",
                           "SPDDKR06",
                           "SPDDKR07",
                           "SPDDKR08",
                           "SPDDKR09",
                           "SPDDKR10",
                           "SPDDKR11",
                           "SPDDKR12",
                           "SPDDOR01",
                           "SPDDOR02",
                           "SPDDOR03",
                           "SPDDOR04",
                           "SPDDOR05",
                           "SPDDOR06",
                           "SPDDOR07",
                           "SPDDOR08",
                           "SPDDOR09",
                           "SPDDOR10",
                           "SPDDOR11",
                           "SPDDOR12",
                           "SPDDPR00",
                           "SPDDPR00",
                           "SPDMKR12",
                           "SPDMOR12",
                           "SPDMPR00",
                           "SPDVKM00",
                           "SPDVKM01",
                           "SPDVKM07",
                           "SPDVKM10",
                           "SPDVOM00",
                           "SPDVOM01",
                           "SPDVOM07",
                           "SPDVOM10",
                           "SPDVPM00",
                           "SPDVPM01",
                           "SPDVPM07",
                           "SPDVPM10"
                       ]
                   }
               ]
           },
           {
               "name": "Invoice",
               "tableName": "invoice",
               "properties": [
                   {
                       "name": "id",
                       "column": "id",
                       "primaryKey": true,
                       "type": "STRING"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "externalId",
                       "column": "external_id",
                       "type": "STRING"
                   },
                   {
                       "name": "status",
                       "column": "status",
                       "type": "ENUM",
                       "options": [
                           "PAID",
                           "UNPAID",
                           "PARTIALLY_PAID"
                       ]
                   },
                   {
                       "name": "typeGroup",
                       "column": "type_group",
                       "type": "ENUM",
                       "options": [
                           "INVOICE",
                           "CREDIT",
                           "ADVANCE_INVOICE",
                           "REPAYMENT_PLAN"
                       ]
                   },
                   {
                       "name": "amount",
                       "column": "amount",
                       "type": "NUMBER"
                   },
                   {
                       "name": "unpaid",
                       "column": "unpaid",
                       "type": "NUMBER"
                   },
                   {
                       "name": "dueAt",
                       "column": "due_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "issueAt",
                       "column": "issue_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "notificationCreatedAt",
                       "column": "notification_created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "notificationDueDateAt",
                       "column": "notification_duedate_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "vs",
                       "column": "vs",
                       "type": "STRING"
                   },
                   {
                       "name": "paymentRequestNotificationSent",
                       "column": "payment_req_notif_sent",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "containsPaymentRequest",
                       "column": "contains_payment_request",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "containsPaymentPlan",
                       "column": "contains_plament_plan",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "contractAccount",
                       "type": "OBJECT",
                       "column": "contract_account_id",
                       "refEntity": "ContractAccount"
                   }
               ]
           },
           {
               "name": "Product",
               "tableName": "product",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "status",
                       "column": "status",
                       "type": "ENUM",
                       "options": [
                           "ACTIVE",
                           "INACTIVE"
                       ]
                   },
                   {
                       "name": "code",
                       "column": "code",
                       "type": "STRING"
                   },
                   {
                       "name": "zp",
                       "column": "zp",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "ee",
                       "column": "ee",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "notCommodity",
                       "column": "not_commodity",
                       "type": "BOOLEAN"
                   }
               ]
           },
           {
               "name": "TariffEntity",
               "tableName": "tariff",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "status",
                       "column": "status",
                       "type": "ENUM",
                       "options": [
                           "ACTIVE",
                           "INACTIVE"
                       ]
                   },
                   {
                       "name": "code",
                       "column": "code",
                       "type": "STRING"
                   },
                   {
                       "name": "type",
                       "column": "type",
                       "type": "ENUM",
                       "options": [
                           "ZP",
                           "EE"
                       ]
                   },
                   {
                       "name": "category",
                       "column": "category",
                       "type": "ENUM",
                       "options": [
                           "HOME",
                           "RETAIL",
                           "WHOLESALE"
                       ]
                   }
               ]
           },
           {
               "name": "UnitedDeliveryPoint",
               "tableName": "united_delivery_point",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "street",
                       "column": "street",
                       "type": "STRING"
                   },
                   {
                       "name": "streetNumber",
                       "column": "street_number",
                       "type": "STRING"
                   },
                   {
                       "name": "city",
                       "column": "city",
                       "type": "STRING"
                   },
                   {
                       "name": "zipCode",
                       "column": "zip_code",
                       "type": "STRING"
                   },
                   {
                       "name": "country",
                       "column": "country",
                       "type": "STRING"
                   },
                   {
                       "name": "businessPartner",
                       "type": "OBJECT",
                       "column": "business_partner_id",
                       "refEntity": "BusinessPartner"
                   },
                   {
                       "name": "deliveryPoints",
                       "type": "LIST",
                       "refEntity": "DeliveryPoint",
                       "joinColumn": "united_delivery_point_id"
                   },
                   {
                       "name": "unitedDeliveryPointOwnerships",
                       "type": "LIST",
                       "refEntity": "UnitedDeliveryPointOwnership",
                       "joinColumn": "united_delivery_point_uuid"
                   },
                   {
                       "name": "types",
                       "column": "types",
                       "type": "STRING"
                   },
                   {
                       "name": "pairingStatus",
                       "column": "pairing_status",
                       "type": "ENUM",
                       "options": [
                           "IN_PROGRESS",
                           "DONE"
                       ]
                   },
                   {
                       "name": "notificationsSettings",
                       "type": "LIST",
                       "refEntity": "CustomerNotificationSetting",
                       "joinColumn": "united_delivery_point_id"
                   }
               ]
           },
           {
               "name": "CustomerRequest",
               "tableName": "customer_request",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "externalId",
                       "column": "external_id",
                       "type": "STRING"
                   },
                   {
                       "name": "externalGuid",
                       "column": "external_guid",
                       "type": "STRING"
                   },
                   {
                       "name": "status",
                       "column": "status",
                       "type": "ENUM",
                       "options": [
                           "PRE_CREATED",
                           "CREATED",
                           "REGISTERED",
                           "CANCELLED_BY_USER",
                           "GENERATED",
                           "SAP_OPEN",
                           "SAP_IN_PROGRESS",
                           "SAP_CANCELLED_BY_USER",
                           "SAP_CANCELLED",
                           "SAP_FINISHED"
                       ]
                   },
                   {
                       "name": "request_cancel",
                       "column": "requestCancel",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "issuedAt",
                       "column": "issued_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "statusUpdatedAt",
                       "column": "status_updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "notificationStatusUpdatedAt",
                       "column": "notification_status_updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "customer",
                       "type": "OBJECT",
                       "column": "customer_id",
                       "refEntity": "CustomerAccount"
                   },
                   {
                       "name": "attachmentsCropped",
                       "column": "attachments_cropped",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "businessPartner",
                       "type": "OBJECT",
                       "column": "business_partner_id",
                       "refEntity": "BusinessPartner"
                   },
                   {
                       "name": "deliveryPoint",
                       "type": "OBJECT",
                       "column": "delivery_point_id",
                       "refEntity": "DeliveryPoint"
                   },
                   {
                       "name": "contractAccount",
                       "type": "OBJECT",
                       "column": "contract_account_id",
                       "refEntity": "ContractAccount"
                   },
                   {
                       "name": "confirmationCode",
                       "column": "confirmation_code",
                       "type": "STRING"
                   },
                   {
                       "name": "confirmedAt",
                       "column": "confirmed_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "registered",
                       "column": "registered",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "registeredAt",
                       "column": "registered_at",
                       "type": "TIMESTAMP"
                   }
               ]
           },
           {
               "name": "CustomerNotification",
               "tableName": "customer_notification",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "source",
                       "column": "source",
                       "type": "ENUM",
                       "options": [
                           "NZP",
                           "EXTERNAL"
                       ]
                   },
                   {
                       "name": "externalId",
                       "column": "external_id",
                       "type": "STRING"
                   },
                   {
                       "name": "status",
                       "column": "status",
                       "type": "ENUM",
                       "options": [
                           "CREATED",
                           "RENDERED"
                       ]
                   },
                   {
                       "name": "header",
                       "column": "header",
                       "type": "STRING"
                   },
                   {
                       "name": "emailBody",
                       "column": "email_body",
                       "type": "STRING"
                   },
                   {
                       "name": "emailSubject",
                       "column": "email_subject",
                       "type": "STRING"
                   },
                   {
                       "name": "smsBody",
                       "column": "sms_body",
                       "type": "STRING"
                   },
                   {
                       "name": "entityType",
                       "column": "entity_type",
                       "type": "ENUM",
                       "options": [
                           "CUSTOMER_ACCOUNT",
                           "EMPLOYEE_ACCOUNT",
                           "BUSINESS_PARTNER",
                           "CONTRACT",
                           "CONTRACT_ACCOUNT",
                           "DELIVERY_POINT",
                           "DELIVERY_POINT_VERSION",
                           "DELIVERY_POINT_FACT",
                           "UNITED_DELIVERY_POINT",
                           "INVOICE",
                           "PAYMENT",
                           "METER_READING",
                           "CUSTOMER_REQUEST",
                           "SAP_NOTIFICATION",
                           "CUSTOMER_NOTIFICATION",
                           "CUSTOMER_NOTIFICATION_TEMPLATE",
                           "REPORT",
                           "ACCESS_GROUP_ENTITY",
                           "CONTRACT_VERSION",
                           "CUSTOMER_ACCOUNT_CHALLENGE_CODE",
                           "COMPONENT_HELP",
                           "CUSTOMER_NOTIFICATION_SETTINGS"
                       ]
                   },
                   {
                       "name": "entityId",
                       "column": "entity_id",
                       "type": "STRING"
                   },
                   {
                       "name": "phone",
                       "column": "phone",
                       "type": "STRING"
                   },
                   {
                       "name": "email",
                       "column": "email",
                       "type": "STRING"
                   },
                   {
                       "name": "locale",
                       "column": "locale",
                       "type": "STRING"
                   },
                   {
                       "name": "employeeLogin",
                       "column": "employee_login",
                       "type": "STRING"
                   },
                   {
                       "name": "customerAccount",
                       "type": "OBJECT",
                       "column": "customer_account_id",
                       "refEntity": "CustomerAccount"
                   },
                   {
                       "name": "shareFrom",
                       "type": "OBJECT",
                       "column": "share_from_id",
                       "refEntity": "CustomerAccount"
                   },
                   {
                       "name": "readAt",
                       "column": "read_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "read",
                       "column": "read",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "businessPartner",
                       "type": "OBJECT",
                       "column": "business_partner_id",
                       "refEntity": "BusinessPartner"
                   },
                   {
                       "name": "deliveryPoint",
                       "type": "OBJECT",
                       "column": "delivery_point_id",
                       "refEntity": "DeliveryPoint"
                   },
                   {
                       "name": "renderStatuses",
                       "type": "LIST",
                       "refEntity": "CustomerNotificationRenderStatus",
                       "joinColumn": "customer_notification_id"
                   },
                   {
                       "name": "sendStatuses",
                       "type": "LIST",
                       "refEntity": "CustomerNotificationSendStatus",
                       "joinColumn": "customer_notification_id"
                   },
                   {
                       "name": "contractAccount",
                       "type": "OBJECT",
                       "column": "contract_account_id",
                       "refEntity": "ContractAccount"
                   }
               ]
           },
           {
               "name": "CustomerNotificationSendStatus",
               "tableName": "customer_notification_send_status",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "priority",
                       "column": "priority",
                       "type": "ENUM",
                       "options": [
                           "LOW",
                           "HIGH"
                       ]
                   },
                   {
                       "name": "channel",
                       "column": "channel",
                       "type": "ENUM",
                       "options": [
                           "EMAIL",
                           "SMS"
                       ]
                   },
                   {
                       "name": "email",
                       "column": "email",
                       "type": "STRING"
                   },
                   {
                       "name": "phone",
                       "column": "phone",
                       "type": "STRING"
                   },
                   {
                       "name": "customerNotification",
                       "type": "OBJECT",
                       "column": "customer_notification_id",
                       "refEntity": "CustomerNotification"
                   }
               ]
           },
           {
               "name": "CustomerNotificationRenderStatus",
               "tableName": "customer_notification_render_status",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "priority",
                       "column": "priority",
                       "type": "ENUM",
                       "options": [
                           "LOW",
                           "HIGH"
                       ]
                   },
                   {
                       "name": "customerNotification",
                       "type": "OBJECT",
                       "column": "customer_notification_id",
                       "refEntity": "CustomerNotification"
                   },
                   {
                       "name": "renderAt",
                       "column": "render_at",
                       "type": "TIMESTAMP"
                   }
               ]
           },
           {
               "name": "CustomerNotificationSetting",
               "tableName": "customer_notification_setting",
               "properties": [
                   {
                       "name": "id",
                       "column": "uuid",
                       "primaryKey": true,
                       "type": "UUID"
                   },
                   {
                       "name": "createdAt",
                       "column": "created_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "updatedAt",
                       "column": "updated_at",
                       "type": "TIMESTAMP"
                   },
                   {
                       "name": "version",
                       "column": "version",
                       "type": "INTEGER"
                   },
                   {
                       "name": "unitedDeliveryPoint",
                       "type": "OBJECT",
                       "column": "united_delivery_point_id",
                       "refEntity": "UnitedDeliveryPoint"
                   },
                   {
                       "name": "customerAccount",
                       "type": "OBJECT",
                       "column": "customer_account_id",
                       "refEntity": "CustomerAccount"
                   },
                   {
                       "name": "email",
                       "column": "email",
                       "type": "STRING"
                   },
                   {
                       "name": "sms",
                       "column": "sms",
                       "type": "STRING"
                   }
               ]
           },
           {
               "name": "BusinessPartnerOwnership",
               "tableName": "business_partner_ownership",
               "properties": [
                   {
                       "name": "businessPartner",
                       "type": "OBJECT",
                       "column": "business_partner_id",
                       "refEntity": "BusinessPartner"
                   },
                   {
                       "name": "customerAccount",
                       "type": "OBJECT",
                       "column": "customer_account_uuid",
                       "refEntity": "CustomerAccount"
                   },
                   {
                       "name": "type",
                       "type": "ENUM",
                       "column": "type",
                       "options": [
                           "OWNER",
                           "SHARING"
                       ]
                   },
                   {
                       "name": "inherited",
                       "column": "inherited",
                       "type": "BOOLEAN"
                   }
               ]
           },
           {
               "name": "ContractAccountOwnership",
               "tableName": "contract_account_ownership",
               "properties": [
                   {
                       "name": "contractAccount",
                       "type": "OBJECT",
                       "column": "contract_account_id",
                       "refEntity": "ContractAccount"
                   },
                   {
                       "name": "customerAccount",
                       "type": "OBJECT",
                       "column": "customer_account_uuid",
                       "refEntity": "CustomerAccount"
                   },
                   {
                       "name": "type",
                       "type": "ENUM",
                       "column": "type",
                       "options": [
                           "OWNER",
                           "SHARING"
                       ]
                   },
                   {
                       "name": "inherited",
                       "column": "inherited",
                       "type": "BOOLEAN"
                   }
               ]
           },
           {
               "name": "ContractOwnership",
               "tableName": "contract_ownership",
               "properties": [
                   {
                       "name": "contract",
                       "type": "OBJECT",
                       "column": "contract_id",
                       "refEntity": "Contract"
                   },
                   {
                       "name": "customerAccount",
                       "type": "OBJECT",
                       "column": "customer_account_uuid",
                       "refEntity": "CustomerAccount"
                   },
                   {
                       "name": "type",
                       "type": "ENUM",
                       "column": "type",
                       "options": [
                           "OWNER",
                           "SHARING"
                       ]
                   },
                   {
                       "name": "inherited",
                       "column": "inherited",
                       "type": "BOOLEAN"
                   }
               ]
           },
           {
               "name": "UnitedDeliveryPointOwnership",
               "tableName": "united_delivery_point_ownership",
               "properties": [
                   {
                       "name": "unitedDeliveryPoint",
                       "type": "OBJECT",
                       "column": "united_delivery_point_uuid",
                       "refEntity": "UnitedDeliveryPoint"
                   },
                   {
                       "name": "customerAccount",
                       "type": "OBJECT",
                       "column": "customer_account_uuid",
                       "refEntity": "CustomerAccount"
                   },
                   {
                       "name": "type",
                       "type": "ENUM",
                       "column": "type",
                       "options": [
                           "OWNER",
                           "SHARING"
                       ]
                   },
                   {
                       "name": "inherited",
                       "column": "inherited",
                       "type": "BOOLEAN"
                   },
                   {
                       "name": "hidden",
                       "column": "hidden",
                       "type": "BOOLEAN"
                   }
               ]
           },
           {
               "name":"CustomerTransactionInvoice",
               "tableName":"customer_transaction_invoice",
               "properties": [
                   {
                       "name":"createdAt",
                       "column":"created_at",
                       "type":"TIMESTAMP"
                   },
                   {
                       "name":"invoice",
                       "column":"invoice_id",
                       "type":"OBJECT",
                       "refEntity":"Invoice"
                   },
                   {
                       "name":"customerTransaction",
                       "column":"customer_transaction_uuid",
                       "type":"OBJECT",
                       "refEntity":"CustomerTransaction"
                   }
               ]
           },
           {
               "name":"CustomerTransaction",
               "tableName":"customer_transaction",
               "properties": [
                   {
                       "name":"id",
                       "column":"uuid",
                       "primaryKey":true,
                       "type":"UUID"
                   },
                   {
                       "name":"updatedAt",
                       "column":"updated_at",
                       "type":"TIMESTAMP"
                   },
                   {
                       "name":"version",
                       "column":"version",
                       "type":"INTEGER"
                   },
                   {
                       "name":"createdAt",
                       "column":"created_at",
                       "type":"TIMESTAMP"
                   },
                   {
                       "name":"lastStatusChangedAt",
                       "column":"last_status_changed_at",
                       "type":"TIMESTAMP"
                   },
                   {
                       "name":"startedAt",
                       "column":"started_at",
                       "type":"TIMESTAMP"
                   },
                   {
                       "name":"finishedAt",
                       "column":"finished_at",
                       "type":"TIMESTAMP"
                   },
                   {
                       "name":"status",
                       "column":"status",
                       "type":"ENUM",
                       "options": [
                           "INIT",
                           "REDIRECTED_TO_IB",
                           "ONLINE_RESPONSE_OK",
                           "ONLINE_RESPONSE_FAILED",
                           "ONLINE_RESPONSE_BANK_TIMEOUT",
                           "MAIL_RESPONSE_OK",
                           "MAIL_RESPONSE_FAILED",
                           "TRANSACTION_TIMEOUT"
                        ]
                   },
                   {
                       "name":"finished",
                       "column":"finished",
                       "type":"BOOLEAN"
                   },
                   {
                       "name":"customer",
                       "type":"OBJECT",
                       "column":"customer_uuid",
                       "refEntity":"CustomerAccount"
                   },
                   {
                       "name":"businessPartner",
                       "type":"OBJECT",
                       "column":"business_partner_id",
                       "refEntity":"BusinessPartner"
                   },
                   {
                       "name":"deliveryPoint",
                       "type":"OBJECT",
                       "column":"delivery_point_id",
                       "refEntity":"DeliveryPoint"
                   },
                   {
                       "name":"vs",
                       "column":"vs",
                       "type":"STRING"
                   },
                   {
                       "name":"amount",
                       "column":"amount",
                       "type":"NUMBER"
                   },
                   {
                       "name":"bulk",
                       "column":"bulk",
                       "type":"BOOLEAN"
                   }
               ]
           }
       ]
   }');