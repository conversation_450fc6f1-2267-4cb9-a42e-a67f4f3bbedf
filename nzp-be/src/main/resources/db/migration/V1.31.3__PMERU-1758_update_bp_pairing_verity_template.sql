UPDATE notification_template_i18n
SET updated_at = now(), version = version + 1, email_body =
    '<#import "spp.macros_sk.ftl" as spp>
     <@spp.notification_email_template oslovenie=""><p>
     		Od zákazníka ${target.customer.email} sme obdržali požiadavku na párovanie zákazníckeho účtu <a href="${portalExternalUrl}/admin/portal-accounts/customers/${attributes.customerId}/detail">${target.customer.email}</a> na obchodného partnera ${businessPartner.name} (${businessPartner.externalId}).
     </p></@spp.notification_email_template>'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'BUSINESS_PARTNER_PAIRING_VERIFY')
AND LOWER(locale) = 'sk';

UPDATE notification_template_i18n
SET updated_at = now(), version = version + 1, email_body =
    '<#import "spp.macros_sk.ftl" as spp>
     <@spp.notification_email_template oslovenie=""><p>
     		[EN] Od zákazníka ${target.customer.email} sme obdržali požiadavku na párovanie zákazníckeho účtu <a href="${portalExternalUrl}/admin/portal-accounts/customers/${attributes.customerId}/detail">${target.customer.email}</a> na obchodného partnera ${businessPartner.name} (${businessPartner.externalId}).
     </p></@spp.notification_email_template>'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'BUSINESS_PARTNER_PAIRING_VERIFY')
AND LOWER(locale) = 'en';