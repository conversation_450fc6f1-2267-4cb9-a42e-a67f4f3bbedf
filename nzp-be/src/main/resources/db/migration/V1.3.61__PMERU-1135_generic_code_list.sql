INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'REPORT_CREATE', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk', 'Vytvorenie reportu', null, (select uuid from generic_code_list where code like 'REPORT_CREATE' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Vytvorenie reportu', null, (select uuid from generic_code_list where code like 'REPORT_CREATE' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'REPORT_CHANGE', 'AUDIT_LOG_CODE', null, null, null);

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk', 'Zmena reportu', null, (select uuid from generic_code_list where code like 'REPORT_CHANGE' and type = 'AUDIT_LOG_CODE'));

INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'en', '[EN] Zmena reportu', null, (select uuid from generic_code_list where code like 'REPORT_CHANGE' and type = 'AUDIT_LOG_CODE'));
