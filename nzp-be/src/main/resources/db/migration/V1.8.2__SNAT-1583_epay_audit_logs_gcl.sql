
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'REGISTRATION_CHALLENGE_CODE_EXPIRED','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Registračný kód expiroval','Registračný kód expiroval', (select uuid from generic_code_list where code like 'REGISTRATION_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Registration challenge code expired','Registration challenge code expired', (select uuid from generic_code_list where code like '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'RECOVERY_CHALLENGE_CODE_EXPIRED','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Expirovaný kód na obnovu hesla','Expirovaný kód na obnovu hesla', (select uuid from generic_code_list where code like 'RECOVERY_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Password recovery challenge code expired','Password recovery challenge code expired', (select uuid from generic_code_list where code like 'RECOVERY_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'PAIRING_SMS_CHALLENGE_CODE_EXPIRED','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Expirovaný sms kód na párovanie','Expirovaný sms kód na párovanie', (select uuid from generic_code_list where code like 'PAIRING_SMS_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Pairing sms challenge code expired','Pairing sms challenge code expired', (select uuid from generic_code_list where code like 'PAIRING_SMS_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'PAIRING_AM_CODE_CHALLENGE_CODE_EXPIRED','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Expirovaný am kód na párovanie','Expirovaný am kód na párovanie', (select uuid from generic_code_list where code like 'PAIRING_AM_CODE_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Pairing am challenge code expired','Pairing am challenge code expired', (select uuid from generic_code_list where code like 'PAIRING_AM_CODE_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'PASSWORD_CHANGE_CHALLENGE_CODE_EXPIRED','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Expirovaný kód na zmenu hesla','Expirovaný kód na zmenu hesla', (select uuid from generic_code_list where code like 'PASSWORD_CHANGE_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Password change challenge code expired','Password change challenge code expired', (select uuid from generic_code_list where code like 'PASSWORD_CHANGE_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EMAIL_CHANGE_CHALLENGE_CODE_EXPIRED','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Expirovaný kód na zmenu emailu','Expirovaný kód na zmenu emailu', (select uuid from generic_code_list where code like 'EMAIL_CHANGE_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Email change challenge code expired','Email change challenge code expired', (select uuid from generic_code_list where code like 'EMAIL_CHANGE_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'PHONE_CHANGE_CHALLENGE_CODE_EXPIRED','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Expirovaný kód na zmenu telefónneho čísla','Expirovaný kód na zmenu telefónneho čísla', (select uuid from generic_code_list where code like 'PHONE_CHANGE_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Phone change challenge code expired','Phone change challenge code expired', (select uuid from generic_code_list where code like 'PHONE_CHANGE_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'DELETE_CUSTOMER_ACCOUNT_CHALLENGE_CODE_EXPIRED','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Expirovaný kód na vymazanie zákazníka','Expirovaný kód na vymazanie zákazníka', (select uuid from generic_code_list where code like 'DELETE_CUSTOMER_ACCOUNT_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Delete customer challenge code expired','Delete customer challenge code expired', (select uuid from generic_code_list where code like 'DELETE_CUSTOMER_ACCOUNT_CHALLENGE_CODE_EXPIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'BUSINESS_PARTNER_PAIRING_PHONE_INVALID','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Párovanie - nevalidné telefónne číslo','Párovanie - nevalidné telefónne číslo', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_PHONE_INVALID' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Business partner pairing phone invalid','Business partner pairing phone invalid', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_PHONE_INVALID' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'BUSINESS_PARTNER_PAIRING_PHONE_MISSING','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Párovanie - chýbajúce telefónne číslo','Párovanie - chýbajúce telefónne číslo', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_PHONE_MISSING' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Business partner pairing phone missing','Business partner pairing phone missing', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_PHONE_MISSING' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'BUSINESS_PARTNER_PAIRING_BP_NOTFOUND','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Párovanie - nenajdený obchodný partner','Párovanie - nenajdený obchodný partner', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_BP_NOTFOUND' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Business partner pairing - business partner not found','Business partner pairing - business partner not found', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_BP_NOTFOUND' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'BUSINESS_PARTNER_PAIRING_INACTIVE','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Párovanie - neaktívny obchodný partner','Párovanie - neaktívny obchodný partner', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_INACTIVE' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Business partner pairing - business partner inactive','Business partner pairing - business partner inactive', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_INACTIVE' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'BUSINESS_PARTNER_PAIRING_ALREADY_PAIRED','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Párovanie - obchodný partner už bol napárovaný','Párovanie - obchodný partner už bol napárovaný', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_ALREADY_PAIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Business partner pairing - business partner already paired','Business partner pairing - business partner already paired', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_ALREADY_PAIRED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'BUSINESS_PARTNER_PAIRING_ALREADY_REQUESTED','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Párovanie - obchodný partner už je v procese párovania','Párovanie - obchodný partner už je v procese párovania', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_ALREADY_REQUESTED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Business partner pairing - there is existing pairing request','Business partner pairing - there is existing pairing request', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_ALREADY_REQUESTED' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'BUSINESS_PARTNER_PAIRING_SCOPES_MISSING','AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'SK','Párovanie - chýbajúce údaje v scope pre párovanie','Párovanie - chýbajúce údaje v scope pre párovanie', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_SCOPES_MISSING' and type ='AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1,'EN','Business partner pairing scopes missing','Business partner pairing scopes missing', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_SCOPES_MISSING' and type ='AUDIT_LOG_CODE'));
