-- SPPNZP-146 - Delete records from tables notification_template_i18n, customer_notification_setting, notification_template_variable, notification_template related to unused automatic notification templates

delete from notification_template_i18n
where notification_template_id in (
    select notification_template.uuid
    from notification_template
    where code in ('EPAY_TRANSACTION_FINNISHED', 'DELIVERY_POINT_CHECK_ZM', 'CUSTOMER_LOCK', 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY', 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY', 'EMPLOYEE_LOCK', 'CUSTOMER_REQUEST_REGISTER')
);

delete from customer_notification_setting
where notification_template_id in (
    select notification_template.uuid
    from notification_template
    where code in ('EPAY_TRANSACTION_FINNISHED', 'DELIVERY_POINT_CHECK_ZM', 'CUSTOMER_LOCK', 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY', 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY', 'EMPLOYEE_LOCK', 'CUSTOMER_REQUEST_REGISTER')
);

delete from notification_template_variable
where notification_template_uuid in (
    select notification_template.uuid
    from notification_template
    where code in ('EPAY_TRANSACTION_FINNISHED', 'DELIVERY_POINT_CHECK_ZM', 'CUSTOMER_LOCK', 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY', 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY', 'EMPLOYEE_LOCK', 'CUSTOMER_REQUEST_REGISTER')
);

delete from notification_template
where code in ('EPAY_TRANSACTION_FINNISHED', 'DELIVERY_POINT_CHECK_ZM', 'CUSTOMER_LOCK', 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY', 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY', 'EMPLOYEE_LOCK', 'CUSTOMER_REQUEST_REGISTER');
