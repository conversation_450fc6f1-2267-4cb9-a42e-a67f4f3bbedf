
-- CUSTOMER_EMAIL_CHANGE_SUCCESS
update notification_template_i18n
set email_body = '<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> zmena mailovej adresy bola úspešná. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>'
where notification_template_i18n.notification_template_id = (select uuid from notification_template nt where nt.code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS');


-- CUSTOMER_SHARING_OWNER_GRANT
update notification_template_i18n
set email_body = 'V<PERSON><PERSON><PERSON><PERSON> z<PERSON>azník, práve ste nastavili zdieľanie zákazníkovi ${attributes.sharingEmail}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>'
where notification_template_i18n.notification_template_id = (select uuid from notification_template nt where nt.code = 'CUSTOMER_SHARING_OWNER_GRANT');


-- CUSTOMER_SHARING_CONSUMER_GRANT
update notification_template_i18n
set email_body = 'Vážený zákazník, práve Vám bolo nastavené zdieľanie od zákazníka ${attributes.sharingEmail}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>'
where notification_template_i18n.notification_template_id = (select uuid from notification_template nt where nt.code = 'CUSTOMER_SHARING_CONSUMER_GRANT');


-- CUSTOMER_SHARING_OWNER_REVOKE
update notification_template_i18n
set email_body = 'Vážený zákazník, práve ste zrušili zdieľanie zákazníkovi ${attributes.sharingEmail}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>'
where notification_template_i18n.notification_template_id = (select uuid from notification_template nt where nt.code = 'CUSTOMER_SHARING_OWNER_REVOKE');


-- CUSTOMER_SHARING_CONSUMER_REVOKE
update notification_template_i18n
set email_body = 'Vážený zákazník, práve Vám bolo zrušené zdieľanie od zákazníka ${attributes.sharingEmail}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>'
where notification_template_i18n.notification_template_id = (select uuid from notification_template nt where nt.code = 'CUSTOMER_SHARING_CONSUMER_REVOKE');


-- CUSTOMER_SHARING_INVITATION
update notification_template_i18n
set email_body = 'Dobrý deň, práve Vám bolo nastavené zdieľanie od zákazníka ${attributes.sharingEmail}.<br/><br/>Pre prístup k zdieľaným údajom sa môžete zaregistrovať na našej stránke: ${portalExternalUrl}<br/><br/>Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>'
where notification_template_i18n.notification_template_id = (select uuid from notification_template nt where nt.code = 'CUSTOMER_SHARING_INVITATION');


-- RK, MRK, DMM, ZM
INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms, execution_type, report_customer_column, report_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_CHECK_RK', 'ACTIVE', 'CUSTOMER_NOTICE', 'Notifikácia prekročenia rezervovanej kapacity (EE)', 'LOW', 'Notifikácia prekročenia rezervovanej kapacity (EE)', null, 'LIMIT_OVERFLOW', true, false, 'AUTOMATIC', null, null);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Prekročenie rezervovanej kapacity (EE)', 'Vážený zákazník, prekročili ste Vašu rezervovanú kapacitu pre spotrebu elektrickej energie. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>', 'Prekročenie rezervovanej kapacity (EE)', '', (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RK' and version = 1), 'SK', 'ACTIVE');

INSERT INTO notification_template
(uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms, execution_type, report_customer_column, report_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_CHECK_MRK', 'ACTIVE', 'CUSTOMER_NOTICE', 'Notifikácia prekročenia maximálnej rezervovanej kapacity (EE)', 'LOW', 'Notifikácia prekročenia maximálnej rezervovanej kapacity (EE)', null, 'LIMIT_OVERFLOW', true, false, 'AUTOMATIC', null, null);
INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'Prekročenie maximálnej rezervovanej kapacity (EE)', 'Vážený zákazník, prekročili ste Vašu maximálnu rezervovanú kapacitu pre spotrebu elektrickej energie. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>', 'Prekročenie rezervovanej kapacity (EE)', '', (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MRK' and version = 1), 'SK', 'ACTIVE');

INSERT INTO notification_template
(uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms, execution_type, report_customer_column, report_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_CHECK_ZM', 'ACTIVE', 'CUSTOMER_NOTICE', 'Notifikácia prekročenia dennej spotreby plynu (ZP)', 'LOW', 'Notifikácia prekročenia dennej spotreby plynu (ZP)', null, 'LIMIT_OVERFLOW', true, false, 'AUTOMATIC', null, null);
INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'Prekročenie dennej spotreby plynu (ZP)', 'Vážený zákazník, prekročili ste Vašu dennú spotrebu plynu. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>', 'Prekročenie rezervovanej kapacity (EE)', '', (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_ZM' and version = 1), 'SK', 'ACTIVE');

INSERT INTO notification_template
(uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms, execution_type, report_customer_column, report_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_CHECK_DMM', 'ACTIVE', 'CUSTOMER_NOTICE', 'Notifikácia prekročenia maximálnej dennej spotreby plynu (ZP)', 'LOW', 'Notifikácia prekročenia maximálnej dennej spotreby plynu (ZP)', null, 'LIMIT_OVERFLOW', true, false, 'AUTOMATIC', null, null);
INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'Prekročenie maximálnej dennej spotreby plynu (ZP)', 'Vážený zákazník, prekročili ste Vašu maximálnu dennú spotrebu plynu. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>', 'Prekročenie rezervovanej kapacity (EE)', '', (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_DMM' and version = 1), 'SK', 'ACTIVE');

-- AM
INSERT INTO notification_template
(uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms, execution_type, report_customer_column, report_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_AM_CHECK_RK', 'ACTIVE', 'CUSTOMER_NOTICE', 'Notifikácia prekročenia rezervovanej kapacity (EE) pre account managera', 'LOW', 'Notifikácia prekročenia rezervovanej kapacity (EE) pre account managera', null, 'LIMIT_OVERFLOW', true, false, 'AUTOMATIC', null, null);
INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'Prekročenie rezervovanej kapacity (EE)', 'Vážený manažér predaja, zákazník priradený ku Vám prekročil rezervovanú kapacitu pre spotrebu elektrickej energie. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>', 'Prekročenie rezervovanej kapacity (EE)', '', (select uuid from notification_template where code = 'DELIVERY_POINT_AM_CHECK_RK' and version = 1), 'SK', 'ACTIVE');

INSERT INTO notification_template
(uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms, execution_type, report_customer_column, report_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_AM_CHECK_MRK', 'ACTIVE', 'CUSTOMER_NOTICE', 'Notifikácia prekročenia maximálnej rezervovanej kapacity (EE) pre account managera', 'LOW', 'Notifikácia prekročenia maximálnej rezervovanej kapacity (EE) pre account managera', null, 'LIMIT_OVERFLOW', true, false, 'AUTOMATIC', null, null);
INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'Prekročenie maximálnej rezervovanej kapacity (EE)', 'Vážený manažér predaja, zákazník priradený ku Vám prekročil maximálnu rezervovanú kapacitu pre spotrebu elektrickej energie. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>', 'Prekročenie maximálnej rezervovanej kapacity (EE)', '', (select uuid from notification_template where code = 'DELIVERY_POINT_AM_CHECK_MRK' and version = 1), 'SK', 'ACTIVE');

INSERT INTO notification_template
(uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms, execution_type, report_customer_column, report_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_AM_CHECK_DMM', 'ACTIVE', 'CUSTOMER_NOTICE', 'Notifikácia prekročenia maximálnej dennej spotreby plynu (ZP) pre account managera', 'LOW', 'Notifikácia prekročenia maximálnej dennej spotreby plynu (ZP) pre account managera', null, 'LIMIT_OVERFLOW', true, false, 'AUTOMATIC', null, null);
INSERT INTO notification_template_i18n
(uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale, status)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'Prekročenie maximálnej dennej spotreby plynu (ZP)', 'Vážený manažér predaja, zákazník priradený ku Vám prekročil maximálnu dennú spotrebu plynu. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>', 'Prekročenie maximálnej dennej spotreby plynu', '', (select uuid from notification_template where code = 'DELIVERY_POINT_AM_CHECK_DMM' and version = 1), 'SK', 'ACTIVE');


-- CUSTOMER_REQUEST_CANCEL_REQUEST
update notification_template_i18n
set email_body = 'Vážený zákazník, žiadosť s názvom ${customerRequest.name} bola zrušená. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>'
where notification_template_i18n.notification_template_id = (select uuid from notification_template nt where nt.code = 'CUSTOMER_REQUEST_CANCEL_REQUEST');


-- CUSTOMER_INVITATION
update notification_template_i18n
set email_body = 'Vážený zákazník, vitajte na portáli SPP, na Váš telefón sme zaslali vygenerované jednorazové heslo, ktoré môžete použiť pri prihlásení do systému: ${portalExternalUrl}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>'
where notification_template_i18n.notification_template_id = (select uuid from notification_template nt where nt.code = 'CUSTOMER_INVITATION');
