-----------------------------------------------------------------------------------------------
---------------- ADD AuditLogCodes - BP pairing automatic
-----------------------------------------------------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_PAIRING_REQUEST_AUTOMATIC', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Automaticky iniciované párovanie obchodného partnera', null, (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_REQUEST_AUTOMATIC' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Automaticky iniciované párovanie obchodného partnera', null, (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_REQUEST_AUTOMATIC' and type = 'AUDIT_LOG_CODE'));


INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Dokončené automatické párovanie obchodného partnera', null, (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Dokončené automatické párovanie obchodného partnera', null, (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC' and type = 'AUDIT_LOG_CODE'));

