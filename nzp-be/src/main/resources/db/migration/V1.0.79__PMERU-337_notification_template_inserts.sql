-- insert values for notification_template
INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_LOCK', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Zablokovanie pristupu zakaznickeho uctu', 'LOW', 'Notifikacia ohladom zablokovania pristupu zakaznickeho uctu', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Zablokovanie prístupu', 'Vážený zákazník, prístup k Vášmu účtu bol zablokovaný z dôvodu ${attributes.lockReason}. Vaše <PERSON>', '<PERSON><PERSON>lo<PERSON><PERSON> prístupu', '<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>azník, prístup k Vášmu účtu bol zablokovaný z dôvodu ${attributes.lockReason}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_LOCK' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'EMPLOYEE_LOCK', 'ACTIVE', 'EMPLOYEE_SYSTEM', 'Zablokovanie pristupu zamestnaneckeho uctu', 'LOW', 'Notifikacia ohladom zablokovania pristupu zamestnaneckeho uctu', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Zablokovanie prístupu', 'Vážený zamestnanec, prístup k Vášmu účtu bol zablokovaný z dôvodu ${attributes.lockReason}. Vaše SPP', 'Zablokovanie prístupu', 'Vážený zamestnanec, prístup k Vášmu účtu bol zablokovaný z dôvodu ${attributes.lockReason}. Vaše SPP', (select uuid from notification_template where code = 'EMPLOYEE_LOCK' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REGISTRATION_REQUEST', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Zaslanie registračného kódu pri registrácii', 'HIGH', 'Notifikacia pre poslanie registracneho verifikacneho kodu', null, null, false, true);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Registrácia zákazníka', 'Vážený zákazník, pre potvrdenie registrácie kliknite na túto URL: https://meru-nzp-dev.isdd.sk/confirm-registration?challangeCodeUuid=${attributes.challengeCodeUuid}&challangeCode=${attributes.challangeCode}. Platnosť kódu je do ${attributes.challengeCodeValidTo}. Vaše SPP', 'Registrácia zákazníka', 'Vážený zákazník, toto je Váš verifikačný kód pre potvrdenie emailovej adresy: ${attributes.challengeCode}. Jeho platnosť je do ${attributes.challengeCodeValidTo}. Jeho ID=${attributes.challengeCodeUuid}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_REQUEST' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REGISTRATION_SUCCESS', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Dokoncenie registracie zakaznika', 'LOW', 'Notifikacia o dokonceni registracie zakaznika', null, null, false, true);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Registrácia zákazníka', 'Vážený zákazník, Vaša registrácia bola úspešná. Pre dokončenie registrácie kliknite na túto URL: URL${attributes.challengeCode}. Platnosť linku je do ${attributes.challengeCodeValidTo}. Jeho ID bolo ${attributes.challengeCodeUuid}. Vaše SPP', 'Registrácia zákazníka', 'Vážený zákazník, Vaša registrácia bola úspešná. Používali ste verifikačný kód ${attributes.challengeCode}, ktorý mal platnosť do ${attributes.challengeCodeValidTo}. Jeho ID bolo ${attributes.challengeCodeUuid}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_REGISTRATION_SUCCESS' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_PASSWORD_RECOVERY_REQUEST', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Poziadavka na zmenu hesla', 'HIGH', 'Notifikacia o poziadavke na zmenu hesla', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Obnova hesla', 'Vážený zákazník, pre potvrdenie obnovy hesla kliknite na túto URL: https://meru-nzp-dev.isdd.sk/confirm-password-recovery?challangeCodeUuid=${attributes.challangeCodeUuid}&challangeCode=${attributes.challangeCode}. Platnosť kódu je do ${attributes.challengeCodeValidTo}. Jeho ID=${attributes.challengeCodeUuid}. Vaše SPP', 'Obnova hesla', 'Vážený zákazník, toto je Váš verifikačný kód pre obnovu hesla: ${attributes.challengeCode}. Jeho platnosť je do ${attributes.challengeCodeValidTo}. Jeho ID=${attributes.challengeCodeUuid}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_REQUEST' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_ACTIVATION_SUCCESS', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Aktivacia uctu zo strany SPP', 'LOW', 'Notifikacia ohladom activacie uctu zo strany SPP', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Aktivácia účtu', 'Vážený zákazník, Váš účet bol úspešne aktivovaný. Vaše SPP', 'Aktivácia účtu', 'Vážený zákazník, Váš účet bol úspešne aktivovaný. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_ACTIVATION_SUCCESS' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_DEACTIVATION_SUCCESS', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Deaktivacia uctu zo strany SPP', 'LOW', 'Notifikacia ohladom deactivacie uctu zo strany SPP', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Deaktivácia účtu', 'Vážený zákazník, Váš účet bol úspešne deaktivovaný. Dôvod deaktivácie je ${attributes.reason}. Vaše SPP', 'Deaktivácia účtu', 'Vážený zákazník, Váš účet bol úspešne deaktivovaný. Dôvod deaktivácie je ${attributes.reason}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_DEACTIVATION_SUCCESS' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_DELETE_REQUEST', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Verifikacna poziadavka pre odstranenie customera', 'LOW', 'Notifikacia ohladom verifikacnej poziadavky pre odstranenie customera', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Vymazanie účtu', 'Vážený zákazník, pre potvrdenie vymazania účtu kliknite na túto URL: https://meru-nzp-dev.isdd.sk/confirm-account-removal?challangeCodeUuid=${attributes.challangeCodeUuid}&challangeCode=${attributes.challangeCode}. Platnosť kódu je do ${attributes.challengeCodeValidTo}. Vaše SPP', 'Vymazanie účtu', 'Vážený zákazník, toto je Váš verifikačný kód pre vymazanie účtu: ${attributes.challengeCode}. Jeho platnosť je do ${attributes.challengeCodeValidTo}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_DELETE_REQUEST' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_DELETE_SUCCESS', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Vymazanie uctu zo strany SPP', 'LOW', 'Notifikacia ohladom vymazu uctu zo strany SPP', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Vymazanie účtu', 'Vážený zákazník, Váš účet bol úspešne vymazaný. Vaše SPP', 'Vymazanie účtu', 'Vážený zákazník, Váš účet bol úspešne vymazaný. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_DELETE_SUCCESS' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_PASSWORD_CHANGE_REQUEST', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Verifikacna poziadavka na zmenu hesla', 'HIGH', 'Notifikacia ohladom verifikacnej poziadavky na zmenu hesla', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Zmena hesla', 'Vážený zákazník, pre potvrdenie zmeny hesla kliknite na túto URL: URL${attributes.challengeCode}. Platnosť kódu je do ${attributes.challengeCodeValidTo}. Vaše SPP', 'Zmena hesla', 'Vážený zákazník, toto je Váš verifikačný kód pre zmenu hesla: ${attributes.challengeCode}. Jeho platnosť je do ${attributes.challengeCodeValidTo}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_REQUEST' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_PASSWORD_CHANGE_SUCCESS', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Dokončená zmena hesla', 'LOW', 'Notifikacia ohladom uspesnej zmeny hesla', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Zmena hesla', 'Vážený zákazník, Vaša zmena hesla bola úspešná. Pre dokončenie kliknite na túto URL: URL${attributes.challengeCode}. Platnosť linku je do ${attributes.challengeCodeValidTo}. Vaše SPP', 'Zmena hesla', 'Vážený zákazník, Vaša zmena hesla bola úspešná. Používali ste verifikačný kód ${attributes.challengeCode}, ktorý mal platnosť do ${attributes.challengeCodeValidTo}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_CHANGE_SUCCESS' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------
---- ***** DEFAULT SMS ******
INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_EMAIL_CHANGE_REQUEST', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Verifikačná požiadavka na zmenu emailu', 'HIGH', 'Notifikacia ohladom verifikacnej poziadavky na zmenu emailu', null, null, false, true);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Zmena mailovej adresy', 'Vážený zákazník, pre potvrdenie zmeny emailu kliknite na túto URL: https://meru-nzp-dev.isdd.sk/confirm-email-change?challangeCodeUuid=${attributes.challangeCodeUuid}&challangeCode=${attributes.challangeCode}. Platnosť kódu je do ${attributes.challengeCodeValidTo}. Vaše SPP', 'Zmena mailovej adresy', 'Vážený zákazník, toto je Váš verifikačný kód pre zmenu emailu: ${attributes.challengeCode}. Jeho platnosť je do ${attributes.challengeCodeValidTo}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_REQUEST' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------
---- ***** DEFAULT SMS ******
INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_EMAIL_CHANGE_SUCCESS', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Dokončená zmena emailu', 'LOW', 'Notifikacia ohladom uspesnej zmeny emailu', null, null, false, true);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Zmena mailovej adresy', 'Vážený zákazník, Vaša zmena mailovej adresy bola úspešná. Pre dokončenie kliknite na túto URL: URL${attributes.challengeCode}. Platnosť linku je do ${attributes.challengeCodeValidTo}. Vaše SPP', 'Zmena mailovej adresy', 'Vážený zákazník, Vaša zmena mailovej adresy bola úspešná. Používali ste verifikačný kód ${attributes.challengeCode}, ktorý mal platnosť do ${attributes.challengeCodeValidTo}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_EMAIL_CHANGE_SUCCESS' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_PHONE_CHANGE_REQUEST', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Verifikačná požiadavka na zmenu telefónneho čísla', 'HIGH', 'Notifikacia ohladom verifikacnej poziadavky na zmenu telefonneho cisla', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Zmena telefónneho čísla', 'Vážený zákazník, pre potvrdenie zmeny telefónneho čísla kliknite na túto URL: URL${attributes.challengeCode}. Platnosť kódu je do ${attributes.challengeCodeValidTo}. Vaše SPP', 'Zmena telefónneho čísla', 'Vážený zákazník, toto je Váš verifikačný kód pre zmenu telefónneho čísla: ${attributes.challengeCode}. Jeho platnosť je do ${attributes.challengeCodeValidTo}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_REQUEST' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_PHONE_CHANGE_SUCCESS', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Dokončená zmena telefónneho čísla', 'LOW', 'Notifikacia ohladom uspesnej zmeny telefonneho cisla', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Zmena telefónneho čísla', 'Vážený zákazník, Vaša zmena telefónneho čísla bola úspešná. Vaše SPP', 'Zmena telefónneho čísla', 'Vážený zákazník, Vaša zmena telefónneho čísla bola úspešná. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_PHONE_CHANGE_SUCCESS' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SHARING_OWNER_GRANT', 'ACTIVE', 'CUSTOMER_NOTICE', 'Notifikacia o nastaveni zdielania pre danu entitu vlastnikovi entity', 'LOW', 'Notifikacia o nastaveni zdielania pre danu entitu vlastnikovi entity', null, 'SHARING', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Nastavenie zdieľania', 'Vážený zákazník, práve ste nastavili zdieľanie zákazníkovi ${attributes.sharingTo}. Vaše SPP', 'Nastavenie zdieľania', 'Vážený zákazník, práve ste nastavili zdieľanie zákazníkovi ${attributes.sharingTo}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_GRANT' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SHARING_CONSUMER_GRANT', 'ACTIVE', 'CUSTOMER_NOTICE', 'Notifikacia o nastaveni zdielania pre danu entitu konzumerovi entity', 'LOW', 'Notifikacia o nastaveni zdielania pre danu entitu konzumerovi entity', null, 'SHARING', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Nastavenie zdieľania', 'Vážený zákazník, práve Vám bolo nastavené zdieľanie od zákazníka ${attributes.sharingFrom}. Vaše SPP', 'Nastavenie zdieľania', 'Vážený zákazník, práve Vám bolo nastavené zdieľanie od zákazníka ${attributes.sharingFrom}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_GRANT' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SHARING_OWNER_REVOKE', 'ACTIVE', 'CUSTOMER_NOTICE', 'Notifikacia o odobrati zdielania pre danu entitu vlastnikovi entity', 'LOW', 'Notifikacia o odobrati zdielania pre danu entitu vlastnikovi entity', null, 'SHARING', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Zrušenie zdieľania', 'Vážený zákazník, práve ste zrušili zdieľanie zákazníkovi ${attributes.sharingTo}. Vaše SPP', 'Zrušenie zdieľania', 'Vážený zákazník, práve ste zrušili zdieľanie zákazníkovi ${attributes.sharingTo}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_SHARING_OWNER_REVOKE' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'ACTIVE', 'CUSTOMER_NOTICE', 'Notifikacia o odobrati zdielania pre danu entitu konzumerovi entity', 'LOW', 'Notifikacia o odobrati zdielania pre danu entitu konzumerovi entity', null, 'SHARING', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Zrušenie zdieľania', 'Vážený zákazník, práve Vám bolo zrušené zdieľanie od zákazníka ${attributes.sharingFrom}. Vaše SPP', 'Zrušenie zdieľania', 'Vážený zákazník, práve Vám bolo zrušené zdieľanie od zákazníka ${attributes.sharingFrom}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_SHARING_CONSUMER_REVOKE' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REQUEST_REGISTER', 'ACTIVE', 'CUSTOMER_NOTICE', 'Zaregistrovanie žiadosti', 'LOW', 'Notifikacia ohladom zaregistrovania ziadosti, cize v case ked sa dostava do BE SAP a ma pridelene cislo', null, 'CUSTOMER_REQUEST', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Zaregistrovanie žiadosti', 'Vážený zákazník, práve bola zaregistrovaná žiadosť ${attributes.customerRequest}. Vaše SPP', 'Zaregistrovanie žiadosti', 'Vážený zákazník, práve bola zaregistrovaná žiadosť ${attributes.customerRequest}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_REQUEST_REGISTER' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REQUEST_STATUS_CHANGE', 'ACTIVE', 'CUSTOMER_NOTICE', 'Zmena stavu žiadosti', 'LOW', 'Notifikacia ohladom zmeny stavu pre ziadost', null, 'CUSTOMER_REQUEST', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Zmena stavu žiadosti', 'Vážený zákazník, žiadosť zmenila stav: ${attributes.customerRequest}. Vaše SPP', 'Zmena stavu žiadosti', 'Vážený zákazník, žiadosť zmenila stav: ${attributes.customerRequest}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_REQUEST_STATUS_CHANGE' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REQUEST_NOTE_CREATE', 'ACTIVE', 'CUSTOMER_NOTICE', 'Pridanie poznámky k žiadosti', 'LOW', 'Notifikacia ohladom pridania poznamky k ziadosti', null, 'CUSTOMER_REQUEST', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Pridanie poznámky k žiadosti', 'Vážený zákazník, k žiadosti ${attributes.customerRequest} bola pridaná poznámka ${attributes.note}. Vaše SPP', 'Pridanie poznámky k žiadosti', 'Vážený zákazník, k žiadosti ${attributes.customerRequest} bola pridaná poznámka ${attributes.note}. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_REQUEST_NOTE_CREATE' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------
INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_PAIRING_REQUEST', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Požiadavka na párovanie obchodného partnera', 'LOW', 'Notifikacia ohladom poziadavky na parovanie Obchodneho partnera', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Párovanie obchodného partnera', 'Vážený zákazník, žiadosť o párovanie obchodného partnera bola zaevidovaná. Vaše SPP', 'Párovanie obchodného partnera', 'Vážený zákazník, žiadosť o párovanie obchodného partnera bola zaevidovaná. Vaše SPP', (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_REQUEST' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------
INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Dokončené párovanie obchodného partnera', 'LOW', 'Notifikacia ohladom uspesneho parovania obchodneho partnera.', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Párovanie obchodného partnera', 'Vážený zákazník, obchodný partner bol úspešne napárovaný. Vaše SPP', 'Párovanie obchodného partnera', 'Vážený zákazník, obchodný partner bol úspešne napárovaný. Vaše SPP', (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Požiadavka na odpárovanie obchodného partnera', 'LOW', 'Notifikacia ohladom poziadavky na odparovanie Obchodneho partnera', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Odpárovanie obchodného partnera', 'Vážený zákazník, žiadosť o odpárovanie obchodného partnera bola zaevidovaná. Vaše SPP', 'Odpárovanie obchodného partnera', 'Vážený zákazník, žiadosť o odpárovanie obchodného partnera bola zaevidovaná. Vaše SPP', (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_REQUEST' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Dokončené odpárovanie obchodného partnera', 'LOW', 'Notifikacia ohladom uspesneho odparovania obchodneho partnera.', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Odpárovanie obchodného partnera', 'Vážený zákazník, obchodný partner bol úspešne odpárovaný. Vaše SPP', 'Odpárovanie obchodného partnera', 'Vážený zákazník, obchodný partner bol úspešne odpárovaný. Vaše SPP', (select uuid from notification_template where code = 'BUSINESS_PARTNER_UNPAIRING_SUCCESS' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Overenie cisla obchodneho partnera pri parovani', 'HIGH', 'Notifikacia ohladom poslania sms pre overenie BP cisla pri parovani.', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Overenie čísla obchodného partnera', 'Vážený zákazník, pre overenie čísla obchodného partnera kliknite na túto URL: URL${attributes.challengeCode}. Platnosť kódu je do ${attributes.challengeCodeValidTo}. Jeho ID=${attributes.challengeCodeUuid}. Vaše SPP', 'Overenie čísla obchodného partnera', 'Vážený zákazník, toto je Váš verifikačný kód pre overenie čísla obchodného partnera: ${attributes.challengeCode}. Jeho platnosť je do ${attributes.challengeCodeValidTo}. Jeho ID=${attributes.challengeCodeUuid}. Vaše SPP', (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_CHALLENGE' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_PAIRING_VERIFY', 'ACTIVE', 'CUSTOMER_NOTICE', 'Overenia parovania pri individualnej obsluhe', 'LOW', 'Notifikacia ohladom overenia parovania pri individualnej obsluhe', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Overenie párovania', 'Vážený zákazník, prebieha overenie obchodného partnera počas jeho párovania v rámci individuálnej obsluhy. Vaše SPP', 'Overenie párovania', 'Vážený zákazník, prebieha overenie obchodného partnera počas jeho párovania v rámci individuálnej obsluhy. Vaše SPP', (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_VERIFY' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_INVOICE_ISSUED', 'ACTIVE', 'CUSTOMER_NOTICE', 'Vystavenie faktúry', 'LOW', 'Notifikacia vystavenej faktury', null, 'INVOICE', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Vystavenie faktúry', 'Vážený zákazník, faktúra bola vystavená. Vaše SPP', 'Vystavenie faktúry', 'Vážený zákazník, faktúra bola vystavená. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ISSUED' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'ACTIVE', 'CUSTOMER_NOTICE', 'Vrátenie peňazí', 'LOW', 'Notifikacia vratenia penazi', null, 'INVOICE', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Vrátenie peňazí', 'Vážený zákazník, Váš preplatok Vám bol odoslaný. Vaše SPP', 'Vrátenie peňazí', 'Vážený zákazník, Váš preplatok Vám bol odoslaný. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_INVOICE_CREDIT_ISSUED' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'ACTIVE', 'CUSTOMER_NOTICE', 'Preddavok', 'LOW', 'Notifikacia preddavku', null, 'INVOICE', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Preddavok', 'Vážený zákazník, prijali sme uhradenie preddavku. Vaše SPP', 'Preddavok', 'Vážený zákazník, prijali sme uhradenie preddavku. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_ISSUED' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_INVOICE_BEFORE_DUE', 'ACTIVE', 'CUSTOMER_NOTICE', 'Blížiaca sa splatnosť faktúry', 'LOW', 'Notifikacia bliziacej sa splatnosti faktury', null, 'INVOICE', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Blížiaca sa splatnosť faktúry', 'Vážený zákazník, blíži sa splatnosť Vašej faktúry. Vaše SPP', 'Blížiaca sa splatnosť faktúry', 'Vážený zákazník, blíži sa splatnosť Vašej faktúry. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_INVOICE_BEFORE_DUE' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'ACTIVE', 'CUSTOMER_NOTICE', 'Blížiaca sa splatnosť preddavku', 'LOW', 'Notifikacia bliziacej sa splatnosti preddavku', null, 'INVOICE', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Blížiaca sa splatnosť preddavku', 'Vážený zákazník, blíži sa splatnosť Vášho preddavku. Vaše SPP', 'Blížiaca sa splatnosť preddavku', 'Vážený zákazník, blíži sa splatnosť Vášho preddavku. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY', 'ACTIVE', 'CUSTOMER_NOTICE', 'Prekročenie rezervovanej kapacity (EE)', 'LOW', 'Notifikacia prekrocenia rezervovanej kapacity (EE)', null, 'LIMIT_OVERFLOW', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Prekročenie rezervovanej kapacity (EE)', 'Vážený zákazník, prekročili ste Vašu rezervovanú kapacitu. Vaše SPP', 'Prekročenie rezervovanej kapacity (EE)', 'Vážený zákazník, prekročili ste Vašu rezervovanú kapacitu. Vaše SPP', (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_RESERVED_CAPACITY' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY', 'ACTIVE', 'CUSTOMER_NOTICE', 'Prekročenie maximálnej rezervovanej kapacity (EE)', 'LOW', 'Notifikacia prekrocenia maximalnej rezervovanej kapacity (EE)', null, 'LIMIT_OVERFLOW', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Prekročenie maximálnej rezervovanej kapacity (EE)', 'Vážený zákazník, prekročili ste Vašu maximálnu rezervovanú kapacitu pre elektrinu. Vaše SPP', 'Prekročenie maximálnej rezervovanej kapacity (EE)', 'Vážený zákazník, prekročili ste Vašu maximálnu rezervovanú kapacitu pre elektrinu. Vaše SPP', (select uuid from notification_template where code = 'DELIVERY_POINT_CHECK_MAXIMUM_RESERVED_CAPACITY' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'METER_READING_CUSTOMER', 'ACTIVE', 'CUSTOMER_NOTICE', 'Vykonanie certifikovaného merania', 'LOW', 'Notifikácia ohľadom potreby vykonania certifikovaného merania', null, 'CUSTOMER_REQUEST', true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Certifikované meranie', 'Vážený zákazník, vykonajte prosím odpočet z Vášho merača a zadajte to do systému. Vaše SPP', 'Certifikované meranie', 'Vážený zákazník, vykonajte prosím odpočet z Vášho merača a zadajte to do systému. Vaše SPP', (select uuid from notification_template where code = 'METER_READING_CUSTOMER' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_INVITATION', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Pozyvaci email na prihlasenie do NZP', 'LOW', 'Notifikacia ohladom odoslania pozyvacieho emailu na prihlasenie do NZP', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Odoslanie pozývacieho emailu', 'Vážený zákazník, na Váš email bol odoslaný pozývací email na prihlásenie do NZP. Vaše SPP', 'Odoslanie pozývacieho emailu', 'Vážený zákazník, na Váš email bol odoslaný pozývací email na prihlásenie do NZP. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_INVITATION' and version = 1), 'SK');

----------------------------------------------------------------------------------------------------------------------------------------------------------

INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_INVITATION_PASSWORD', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Vygenerovanie jednorazoveho hesla zakaznikovi v SMS', 'HIGH', 'Odoslanie vygenerovaneho jednorazoveho hesla zakaznikovi v SMS', null, null, true, false);
INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, header, email_body, email_subject, sms_body, notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'Jednorazové heslo', 'Vážený zákazník, bolo Vám vygenerované jednorazové heslo pre prihlásenie do NZP: ${attributes.generatedPassword}. Po prvom použití bude potrebné heslo zmeniť. Vaše SPP', 'Jednorazové heslo', 'Vážený zákazník, bolo Vám vygenerované jednorazové heslo pre prihlásenie do NZP: ${attributes.generatedPassword}. Po prvom použití bude potrebné heslo zmeniť. Vaše SPP', (select uuid from notification_template where code = 'CUSTOMER_INVITATION_PASSWORD' and version = 1), 'SK');
