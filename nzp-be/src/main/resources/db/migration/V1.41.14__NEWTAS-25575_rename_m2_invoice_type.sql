-- Rename GCL i18ns "Nekomoditný produkt" -> "Čistá elektrina"
UPDATE generic_code_list_i18n
SET name = 'Čistá elektrina', updated_at = now()
WHERE code_list_uuid = (
    SELECT uuid FROM generic_code_list
    WHERE type = 'INVOICE_TYPE'
    AND code = 'M2')
AND locale = 'SK';

UPDATE generic_code_list_i18n
SET name = '[EN] Čistá elektrina', updated_at = now()
WHERE code_list_uuid = (
    SELECT uuid FROM generic_code_list
    WHERE type = 'INVOICE_TYPE'
    AND code = 'M2')
AND locale = 'EN';

-- Rename GCL i18ns "<PERSON><PERSON><PERSON><PERSON> stopka ZP" -> "<PERSON><PERSON><PERSON><PERSON> stopka"
UPDATE generic_code_list_i18n
SET name = '<PERSON><PERSON>ík<PERSON> stopka', updated_at = now()
WHERE code_list_uuid = (
    SELECT uuid FROM generic_code_list
    WHERE type = 'INVOICE_SUB_TYPE'
    AND code = 'NP_US')
AND locale = 'SK';

UPDATE generic_code_list_i18n
SET name = '[EN] Uhl<PERSON> stopka', updated_at = now()
WHERE code_list_uuid = (
    SELECT uuid FROM generic_code_list
    WHERE type = 'INVOICE_SUB_TYPE'
    AND code = 'NP_US')
AND locale = 'EN';
