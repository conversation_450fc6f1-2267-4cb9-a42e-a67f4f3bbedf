
-- create "pre-registration_request' table
CREATE TABLE registration_batch_request
(
    uuid                    uuid                      NOT NULL,
    created_at              TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at              TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                 INTEGER                   NOT NULL,
    created_by              CHARACTER VARYING(255)              NOT NULL,
    updated_by              CHARACTER VARYING(255)              NOT NULL,
    scheduled_at            TIMESTAMP WITH TIME ZONE,
    status                  CHARACTER VARYING(255)              NOT NULL,
    filename                CHARACTER VARYING(1000)             NOT NULL,
    filesize                INTEGER                             NOT NULL,
    filetype                CHARACTER VARYING(64)             NOT NULL,
    content                 BYTEA,
    report_content          BYTEA,
    report                  boolean                           NOT NULL,
    success_count           INTEGER,
    failed_count            INTEGER,
    retry_count             INTEGER,
    locked_by               CHARACTER VARYING(50),

    CONSTRAINT pk_pre_registration_request PRIMARY KEY (uuid)
)
WITH (
    OIDS = FALSE
);

-- create new access right 'CUSTOMERS_BATCH_REGISTRATION'
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue)
values ('CUSTOMERS_BATCH_IMPORT', current_timestamp, current_timestamp, 1, 'Hromadné registrovanie zakaznických účtov', '', false, false) ;

-- AUDIT log code
INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'EMPLOYEE_REGISTRATION_BATCH_REQUEST', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Hromadná registrácia zákazníka zamestnancom', null, (select uuid from generic_code_list where code like 'EMPLOYEE_REGISTRATION_BATCH_REQUEST' and type = 'AUDIT_LOG_CODE'));

