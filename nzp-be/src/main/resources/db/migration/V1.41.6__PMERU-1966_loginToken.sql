-- AUDIT log code
INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'LOGIN_CHALLENGE_CODE_EXPIRED', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Expirácia vygenerovaného tokenu zo SAP registrácie', null, (select uuid from generic_code_list where code like 'LOGIN_CHALLENGE_CODE_EXPIRED' and type = 'AUDIT_LOG_CODE'));

-- AUDIT log code
INSERT INTO generic_code_list
    (uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'LOGIN_CHALLENGE_CODE_INVALID', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
    (uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'sk_SK', 'Expirácia vygenerovaného tokenu zo SAP registrácie', null, (select uuid from generic_code_list where code like 'LOGIN_CHALLENGE_CODE_INVALID' and type = 'AUDIT_LOG_CODE'));


-- update notificationtemplte (URI)
UPDATE notification_template_i18n SET email_body = '<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"><p>v SPP robíme všetko pre to, aby sme Vám zjednodušili</p><p>to, čo potrebujete vybaviť.</p><p>Dôkazom našej snahy je aj nový zákaznícky portál</p><p>Moje SPP, ktorý Vám ponúka jednoduchý prehľad</p><p>Vašich odberných miest, faktúr, či umožňuje podať</p><p>žiadosť z pohodlia Vášho domova.</p><p>Pozývame Vás — objavte portál Moje SPP jediným</p><p>klikom a ušetrite čas dnes aj nabudúce.</p><p><a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName}&amp;lastName=${customer.lastName}&amp;type=pre_registration">Objaviť Moje SPP teraz</a></p></@spp.notification_email_template>'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_BATCH_REQUEST_REGISTRATION');

UPDATE notification_template_i18n SET email_body = '<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"><p>radi by sme Vám pripomenuli, že objaviť nový</p><p>zákaznícky portál Moje SPP je teraz oveľa</p><p>jednoduchšie.</p><p>Majte prehľad o Vašich službách z pohodlia Vášho</p><p>domova - stačí jeden klik ...</p><p><a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName}&amp;lastName=${customer.lastName}&amp;type=pre_registration">Objaviť Moje SPP teraz</a></p></@spp.notification_email_template>'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_BATCH_INVITATION_REMINDER');

UPDATE notification_template_i18n SET email_body = '<#import "spp.macros_sk.ftl" as spp> <@spp.notification_email_template oslovenie="${customer.firstName!?esc} ${customer.lastName!?esc}"><p>v SPP robíme všetko pre to, aby sme Vám zjednodušili</p><p>to, čo potrebujete vybaviť.</p><p>Dôkazom našej snahy je aj nový zákaznícky portál</p><p>Moje SPP, ktorý Vám ponúka jednoduchý prehľad</p><p>Vašich odberných miest, faktúr, či umožňuje podať</p><p>žiadosť z pohodlia Vášho domova.</p><p>Pozývame Vás — objavte portál Moje SPP jediným</p><p>klikom a ušetrite čas dnes aj nabudúce.</p><p><a href="${portalExternalUrl}/registration?challengeCode=${attributes.challengeCode}&amp;challengeCodeUuid=${attributes.challengeCodeUuid}&amp;email=${customer.email}&amp;firstName=${customer.firstName}&amp;lastName=${customer.lastName}&amp;type=pre_registration">Objaviť Moje SPP teraz</a></p></@spp.notification_email_template>'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_SAP_REGISTRATION_REQUEST');
