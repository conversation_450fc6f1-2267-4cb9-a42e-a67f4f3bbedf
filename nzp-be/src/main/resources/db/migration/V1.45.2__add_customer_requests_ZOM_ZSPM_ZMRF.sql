-- PMERU-2028, PMERU-2029

-- PMERU-2028 CREATE NEW TEMPLATE - ZOM_ZSPM
INSERT INTO customer_request_template(
    uuid, created_at, updated_at, version, status, code, type, link)
VALUES (
           uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZSPM', 'DIGITAL', null);

-- CREATE NEW TEMPLATE I18Ns
INSERT INTO customer_request_template_i18n (uuid, created_at, updated_at, version, locale, name, description, customer_request_template_uuid)
VALUES (
           uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Zmena spôsobu platby a fakturačného cyklu', 'Žiadosť o zmenu spôsobu platby a fakturačného cyklu', (
               SELECT uuid FROM customer_request_template
               WHERE code = 'ZOM_ZSPM'));

INSERT INTO customer_request_template_i18n (uuid, created_at, updated_at, version, locale, name, description, customer_request_template_uuid)
VALUES (
           uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Žiadosť odberné miesto - Zmena spôsobu platby a fakturačného cyklu', '[EN] Žiadosť o zmenu spôsobu platby a fakturačného cyklu', (
        SELECT uuid FROM customer_request_template
        WHERE code = 'ZOM_ZSPM'));

-- PMERU-2029 - CREATE NEW TEMPLATE - ZOM_ZMRF
INSERT INTO customer_request_template(
    uuid, created_at, updated_at, version, status, code, type, link)
VALUES (
           uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'ZOM_ZMRF', 'DIGITAL', null);

-- CREATE NEW TEMPLATE I18Ns
INSERT INTO customer_request_template_i18n (uuid, created_at, updated_at, version, locale, name, description, customer_request_template_uuid)
VALUES (
           uuid_generate_v4(), now(), now(), 1, 'SK', 'Žiadosť odberné miesto - Zmena fakturačného cyklu', 'Žiadosť o zmenu fakturačného cyklu', (
        SELECT uuid FROM customer_request_template
        WHERE code = 'ZOM_ZMRF'));

INSERT INTO customer_request_template_i18n (uuid, created_at, updated_at, version, locale, name, description, customer_request_template_uuid)
VALUES (
           uuid_generate_v4(), now(), now(), 1, 'EN', '[EN] Žiadosť odberné miesto - Zmena fakturačného cyklu', '[EN] Žiadosť o zmenu fakturačného cyklu', (
        SELECT uuid FROM customer_request_template
        WHERE code = 'ZOM_ZMRF'));

