-------------------
-- business_partner
-------------------

CREATE TABLE business_partner
(
    uuid          uuid                      NOT NULL,
    created_at    TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at    TIMESTAMP WITH TIME ZONE  NOT NULL,
    version       integer                   NOT NULL,
    
    external_id   CHARACTER VARYING(50)     NOT NULL,
    status        CHARACTER VARYING(20)     NOT NULL,
    type          CHARACTER VARYING(50)     NOT NULL,
    category      CHARACTER VARYING(50)     NOT NULL,
    queue         CHARACTER VARYING(50)     NOT NULL,

    CONSTRAINT pk_business_partner PRIMARY KEY (uuid)
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_business_partner_external_id on business_partner(external_id);
CREATE INDEX idx_business_partner_queue on business_partner(queue);



---------------------------
-- business_partner_contact
---------------------------

CREATE TABLE business_partner_contact
(
    uuid                  uuid                      NOT NULL,
    created_at            TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at            TIMESTAMP WITH TIME ZONE  NOT NULL,
    version               integer                   NOT NULL,
    
    external_id           CHARACTER VARYING(50)     NOT NULL,
    type                  CHARACTER VARYING(50)     NOT NULL,
    sex                   CHARACTER VARYING(50),
    email                 CHARACTER VARYING(64),
    phone                 CHARACTER VARYING(32),
    first_name            CHARACTER VARYING(100),
    last_name             CHARACTER VARYING(100),
    birth_number          CHARACTER VARYING(16),
    id_card_type          CHARACTER VARYING(50),
    id_card_number        CHARACTER VARYING(50),
    id_card_validity      DATE,
    
    business_partner_id   uuid                      NOT NULL,

    CONSTRAINT pk_business_partner_contact PRIMARY KEY (uuid),
    CONSTRAINT fk_business_partner_contact_business_partner FOREIGN KEY (business_partner_id)
        REFERENCES business_partner (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_business_partner_contact_external_id on business_partner_contact(external_id);
CREATE INDEX idx_business_partner_contact_business_partner_id on business_partner_contact(business_partner_id);
CREATE INDEX idx_business_partner_contact_email on business_partner_contact(email);



---------------------------
-- business_partner_address
---------------------------

CREATE TABLE business_partner_address
(
    uuid                  uuid                      NOT NULL,
    created_at            TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at            TIMESTAMP WITH TIME ZONE  NOT NULL,
    version               integer                   NOT NULL,
    
    external_id           CHARACTER VARYING(50)     NOT NULL,
    type                  CHARACTER VARYING(50)     NOT NULL,
    street                CHARACTER VARYING(64),
    number                CHARACTER VARYING(32),
    city                  CHARACTER VARYING(64),
    zip_code              CHARACTER VARYING(32),
    country               CHARACTER VARYING(64),
    
    business_partner_id   uuid                      NOT NULL,

    CONSTRAINT pk_business_partner_address PRIMARY KEY (uuid),
    CONSTRAINT fk_business_partner_address_business_partner FOREIGN KEY (business_partner_id)
        REFERENCES business_partner (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_business_partner_address_external_id on business_partner_address(external_id);
CREATE INDEX idx_business_partner_address_business_partner_id on business_partner_address(business_partner_id);



----------------------------
-- business_partner_approval
----------------------------

CREATE TABLE business_partner_approval
(
    uuid                  uuid                      NOT NULL,
    created_at            TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at            TIMESTAMP WITH TIME ZONE  NOT NULL,
    version               integer                   NOT NULL,
    
    type                  CHARACTER VARYING(50)     NOT NULL,
    approval              boolean   			    NOT NULL,
    
    business_partner_id   uuid                      NOT NULL,

    CONSTRAINT pk_business_partner_approval PRIMARY KEY (uuid),
    CONSTRAINT fk_business_partner_approval_business_partner FOREIGN KEY (business_partner_id)
        REFERENCES business_partner (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_business_partner_approval_business_partner_id on business_partner_approval(business_partner_id);