-- set SPP customer request cancel notification subject
update notification_template_i18n
set email_subject = '${customerRequest.externalId}/${customerRequest.code}/${businessPartner.externalId}/${businessPartner.kindCode}/Zrušená'
where notification_template_id = (select uuid from notification_template where code like 'CUSTOMER_REQUEST_CANCEL_REQUEST');

-- add businessPartner.externalId attributes to related notification templates
insert into notification_template_variable
select nt.uuid, now(), now(), 1, 'businessPartner.externalId', 'SAP ID obchodného partnera', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER', 'EPAY_TRANSACTION_FINISHED', 'CUSTOMER_INVOICE_ISSUED', 'CUSTOMER_SHARING_OWNER_GRANT', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'CUSTOMER_SHARING_OWNER_REVOKE', 'CUSTOMER_SHARING_CONSUMER_REVOKE', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'BUSINESS_PARTNER_PAIRING_REQUEST', 'BUSINESS_PARTNER_PAIRING_SUCCESS', 'BUSINESS_PARTNER_UNPAIRING_REQUEST', 'BUSINESS_PARTNER_UNPAIRING_SUCCESS', 'BUSINESS_PARTNER_PAIRING_CHALLENGE', 'BUSINESS_PARTNER_PAIRING_VERIFY', 'CUSTOMER_INVOICE_CREDIT_ISSUED', 'CUSTOMER_INVOICE_ADVANCE_ISSUED', 'CUSTOMER_INVOICE_BEFORE_DUE', 'CUSTOMER_INVOICE_ADVANCE_BEFORE_DUE', 'CUSTOMER_SHARING_INVITATION', 'CUSTOMER_REQUEST_CANCEL_REQUEST', 'CUSTOMER_INVOICE_SAP_ISSUED', 'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_ZM', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_MRK', 'BUSINESS_PARTNER_VERIFY_REVOKE');

-- add customerRequest.code attributes to related notification templates
insert into notification_template_variable
select nt.uuid, now(), now(), 1, 'customerRequest.code', 'Kód žiadosti', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_CANCEL_REQUEST', 'ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE');

-- remove old cancel email for notifications
delete from config_parameter where id like 'customer.request.cancelEmail';

-- add new cancel emails for notifications
insert into config_parameter (created_at, updated_at, version, id, value, target, type) values
(now(), now(), 1, 'customer.request.cancelEmail.home', '<EMAIL>', 'UI', 'STRING'),
(now(), now(), 1, 'customer.request.cancelEmail.organisation', '<EMAIL>', 'UI', 'STRING');