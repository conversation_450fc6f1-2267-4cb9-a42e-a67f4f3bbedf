-----------------
-- notification_template_variable-changes
-----------------
insert into notification_template_variable select (select uuid from notification_template where code = 'METER_READING_CUSTOMER'), now(), now(), 1, ntv.variable, ntv.name, ntv.description, ntv.type, ntv.notification_template_execution_type, uuid_generate_v4() from notification_template_variable ntv
where ntv.notification_template_uuid = (select uuid from notification_template where code = 'BUSINESS_PARTNER_PAIRING_SUCCESS')
  and variable not like '%customer.%' and variable not like 'target.%' and variable not like 'notification%' and variable not like 'portalExternalUrl' and variable not like 'locale';

-- add UnitedDeliveryPoint variable to all notification templates that have DeliveryPoint set
insert into notification_template_variable select ntv.notification_template_uuid, now(), now(), 1, 'unitedDeliveryPoint.id', 'ID zlúčeného odbern<PERSON>ho miesta', 'Technický identifikátor pre zl<PERSON><PERSON>ené odberné miesto', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template_variable ntv
where ntv.variable = 'deliveryPoint.type';

-- METER_READING_CUSTOMER - update template body
update notification_template_i18n
set email_body = '<#import "spp.macros_sk.ftl" as spp>
<@spp.notification_email_template oslovenie="">v týchto dňoch máte možnosť nahlásiť samoodpočet spotreby na odbernom mieste typu ${deliveryPoint.type} s adresou ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city}.<br>Vykonanie samoodpočtu online je jednoduché a má veľkú výhodu — nemusíte čakať na odpočtára.<br>Odčítajte, prosím, stav na svojom meradle a zadajte ho do portálového účtu.<br><a href="${portalExternalUrl}/delivery-points/${unitedDeliveryPoint.id}/self-read">Nahlásiť stav meradla</a></@spp.notification_email_template>'
where notification_template_id = (select nt.uuid from notification_template nt where code = 'METER_READING_CUSTOMER')
and locale = 'SK';


update notification_template_i18n
set email_body = '<#import "spp.macros_en.ftl" as spp>
<@spp.notification_email_template oslovenie="">[EN] v týchto dňoch máte možnosť nahlásiť samoodpočet spotreby na odbernom mieste typu ${deliveryPoint.type} s adresou ${deliveryPoint.street} ${deliveryPoint.streetNumber}, ${deliveryPoint.city}.<br>Vykonanie samoodpočtu online je jednoduché a má veľkú výhodu — nemusíte čakať na odpočtára.<br>Odčítajte, prosím, stav na svojom meradle a zadajte ho do portálového účtu.<br><a href="${portalExternalUrl}/delivery-points/${unitedDeliveryPoint.id}/self-read">Nahlásiť stav meradla</a></@spp.notification_email_template>'
where notification_template_id = (select nt.uuid from notification_template nt where code = 'METER_READING_CUSTOMER')
  and locale = 'EN';
