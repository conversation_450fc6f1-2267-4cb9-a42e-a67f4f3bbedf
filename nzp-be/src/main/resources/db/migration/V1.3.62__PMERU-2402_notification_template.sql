INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, execution_type,
    description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_EMAIL_ALREADY_REGISTERED', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Email už je zaregistrovaný', 'HIGH', 'AUTOMATIC',
    'Notifikacia email už je zaregistrovaný', null, null, true, false);

INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, status, header, email_body,
    email_subject, sms_body,
    notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'Email už je zaregistrovaný',
    'Vážený zákazník, požiadavka na zmenu emailu bola zamietnuta, email už je zaregistrovaný. Ak ste túto akciu <PERSON>, prosím z dôvodu ochrany vasho účtu nahláste podozrivú aktivitu. Vaše SPP <br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>',
    'Zmena emailovej adresy. Existujúci účet',
    null, (select uuid from notification_template where code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED' and version = 1), 'SK');

INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, status, header, email_body,
    email_subject, sms_body,
    notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', '[EN] Email už je zaregistrovaný',
    '[EN] Vážený zákazník, požiadavka na zmenu emailu bola zamietnuta, email už je zaregistrovaný. Ak ste túto akciu nevykonal, prosím z dôvodu ochrany vasho účtu nahláste podozrivú aktivitu. Vaše SPP <br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>',
    '[EN] Zmena emailovej adresy. Existujúci účet',
    null, (select uuid from notification_template where code = 'CUSTOMER_EMAIL_ALREADY_REGISTERED' and version = 1), 'EN');