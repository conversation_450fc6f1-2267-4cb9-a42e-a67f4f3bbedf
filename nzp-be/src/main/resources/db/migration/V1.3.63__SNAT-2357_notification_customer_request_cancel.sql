
-- new notification: CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER
INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, execution_type,
    description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Z<PERSON>š<PERSON><PERSON> žiadosti', 'LOW', 'AUTOMATIC',
    'Notifikácia o zrušení žiadosti pre zákazníka', null, null, true, false);

INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, status, header, email_body,
    email_subject, sms_body,
    notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', '<PERSON><PERSON>š<PERSON>e žiadosti z<PERSON>azníka',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>, ž<PERSON><PERSON><PERSON> s názvom ${customerRequest.name} bola zrušená. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>',
    'Zrušenie žiadosti zákazníka',
    null, (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER' and version = 1), 'SK');

INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, status, header, email_body,
    email_subject, sms_body,
    notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', '[EN] Zrušenie žiadosti zákazníka',
    '[EN] Vážený zákazník, žiadosť s názvom ${customerRequest.name} bola zrušená. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>',
    '[EN] Zrušenie žiadosti zákazníka',
    null, (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER' and version = 1), 'EN');

-- update text of CUSTOMER_REQUEST_CANCEL_REQUEST
update notification_template_i18n nti18n
set email_body = 'Dobrý deň, bolo vyžiadané zrušenie zákazníckej žiadosti s názvom ${customerRequest.name} a SAP ID: ${customerRequest.externalId}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>'
where nti18n.notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1)
 and nti18n.locale = 'SK';

update notification_template_i18n nti18n
set email_body = '[EN] Dobrý deň, bolo vyžiadané zrušenie zákazníckej žiadosti s názvom ${customerRequest.name} a SAP ID: ${customerRequest.externalId}. Vaše SPP<br/><br/><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>'
where nti18n.notification_template_id = (select uuid from notification_template where code = 'CUSTOMER_REQUEST_CANCEL_REQUEST' and version = 1)
  and nti18n.locale = 'EN';



-- common fields that are always present
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.email', 'Email zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.firstName', 'Meno zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.lastName', 'Priezvisko zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customer.phone', 'Telefónne číslo zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.email', 'Email zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.firstName', 'Meno zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.lastName', 'Priezvisko zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'logged.customer.phone', 'Telefónne číslo zalogovaného používateľa', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationTemplateCode', 'Kód notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'notificationUuid', 'Uuid notifikácie', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'portalExternalUrl', 'Externá vonkajšia URL na ktorej je spustený portál', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.email', 'Email cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.firstName', 'Meno cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.lastName', 'Priezvisko cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.customer.phone', 'Telefónne číslo cieleného zákazníka', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.createdAt', 'Dátum vytvorenia cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.type', 'Typ cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.updatedAt', 'Dátum poslednej aktualizácie cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.uuid', 'Id cielenej entity', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'target.entity.externalId', 'SAP id cielenej entity', 'Týka sa iba SAP entít.', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'locale', 'Lokalizácia', null, 'NUMBER', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');


-- target: BusinessPartner
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.name', 'Názov obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.firstName', 'Meno obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.lastName', 'Priezvisko obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.email', 'Email obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.phone', 'Telefonné číslo obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.street', 'Ulica obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.streetNumber', 'Číslo domu obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.city', 'Obec obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.zipCode', 'PSČ obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.country', 'Štát obchodného partnera', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amFirstName', 'Obchodný partner - meno manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amLastName', 'Obchodný partner - priezvisko manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amEmail', 'Obchodný partner - email manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amPhone', 'Obchodný partner - telefónne číslo manažéra predaja', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'businessPartner.amName', 'Obchodný partner - meno pobočky', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');

-- target: CustomerRequest
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.externalId', 'SAP ID žiadosti', 'Dostupné iba ak bola žiadosť zaevidovaná v SAPe', 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.name', 'Názov žiadosti', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.status.code', 'Kód stavu žiadosti', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');
insert into notification_template_variable select nt.uuid, now(), now(), 1, 'customerRequest.status.name', 'Stav žiadosti', null, 'STRING', 'AUTOMATIC', uuid_generate_v4() from notification_template nt where nt.code in ('CUSTOMER_REQUEST_CANCEL_REQUEST_CUSTOMER');