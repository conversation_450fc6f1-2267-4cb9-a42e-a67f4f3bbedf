INSERT INTO access_group (uuid, code, created_at , updated_at ,"version", name, description) values (uuid_generate_v4(), 'NZP_COMPONENT_HELP', current_timestamp, current_timestamp, 1, '<PERSON><PERSON><PERSON><PERSON><PERSON> help komponentu', '');

INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('COMPONENT_HELP_VIEW', current_timestamp, current_timestamp, 1, 'Moznost prezerat help komponent', '', false, false);
INSERT INTO access_right (code, created_at , updated_at ,"version", name, description, admin, option_queue) values ('COMPONENT_HELP_EDIT', current_timestamp, current_timestamp, 1, 'Moznost upravovat help komponent', '', false, false);

INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('COMPONENT_HELP_VIEW', (select uuid from access_group where code = 'NZP_COMPONENT_HELP'), current_timestamp, 'GRANT', NULL);
INSERT INTO access_group_right (access_right_code, access_group_uuid, created_at, operation, queue) values ('COMPONENT_HELP_EDIT', (select uuid from access_group where code = 'NZP_COMPONENT_HELP'), current_timestamp, 'GRANT', NULL);