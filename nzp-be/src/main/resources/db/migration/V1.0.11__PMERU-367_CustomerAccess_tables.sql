------------------------------------
-- customer_account
------------------------------------

create table customer_account (
    uuid                           uuid                      NOT NULL,
    created_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                        INTEGER                   NOT NULL,
    email                          CHARACTER VARYING(64)     NOT NULL,
    external_id                    CHARACTER VARYING(64),
    external_password              CHARACTER VARYING(512),
    password                       CHARACTER VARYING(512),
    type                           CHARACTER VARYING(50)     NOT NULL,
    status                         CHARACTER VARYING(50)     NOT NULL,
    activation_at                  TIMESTAMP WITH TIME ZONE,
    registration_at                TIMESTAMP WITH TIME ZONE,
    lock_until                     TIMESTAMP WITH TIME ZONE,
    login_success_at               TIMESTAMP WITH TIME ZONE,
    login_unsuccess_at             TIMESTAMP WITH TIME ZONE,
    password_updated_at            TIMESTAMP WITH TIME ZONE,
    deactivation_reason            CHARACTER VARYING(50),
    locale                         CHARACTER VARYING(8),
    constraint pk_customer_account primary key (uuid)
)
WITH (
    OIDS = FALSE
);

create index idx_customer_account_email_status on customer_account (email, status);

------------------------------------
-- customer_approval
------------------------------------

create table customer_approval (
    uuid                           uuid                      NOT NULL,
    created_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                        INTEGER                   NOT NULL,
    type                           CHARACTER VARYING(50)     NOT NULL,
    approval                       BOOLEAN   			     NOT NULL,
    customer_id                    uuid                      NOT NULL,
    constraint pk_customer_approval primary key (uuid),
    constraint fk_customer_approval_customer_id foreign key (customer_id) references customer_account
)
WITH (
    OIDS = FALSE
);

create index idx_customer_approval_customer on customer_approval (customer_id);

------------------------------------
-- customer_account_challenge_code
------------------------------------

create table customer_account_challenge_code (
    uuid                           uuid                      NOT NULL,
    created_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                        INTEGER                   NOT NULL,
    type                           CHARACTER VARYING(50)     NOT NULL,
    code                           CHARACTER VARYING(50),
    value                          CHARACTER VARYING(512),
    valid_to                       TIMESTAMP WITH TIME ZONE  NOT NULL,
    customer_id                    uuid                      NOT NULL,
    business_partner_id            uuid,
    constraint pk_customer_account_challenge_code primary key (uuid),
    constraint fk_customer_account_challenge_code_customer_id foreign key (customer_id) references customer_account,
    constraint fk_customer_account_challenge_code_business_partner_id foreign key (business_partner_id) references business_partner
)
WITH (
    OIDS = FALSE
);

create index idx_customer_account_challenge_code_customer_type on customer_account_challenge_code (customer_id, type);

create index idx_customer_account_challenge_code_business_partner on customer_account_challenge_code (business_partner_id);
