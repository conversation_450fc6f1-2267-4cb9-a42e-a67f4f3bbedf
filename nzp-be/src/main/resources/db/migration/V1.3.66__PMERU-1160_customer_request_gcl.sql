
-- update existing
UPDATE generic_code_list SET code = 'SAP_FINISHED' where code = 'FINISHED' and type = 'CUSTOMER_REQUEST_STATUS';
UPDATE generic_code_list SET code = 'SAP_CANCELLED' where code = 'CANCELLED' and type = 'CUSTOMER_REQUEST_STATUS';
UPDATE generic_code_list SET code = 'SAP_CANCELLED_BY_USER' where code = 'CANCELLATION' and type = 'CUSTOMER_REQUEST_STATUS';
UPDATE generic_code_list SET code = 'CANCELLED_BY_USER' where code = 'CANCELLED_BY_CUSTOMER' and type = 'CUSTOMER_REQUEST_STATUS';
delete from generic_code_list_i18n
 where code_list_uuid in (select uuid from generic_code_list where code='CREATED_AS_PAPER' and type = 'CUSTOMER_REQUEST_STATUS');
delete from generic_code_list where code='CREATED_AS_PAPER' and type = 'CUSTOMER_REQUEST_STATUS';

-- insert new
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SAP_IN_PROGRESS', 'CUSTOMER_REQUEST_STATUS', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'sk', 'V spracovaní', null, (select uuid from generic_code_list where code like 'SAP_IN_PROGRESS' and type = 'CUSTOMER_REQUEST_STATUS'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'en', 'In progress', null, (select uuid from generic_code_list where code like 'SAP_IN_PROGRESS' and type = 'CUSTOMER_REQUEST_STATUS'));

-------------------------------------------------------------------------------------------------------------------------

INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SAP_OPEN', 'CUSTOMER_REQUEST_STATUS', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'sk', 'Založená', null, (select uuid from generic_code_list where code like 'SAP_OPEN' and type = 'CUSTOMER_REQUEST_STATUS'));

INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'en', 'Open', null, (select uuid from generic_code_list where code like 'SAP_OPEN' and type = 'CUSTOMER_REQUEST_STATUS'));

-------------------------------------------------------------------------------------------------------------------------
