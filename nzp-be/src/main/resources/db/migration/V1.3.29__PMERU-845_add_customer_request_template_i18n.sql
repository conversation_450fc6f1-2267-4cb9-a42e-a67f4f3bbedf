CREATE TABLE customer_request_template_i18n
(
    uuid                            uuid                      NOT NULL,
    created_at                      TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                      TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                         integer                   NOT NULL,
    locale                          CHARACTER VARYING(8)      NOT NULL,
    name                            CHARACTER VARYING(255)    NOT NULL,
    description                     text,
    customer_request_template_uuid  uuid     NOT NULL,

    CONSTRAINT pk_customer_request_template_i18n PRIMARY KEY (uuid),
    CONSTRAINT fk_customer_request_template_i18n_customer_request_template_uuid FOREIGN KEY (customer_request_template_uuid)
        REFERENCES customer_request_template (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE UNIQUE INDEX idx_customer_request_template_i18n_locale on customer_request_template_i18n(customer_request_template_uuid, locale);

ALTER TABLE customer_request_template DROP COLUMN name;
ALTER TABLE customer_request_template DROP COLUMN description;


