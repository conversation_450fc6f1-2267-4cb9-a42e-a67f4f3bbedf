----------------
-- interval_meter_reading_zp
----------------

CREATE TABLE interval_meter_reading_zp
(
    uuid                         uuid                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      integer                   NOT NULL,
    
    external_id                  CHARACTER VARYING(50),
    value                        NUMERIC(19,2)             NOT NULL,
    read_at                      TIMESTAMP WITH TIME ZONE  NOT NULL,
    
    delivery_point_external_id   CHARACTER VARYING(50)     NOT NULL,

    CONSTRAINT pk_interval_meter_reading_zp PRIMARY KEY (uuid)
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_imr_zp_external_id on interval_meter_reading_zp(external_id);
CREATE INDEX idx_imr_zp_delivery_point_external_id_read_at on interval_meter_reading_zp(delivery_point_external_id, read_at);