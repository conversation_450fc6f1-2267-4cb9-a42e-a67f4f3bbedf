create table address (
    uuid                           uuid                      NOT NULL,
    created_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                     TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                        INTEGER                   NOT NULL,
    country                        CHARACTER VARYING(10),
    street_code                    CHARACTER VARYING(20),
    house_num_l                    INTEGER,
    house_num_h                    INTEGER,
    hsn_ind                        INTEGER,
    zip                            CHARACTER VARYING(20),
    city_code                      CHARACTER VARYING(20),
    city                           CHARACTER VARYING(512),
    district                       CHARACTER VARYING(512),
    street                         CHARACTER VARYING(512),
    synchronization_log_uuid       uuid                      NOT NULL,
    synchronization_at             TIMESTAMP WITH TIME ZONE,
    ft                             CHARACTER VARYING(512),
    constraint pk_address primary key (uuid),
    constraint fk_address_synchronization_log foreign key (synchronization_log_uuid) references synchronization_log
)
WITH (
    OIDS = FALSE
);

create index idx_address_synchronization_log_uuid
    on address (synchronization_log_uuid);

create index idx_address_ft
    on address (ft);

