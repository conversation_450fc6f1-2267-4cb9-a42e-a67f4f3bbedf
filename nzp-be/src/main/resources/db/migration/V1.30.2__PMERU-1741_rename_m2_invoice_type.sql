-- Rename GCL i18ns "<PERSON><PERSON><PERSON><PERSON> stopka" -> "Nekomoditný produkt"
UPDATE generic_code_list_i18n
SET name = 'Nekomoditný produkt', updated_at = now()
WHERE code_list_uuid = (
    SELECT uuid FROM generic_code_list
    WHERE type = 'INVOICE_TYPE'
    AND code = 'M2')
AND locale = 'SK';

UPDATE generic_code_list_i18n
SET name = '[EN] Nekomoditný produkt', updated_at = now()
WHERE code_list_uuid = (
    SELECT uuid FROM generic_code_list
    WHERE type = 'INVOICE_TYPE'
    AND code = 'M2')
AND locale = 'EN';