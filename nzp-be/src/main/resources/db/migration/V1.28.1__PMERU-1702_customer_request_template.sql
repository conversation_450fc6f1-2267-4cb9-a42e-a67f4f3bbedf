INSERT INTO customer_request_template
(
    uuid,
    created_at,
    updated_at,
    version,
    status,
    code,
    price,
    type,
    link,
    confirmation_required
)
VALUES
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ZOM_ZSP',
    null,
    'DIGITAL',
    null,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ZOM_ZPPP',
    null,
    'DIGITAL',
    null,
    false
);

update  customer_request_template set status = 'INACTIVE' where code = 'ZOM_ZSPAPPP';


INSERT INTO customer_request_template_i18n (
    uuid,
    created_at,
    updated_at,
    version,
    locale,
    name,
    description,
    customer_request_template_uuid
) VALUES (
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'SK',
    'Žiadosť odberné miesto - Zmena spôsobu platby',
    'Žiadosť o zmenu spôsobu platby.',
    (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZSP')
),
(
     uuid_generate_v4(),
     now(),
     now(),
     1,
     'SK',
     'Žiados<PERSON> odberné miesto - Zmena periodicity preddavkovej platby',
     '<PERSON><PERSON><PERSON>ť o zmenu periodicity preddavkovej platby.',
     (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZPPP')
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'EN',
    '[EN] Žiadosť odberné miesto - Zmena spôsobu platby',
    '[EN] Žiadosť o zmenu spôsobu platby.',
    (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZSP')
),
(
     uuid_generate_v4(),
     now(),
     now(),
     1,
     'EN',
     '[EN] Žiadosť odberné miesto - Zmena periodicity preddavkovej platby',
     '[EN] Žiadosť o zmenu periodicity preddavkovej platby.',
     (SELECT uuid FROM customer_request_template crt WHERE crt.code LIKE 'ZOM_ZPPP')
)
