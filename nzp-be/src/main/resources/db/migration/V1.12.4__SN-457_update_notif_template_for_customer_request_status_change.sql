-----------------------------------------------------------------------------------------------
---------------- CHANGE Notification templates (header - portal notification) - ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE, CUSTOMER_REQUEST_STATUS_CHANGE
---------------- ADD SAP request ID + request status
-----------------------------------------------------------------------------------------------

UPDATE notification_template_i18n
SET version = (version + 1), updated_at = now(), header = '<PERSON><PERSON>a stavu žiadosti - ${customerRequest.externalId} (${customerRequest.status.name})'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE')
  AND locale = 'SK';

UPDATE notification_template_i18n
SET version = (version + 1), updated_at = now(), header = '[EN] Zmena stavu žiadosti - ${customerRequest.externalId} (${customerRequest.status.name})'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'ANONYMOUS_CUSTOMER_REQUEST_STATUS_CHANGE')
  AND locale = 'EN';

UPDATE notification_template_i18n
SET version = (version + 1), updated_at = now(), header = 'Zmena stavu žiadosti - ${customerRequest.externalId} (${customerRequest.status.name})'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_STATUS_CHANGE')
  AND locale = 'SK';

UPDATE notification_template_i18n
SET version = (version + 1), updated_at = now(), header = '[EN] Zmena stavu žiadosti - ${customerRequest.externalId} (${customerRequest.status.name})'
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_STATUS_CHANGE')
  AND locale = 'EN';
