
-------------------
-- notification_template
-------------------

CREATE TABLE notification_template
(
    uuid                        uuid                        NOT NULL,
    created_at                  TIMESTAMP WITH TIME ZONE    NOT NULL,
    updated_at                  TIMESTAMP WITH TIME ZONE    NOT NULL,
    version                     integer                     NOT NULL,

    code                        CHARACTER VARYING(50)       NOT NULL,
    status                      CHARACTER VARYING(50)       NOT NULL,
    type                        CHARACTER VARYING(50)       NOT NULL,
    name                        CHARACTER VARYING(512)      NOT NULL,
    priority                    CHARACTER VARYING(50)       NOT NULL,
    description                 text,
    attributes                  text,

    CONSTRAINT pk_notification_template PRIMARY KEY (uuid)
)
WITH (
    OIDS = FALSE
);

-- indexes
create unique index idx_notification_template_code_unq on notification_template (code);

-------------------
-- notification_template_attachment
-------------------

CREATE TABLE notification_template_attachment
(
    uuid                        uuid                        NOT NULL,
    created_at                  TIMESTAMP WITH TIME ZONE    NOT NULL,

    status                      CHARACTER VARYING(50)       NOT NULL,
    channel                     CHARACTER VARYING(50)       NOT NULL,
    mime_type                   CHARACTER VARYING(20),
    length                      integer,
    name                        CHARACTER VARYING(512)      NOT NULL,
    file_name                   CHARACTER VARYING(255),
    description                 text,
    content                     bytea,

    notification_template_id    uuid                        NOT NULL,

    CONSTRAINT pk_notification_template_attachment PRIMARY KEY (uuid),
    CONSTRAINT fk_notification_template_attachment_notification_template FOREIGN KEY (notification_template_id)
        REFERENCES notification_template (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_notification_template_attachment_notification_template_id on notification_template_attachment(notification_template_id);


-------------------
-- notification_template_i18n
-------------------

CREATE TABLE notification_template_i18n
(
    uuid                        uuid                        NOT NULL,
    created_at                  TIMESTAMP WITH TIME ZONE    NOT NULL,
    updated_at                  TIMESTAMP WITH TIME ZONE    NOT NULL,
    version                     integer                     NOT NULL,

    header                      text,
    email_body                  text,
    email_subject               text,
    sms_body                    text,

    notification_template_id    uuid                        NOT NULL,
    locale                      CHARACTER VARYING(2)        NOT NULL,

    CONSTRAINT pk_notification_template_i18n PRIMARY KEY (uuid),
    CONSTRAINT fk_notification_template_i18n_notification_template FOREIGN KEY (notification_template_id)
        REFERENCES notification_template (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_notification_template_i18n_notification_template_id on notification_template_i18n(notification_template_id);
create unique index idx_nt_i18n_nt_id_local_unq on notification_template_i18n (notification_template_id, locale);
