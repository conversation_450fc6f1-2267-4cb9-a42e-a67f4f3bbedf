-------------------
-- customer_request
-------------------

CREATE TABLE customer_request
(
    uuid                         uuid                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      INTEGER                   NOT NULL,

    external_id                  CHARACTER VARYING(50),
    status                       CHARACTER VARYING(50)     NOT NULL,
    request_cancel               BOOLEAN,
    issued_at                    TIMESTAMP WITH TIME ZONE  NOT NULL,
    status_updated_at            TIMESTAMP WITH TIME ZONE  NOT NULL,
    content                      TEXT                      NOT NULL,
    entity_type                  CHARACTER VARYING(50),
    entity_id                    CHARACTER VARYING(50),
    customer_id                  uuid,
    customer_request_template_id uuid                      NOT NULL,

    CONSTRAINT pk_customer_request PRIMARY KEY (uuid),
    CONSTRAINT fk_customer_request_customer FOREIGN KEY (customer_id)
            REFERENCES customer_account (uuid) MATCH SIMPLE
            ON UPDATE NO ACTION
            ON DELETE NO ACTION,
    CONSTRAINT fk_customer_request_customer_request_template FOREIGN KEY (customer_request_template_id)
                REFERENCES customer_request_template (uuid) MATCH SIMPLE
                ON UPDATE NO ACTION
                ON DELETE NO ACTION
);

-- indexes
CREATE INDEX idx_customer_request_status on customer_request(status);
CREATE INDEX idx_customer_request_customer_id on customer_request(customer_id);
CREATE INDEX idx_customer_request_customer_request_template_id on customer_request(customer_request_template_id);
