INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REGISTRATION_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pokus o registráciu účtu', 'Pokus o registráciu účtu', (select uuid from generic_code_list where code like 'CUSTOMER_REGISTRATION_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Customer registration attempt', 'Customer registration attempt', (select uuid from generic_code_list where code like 'CUSTOMER_REGISTRATION_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_PASSWORD_CHANGE_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pokus o zmenu hesla', 'Pokus o zmenu hesla', (select uuid from generic_code_list where code like 'CUSTOMER_PASSWORD_CHANGE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Password change attempt.', 'Password change attempt.', (select uuid from generic_code_list where code like 'CUSTOMER_PASSWORD_CHANGE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_PASSWORD_RECOVERY_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pokus o obnovu hesla', 'Pokus o obnovu hesla', (select uuid from generic_code_list where code like 'CUSTOMER_PASSWORD_RECOVERY_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Customer password recovery attempt.', 'Customer password recovery attempt.', (select uuid from generic_code_list where code like 'CUSTOMER_PASSWORD_RECOVERY_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_DELETE_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pokus o zmazanie účtu', 'Pokus o zmazanie účtu', (select uuid from generic_code_list where code like 'CUSTOMER_DELETE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Customer delete attempt', 'Customer delete attempt', (select uuid from generic_code_list where code like 'CUSTOMER_DELETE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_PHONE_CHANGE_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pokus o zmenu telefónneho čísla', 'Pokus o zmenu telefónneho čísla', (select uuid from generic_code_list where code like 'CUSTOMER_PHONE_CHANGE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Phone change attempt', 'Phone change attempt', (select uuid from generic_code_list where code like 'CUSTOMER_PHONE_CHANGE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_EMAIL_CHANGE_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pokus o zmenu emailu', 'Pokus o zmenu emailu', (select uuid from generic_code_list where code like 'CUSTOMER_EMAIL_CHANGE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Email change attempt', 'Email change attempt', (select uuid from generic_code_list where code like 'CUSTOMER_EMAIL_CHANGE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_NAME_CHANGE_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pokus o zmenu mena', 'Pokus o zmenu mena', (select uuid from generic_code_list where code like 'CUSTOMER_NAME_CHANGE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Customer name change attempt.', 'Customer name change attempt.', (select uuid from generic_code_list where code like 'CUSTOMER_NAME_CHANGE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'BUSINESS_PARTNER_PAIRING_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pokus o párovanie obchodného partnera', 'Pokus o párovanie obchodného partnera', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Business partner pairing attempt.', 'Business partner pairing attempt.', (select uuid from generic_code_list where code like 'BUSINESS_PARTNER_PAIRING_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_SHARING_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pokus o zdieľanie', 'Pokus o zdieľanie', (select uuid from generic_code_list where code like 'CUSTOMER_SHARING_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Sharing attempt', 'Sharing attempt', (select uuid from generic_code_list where code like 'CUSTOMER_SHARING_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_REQUEST_CREATE_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pokus o vytvorenie žiadosti', 'Pokus o vytvorenie žiadosti', (select uuid from generic_code_list where code like 'CUSTOMER_REQUEST_CREATE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Customer request create attempt.', 'Customer request create attempt.', (select uuid from generic_code_list where code like 'CUSTOMER_REQUEST_CREATE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list
(uuid, created_at, updated_at, version, code, type, valid_from, valid_to, parent_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'IDM_AUTHENTICATE_ATTEMPT', 'AUDIT_LOG_CODE', null, null, null);
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'SK', 'Pokus o idm autentifikáciu', 'Pokus o idm autentifikáciu', (select uuid from generic_code_list where code like 'IDM_AUTHENTICATE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
INSERT INTO generic_code_list_i18n
(uuid, created_at, updated_at, version, locale, name, description, code_list_uuid)
VALUES
(uuid_generate_v4(), now(), now(), 1, 'EN', 'Idm authenticate attempt.', 'Idm authenticate attempt.', (select uuid from generic_code_list where code like 'IDM_AUTHENTICATE_ATTEMPT' and type = 'AUDIT_LOG_CODE'));
