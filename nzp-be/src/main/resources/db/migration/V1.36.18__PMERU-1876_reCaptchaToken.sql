CREATE TABLE re_captcha_token (
    uuid                        uuid                            NOT NULL,
    created_at                  TIMESTAMP WITH TIME ZONE        NOT NULL,
    updated_at                  TIMESTAMP WITH TIME ZONE        NOT NULL,
    version                     INTEGER                         NOT NULL,

    re_captcha_code             CHARACTER VARYING(512)          NOT NULL,
    token_type                  CHARACTER VARYING(128)          NOT NULL,
    valid_until                 TIMESTAMP WITH TIME ZONE        NOT NULL,
    remaining_uses_count        INTEGER                         NOT NULL,

    CONSTRAINT pk_re_captcha_token PRIMARY KEY (uuid));