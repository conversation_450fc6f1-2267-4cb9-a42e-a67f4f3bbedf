-----------
-- customer_notification
-----------

CREATE TABLE customer_notification
(
    uuid                     uuid                      NOT NULL,
    created_at               TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at               TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                  integer                   NOT NULL,

    source                   CHARACTER VARYING(50)     NOT NULL,
    external_id              CHARACTER VARYING(50),
    status                   CHARACTER VARYING(50)     NOT NULL,
    header                   text                      NOT NULL,
    email_body               text,
    email_subject            text,
    sms_body                 text,
    entity_type              CHARACTER VARYING(50),
    entity_id                CHARACTER VARYING(50),
    phone                    CHARACTER VARYING(20),
    email                    CHARACTER VARYING(64),
    attributes               text,
    attachments              text,
    locale                   CHARACTER VARYING(5),

    notification_template_id uuid                      NOT NULL,
    customer_account_id      uuid,
    share_from_id            uuid,
    read                     boolean default false     NOT NULL,

    CONSTRAINT fk_customer_notification_notification_template FOREIGN KEY (notification_template_id)
        REFERENCES notification_template (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk_customer_notification_customer_account FOREIGN KEY (customer_account_id)
        REFERENCES customer_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk_customer_notification_share_from FOREIGN KEY (share_from_id)
        REFERENCES customer_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT pk_customer_notification PRIMARY KEY (uuid, read),
    unique (uuid, read)
) PARTITION BY LIST (read)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_customer_notification_notification_template_id on customer_notification(notification_template_id);
CREATE INDEX idx_customer_notification_entity_type_entity_id on customer_notification(entity_type, entity_id);

-- partition tables

-- default partition
create table customer_notification_default_partition PARTITION OF customer_notification DEFAULT;
-- seen true partition
create table customer_notification_read_partition partition of customer_notification for values in (true);

------------------------------------------------------------------------
---- Do not delete, for reference if we need to partition by date as subpartition of read parition
------------------------------------------------------------------------
--
--DO
--$script$
--    DECLARE
--        partition_name TEXT;
--    BEGIN
----        FOR i IN 0..30 LOOP
----            partition_name := format(
--                    'create table if not exists customer_notification_%s partition of customer_notification for values from (''%s'') to (''%s'')',
--                    to_char(TO_DATE('20200101','YYYYMMDD') + make_interval(years := i), 'YYYY_MM_DD'),
--                    to_char(TO_DATE('20200101','YYYYMMDD') + make_interval(years := i), 'YYYY-MM-DD'),
--                    to_char(TO_DATE('20200101','YYYYMMDD') + make_interval(years := i+1), 'YYYY-MM-DD')
--               );
--            EXECUTE partition_name;
--
--            RAISE NOTICE 'Partition created: %', partition_name;
--        END LOOP;
--    END
--$script$;

-------------------
-- customer_notification_setting
-------------------

CREATE TABLE customer_notification_setting
(
    uuid                        uuid                        NOT NULL,
    created_at                  TIMESTAMP WITH TIME ZONE    NOT NULL,
    updated_at                  TIMESTAMP WITH TIME ZONE    NOT NULL,
    version                     integer                     NOT NULL,

    email                       boolean,
    sms                         boolean,

    customer_account_id         uuid                        NOT NULL,
    notification_template_id    uuid                        NOT NULL,

    CONSTRAINT pk_customer_notification_setting PRIMARY KEY (uuid),
    CONSTRAINT fk_customer_notification_setting_customer_account FOREIGN KEY (customer_account_id)
        REFERENCES customer_account (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk_customer_notification_setting_notification_template FOREIGN KEY (notification_template_id)
        REFERENCES notification_template (uuid) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE UNIQUE INDEX idx_customer_notif_setting_customer_account_notif_template on customer_notification_setting(customer_account_id, notification_template_id);



-------------------
-- customer_notification_render_status
-------------------

CREATE TABLE customer_notification_render_status
(
    uuid                        uuid                        NOT NULL,
    created_at                  TIMESTAMP WITH TIME ZONE    NOT NULL,
    updated_at                  TIMESTAMP WITH TIME ZONE    NOT NULL,
    version                     integer                     NOT NULL,

    priority                    CHARACTER VARYING(50)       NOT NULL,
    locked_by                   CHARACTER VARYING(50),
    retry_count                 integer,
    customer_notification_id    uuid                        NOT NULL,

    CONSTRAINT pk_customer_notification_render_status PRIMARY KEY (uuid, priority)
)PARTITION BY LIST(priority)
WITH (
    OIDS = FALSE
);

-- partition tables

-- default partition - if none match LOW/HIGH [change of priority mandatory/priority values]
create table customer_notification_render_status_default_partition
    PARTITION OF customer_notification_render_status DEFAULT;

create table customer_notification_render_status_low_priotiry
   partition of customer_notification_render_status
   for values in ('LOW');

create table customer_notification_render_status_high_priotiry
   partition of customer_notification_render_status
   for values in ('HIGH');

-- indexes
CREATE INDEX idx_customer_notif_render_status_customer_notif_id on customer_notification_render_status(customer_notification_id);
CREATE INDEX idx_customer_notif_render_status_updated_at on customer_notification_render_status(updated_at);

-------------------
-- customer_notification_send_status
-------------------

CREATE TABLE customer_notification_send_status
(
    uuid                                uuid                        NOT NULL,
    created_at                          TIMESTAMP WITH TIME ZONE    NOT NULL,
    updated_at                          TIMESTAMP WITH TIME ZONE    NOT NULL,
    version                             integer                     NOT NULL,

    priority                            CHARACTER VARYING(50)       NOT NULL,
    channel                             CHARACTER VARYING(50)       NOT NULL,
    email                               CHARACTER VARYING(64),
    phone                               CHARACTER VARYING(64),
    retry_count                         integer,
    locked_by                           CHARACTER VARYING(50),
    customer_notification_id            uuid                        NOT NULL,

    CONSTRAINT pk_customer_notification_send_status PRIMARY KEY (uuid, priority)
)
PARTITION BY LIST(priority)
WITH (
    OIDS = FALSE
);

-- partition tables

-- default partition - if none match LOW/HIGH [change of priority mandatory/priority values]
create table customer_notification_send_status_default_partition
    PARTITION OF customer_notification_send_status DEFAULT;

create table customer_notification_send_status_low_priotiry
   partition of customer_notification_send_status
   for values in ('LOW');

create table customer_notification_send_status_high_priotiry
   partition of customer_notification_send_status
   for values in ('HIGH');

-- indexes
CREATE INDEX idx_customer_notif_send_status_customer_notif_id on customer_notification_send_status(customer_notification_id);
CREATE INDEX idx_customer_notif_send_status_updated_at on customer_notification_send_status(updated_at);
