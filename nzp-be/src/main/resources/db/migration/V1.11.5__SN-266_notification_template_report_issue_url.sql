-- remove obsolete link to report suspicious issue, as the link is contained in the freemarker template macro
update notification_template_i18n
set email_body = replace(email_body, ' Vaše SPP<br><br><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a></p><p><br>', '')
where email_body like '%${portalExternalUrl}%';

update notification_template_i18n
set email_body = replace(email_body, '<p>Vaše SPP<br><br><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a></p>', '')
where email_body like '%${portalExternalUrl}%';

update notification_template_i18n
set email_body = replace(email_body, '<br><br><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Nahlásiť podozrivú aktivitu</a>', '')
where email_body like '%${portalExternalUrl}%';

update notification_template_i18n
set email_body = replace(email_body, 'Your SPP<br><br><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}">Report suspicious activity</a><a href="https://testnzp.spp.sk/$%7BportalExternalUrl%7D/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}"></a><a href="${portalExternalUrl}/report-issue?notificationUuid=${notificationUuid}&amp;notificationCode=${notificationTemplateCode}"></a>', '')
where email_body like '%${portalExternalUrl}%';

update notification_template_i18n
set email_body = regexp_replace(email_body, ' Vaše SPP.*Nahlásiť podozrivú aktivitu</a>', '')
where email_body is not null and email_body != '' and notification_template_id in (select nt.uuid from notification_template nt where nt.execution_type = 'AUTOMATIC');

update notification_template_i18n
set email_body = regexp_replace(email_body, '<br><br>Vaše SPP.*Nahlásiť podozrivú aktivitu</a>', '')
where email_body is not null and email_body != '' and notification_template_id in (select nt.uuid from notification_template nt where nt.execution_type = 'AUTOMATIC');
