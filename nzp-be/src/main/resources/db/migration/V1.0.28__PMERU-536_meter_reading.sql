----------------
-- meter_reading
----------------

CREATE TABLE meter_reading
(
    uuid                         uuid                      NOT NULL,
    created_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    updated_at                   TIMESTAMP WITH TIME ZONE  NOT NULL,
    version                      integer                   NOT NULL,
    
    external_id                  CHARACTER VARYING(50)     NOT NULL,
    type                         CHARACTER VARYING(50)     NOT NULL,
    value_low                    NUMERIC(19,2)             NOT NULL,
    value_high                   NUMERIC(19,2),
    reason                       text                      NOT NULL,
    read_at                      TIMESTAMP WITH TIME ZONE  NOT NULL,
    
    delivery_point_external_id   CHARACTER VARYING(50)     NOT NULL,

    CONSTRAINT pk_meter_reading PRIMARY KEY (uuid)
)
WITH (
    OIDS = FALSE
);

-- indexes
CREATE INDEX idx_meter_reading_external_id on meter_reading(external_id);
CREATE INDEX idx_meter_reading_delivery_point_external_id on meter_reading(delivery_point_external_id);