----------
-- product
----------

INSERT INTO  product
(
    uuid,
    created_at,
    updated_at,
    version,
    status,
    code,
    name,
    description,
    zp,
    ee,
    not_commodity
)
VALUES
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ASIST_2_N',
    'Asistenčné služby od SPP',
    'Asistenčné služby od SPP',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ASIST_ELE_N',
    'Asistenčné služby - elektrina',
    'Asistenčné služby - elektrina',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ASIST_N',
    'Asistenčné služby od SPP',
    'Asistenčné služby od SPP',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ASIST_PLYN_N',
    'Asisten<PERSON>n<PERSON> služby - plyn',
    'Asistenčné služby - plyn',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ASIST_PRAV_2_N',
    'Asistenčné služby s Právnou asistenciou',
    'Asistenčné služby s Právnou asistenciou',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ASIST_PRAV_N',
    'Asistenčné služby + právna asistencia',
    'Asistenčné služby + právna asistencia',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'BAL_2ELE_2GAS_NEUT',
    'Balíček 2x plyn 2x ele 1x neutilita',
    'Balíček 2x plyn 2x ele 1x neutilita',
    true,
    true,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'BAL_ELE_GAS',
    'Balíček elektrina plyn',
    'Balíček elektrina plyn',
    true,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'BAL_ELE_GAS_5NEUT',
    'Balíček 1x ele 1x plyn 5x neutilita',
    'Balíček 1x ele 1x plyn 5x neutilita',
    true,
    true,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'BAL_ELE_NEUT',
    'Balíček 1x ELE 1x Neutilita',
    'Balíček 1x ELE 1x Neutilita',
    false,
    true,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'BAL_GAS_NEUT',
    'Balíček 1x plyn 1x neutilita',
    'Balíček 1x plyn 1x neutilita',
    true,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'CENPROF_E',
    'Cenový profil Elektrina',
    'Cenový profil Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'DPI_P',
    'DPI Plyn',
    'DPI Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'DUMMY_E',
    'Dummy produkt pre elektrinu',
    'Dummy produkt pre elektrinu',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'DUMMY_P',
    'Dummy produkt pre plyn',
    'Dummy produkt pre plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ELE_REF_HO',
    'Referečný produkt HO Elektrina',
    'Referečný produkt HO Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ELE_REF_IO_SV',
    'Referenčný produkt IO EE SV',
    'Referenčný produkt IO EE SV',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ELE_VERNYM_E',
    'Elektrina Verným',
    'Elektrina Verným',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'EVE_2018_E',
    'EVE 2018 Elektrina',
    'EVE 2018 Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'EVE_E',
    'EVE Elektrina',
    'EVE Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'EVE_II_2018_E',
    'EVE II 2018 Elektrina',
    'EVE II 2018 Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'EXKLUSIV_P',
    'Exklusiv Plyn',
    'Exklusiv Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'FIX_12_E',
    'Fixná cena 12 Elektrina',
    'Fixná cena 12 Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'FIX_12_P',
    'Fixná cena 12 Plyn',
    'Fixná cena 12 Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'FIX_24_E',
    'Fixná cena 24 Elektrina',
    'Fixná cena 24 Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'FIX_24_EWI_P',
    'Fixná cena 24 EW Plyn',
    'Fixná cena 24 EW Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'FIX_24_P',
    'Fixná cena 24 Plyn',
    'Fixná cena 24 Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'FIX_24_REA_P',
    'Fixná cena 24 REA Plyn',
    'Fixná cena 24 REA Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'FIX_24_RTC_P',
    'Fixná cena 24 RTC Plyn',
    'Fixná cena 24 RTC Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'FIX_24_SP_P',
    'Fixná cena 24 SP Plyn',
    'Fixná cena 24 SP Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'FIX_HEDG_IO_E',
    'Fixácia – samostatný hedging Elektrina',
    'Fixácia – samostatný hedging Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'FIX_HEDG_IO_P',
    'Fixácia - samostatný hedging - Plyn',
    'Fixácia - samostatný hedging - Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'GAS_REF_HO',
    'Referenčný produkt HO Plyn',
    'Referenčný produkt HO Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'GAS_REF_IO_SV',
    'Referenčný produkt IO SV Plyn',
    'Referenčný produkt IO SV Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'INDEX_IO_P',
    'Indexovaná cena Plyn',
    'Indexovaná cena Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'IT_ASIST_2_N',
    'IT asistencia od SPP',
    'IT asistencia od SPP',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'IT_ASIST_N',
    'IT asistencia',
    'IT asistencia',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'KOMBI_A24_EWI_P',
    'Kombi A24 EWI Plyn',
    'Kombi A24 EWI Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'KOMBI_A24_RTC_P',
    'Kombi A24 RTC Plyn',
    'Kombi A24 RTC Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'KOMBI_A24_SP_P',
    'Kombi A24 SP Plyn',
    'Kombi A24 SP Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'KOMBI_B24_RTC_P',
    'Kombi B24 RTC Plyn',
    'Kombi B24 RTC Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'KOMBI_C24_RTC_P',
    'Kombi C24 RTC Plyn',
    'Kombi C24 RTC Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'M3_M4_P',
    'M3/M4 Plyn',
    'M3/M4 Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'MIG_ASIST_2_N',
    'Asistenčné služby od SPP',
    'Asistenčné služby od SPP',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'MIG_ASIST_N',
    'Asistenčné služby SPP',
    'Asistenčné služby SPP',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'MIG_BALIK_N',
    'Balík 1-2-3',
    'Balík 1-2-3',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'MOD_NEUTILITY',
    'Modul pre neutilitné produkty',
    'Modul pre neutilitné produkty',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'MPO_',
    'NEREG_E	Nefixný cenník MPO Elektrina',
    'NEREG_E	Nefixný cenník MPO Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ONLINE_ZLAVA_E',
    'Online Elektrina',
    'Online Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'PDO_IC_PACK',
    'Balíčkový vyhľadávač',
    'Balíčkový vyhľadávač',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'PDO_UTIL',
    'Utilitný vyhľadávač',
    'Utilitný vyhľadávač',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'POIST_DOM_N',
    'Poistenie domácnosti',
    'Poistenie domácnosti',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'POIST_PLAT_EN_N',
    'Poistenie platieb za energie',
    'Poistenie platieb za energie',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'REGUL_E',
    'Regulovaný cenník Elektrina',
    'Regulovaný cenník Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'REGUL_P',
    'Regulovaný cenník Plyn',
    'Regulovaný cenník Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'STANDARD_E',
    'Štandardný cenník Elektrina',
    'Štandardný cenník Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'STANDARD_P',
    'Štandardný cenník Plyn',
    'Štandardný cenník Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'SUPER_12_E',
    'Supervýhodne 12 Elektrina',
    'Supervýhodne 12 Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'SUPER_12_RTC_P',
    'Supervýhodne 12 RTC Plyn',
    'Supervýhodne 12 RTC Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'SUPER_24_E',
    'Supervýhodne 24 Elektrina',
    'Supervýhodne 24 Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'SUPER_24_EWI_P',
    'Supervýhodne 24 EWI Plyn',
    'Supervýhodne 24 EWI Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'SUPER_24_RTC_P',
    'Supervýhodne 24 RTC Plyn',
    'Supervýhodne 24 RTC Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'SV_IO_E',
    'SV IO EE',
    'SV IO EE',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'SV_IO_P',
    'SV IO P',
    'SV IO P',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'TARIFPROF_E',
    'Tarifný profil Elektrina',
    'Tarifný profil Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'TOP_2016_E',
    'TOP 2016 Elektrina',
    'TOP 2016 Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'UHLIK_EE1_N',
    'Uhlíková stopka – EE1',
    'Uhlíková stopka – EE1',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'UHLIK_EE2_N',
    'Uhlíková stopka – EE2',
    'Uhlíková stopka – EE2',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'UHLIK_EE3_N',
    'Uhlíková stopka – EE3',
    'Uhlíková stopka – EE3',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'UHLIK_ZP1_N',
    'Uhlíková stopka – ZP1',
    'Uhlíková stopka – ZP1',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'UHLIK_ZP2_N',
    'Uhlíková stopka – ZP2',
    'Uhlíková stopka – ZP2',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'UHLIK_ZP3_N',
    'Uhlíková stopka – ZP3',
    'Uhlíková stopka – ZP3',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'VIP_E',
    'VIP Elektrina',
    'VIP Elektrina',
    false,
    true,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'VYHODNE_12_P',
    'Výhodne 12 Plyn',
    'Výhodne 12 Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'VYHODNE_24_P',
    'Výhodne 24 Plyn',
    'Výhodne 24 Plyn',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'VYHODNE_PLUS_P',
    'Plyn Výhodne+',
    'Plyn Výhodne+',
    true,
    false,
    false
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ZDRAV_ASIST_2_N',
    'Zdravotná asistencia od SPP',
    'Zdravotná asistencia od SPP',
    false,
    false,
    true
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'ZDRAV_ASIST_N',
    'Zdravotná asistencia+',
    'Zdravotná asistencia+',
    false,
    false,
    true
);



----------
-- tariff
----------

INSERT INTO tariff
(
    uuid,
    created_at,
    updated_at,
    version,
    status,
    code,
    name,
    description,
    type
)
VALUES
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'D1',
    'D1',
    'Tarif D1 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov od 0 vrátane do 2 138 kWh vrátane (približne od 0 vrátane do 200 m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'D2',
    'D2',
    'Tarif D2 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 2 138 kWh do 18 173 kWh vrátane (približne nad 200 m3 do 1 700 m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'D3',
    'D3',
    'Tarif D3 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 18 173 kWh do 42 760 kWh vrátane (približne nad 1 700 m3 do 4 000 m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'D4',
    'D4',
    'Tarif D4 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 42 760 kWh do 69 485 kWh vrátane (približne nad 4 000 m3 do 6 500 m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'D5',
    'D5',
    'Tarif D5 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 69 485 kWh do 85 000 kWh vrátane (približne nad 6 500 m3 do 7 951m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'D6',
    'D6',
    'Tarif D6 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 85 000 kWh do 100 000 kWh vrátane (približne nad 7 951 m3 do 9 355m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'D7',
    'D7',
    'Tarif D7 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 100 000 kWh do 300 000 kWh vrátane (približne nad 9 355 m3 do 28 064 m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'D8',
    'D8',
    'Tarif D8 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 300 000 kWh do 641 400 kWh vrátane (približne nad 28 064 m3 do 60 000 m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'DD1',
    'DD1',
    'DD1 je sadzba vhodná pre bežnú spotrebu v menšej domácnosti. Meranie spotreby elektriny nie je rozdelené so pásiem.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'DD2',
    'DD2',
    'DD2 je sadzba vhodná pre bežnú spotrebu vo väčšej domácnosti. Meranie spotreby elektriny nie je rozdelené do pásiem.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'DD3',
    'DD3',
    'DD3 je sadzba vhodná u Odberateľov, ktorí vedia spotrebovať podstatnú časť elektriny v čase nízkej tarify (NT), napríklad na akumulačný ohrev vody. NT sa poskytuje minimálne 8 hodín denne s fixne určeným časom prevádzky v pásme NT v nepretržitom trvaní aspoň tri hodiny, pričom blokovanie elektrických spotrebičov sa nevyžaduje.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'DD4',
    'DD4',
    'DD4 je sadzba vhodná na akumulačné vykurovanie. NT sa poskytuje minimálne 8 hodín denne s blokovaním akumulačných elektrických spotrebičov počas doby platnosti VT.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'DD5',
    'DD5',
    'DD5 je sadzba určená pre priamovýhrevné vykurovanie. Sadzbu možno prideliť pre plne elektrifikované odberné miesto s pevne inštalovanými elektrickými priamovýhrevnými spotrebičmi so ich technického blokovania samostatným elektrickým obvodom počas pásma VT. NT sa poskytuje zabezpečením ich technického blokovania samostatným elektrickým obvodom počas pásma VT. NT sa poskytuje minimálne 20 hodín.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'DD6',
    'DD6',
    'DD6 je sadzba určená pre odberné miesta, kde sa na vykurovanie a prípravu teplej úžitkovej vody využíva tepelné čerpadlo. NT sa poskytuje minimálne 20 hodín denne s blokovaním elektrických spotrebičov na vykurovanie počas doby platnosti VT.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'DD7',
    'DD7',
    'DD7 je sadzba určená pre odberné miesta s prevažujúcou spotrebou počas víkendov. NT sa poskytuje celoročne od piatka od 15.00 hod. do pondelka do 6.00 hod.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'DD8',
    'DD8',
    'DD8 je sadzba určená pre akumulačné vykurovanie. NT sa poskytuje minimálne 8 hodín denne s blokovaním akumulačných elektrických spotrebičov počas doby platnosti VT s určeným minimálnym inštalovaným výkonom akumulačných spotrebičov.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'M1',
    'M1',
    'Tarif M1 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov od 0 vrátane do 2 138 kWh vrátane (približne od 0 vrátane do 200 m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'M2',
    'M2',
    'Tarif M2 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 2 138 kWh do 18 173 kWh vrátane (približne nad 200 m3 do 1 700 m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'M3',
    'M3',
    'Tarif M3 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 18 173 kWh do 42 760 kWh vrátane (približne nad 1 700 m3 do 4 000 m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'M4',
    'M4',
    'Tarif M4 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 42 760 kWh do 69 485 kWh vrátane (približne nad 4 000 m3 do 6 500 m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'M5',
    'M5',
    'Tarif M5 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 69 485 kWh do 85 000 kWh vrátane (približne nad 6 500 m3 do 7 951m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'M6',
    'M6',
    'Tarif M6 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 85 000 kWh do 100 000 kWh vrátane (približne nad 7 951 m3 do 9 355m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'M7',
    'M7',
    'Tarif M7 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 100 000 kWh do 300 000 kWh vrátane (približne nad 9 355 m3 do 28 064 m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'M8',
    'M8',
    'Tarif M8 je odporúčaný pre odberné miesta, ktorých odber plynu za 12 po sebe nasledujúcich kalendárnych mesiacov nad 300 000 kWh do 641 400 kWh vrátane (približne nad 28 064 m3 do 60 000 m3 vrátane).',
    'ZP'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'C2-X3 (TDO1)',
    'C2-X3 (TDO1)',
    'Jednotarifný odber.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'C2-X3 (TDO2)',
    'C2-X3 (TDO2)',
    'Dvojtarifný odber - ostatný.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'C2-X3 (TDO3)',
    'C2-X3 (TDO3)',
    'Dvojtarifný odber - priamovýhrevné vykurovanie.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'C2-X3 (TDO8)',
    'C2-X3 (TDO8)',
    'Verejné osvetlenie.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'C1',
    'C1',
    'Jednopásmová sadzba s nižšou spotrebou elektriny.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'C2',
    'C2',
    'Jednopásmová sadzba so strednou spotrebou elektriny.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'C3',
    'C3',
    'Jednopásmová sadzba s vyššou spotrebou elektriny.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'C4',
    'C4',
    'Dvojpásmová sadzba s nižšou spotrebou elektriny.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'C5',
    'C5',
    'Dvojpásmová sadzba so strednou spotrebou elektriny - doba platnosti nízkej tarify 8 hodín denne (pre strednú spotrebu vo vysokej tarife).',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'C6',
    'C6',
    'Dvojpásmová sadzba s vyššou spotrebou elektriny - doba platnosti nízkej tarify 8 hodín denne (pre vyššiu spotrebu vo vysokej tarife).',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'C7',
    'C7',
    'Dvojpásmová sadzba - doba platnosti nízkej tarify 20 hodín denne (priamo výhrevné elektrické spotrebiče sú blokované v čase vysokého pásma).',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'C8',
    'C8',
    'Dvojpásmová sadzba pre tepelné čerpadlo - doba platnosti nízkej tarify 20 hodín denne (výhrevné elektrické spotrebiče sú blokované v čase vysokého pásma)',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'X3-C2 (TDO1)',
    'X3-C2 (TDO1)',
    'Jednotarifný odber.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'X3-C2 (TDO2)',
    'X3-C2 (TDO2)',
    'Dvojtarifný odber - ostatný.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'X3-C2 (TDO3)',
    'X3-C2 (TDO3)',
    'Dvojtarifný odber - priamovýhrevné vykurovanie.',
    'EE'
),
(
    uuid_generate_v4(),
    now(),
    now(),
    1,
    'ACTIVE',
    'X3-C2 (TDO8)',
    'X3-C2 (TDO8)',
    'Verejné osvetlenie.',
    'EE'
);





