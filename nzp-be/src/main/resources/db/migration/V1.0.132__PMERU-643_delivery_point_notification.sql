ALTER TABLE delivery_point          ADD COLUMN notification_rkmrk_checked_at        TIMESTAMP WITH TIME ZONE;
ALTER TABLE delivery_point          ADD COLUMN notification_zm_checked_at           TIMESTAMP WITH TIME ZONE;
ALTER TABLE delivery_point          ADD COLUMN notification_dmm_checked_at          TIMESTAMP WITH TIME ZONE;

ALTER TABLE delivery_point_fact     ADD COLUMN check_rkmrk_locked_by                CHARACTER VARYING(50);
ALTER TABLE delivery_point_fact     ADD COLUMN check_zm_locked_by                   CHARACTER VARYING(50);
ALTER TABLE delivery_point_fact     ADD COLUMN check_dmm_locked_by                  CHARACTER VARYING(50);

ALTER TABLE delivery_point_fact     ADD COLUMN check_rkmrk_retry_count              INTEGER;
ALTER TABLE delivery_point_fact     ADD COLUMN check_zm_retry_count                 INTEGER;
ALTER TABLE delivery_point_fact     ADD COLUMN check_dmm_retry_count                INTEGER;

create index idx_delivery_point_fact_operand on delivery_point_fact(operand);