-- DeliveryPointConsumption
DELETE FROM notification_template_variable WHERE variable IN (
            	'deliveryPointConsumption.limit',
            	'deliveryPointConsumption.exceeded.value',
            	'deliveryPointConsumption.exceeded.period.from',
            	'deliveryPointConsumption.exceeded.period.to');

INSERT INTO notification_template_variable
    SELECT nt.uuid, now(), now(), 1, 'attributes.consumptionLimit', 'Maximálny odber', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4()
    FROM notification_template nt
    WHERE nt.code IN (
        'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_RK',
        'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_AM_CHECK_MRK',
        'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_DMM');

INSERT INTO notification_template_variable
    SELECT nt.uuid, now(), now(), 1, 'attributes.consumptionExceeded.value', '<PERSON><PERSON><PERSON> hodnota', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4()
    FROM notification_template nt
    WHERE nt.code IN (
        'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_RK',
        'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_AM_CHECK_MRK',
        'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_DMM');

INSERT INTO notification_template_variable
    SELECT nt.uuid, now(), now(), 1, 'attributes.consumptionExceeded.from', 'Interval nameranej hodnoty od', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4()
    FROM notification_template nt
    WHERE nt.code IN (
        'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_RK',
        'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_AM_CHECK_MRK',
        'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_DMM');

INSERT INTO notification_template_variable
    SELECT nt.uuid, now(), now(), 1, 'attributes.consumptionExceeded.to', 'Interval nameranej hodnoty do', NULL, 'NUMBER', 'AUTOMATIC', uuid_generate_v4()
    FROM notification_template nt
    WHERE nt.code IN (
        'DELIVERY_POINT_CHECK_RK', 'DELIVERY_POINT_AM_CHECK_RK',
        'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_AM_CHECK_MRK',
        'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_AM_CHECK_DMM');