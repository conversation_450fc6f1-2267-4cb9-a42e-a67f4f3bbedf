INSERT INTO notification_template
    (uuid, created_at, updated_at, version, code, status, type, name, priority, execution_type,
    description, attributes, template_group, default_email, default_sms)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS', 'ACTIVE', 'CUSTOMER_SYSTEM', 'Dokončená obnova hesla', 'LOW', 'AUTOMATIC',
    'Notifikacia ohladom uspesnej obnovy hesla', null, null, true, false);

INSERT INTO notification_template_i18n
    (uuid, created_at, updated_at, version, status, header, email_body,
    email_subject, sms_body,
    notification_template_id, locale)
VALUES
    (uuid_generate_v4(), now(), now(), 1, 'ACTIVE', 'Obnova hesla', '<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>azník, Obnova Vášho hesla bola úspešná. Vaše SPP',
    'Obnova hesla', '<PERSON>az<PERSON><PERSON>, Obnova Vasho hesla bola uspesna. Vase SPP',
    (select uuid from notification_template where code = 'CUSTOMER_PASSWORD_RECOVERY_SUCCESS' and version = 1), 'SK');