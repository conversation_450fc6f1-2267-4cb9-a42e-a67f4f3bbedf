-- SPS-380 - Fix delivery point check limit processing

-- add column which holds date of the last checked meter reading
ALTER TABLE delivery_point ADD COLUMN notification_limit_last_checked_meter_reading_date TIMESTAMP WITH TIME ZONE;

-- set correct date
UPDATE delivery_point
SET notification_limit_last_checked_meter_reading_date = notification_limit_checked_at - interval '2 days'
where notification_limit_checked_at is not null;

-- Fix retry count (some DP are not picked up since retry count is too high)
UPDATE delivery_point SET check_limit_retry_count = null WHERE check_limit_retry_count is not null;
