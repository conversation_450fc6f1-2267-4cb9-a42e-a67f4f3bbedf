INSERT INTO public.customer_request_template (uuid,created_at,updated_at,"version",status,code,confirmation_required,"type",link,confirmation_valid_days,completion_required,completion_valid_days,price_collective,price_individual) VALUES
	 (uuid_generate_v4(), now(), now(),1,'ACTIVE','X_MS',false,'DIGITAL',NULL,NULL,false,NULL,NULL,NULL);

INSERT INTO public.customer_request_template_i18n (uuid,created_at,updated_at,"version",locale,"name",description,customer_request_template_uuid) VALUES
	 (uuid_generate_v4(), now(), now(),1,'SK','Žiadosť obchodný partner - marketingove suhlasy','Marketingove suhlasy',(select uuid from customer_request_template where code = 'X_MS'));

INSERT INTO public.customer_request_template_i18n (uuid,created_at,updated_at,"version",locale,"name",description,customer_request_template_uuid) VALUES
	 (uuid_generate_v4(), now(), now(),1,'EN','Request - marketing consets','Marketing consents',(select uuid from customer_request_template where code = 'X_MS'));


INSERT INTO component_help (uuid,created_at,updated_at,"version",screen,field,status,help_order) VALUES
     	 (uuid_generate_v4(),now(), now(),1,'NUN','NUN_NEWS_SPP_PARTNERS','ACTIVE',820);

INSERT INTO component_help_i18n (uuid,created_at,updated_at,"version",locale,"content",component_help_id) VALUES
     	 (uuid_generate_v4(),now(), now(),1,'EN','<div> <span class="font-weight-500 text-black">I also want news and information from SPP partners</span>
      <br> <small class="text-secondary">e.g. SPP Foundation, SPP Ekofond and others</small>
     </div>',(select uuid  from component_help ch where field = 'NUN_NEWS_SPP_PARTNERS')),
     	 (uuid_generate_v4(),now(), now(),1,'SK','<div> <span class="font-weight-500 text-black">Chcem novinky a informácie aj od partnerov SPP</span>
      <br> <small class="text-secondary">napr. Nadácia SPP, Ekofond SPP a iné</small>
     </div>',(select uuid  from component_help ch where field = 'NUN_NEWS_SPP_PARTNERS'));