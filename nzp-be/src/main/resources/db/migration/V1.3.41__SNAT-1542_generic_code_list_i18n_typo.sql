-------------------------
-- generic_code_list_i18n '<PERSON><PERSON><PERSON> spoloč<PERSON>' -> '<PERSON><PERSON><PERSON> spoloč<PERSON>'
-------------------------
UPDATE generic_code_list_i18n SET name = '<PERSON><PERSON><PERSON> spol<PERSON>', updated_at = now() WHERE (code_list_uuid = (select uuid from generic_code_list where code = 'COMPANY_TERMINATION' and type = 'CONTRACT_TERMINATION_REASON'));
