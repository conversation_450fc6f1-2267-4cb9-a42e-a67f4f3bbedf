-- SPPNZP-163
-- Configure push attributes for notification templates (based on data in SPP TEST env 11.02.2025)

-- Disable push notification if necessary
UPDATE notification_template
SET enable_push = FALSE,
    updated_at = NOW(),
    version = version + 1
WHERE execution_type = 'AUTOMATIC'
  AND enable_push IS TRUE
  AND code NOT IN ('BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC', 'CUSTOMER_REQUEST_COMPLETION_DONE', 'CUSTOMER_REQUEST_COMPLETION_EXPIRED', 'CUSTOMER_REQUEST_COMPLETION_REMINDER', 'CUSTOMER_REQUEST_COMPLETION_REQUEST', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_REJECT_REQUEST', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_RK', 'METER_READING_CUSTOMER');

-- Enable push notification if necessary
UPDATE notification_template
SET enable_push = TRUE,
    updated_at = NOW(),
    version = version + 1
WHERE execution_type = 'AUTOMATIC'
  AND enable_push IS NOT TRUE
  AND code IN ('BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC', 'CUSTOMER_REQUEST_COMPLETION_DONE', 'CUSTOMER_REQUEST_COMPLETION_EXPIRED', 'CUSTOMER_REQUEST_COMPLETION_REMINDER', 'CUSTOMER_REQUEST_COMPLETION_REQUEST', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_REJECT_REQUEST', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_RK', 'METER_READING_CUSTOMER');

-- Clear push attributes for not configured automatic push templates
UPDATE notification_template_i18n
SET push_title = NULL,
    push_text = NULL,
    push_body = NULL,
    push_redirection = NULL,
    updated_at = NOW(),
    version = version + 1
WHERE
    notification_template_id IN (
        SELECT uuid
        FROM notification_template
        WHERE execution_type = 'AUTOMATIC'
          AND code NOT IN ('BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC', 'CUSTOMER_REQUEST_COMPLETION_DONE', 'CUSTOMER_REQUEST_COMPLETION_EXPIRED', 'CUSTOMER_REQUEST_COMPLETION_REMINDER', 'CUSTOMER_REQUEST_COMPLETION_REQUEST', 'CUSTOMER_REQUEST_NOTE_CREATE', 'CUSTOMER_REQUEST_REJECT_REQUEST', 'CUSTOMER_REQUEST_STATUS_CHANGE', 'CUSTOMER_SHARING_CONSUMER_GRANT', 'DELIVERY_POINT_CHECK_DMM', 'DELIVERY_POINT_CHECK_MRK', 'DELIVERY_POINT_CHECK_RK', 'METER_READING_CUSTOMER')
    ) and (
    push_body is not null
        or push_redirection is not null
        or push_title is not null
        or push_text is not null
    );

-- Set push attributes for configured automatic push templates
UPDATE notification_template_i18n
SET push_title = 'Reserved electricity capacity exceeded',
    push_body = 'You have exceeded the maximum electricity capacity',
    push_text = 'Please note that you have exceeded your maximum reserved capacity for electricity consumption at your supply point.',
    push_redirection = NULL,
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_MRK') and upper(locale) = 'EN';

UPDATE notification_template_i18n
SET push_title = 'Reserved electricity capacity exceeded',
    push_body = 'You have exceeded the reserved electricity capacity',
    push_text = 'Please note that you have exceeded your reserved capacity for electricity consumption at your supply point.',
    push_redirection = NULL,
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_RK') and upper(locale) = 'EN';

UPDATE notification_template_i18n
SET push_title = 'Daily gas consumption limit exceeded',
    push_body = 'You have exceeded the daily gas consumption limit',
    push_text = 'Please note that you have exceeded your daily gas consumption limit at your supply point.',
    push_redirection = NULL,
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_DMM') and upper(locale) = 'EN';

UPDATE notification_template_i18n
SET push_title = 'Assigning supply points to account',
    push_body = 'Supply points have been assigned to your account',
    push_text = 'From this point on, you can track your details and make online requests.',
    push_redirection = '/delivery-points',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC') and upper(locale) = 'EN';

UPDATE notification_template_i18n
SET push_title = 'Supply point transfer request',
    push_body = 'Transfer request refused',
    push_text = 'The new owner refused to transfer the supply point.',
    push_redirection = '/customer-requests/${target.entity.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_REJECT_REQUEST') and upper(locale) = 'EN';

UPDATE notification_template_i18n
SET push_title = 'Prekročenie rezervovanej kapacity elektriny',
    push_body = 'Prekročili ste maximálnu kapacitu elektriny',
    push_text = 'Upozorňujeme Vás, že na odbernom mieste ste prekročili Vašu maximálnu rezervovanú kapacitu pre spotrebu elektriny.',
    push_redirection = NULL,
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_MRK') and upper(locale) = 'SK';

UPDATE notification_template_i18n
SET push_title = 'Požiadavka na prepis odberného miesta',
    push_body = 'Vypršala platnosť žiadosti o prepis',
    push_text = 'Nový majiteľ nedokončil žiadosť o prepis odberného miesta v danom termíne.',
    push_redirection = '/customer-requests/${target.entity.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_COMPLETION_EXPIRED') and upper(locale) = 'SK';

UPDATE notification_template_i18n
SET push_title = 'Supply point transfer request',
    push_body = 'Supply point transfer request completion',
    push_text = 'The new owner completed the supply point transfer request.',
    push_redirection = '/customer-requests/${target.entity.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_COMPLETION_DONE') and upper(locale) = 'EN';

UPDATE notification_template_i18n
SET push_title = 'Supply point transfer request',
    push_body = 'Transfer request expired',
    push_text = 'The new owner did not complete the supply point transfer request by the deadline.',
    push_redirection = '/customer-requests/${target.entity.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_COMPLETION_EXPIRED') and upper(locale) = 'EN';

UPDATE notification_template_i18n
SET push_title = 'Prekročenie rezervovanej kapacity elektriny',
    push_body = 'Prekročili ste rezervovanú kapacitu elektriny',
    push_text = 'Upozorňujeme Vás, že na odbernom mieste ste prekročili Vašu rezervovanú kapacitu pre spotrebu elektriny.',
    push_redirection = NULL,
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_RK') and upper(locale) = 'SK';

UPDATE notification_template_i18n
SET push_title = 'Zmena stavu požiadavky',
    push_body = 'Pri riešení Vašej požiadavky došlo k zmene stavu',
    push_text = 'Pozrite si stav riešenia Vašej požiadavky.',
    push_redirection = '/customer-requests/${customerRequest.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_NOTE_CREATE') and upper(locale) = 'SK';

UPDATE notification_template_i18n
SET push_title = 'New access to supply point data',
    push_body = 'You have obtained new access to supply point data',
    push_text = 'Your account has been granted new access to the supply point data. See those data in your account.',
    push_redirection = '/delivery-points',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_SHARING_CONSUMER_GRANT') and upper(locale) = 'EN';

UPDATE notification_template_i18n
SET push_title = 'Požiadavka na prepis odberného miesta',
    push_body = 'Dokončite žiadosť o prepis odberného miesta',
    push_text = 'Pre dokončenie žiadosti a bližšie informácie o prepise odberného miesta kliknite.',
    push_redirection = '/customer-requests/${target.entity.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_COMPLETION_REQUEST') and upper(locale) = 'SK';

UPDATE notification_template_i18n
SET push_title = 'Request status change',
    push_body = 'The status of your request has changed',
    push_text = 'Check the status of your request.',
    push_redirection = '/customer-requests/${customerRequest.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_NOTE_CREATE') and upper(locale) = 'EN';

UPDATE notification_template_i18n
SET push_title = 'Zmena stavu požiadavky',
    push_body = 'Pri riešení Vašej požiadavky došlo k zmene stavu',
    push_text = 'Pozrite si stav riešenia Vašej požiadavky',
    push_redirection = '/customer-requests/${customerRequest.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_STATUS_CHANGE') and upper(locale) = 'SK';

UPDATE notification_template_i18n
SET push_title = 'Požiadavka na prepis odberného miesta',
    push_body = 'Máte nedokončenú požiadavku na prepis',
    push_text = 'Pre dokončenie žiadosti a bližšie informácie o prepise odberného miesta kliknite.',
    push_redirection = '/customer-requests/${target.entity.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_COMPLETION_REMINDER') and upper(locale) = 'SK';

UPDATE notification_template_i18n
SET push_title = 'Nový prístup k údajom odberných miest',
    push_body = 'Získali ste nový prístup k údajom odberného miesta',
    push_text = 'Do Vášho účtu bol pridelený nový prístup k údajom odberného miesta. Pozrite si tieto údaje vo svojom účte.',
    push_redirection = '/delivery-points',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_SHARING_CONSUMER_GRANT') and upper(locale) = 'SK';

UPDATE notification_template_i18n
SET push_title = 'Request status change',
    push_body = 'The status of your request has changed',
    push_text = 'Check the status of your request.',
    push_redirection = '/customer-requests/${customerRequest.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_STATUS_CHANGE') and upper(locale) = 'EN';

UPDATE notification_template_i18n
SET push_title = 'Požiadavka na prepis odberného miesta',
    push_body = 'Odmietnutie žiadosti o prepis',
    push_text = 'Nový majiteľ odmietol žiadosť o prepis odberného miesta.',
    push_redirection = '/customer-requests/${target.entity.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_REJECT_REQUEST') and upper(locale) = 'SK';

UPDATE notification_template_i18n
SET push_title = 'Požiadavka na prepis odberného miesta',
    push_body = 'Dokončenie žiadosti o prepis odberného miesta',
    push_text = 'Nový majiteľ dokončil žiadosť o prepis odberného miesta',
    push_redirection = '/customer-requests/${target.entity.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_COMPLETION_DONE') and upper(locale) = 'SK';

UPDATE notification_template_i18n
SET push_title = 'Priradenie odberných miest k účtu',
    push_body = 'K Vášmu účtu boli priradené odberné miesta',
    push_text = 'Od tohto okamihu si môžete sledovať detailné údaje a  zadávať online požiadavky.',
    push_redirection = '/delivery-points',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'BUSINESS_PARTNER_PAIRING_SUCCESS_AUTOMATIC') and upper(locale) = 'SK';

UPDATE notification_template_i18n
SET push_title = 'Prekročenie dennej spotreby plynu',
    push_body = 'Prekročili ste dennú spotrebu plynu',
    push_text = 'Upozorňujeme Vás, že na odbernom mieste ste prekročili dennú spotrebu plynu.',
    push_redirection = NULL,
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'DELIVERY_POINT_CHECK_DMM') and upper(locale) = 'SK';

UPDATE notification_template_i18n
SET push_title = 'Supply point transfer request',
    push_body = 'Complete the supply point transfer request',
    push_text = 'Click to complete the request and get more information about the supply point transfer.',
    push_redirection = '/customer-requests/${target.entity.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_COMPLETION_REQUEST') and upper(locale) = 'EN';

UPDATE notification_template_i18n
SET push_title = 'Supply point transfer request',
    push_body = 'You have an unfinished transfer request',
    push_text = 'Click to complete the request and get more information about the supply point transfer.',
    push_redirection = '/customer-requests/${target.entity.uuid}',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'CUSTOMER_REQUEST_COMPLETION_REMINDER') and upper(locale) = 'EN';

UPDATE notification_template_i18n
SET push_title = 'Self-reading option',
    push_body = 'Read the meter',
    push_text = 'Enter the readings in the My SPP portal.',
    push_redirection = '/delivery-points/${unitedDeliveryPoint.id}/self-read',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'METER_READING_CUSTOMER') and upper(locale) = 'EN';

UPDATE notification_template_i18n
SET push_title = 'Možnosť vykonať samoodpočet',
    push_body = 'Odčítajte hodnoty z meradla',
    push_text = 'Odčítané hodnoty zadajte v portáli Moje SPP.',
    push_redirection = '/delivery-points/${unitedDeliveryPoint.id}/self-read',
    updated_at = NOW(),
    version = version + 1
WHERE notification_template_id = (SELECT uuid FROM notification_template WHERE code = 'METER_READING_CUSTOMER') and upper(locale) = 'SK';
