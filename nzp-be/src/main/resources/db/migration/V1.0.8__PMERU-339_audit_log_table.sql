CREATE TABLE audit_log
(
    uuid            uuid                        NOT NULL,
    created_at      TIMESTAMP WITH TIME ZONE    NOT NULL,
    code            CHARACTER VARYING(50)       NOT NULL,
    entity_type     CHARACTER VARYING(50),
    entity_id       CHARACTER VARYING(72),
    session_id      CHARACTER VARYING(50),
    request_id      CHARACTER VARYING(50),
    remote_address  CHARACTER VARYING(20),
    customer        uuid,
    employee        uuid,
    attributes      text,

    CONSTRAINT pk_audit_log PRIMARY KEY (uuid)
)
WITH (
    OIDS = FALSE
);

CREATE INDEX idx_audit_code ON audit_log (code);
CREATE INDEX idx_audit_entity_type_id ON audit_log (entity_type, entity_id);
CREATE INDEX idx_audit_customer ON audit_log (customer);
CREATE INDEX idx_audit_employee ON audit_log (employee);

-- TODO: update SQL to create table with range partitioning for created_at column: ...PARTITION BY RANGE (created_at)
-- TODO: create SQL procedure to manage partitions creation