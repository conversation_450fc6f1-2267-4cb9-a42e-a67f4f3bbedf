package sk.spp.nzp.be.assembler.notification.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.notification.NotificationTemplateAttachment;
import sk.spp.nzp.be.assembler.notification.NotificationTemplateAttachmentAssembler;
import sk.spp.nzp.commons.model.notification.NotificationTemplateAttachmentEntity;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class NotificationTemplateAttachmentAssemblerImpl implements NotificationTemplateAttachmentAssembler {

    @Override
    public NotificationTemplateAttachmentEntity map(NotificationTemplateAttachment input, NotificationTemplateAttachmentEntity output) {

        output.setChannel(input.getChannel());
        output.setDescription(input.getDescription());
        output.setLength(input.getLength());
        output.setMimeType(input.getMimeType());
        output.setName(input.getName());
        output.setStatus(input.getStatus());
        output.setFileName(input.getFileName());

        return output;
    }

    @Override
    public NotificationTemplateAttachment map(NotificationTemplateAttachmentEntity input, NotificationTemplateAttachment output) {

        output.setId(input.getId().toString());
        output.setChannel(input.getChannel());
        output.setDescription(input.getDescription());
        output.setLength(input.getLength());
        output.setMimeType(input.getMimeType());
        output.setName(input.getName());
        output.setStatus(input.getStatus());
        output.setFileName(input.getFileName());

        return output;
    }

    @Override
    public NotificationTemplateAttachment mapWithContent(NotificationTemplateAttachmentEntity input, NotificationTemplateAttachment output) {

        map(input, output);
        output.setContent(input.getContent());

        return output;
    }

    @Override
    public List<NotificationTemplateAttachment> map(Collection<NotificationTemplateAttachmentEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new NotificationTemplateAttachment())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }


    @Override
    public List<NotificationTemplateAttachment> mapWithContent(Collection<NotificationTemplateAttachmentEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> mapWithContent(i, new NotificationTemplateAttachment())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}
