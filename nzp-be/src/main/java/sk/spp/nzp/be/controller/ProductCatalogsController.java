package sk.spp.nzp.be.controller;

import org.springframework.web.bind.annotation.*;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.annotation.paging.PagingAsQueryParams;
import sk.spp.nzp.be.api.common.PagedResponse;
import sk.spp.nzp.be.api.common.QueryStringPaging;
import sk.spp.nzp.be.api.customerprofile.Product;
import sk.spp.nzp.be.api.customerprofile.ProductsSearch;
import sk.spp.nzp.be.api.customerprofile.TariffAndRatesSearch;
import sk.spp.nzp.be.api.customerprofile.TariffRate;
import sk.spp.nzp.be.service.customerprofile.ProductCatalogService;
import sk.spp.nzp.commons.api.customerprofile.enums.DeliveryCategory;
import sk.spp.nzp.commons.api.customerprofile.enums.ProductStatus;
import sk.spp.nzp.commons.api.customerprofile.enums.ProductType;
import sk.spp.nzp.commons.api.customerprofile.enums.TariffStatus;
import sk.spp.nzp.commons.api.customerprofile.enums.TariffType;

import java.util.Set;

@RestController
@RequestMapping("/product-catalogs")
public class ProductCatalogsController {

    private ProductCatalogService productCatalogService;

    public ProductCatalogsController(ProductCatalogService productCatalogService) {
        this.productCatalogService = productCatalogService;
    }

    @Log
    @LogParam
    @GetMapping("/tariff-rates")
    @PagingAsQueryParams
    public PagedResponse<TariffRate> getTariffsAndRates(
            @LogParam("type") TariffType type,
            @LogParam("status") TariffStatus status,
            @LogParam("category") DeliveryCategory category,
            @LogParam("distributionArea") @RequestParam(required = false) String distributionArea,
            @LogParam("paging") QueryStringPaging paging
    ) {
        TariffAndRatesSearch search = new TariffAndRatesSearch();
        search.setType(type);
        search.setStatus(status == null ? TariffStatus.ACTIVE : status);
        search.setPaging(paging.toPaging());

        if (category != null) {
            search.addCategory(category);
        }
        search.setDistributionArea(distributionArea);

        return productCatalogService.fetchTariffsAndRates(search);
    }

    @Log
    @LogParam
    @PostMapping("/tariff-rates/search")
    public PagedResponse<TariffRate> searchTariffsAndRates(@LogParam("search") @RequestBody TariffAndRatesSearch search) {
        return productCatalogService.fetchTariffsAndRates(search);
    }

    @Log
    @LogParam
    @GetMapping("/products")
    @PagingAsQueryParams
    public PagedResponse<Product> getProducts(
            @LogParam("type") ProductType type,
            @LogParam("status") ProductStatus status,
            @LogParam("paging") QueryStringPaging paging
    ) {
        ProductsSearch search = new ProductsSearch();
        search.setStatus(status == null ? ProductStatus.ACTIVE : null);
        search.setPaging(paging.toPaging());

        if (type != null) {
            search.setTypes(Set.of(type));
            search.setExcludeOtherTypes(true);
        }

        return productCatalogService.fetchProducts(search);
    }

    @Log
    @LogParam
    @PostMapping("/products/search")
    public PagedResponse<Product> searchProducts(@LogParam("search") @RequestBody ProductsSearch search) {
        return productCatalogService.fetchProducts(search);
    }

}
