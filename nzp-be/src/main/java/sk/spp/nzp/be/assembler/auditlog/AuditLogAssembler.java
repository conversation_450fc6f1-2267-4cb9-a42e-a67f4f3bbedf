package sk.spp.nzp.be.assembler.auditlog;

import sk.spp.nzp.be.api.auditlog.AuditLog;
import sk.spp.nzp.be.api.auditlog.AuditLogSummary;
import sk.spp.nzp.commons.model.audit.AuditLogEntity;

import java.util.List;

public interface AuditLogAssembler {

    AuditLogSummary assemble(AuditLogEntity input, AuditLogSummary output, boolean canFillEmployee, String employeeLogin);

    AuditLog assemble(AuditLogEntity input, AuditLog output, boolean canFillEmployee, String employeeLogin);

    List<AuditLog> map(List<AuditLogEntity> input, boolean canFillEmployee, String employeeLogin);

    List<AuditLogSummary> mapSummary(List<AuditLogEntity> input, boolean canFillEmployee, String employeeLogin);

    List<AuditLog> mapFull(List<AuditLogEntity> input, boolean canFillEmployee, String employeeLogin);

    AuditLog mapFull(AuditLogEntity input, AuditLog output, boolean canFillEmployee, String employeeLogin);

}
