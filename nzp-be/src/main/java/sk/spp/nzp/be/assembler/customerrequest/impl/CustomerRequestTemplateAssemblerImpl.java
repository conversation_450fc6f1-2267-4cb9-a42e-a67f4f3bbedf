package sk.spp.nzp.be.assembler.customerrequest.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestTemplate;
import sk.spp.nzp.be.assembler.customerrequest.CustomerRequestTemplateAssembler;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestTemplateEntity;
import sk.spp.nzp.commons.service.common.LocaleResolver;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class CustomerRequestTemplateAssemblerImpl implements CustomerRequestTemplateAssembler {

    private LocaleResolver localeResolver;

    public CustomerRequestTemplateAssemblerImpl(LocaleResolver localeResolver) {
        this.localeResolver = localeResolver;
    }

    @Override
    public CustomerRequestTemplate map(CustomerRequestTemplateEntity input, CustomerRequestTemplate output) {
        output.setUuid(input.getId().toString());
        output.setCode(input.getCode());
        output.setStatus(input.getStatus());
        output.setPriceCollective(input.getPriceCollective());
        output.setPriceIndividual(input.getPriceIndividual());
        output.setType(input.getType());
        output.setLink(input.getLink());
        output.setConfirmationValidDays(input.getConfirmationValidDays());
        output.setCompletionValidDays(input.getCompletionValidDays());
        output.setAutomated(input.getAutomated());

        localeResolver.resolve(Optional.ofNullable(input.getTranslations()).orElse(Collections.emptyList())).ifPresent(v->{
            output.setName(v.getName());
            output.setDescription(v.getDescription());
        });

        return output;
    }

    @Override
    public List<CustomerRequestTemplate> map(List<CustomerRequestTemplateEntity> input, List<CustomerRequestTemplate> output) {
        List<CustomerRequestTemplate> templates = input.stream().map(e -> map(e, new CustomerRequestTemplate())).collect(Collectors.toList());
        output.addAll(templates);

        return output;
    }

}

