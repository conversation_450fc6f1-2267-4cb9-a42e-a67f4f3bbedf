package sk.spp.nzp.be.configuration;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;
import sk.isdd.common.logging.Logged;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

@Validated
@Configuration
@ConditionalOnProperty(value="restriction.policy.enabled", havingValue = "true")
@ConfigurationProperties(prefix="restriction.policy")
public class RateLimitConfigurationHolder {

    @NotEmpty
    private List<RateLimitConfigEntry> entries = new ArrayList<>();

    public List<RateLimitConfigEntry> getEntries() {
        return entries;
    }

    public void setEntries(List<RateLimitConfigEntry> entries) {
        this.entries = entries;
    }

    public static class RateLimitConfigEntry {

        @Logged
        private String url;

        @Logged
        private RestrictionType keyType;

        @Logged
        private RestrictAction action;

        @Logged
        private Long periodMinutes;

        @Logged
        private Long count;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public RestrictionType getKeyType() {
            return keyType;
        }

        public void setKeyType(RestrictionType keyType) {
            this.keyType = keyType;
        }

        public RestrictAction getAction() {
            return action;
        }

        public void setAction(RestrictAction action) {
            this.action = action;
        }

        public Long getPeriodMinutes() {
            return periodMinutes;
        }

        public void setPeriodMinutes(Long periodMinutes) {
            this.periodMinutes = periodMinutes;
        }

        public Long getCount() {
            return count;
        }

        public void setCount(Long count) {
            this.count = count;
        }

        @Override
        public String toString() {
            return "RateLimitConfigEntry{" +
                    "url='" + url + '\'' +
                    ", keyType=" + keyType +
                    ", action=" + action +
                    ", periodMinutes=" + periodMinutes +
                    ", count=" + count +
                    '}';
        }
    }

    public enum RestrictionType {
        IP,
        URL
    }

    public enum RestrictAction {
        DENIED,
        GOOGLE_RECAPTCHA
    }

}
