package sk.spp.nzp.be.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.spp.nzp.be.service.customerprofile.ContractAccountService;
import sk.spp.nzp.be.service.customerprofile.ContractService;
import sk.spp.nzp.be.service.customerprofile.DeliveryPointService;
import sk.spp.nzp.be.service.customerprofile.InvoiceService;

@RestController
@RequestMapping("/contract-accounts")
public class ContractAccountController {

    private ContractAccountService contractAccountService;
    private DeliveryPointService deliveryPointService;
    private ContractService contractService;
    private InvoiceService invoiceService;

    public ContractAccountController(ContractAccountService contractAccountService,
                                     DeliveryPointService deliveryPointService,
                                     ContractService contractService,
                                     InvoiceService invoiceService) {
        
        this.contractAccountService = contractAccountService;
        this.deliveryPointService = deliveryPointService;
        this.contractService = contractService;
        this.invoiceService = invoiceService;
    }

    // TODO: delete unnecessary code
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("hasRole('CUSTOMER') and (not hasRole('EMPLOYEE') OR hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE'))")
//    @GetMapping(value = "/{uuid}", produces = {MediaType.APPLICATION_JSON_VALUE})
//    public ContractAccount getByUuid(@LogParam("uuid") @PathVariable(value = "uuid") String uuid) {
//
//        return contractAccountService.getByUuid(UUID.fromString(uuid));
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("hasRole('CUSTOMER') and (not hasRole('EMPLOYEE') OR hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE'))")
//    @GetMapping(value = "/{uuid}/contracts", produces = {MediaType.APPLICATION_JSON_VALUE})
//    @PagingAsQueryParams
//    public PagedResponse<Contract> getContracts(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("queryStringPaging ")  QueryStringPaging  queryStringPaging) {
//
//        return contractService.getByContractAccount(uuid, new ContractSearch().setPaging(queryStringPaging.toPaging()));
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("hasRole('CUSTOMER') and (not hasRole('EMPLOYEE') OR hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE'))")
//    @PostMapping(value = "/{uuid}/contracts/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public PagedResponse<Contract> contractSearch(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("contractSearch ") @RequestBody ContractSearch contractSearch) {
//
//        return contractService.getByContractAccount(uuid, contractSearch);
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and not hasRole('ROLE_EMPLOYEE')) or (hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW'))")
//    @GetMapping(value = "/{uuid}/delivery-points", produces = {MediaType.APPLICATION_JSON_VALUE})
//    @PagingAsQueryParams
//    public PagedResponse<DeliveryPoint> getDeliveryPoints(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("queryStringPaging ")  QueryStringPaging  queryStringPaging) {
//
//        return deliveryPointService.getByContractAccount(uuid, new DeliveryPointSearch().setPaging(queryStringPaging.toPaging()));
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and not hasRole('ROLE_EMPLOYEE')) or (hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW'))")
//    @PostMapping(value = "/{uuid}/delivery-points/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public PagedResponse<DeliveryPoint> deliveryPointSearch(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("deliveryPointSearch ") @RequestBody DeliveryPointSearch deliveryPointSearch) {
//
//        return deliveryPointService.getByContractAccount(uuid, deliveryPointSearch);
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and not hasRole('ROLE_EMPLOYEE')) or (hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW'))")
//    @GetMapping(value = "/{uuid}/invoices", produces = {MediaType.APPLICATION_JSON_VALUE})
//    @PagingAsQueryParams
//    public PagedResponse<Invoice> getInvoices(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("queryStringPaging ")  QueryStringPaging  queryStringPaging) {
//
//        return invoiceService.getInvoices(
//                new InvoiceSearch()
//                        .setPaging(queryStringPaging.toPaging())
//                        .withContractAccountId(uuid)
//        );
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and not hasRole('ROLE_EMPLOYEE')) or (hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW'))")
//    @PostMapping(value = "/{uuid}/invoices/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public PagedResponse<Invoice> invoiceSearch(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("invoiceSearch ") @RequestBody InvoiceSearch invoiceSearch) {
//
//        return invoiceService.getInvoices(invoiceSearch.withContractAccountId(uuid));
//    }
}
