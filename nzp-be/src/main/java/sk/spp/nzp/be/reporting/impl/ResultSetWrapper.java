package sk.spp.nzp.be.reporting.impl;

import sk.spp.nzp.be.reporting.IteratorWrapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class ResultSetWrapper implements IteratorWrapper {

    private ResultSet resultSet;

    public ResultSetWrapper(ResultSet resultSet) {
        this.resultSet = resultSet;
    }

    @Override
    public boolean hasNext() throws SQLException {
        return resultSet.next();
    }

    @Override
    public Object[] next() throws SQLException {
        return mapToObjectArray(resultSet);
    }

    @Override
    public void close() throws SQLException {
        //do nothing - JdbcTemplate will close
    }

    private Object[] mapToObjectArray(ResultSet resultSet) throws SQLException {
        int cols = resultSet.getMetaData().getColumnCount();
        Object[] arr = new Object[cols];
        for (int i = 0; i < cols; i++) {
            arr[i] = resultSet.getObject(i + 1);
        }
        return arr;
    }
}
