package sk.spp.nzp.be.converters;

import org.apache.commons.lang3.tuple.MutablePair;
import sk.spp.nzp.be.api.customerconsumption.MeterReading;
import sk.spp.nzp.commons.api.customerconsumption.ConsumptionSearch;
import sk.spp.nzp.commons.api.customerconsumption.ConsumptionSearchResponse;
import sk.spp.nzp.commons.api.customerprofile.enums.DeliveryPointType;
import sk.spp.nzp.commons.api.customerprofile.enums.Units;
import sk.spp.nzp.commons.model.customerprofile.DeliveryPointEntity;

import java.math.BigDecimal;

public interface MeterReadingUnitsConverter {

    void convert(MeterReading meterReading, DeliveryPointEntity deliveryPoint, Units units);

    void convert(ConsumptionSearchResponse consumption, DeliveryPointEntity deliveryPoint, ConsumptionSearch consumptionSearch);

    MutablePair<BigDecimal, Units> convert(BigDecimal value, DeliveryPointType type, Units units);

    Units getDefaultUnits(DeliveryPointType type);
}