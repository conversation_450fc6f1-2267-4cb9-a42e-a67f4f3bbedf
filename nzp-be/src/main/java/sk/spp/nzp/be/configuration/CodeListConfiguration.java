package sk.spp.nzp.be.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import sk.spp.nzp.be.service.codelist.CodeListProvider;
import sk.spp.nzp.be.service.codelist.provider.EnumCodeListProvider;
import sk.spp.nzp.commons.api.customerprofile.enums.ContractStatus;
import sk.spp.nzp.commons.model.audit.enums.AuditLogCode;
import sk.spp.nzp.commons.model.codelist.enums.GenericCodeListStatus;

import java.util.List;

@Configuration
public class CodeListConfiguration {

    @Bean(name = "enumCodeListProvider")
    public CodeListProvider enumCodeListProvider() {
        return new EnumCodeListProvider(List.of(
                GenericCodeListStatus.class,
                AuditLogCode.class,
                ContractStatus.class
        ));
    }

}
