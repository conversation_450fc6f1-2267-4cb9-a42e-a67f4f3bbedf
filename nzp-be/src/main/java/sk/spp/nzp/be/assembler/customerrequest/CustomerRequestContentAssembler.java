package sk.spp.nzp.be.assembler.customerrequest;

import sk.spp.nzp.commons.model.customerprofile.enums.CompletionView;
import sk.spp.nzp.commons.api.customerrequest.request.base.CustomerRequestContent;

public interface CustomerRequestContentAssembler {

    CustomerRequestContent getFilteredContent(CustomerRequestContent entityContent, CompletionView completionView);

    CustomerRequestContent filterUpdateContent(CustomerRequestContent dtoContent, CustomerRequestContent currentEntityContent, CompletionView completionView);

}