package sk.spp.nzp.be.controller;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.service.notification.NotificationScheduleService;
import sk.spp.nzp.be.service.notification.NotificationTemplateAttachmentService;
import sk.spp.nzp.be.service.notification.NotificationTemplateService;
import sk.spp.nzp.be.service.reporting.ReportService;

import java.util.UUID;

@RestController
@RequestMapping("/admin/notification-schedules")
public class AdminNotificationScheduleController {
    private NotificationTemplateService notificationTemplateService;

    private NotificationScheduleService notificationScheduleService;

    private NotificationTemplateAttachmentService notificationTemplateAttachmentService;

    private ReportService reportService;

    public AdminNotificationScheduleController(NotificationTemplateService notificationTemplateService,
                                               NotificationScheduleService notificationScheduleService,
                                               NotificationTemplateAttachmentService notificationTemplateAttachmentService,
                                               ReportService reportService) {
        this.notificationTemplateService = notificationTemplateService;
        this.notificationScheduleService = notificationScheduleService;
        this.notificationTemplateAttachmentService = notificationTemplateAttachmentService;
        this.reportService = reportService;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_EDIT')")
    @PostMapping(value = "/{uuid}/stop")
    public void stopNotificationTemplate(@LogParam("uuid") @PathVariable UUID uuid) {

        notificationScheduleService.stopNotificationSchedule(uuid);
    }
}