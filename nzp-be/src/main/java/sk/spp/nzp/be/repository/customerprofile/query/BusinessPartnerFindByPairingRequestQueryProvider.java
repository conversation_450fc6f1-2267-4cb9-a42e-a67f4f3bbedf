package sk.spp.nzp.be.repository.customerprofile.query;

import javax.persistence.EntityManager;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;

import sk.spp.nzp.be.api.customerprofile.PairBusinessPartnerRequest;
import sk.spp.nzp.be.api.customerprofile.PairBusinessPartnerRequestScope;
import sk.spp.nzp.be.api.customerprofile.PairBusinessPartnerRequestScopeKey;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.model.customerprofile.BusinessPartnerEntity;
import sk.spp.nzp.commons.model.customerprofile.QBusinessPartnerEntity;
import sk.spp.nzp.commons.model.customerprofile.QContractAccountEntity;
import sk.spp.nzp.commons.model.customerprofile.QContractEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

public class BusinessPartnerFindByPairingRequestQueryProvider  implements QueryDslProvider<BusinessPartnerEntity> {

    private PairBusinessPartnerRequest pairBusinessPartnerRequest;
    private BusinessPartnerQueue businessPartnerQueue;
    
    public BusinessPartnerFindByPairingRequestQueryProvider(PairBusinessPartnerRequest pairBusinessPartnerRequest, BusinessPartnerQueue businessPartnerQueue) {
        
        this.pairBusinessPartnerRequest = pairBusinessPartnerRequest;
        this.businessPartnerQueue = businessPartnerQueue;
    }

    @Override
    public JPAQuery<BusinessPartnerEntity> getQuery(EntityManager em) {
        
        QBusinessPartnerEntity businessPartnerEntity = QBusinessPartnerEntity.businessPartnerEntity;
        QContractAccountEntity contractAccountEntity = QContractAccountEntity.contractAccountEntity;
        QContractEntity contractEntity = QContractEntity.contractEntity;
        
        JPAQuery<BusinessPartnerEntity> output = new JPAQuery<BusinessPartnerEntity>(em).from(businessPartnerEntity);
        
        
        BooleanExpression bpExtIdExp = null;
        PairBusinessPartnerRequestScope bpScope = pairBusinessPartnerRequest.getScopes().stream().filter(i-> PairBusinessPartnerRequestScopeKey.BUSINESS_PARTNER_EXTERNAL_ID.equals(i.getScope())).findFirst().orElse(null);
        
        if(bpScope != null && bpScope.getValue() != null) {
            bpExtIdExp = businessPartnerEntity.externalId.eq(bpScope.getValue());
        }
        
        
        BooleanExpression bpCrnExp = null;
        PairBusinessPartnerRequestScope bpCrmScope = pairBusinessPartnerRequest.getScopes().stream().filter(i-> PairBusinessPartnerRequestScopeKey.BUSINESS_PARTNER_ICO.equals(i.getScope())).findFirst().orElse(null);
        
        if(bpCrmScope != null && bpCrmScope.getValue() != null) {
            bpCrnExp = businessPartnerEntity.companyRegistrationNumber.eq(bpCrmScope.getValue());
        }

        BooleanExpression bpQueueExp = null;
        if(businessPartnerQueue != null) {
            bpQueueExp = businessPartnerEntity.queue.eq(businessPartnerQueue);
        }
        
        
        PairBusinessPartnerRequestScope contractScope = pairBusinessPartnerRequest.getScopes().stream().filter(i-> PairBusinessPartnerRequestScopeKey.CONTRACT_EXTERNAL_ID.equals(i.getScope())).findFirst().orElse(null);

        if(contractScope != null && contractScope.getValue() != null) {
            output
                .join(contractAccountEntity).on(contractAccountEntity.businessPartnerId.eq(businessPartnerEntity.id))
                .join(contractEntity).on(contractEntity.externalId.eq(contractScope.getValue()), contractEntity.contractAccountId.eq(contractAccountEntity.id));
        }
        
        output = output.where(bpExtIdExp, bpCrnExp, bpQueueExp);
        
        return output;
    }
}
