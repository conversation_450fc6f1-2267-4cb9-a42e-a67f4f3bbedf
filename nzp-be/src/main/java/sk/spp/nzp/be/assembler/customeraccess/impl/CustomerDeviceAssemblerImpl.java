package sk.spp.nzp.be.assembler.customeraccess.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customeraccess.CustomerDeviceCreateRequest;
import sk.spp.nzp.be.assembler.customeraccess.CustomerDeviceAssembler;
import sk.spp.nzp.commons.model.customeraccess.CustomerDeviceEntity;

@Component
public class CustomerDeviceAssemblerImpl implements CustomerDeviceAssembler {
    @Override
    public CustomerDeviceEntity map(CustomerDeviceCreateRequest input, CustomerDeviceEntity output) {
        output.setToken(input.getDeviceToken());
        output.setName(input.getDeviceName());
        return output;
    }
}
