package sk.spp.nzp.be.controller;

import java.util.Collection;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.commons.api.employeeaccess.AccessRight;
import sk.spp.nzp.be.service.employeeaccess.AccessGroupService;

@RestController
@RequestMapping("/admin/access-rights")
public class AdminAccessRightController {

    private AccessGroupService accessGroupService;
    
    public AdminAccessRightController(AccessGroupService accessGroupService) {
        
        this.accessGroupService = accessGroupService;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('ACCESS_GROUPS_VIEW')")
    @GetMapping(value = "/")
    public Collection<AccessRight> getAllAccessRights() {

        return accessGroupService.getAllAccessRights();
    }
}
