package sk.spp.nzp.be.assembler.customerconsumption;

import java.util.Collection;
import java.util.List;

import sk.spp.nzp.be.api.customerconsumption.MeterReading;
import sk.spp.nzp.commons.api.customerprofile.enums.Units;
import sk.spp.nzp.commons.model.customerconsumption.MeterReadingEntity;

public interface MeterReadingAssembler {

    MeterReading map(MeterReadingEntity input, MeterReading output);

    MeterReading map(MeterReadingEntity input, MeterReading output, Units units);

    List<MeterReading> map(Collection<MeterReadingEntity> input);

    List<MeterReading> map(Collection<MeterReadingEntity> input, Units units);
}
