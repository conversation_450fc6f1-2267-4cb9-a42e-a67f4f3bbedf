package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.apache.commons.lang3.RegExUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.common.Sharing;
import sk.spp.nzp.be.api.common.SharingInfo;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerSummary;
import sk.spp.nzp.be.api.customerprofile.ContractAccountSummary;
import sk.spp.nzp.be.api.customerprofile.DeliveryPointSummary;
import sk.spp.nzp.be.api.customerprofile.InvoiceSummary;
import sk.spp.nzp.be.assembler.customerprofile.BusinessPartnerAssembler;
import sk.spp.nzp.be.assembler.customerprofile.ContractAccountAssembler;
import sk.spp.nzp.be.assembler.customerprofile.DeliveryPointAssembler;
import sk.spp.nzp.be.assembler.customerprofile.InvoiceSummaryAssembler;
import sk.spp.nzp.be.service.customerprofile.InvoiceEntityService;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceStatus;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceTypeGroup;
import sk.spp.nzp.commons.api.customersharing.enums.OwnershipType;
import sk.spp.nzp.commons.assembler.codelist.CodeListItemAssembler;
import sk.spp.nzp.commons.enums.CodeListType;
import sk.spp.nzp.commons.enums.InvoiceTypeCode;
import sk.spp.nzp.commons.model.customerprofile.ContractAccountEntity;
import sk.spp.nzp.commons.model.customerprofile.InvoiceEntity;
import sk.spp.nzp.commons.model.customersharing.ContractAccountOwnershipEntity;
import sk.spp.nzp.commons.service.common.HistoryContextProvider;
import sk.spp.nzp.commons.service.common.impl.HistoryPeriod;
import sk.spp.nzp.commons.utils.Expressions;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class InvoiceSummaryAssemblerImpl implements InvoiceSummaryAssembler {

    public final static String LEADING_ZERO_REGEX = "^0+";

    private DeliveryPointAssembler deliveryPointAssembler;
    private CodeListItemAssembler genericCodeListAssembler;
    private BusinessPartnerAssembler businessPartnerAssembler;
    private ContractAccountAssembler contractAccountAssembler;
    private HistoryContextProvider historyContextProvider;
    private InvoiceEntityService invoiceEntityService;

    public InvoiceSummaryAssemblerImpl(
            DeliveryPointAssembler deliveryPointAssembler,
            CodeListItemAssembler genericCodeListAssembler,
            BusinessPartnerAssembler businessPartnerAssembler,
            ContractAccountAssembler contractAccountAssembler,
            HistoryContextProvider historyContextProvider,
            InvoiceEntityService invoiceEntityService) {

        this.deliveryPointAssembler = deliveryPointAssembler;
        this.genericCodeListAssembler = genericCodeListAssembler;
        this.businessPartnerAssembler = businessPartnerAssembler;
        this.contractAccountAssembler = contractAccountAssembler;
        this.historyContextProvider = historyContextProvider;
        this.invoiceEntityService = invoiceEntityService;
    }

    @Override
    public List<InvoiceSummary> map(Collection<InvoiceEntity> input) {

        if(input != null && !input.isEmpty()) {
            return input.stream().map(i -> map(i, new InvoiceSummary())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public InvoiceSummary map(InvoiceEntity input, InvoiceSummary output) {
        return map(input, output, false);
    }

    @Override
    public InvoiceSummary map(InvoiceEntity input, InvoiceSummary output, boolean onlyRefData) {

        output.setId(input.getId());
        output.setExternalId(RegExUtils.replaceFirst(input.getExternalId(), LEADING_ZERO_REGEX,""));

        if (onlyRefData) {
            return output;
        }

        output.setReference(RegExUtils.replaceFirst(input.getReference(), LEADING_ZERO_REGEX,""));
        output.setStatus(input.getStatus());
        output.setAmount(input.getAmount());
        output.setUnpaid(input.getUnpaid());
        output.setOverpaid(input.getOverpaid());
        output.setFileArchiveId(input.getFileArchiveId());
        output.setTypeGroup(input.getTypeGroup());
        output.setDueAt(input.getDueAt());
        output.setIssueAt(input.getIssueAt());
        output.setVs(RegExUtils.replaceFirst(input.getVs(), LEADING_ZERO_REGEX,""));
        output.setContainsPaymentPlan(input.getContainsPaymentPlan());
        output.setContainsPaymentRequest(input.getContainsPaymentRequest());
        output.setPaymentVisibility(invoiceEntityService.getPaymentVisibility(input));

        if(input.getType() != null) {
            output.setType(genericCodeListAssembler.mapSummary(CodeListType.INVOICE_TYPE, input.getType(), new CodeListItem()));
        }

        Optional.ofNullable(input.getSubType())
                .map(subType -> genericCodeListAssembler.mapSummary(CodeListType.INVOICE_SUB_TYPE, subType.name(), new CodeListItem()))
                .ifPresent(output::setSubType);

        if (input.getInvoiceContractList() != null) {
            output.setDeliveryPoints(
                    input.getInvoiceContractList()
                            .stream()
                            .filter(v-> Expressions.tryGet(()->v.getContract().getDeliveryPoint()) != null)
                            .map(v->deliveryPointAssembler.map(
                                    v.getContract().getDeliveryPoint(),
                                    new DeliveryPointSummary(),
                                    historyContextProvider.createDeliveryPointHistoryContext(v.getContract(), HistoryPeriod.CURRENT)))
                            .collect(Collectors.toList())
            );
        }

        if (input.getContractAccount() != null && input.getBusinessPartner() != null) {
            output.setBusinessPartner(businessPartnerAssembler.map(input.getBusinessPartner(), new BusinessPartnerSummary()));
        }

        if (input.getContractAccount() != null) {
            output.setContractAccount(contractAccountAssembler.mapSummary(input.getContractAccount(), new ContractAccountSummary(), false));
        }

        List<ContractAccountOwnershipEntity> ownerships = Optional.ofNullable(input.getContractAccount())
                .map(ContractAccountEntity::getOwnerships)
                .orElse(List.of());

        if (!ownerships.isEmpty()) {
            output.setSharing(getSharing(ownerships));
        }

        setInstallmentVisibility(output);

        return output;
    }

    // Sharings
    private Sharing getSharing(List<ContractAccountOwnershipEntity> ownerships) {
        Sharing sharing = new Sharing();

        for (ContractAccountOwnershipEntity ownership : ownerships) {
            SharingInfo info = convert(ownership);

            if (OwnershipType.OWNER.equals(ownership.getType())) {
                sharing.setBy(info); // Expecting only single owner

            } else {
                sharing.addTo(info);
            }
        }
        return sharing;
    }

    private SharingInfo convert(ContractAccountOwnershipEntity ownership) {
        SharingInfo info = new SharingInfo();

        Optional.ofNullable(ownership.getCustomerAccount())
                .ifPresent(customer -> {

                    info.setEmail(customer.getEmail());
                    info.setFirstName(customer.getFirstName());
                    info.setLastName(customer.getLastName());
                });

        info.setInherited(ownership.isInherited());

        return info;
    }

    private void setInstallmentVisibility(InvoiceSummary input) {

        boolean isInvoiceGroup = InvoiceTypeGroup.INVOICE.equals(input.getTypeGroup())
                || InvoiceTypeGroup.OTHERS.equals(input.getTypeGroup());

        boolean isUnpaid = InvoiceStatus.UNPAID.equals(input.getStatus())
                || InvoiceStatus.PARTIALLY_PAID.equals(input.getStatus());

        boolean sharedToEmpty = Optional.ofNullable(input.getSharing())
                .map(Sharing::getTo)
                .map(List::isEmpty)
                .orElse(true);

        boolean isInstallmentEligible = Optional.ofNullable(input.getType())
                .map(CodeListItem::getCode)
                .flatMap(InvoiceTypeCode::fromString)
                .map(InvoiceTypeCode::isInstallmentEligible)
                .orElse(false);

        input.setInstallmentVisibility(isInvoiceGroup && isUnpaid && sharedToEmpty && isInstallmentEligible);
    }

}
