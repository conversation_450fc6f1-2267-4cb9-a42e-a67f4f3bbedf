package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerprofile.DeliveryPointSummary;
import sk.spp.nzp.be.api.customerprofile.InvoiceItem;
import sk.spp.nzp.be.assembler.customerprofile.DeliveryPointAssembler;
import sk.spp.nzp.be.assembler.customerprofile.InvoiceItemAssembler;
import sk.spp.nzp.commons.model.customerprofile.InvoiceRawEntity;
import sk.spp.nzp.commons.service.common.HistoryContextProvider;
import sk.spp.nzp.commons.service.common.impl.HistoryPeriod;
import sk.spp.nzp.commons.utils.Expressions;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class InvoiceItemAssemblerImpl implements InvoiceItemAssembler {

    private DeliveryPointAssembler deliveryPointAssembler;
    private HistoryContextProvider historyContextProvider;

    public InvoiceItemAssemblerImpl(DeliveryPointAssembler deliveryPointAssembler, HistoryContextProvider historyContextProvider) {
        this.historyContextProvider = historyContextProvider;
        this.deliveryPointAssembler = deliveryPointAssembler;
    }

    @Override
    public InvoiceItem map(InvoiceRawEntity input, InvoiceItem output) {
        output.setId(input.getId().toString());
        output.setAmount(input.getAmount());
        output.setStatus(input.getStatus());
        output.setExecuteAt(input.getExecuteAt());
        output.setStatus(input.getStatus());
        output.setDueAt(input.getDueAt());
        output.setRepaymentPlanId(input.getRepaymentPlanId());
        output.setPaymentRequestInvoiceId(input.getPaymentRequestInvoiceId());
        output.setVs(input.getVs());
        return output;
    }

    @Override
    public InvoiceItem mapFull(InvoiceRawEntity input, InvoiceItem output) {
        map(input, output);

        if (Expressions.tryGet(()->input.getContract().getDeliveryPoint()) != null) {
            output.setDeliveryPointSummary(deliveryPointAssembler.map(input.getContract().getDeliveryPoint(), new DeliveryPointSummary(),
                    historyContextProvider.createDeliveryPointHistoryContext(input.getContract(), HistoryPeriod.CURRENT)));
        }
        return output;
    }

    @Override
    public List<InvoiceItem> map(Collection<InvoiceRawEntity> input) {
        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new InvoiceItem())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }


    @Override
    public List<InvoiceItem> mapFull(Collection<InvoiceRawEntity> input) {
        if (input != null && !input.isEmpty()) {

            return input.stream()
                    .map(i -> mapFull(i, new InvoiceItem()))
                    .sorted((v1,v2)->v1.getDueAt().compareTo(v2.getDueAt()))
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}