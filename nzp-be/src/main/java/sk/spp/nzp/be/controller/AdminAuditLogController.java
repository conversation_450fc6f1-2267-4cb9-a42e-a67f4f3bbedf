package sk.spp.nzp.be.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;

import org.springframework.web.bind.annotation.*;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.auditlog.AuditLog;
import sk.spp.nzp.be.api.auditlog.AuditLogSearch;
import sk.spp.nzp.be.api.auditlog.AuditLogSummary;
import sk.spp.nzp.be.api.common.PagedResponse;
import sk.spp.nzp.commons.api.reporting.DataReportFormat;
import sk.spp.nzp.be.service.auditlog.AuditLogService;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/admin/audit-logs")
public class AdminAuditLogController {

    @Value("${reporting.export.filename}")
    private String fileName;

    private AuditLogService auditLogService;

    public AdminAuditLogController(AuditLogService auditLogService) {
        super();
        this.auditLogService = auditLogService;
    }
    
    @Log
    @LogParam
    @Secured("ROLE_EMPLOYEE")
    @PreAuthorize("hasAuthority('AUDIT_LOGS_CUSTOMERS') or hasAuthority('AUDIT_LOGS_CUSTOMERS_WITH_EMPLOYEES') or hasAuthority('AUDIT_LOGS_EMPLOYEES')")
    @PostMapping("/search")
    public PagedResponse<AuditLogSummary> searchAuditLogs(@LogParam("adminAuditLogSearch") @RequestBody AuditLogSearch adminAuditLogSearch) {
        return auditLogService.getByCustomerAuditLogSearch(adminAuditLogSearch);
    }

    @Log
    @LogParam
    @Secured("ROLE_EMPLOYEE")
    @PreAuthorize("hasAuthority('AUDIT_LOGS_CUSTOMERS') or hasAuthority('AUDIT_LOGS_CUSTOMERS_WITH_EMPLOYEES') or hasAuthority('AUDIT_LOGS_EMPLOYEES')")
    @GetMapping(value = "/{uuid}", produces = {"application/json"})
    public AuditLog getAuditLog(@LogParam("uuid") @PathVariable UUID uuid) {
        return auditLogService.getAuditLog(uuid);
    }

    @Log
    @LogParam
    @Secured("ROLE_EMPLOYEE")
    @PreAuthorize("hasAuthority('AUDIT_LOGS_CUSTOMERS') or hasAuthority('AUDIT_LOGS_CUSTOMERS_WITH_EMPLOYEES') or hasAuthority('AUDIT_LOGS_EMPLOYEES')")
    @PostMapping("/search/download-export")
    public void exportAuditLogs(@LogParam("adminAuditLogSearch") @RequestBody AuditLogSearch adminAuditLogSearch,
                                @LogParam("format") @RequestParam DataReportFormat format,
                                @LogParam("columns") @RequestParam(required = false) List<String> columns,
                                HttpServletResponse response) throws IOException {

        ContentDisposition contentDisposition;
        if (DataReportFormat.CSV.equals(format)) {
            contentDisposition = ContentDisposition.builder("attachment")
                    .filename(fileName + ".csv")
                    .build();

            response.setContentType("text/csv");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
        } else {
            contentDisposition = ContentDisposition.builder("attachment")
                    .filename(fileName + ".xlsx")
                    .build();

            response.setContentType("application/vnd.ms-excels");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
        }

        auditLogService.getByCustomerAuditLogSearchExport(adminAuditLogSearch, format, columns, response.getOutputStream());
    }
}
