package sk.spp.nzp.be.repository.customerprofile.query;

import java.util.UUID;

import javax.persistence.EntityManager;

import com.querydsl.jpa.impl.JPAQuery;

import sk.spp.nzp.be.models.customerprofile.ContractAccountSearch;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.model.customerprofile.ContractAccountEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

public class ContractAccountFindByBusinessPartnerUuidQueryProvider extends ContractAccountBaseFindByQueryProvider implements QueryDslProvider<ContractAccountEntity> {

    private String businessPartnerId;
    private ContractAccountSearch contractAccountSearch;
    private UUID sharedCustomerUuid;
    private BusinessPartnerQueue businessPartnerQueue;

    public ContractAccountFindByBusinessPartnerUuidQueryProvider(
            String businessPartnerId,
            ContractAccountSearch contractAccountSearch,
            UUID sharedCustomerUuid,
            BusinessPartnerQueue businessPartnerQueue
    ) {
        this.businessPartnerId = businessPartnerId;
        this.contractAccountSearch = contractAccountSearch;
        this.sharedCustomerUuid = sharedCustomerUuid;
        this.businessPartnerQueue = businessPartnerQueue;
    }

    @Override
    public JPAQuery<ContractAccountEntity> getQuery(EntityManager em) {
        return findByBaseQuery(em, businessPartnerId, contractAccountSearch, sharedCustomerUuid, businessPartnerQueue, true);
    }
}
