package sk.spp.nzp.be.assembler.customerprofile;

import sk.spp.nzp.be.api.customerprofile.InvoiceItem;
import sk.spp.nzp.commons.model.customerprofile.InvoiceRawEntity;

import java.util.Collection;
import java.util.List;

public interface InvoiceItemAssembler {

    InvoiceItem map(InvoiceRawEntity input, InvoiceItem output);

    List<InvoiceItem> map(Collection<InvoiceRawEntity> input);

    InvoiceItem mapFull(InvoiceRawEntity input, InvoiceItem output);

    List<InvoiceItem> mapFull(Collection<InvoiceRawEntity> input);
}