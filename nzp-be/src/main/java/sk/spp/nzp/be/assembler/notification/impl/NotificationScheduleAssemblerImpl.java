package sk.spp.nzp.be.assembler.notification.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.notification.CustomerNotificationCounts;
import sk.spp.nzp.be.api.notification.NotificationSchedule;
import sk.spp.nzp.be.api.notification.NotificationScheduleSummary;
import sk.spp.nzp.be.assembler.notification.NotificationScheduleAssembler;
import sk.spp.nzp.commons.model.notification.NotificationScheduleEntity;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class NotificationScheduleAssemblerImpl implements NotificationScheduleAssembler {

    @Override
    public NotificationScheduleEntity map(NotificationSchedule input, NotificationScheduleEntity output) {

        output.setBatchDelay(input.getBatchDelay());
        output.setBatchSize(input.getBatchSize());
        output.setTimeFrom(input.getTimeFrom());
        output.setTimeTo(input.getTimeTo());
        output.setWorkWeak(input.getWorkWeak());
        output.setScheduledAt(input.getScheduledAt());

        return output;
    }

    @Override
    public NotificationSchedule map(NotificationScheduleEntity input, NotificationSchedule output) {

        output.setId(input.getId().toString());
        output.setBatchDelay(input.getBatchDelay());
        output.setBatchSize(input.getBatchSize());
        output.setTimeFrom(input.getTimeFrom());
        output.setTimeTo(input.getTimeTo());
        output.setWorkWeak(input.getWorkWeak());
        output.setScheduledAt(input.getScheduledAt());
        return output;
    }

    @Override
    public NotificationScheduleSummary map(NotificationScheduleEntity input, NotificationScheduleSummary output) {

        output.setId(input.getId());
        output.setBatchDelay(input.getBatchDelay());
        output.setBatchSize(input.getBatchSize());
        output.setTimeFrom(input.getTimeFrom());
        output.setTimeTo(input.getTimeTo());
        output.setWorkWeak(input.getWorkWeak());
        output.setEmployeeLogin(input.getEmployeeLogin());
        output.setCreatedAt(input.getCreatedAt());
        output.setTotal(input.getTotal());
        output.setFailed(input.getFailed());
        output.setStatus(input.getStatus());
        output.setScheduledAt(input.getScheduledAt());
        return output;
    }

    @Override
    public List<NotificationSchedule> map(Collection<NotificationScheduleEntity> input) {
        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new NotificationSchedule())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public List<NotificationScheduleSummary> mapSummary(List<NotificationScheduleEntity> input) {
        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new NotificationScheduleSummary())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public CustomerNotificationCounts mapCounts(sk.spp.nzp.commons.model.notification.CustomerNotificationCounts input, CustomerNotificationCounts output) {
        if (input == null) {
            return output;
        }

        output.setTotal(input.getTotal());
        output.setProcessing(input.getProcessing());
        output.setSentEmail(input.getSentEmail());
        output.setSentSms(input.getSentSms());
        output.setSentPush(input.getSentPush());
        output.setSentPortal(input.getSentPortal());

        return output;
    }
}