package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerprofile.*;
import sk.spp.nzp.be.assembler.customerprofile.AddressEmbedableAssembler;
import sk.spp.nzp.be.assembler.customerprofile.BusinessPartnerAssembler;
import sk.spp.nzp.be.assembler.customerprofile.DeliveryPointAssembler;
import sk.spp.nzp.be.assembler.customerprofile.UnitedDeliveryPointAssembler;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.model.customeraccess.enums.QueueAccessEnum;
import sk.spp.nzp.commons.model.customerprofile.ContractEntity;
import sk.spp.nzp.commons.model.customerprofile.UnitedDeliveryPointEntity;
import sk.spp.nzp.commons.model.customersharing.UnitedDeliveryPointOwnershipEntity;
import sk.spp.nzp.commons.service.common.HistoryContextProvider;
import sk.spp.nzp.commons.service.common.impl.HistoryPeriod;
import sk.spp.nzp.commons.utils.ContractUtils;
import sk.spp.nzp.commons.utils.Expressions;
import sk.spp.nzp.commons.utils.ValidityUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
public class UnitedDeliveryPointAssemblerImpl implements UnitedDeliveryPointAssembler {

    private static final Logger logger = LoggerFactory.getLogger(UnitedDeliveryPointAssemblerImpl.class);

    private BusinessPartnerAssembler businessPartnerAssembler;
    private DeliveryPointAssembler deliveryPointAssembler;
    private AddressEmbedableAssembler addressEmbedableAssembler;
    private HistoryContextProvider historyContextProvider;

    public UnitedDeliveryPointAssemblerImpl(BusinessPartnerAssembler businessPartnerAssembler,
                                            DeliveryPointAssembler deliveryPointAssembler,
                                            AddressEmbedableAssembler addressEmbedableAssembler,
                                            HistoryContextProvider historyContextProvider) {
        this.businessPartnerAssembler = businessPartnerAssembler;
        this.deliveryPointAssembler = deliveryPointAssembler;
        this.addressEmbedableAssembler = addressEmbedableAssembler;
        this.historyContextProvider = historyContextProvider;
    }

    @Override
    public UnitedDeliveryPoint map(UnitedDeliveryPointEntity input,
                                   UnitedDeliveryPoint output,
                                   UUID customerId,
                                   boolean includeInactive,
                                   List<DeliveryPoint.Fetch> fetches) {
        List<ContractEntity> contracts;
        output.setId(input.getId().toString());
        output.setHidden(isHidden(input, customerId));
        output.setPairingStatus(input.getPairingStatus());

        if(input.getAddress() != null){
            output.setAddress(addressEmbedableAssembler.map(input.getAddress(), new Address()));
        }
        if(input.getBusinessPartner() != null) {
            output.setBusinessPartner(businessPartnerAssembler.map(input.getBusinessPartner(), new BusinessPartner()));
        }

        // filter contracts (based on invalid)
        contracts = includeInactive
                ? input.getContracts()
                : ValidityUtils.getActual(input.getContracts());

        output.setDeliveryPoints(
                ContractUtils.distinctContractForDeliveryPoint(contracts)
                        .stream()
                        .filter(v-> Expressions.notNull(v.getDeliveryPoint())) // NEWTAS-18880
                        .map(v->deliveryPointAssembler.map(
                                v.getDeliveryPoint(),
                                new DeliveryPoint(),
                                historyContextProvider.createDeliveryPointHistoryContext(v, HistoryPeriod.CURRENT),
                                fetches))
                        .collect(Collectors.toList()));
        return output;
    }

    @Override
    public UnitedDeliveryPointSummary map(UnitedDeliveryPointEntity unitedDeliveryPointEntity, UnitedDeliveryPointSummary unitedDeliveryPointSummary, UUID customerId, boolean includeInactive) {
        return map(unitedDeliveryPointEntity, unitedDeliveryPointSummary, customerId, null, includeInactive, null);
    }

    @Override
    public UnitedDeliveryPointSummary map(UnitedDeliveryPointEntity unitedDeliveryPointEntity, UnitedDeliveryPointSummary unitedDeliveryPointSummary, UUID customerId, boolean includeInactive, List<DeliveryPointSummary.Fetch> fetches) {
        return map(unitedDeliveryPointEntity, unitedDeliveryPointSummary, customerId, fetches, includeInactive, null);
    }

    @Override
    public UnitedDeliveryPointSummary map(UnitedDeliveryPointEntity input, UnitedDeliveryPointSummary output, UUID customerId, List<DeliveryPointSummary.Fetch> fetches, boolean includeInactive, BusinessPartnerQueue bpQueueSecurity) {
        List<ContractEntity> contracts;

        output.setId(input.getId());
        output.setHidden(isHidden(input, customerId));

        UnitedDeliveryPointAddress unitedDeliveryPointAddress = new UnitedDeliveryPointAddress();
        output.setAddress(map(input, unitedDeliveryPointAddress));
        output.setPairingStatus(input.getPairingStatus());

        if (input.getBusinessPartner() != null) {
            output.setBusinessPartner(businessPartnerAssembler.map(input.getBusinessPartner(), new BusinessPartnerSummary()));
        }

        // filter contracts (based on invalid)
        contracts = includeInactive
                ? input.getContracts()
                : ValidityUtils.getActual(input.getContracts());

        output.setDeliveryPoints(
                ContractUtils.distinctContractForDeliveryPoint(contracts)
                        .stream()
                        .filter(v-> Expressions.notNull(v.getDeliveryPoint())) // NEWTAS-18880
                        .map(v->deliveryPointAssembler.map(
                                v.getDeliveryPoint(),
                                new DeliveryPointSummary(),
                                fetches,
                                historyContextProvider.createDeliveryPointHistoryContext(v, HistoryPeriod.CURRENT)))
                        .collect(Collectors.toList()));


        if (bpQueueSecurity != null && bpQueueSecurity != input.getBusinessPartner().getQueue()) output.setAccess(QueueAccessEnum.NO_QUEUE_ACCESS);

        return output;
    }

    @Override
    public UnitedDeliveryPointSummary mapWithoutBP(
            UnitedDeliveryPointEntity input,
            UnitedDeliveryPointSummary output,
            UUID customerId,
            List<DeliveryPointSummary.Fetch> fetches,
            boolean includeInactive,
            BusinessPartnerQueue bpQueueSecurity,
            boolean includeDp) {
        List<ContractEntity> contracts;

        output.setId(input.getId());
        output.setHidden(isHidden(input, customerId));

        UnitedDeliveryPointAddress unitedDeliveryPointAddress = new UnitedDeliveryPointAddress();
        output.setAddress(map(input, unitedDeliveryPointAddress));
        output.setPairingStatus(input.getPairingStatus());

        // filter contracts (based on invalid)
        contracts = includeInactive
                ? input.getContracts()
                : ValidityUtils.getActual(input.getContracts());

        if (includeDp){
            output.setDeliveryPoints(
                    ContractUtils.distinctContractForDeliveryPoint(contracts)
                            .stream()
                            .filter(v-> Expressions.notNull(v.getDeliveryPoint()))
                            .map(v->deliveryPointAssembler.map(
                                    v.getDeliveryPoint(),
                                    new DeliveryPointSummary(),
                                    fetches,
                                    historyContextProvider.createDeliveryPointHistoryContext(v, HistoryPeriod.CURRENT)))
                            .collect(Collectors.toList()));
        }
        if (bpQueueSecurity != null && bpQueueSecurity != input.getBusinessPartner().getQueue()) output.setAccess(QueueAccessEnum.NO_QUEUE_ACCESS);

        return output;
    }

    @Override
    public UnitedDeliveryPointAddress map(UnitedDeliveryPointEntity input, UnitedDeliveryPointAddress output) {
        output.setStreet(input.getStreet());
        output.setStreetNumber(input.getStreetNumber());
        output.setCity(input.getCity());
        output.setZipCode(input.getZipCode());
        output.setCountry(input.getCountry());
        return output;
    }

    private Boolean isHidden(UnitedDeliveryPointEntity input, UUID customerId) {
        UnitedDeliveryPointOwnershipEntity unitedDeliveryPointOwnershipEntity = Optional.of(input.getOwnerships()).orElse(Collections.emptyList())
                .stream()
                .filter(o -> o.getCustomerAccount().getId().equals(customerId))
                .findFirst()
                .orElse(null);

        return unitedDeliveryPointOwnershipEntity != null ? unitedDeliveryPointOwnershipEntity.getHidden() : Boolean.FALSE;
    }
}
