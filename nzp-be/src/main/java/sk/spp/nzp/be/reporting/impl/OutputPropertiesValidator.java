package sk.spp.nzp.be.reporting.impl;

import sk.spp.nzp.commons.api.reporting.ReportDefinition;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class OutputPropertiesValidator {

    private List<String> aliasedOutputProperties;
    private ReportDefinition reportDefinition;

    private List<String> errors = new ArrayList<>();

    public OutputPropertiesValidator(List<String> aliasedOutputProperties, ReportDefinition reportDefinition) {
        this.aliasedOutputProperties = aliasedOutputProperties;
        this.reportDefinition = reportDefinition;
    }

    public void validate() {
        for (String outputProp : reportDefinition.getOutputProps()) {
            if (!aliasedOutputProperties.contains(outputProp)) {
                errors.add("Invalid output property: " + outputProp);
            }
        }
    }

    public List<String> getErrors() {
        return errors.stream().distinct().collect(Collectors.toList());
    }
}
