package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerprofile.TariffRate;
import sk.spp.nzp.be.assembler.customerprofile.TariffRateAssembler;
import sk.spp.nzp.commons.model.customerprofile.TariffEntity;
import sk.spp.nzp.commons.service.common.LocaleResolver;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class TariffRateAssemblerImpl implements TariffRateAssembler {

    private LocaleResolver localeResolver;

    public TariffRateAssemblerImpl(LocaleResolver localeResolver) {
        this.localeResolver = localeResolver;
    }

    @Override
    public TariffRate map(TariffEntity input, TariffRate output) {
        output.setUuid(input.getId().toString());
        output.setCode(input.getCode());
        output.setStatus(input.getStatus());
        output.setType(input.getType());
        output.setCategory(input.getCategory());
        output.setDistributionAreas(input.getDistributionArea());

        localeResolver.resolve(
                Optional.ofNullable(input.getTranslations()).orElse(Collections.emptyList()))
                .ifPresent(v -> {
                    output.setName(v.getName());
                    output.setDescription(v.getDescription());
                });

        return output;
    }

    @Override
    public List<TariffRate> map(Collection<TariffEntity> input) {
        if (input == null) {
            return Collections.emptyList();
        }

        return input.stream().map(t -> map(t, new TariffRate())).collect(Collectors.toList());
    }

}
