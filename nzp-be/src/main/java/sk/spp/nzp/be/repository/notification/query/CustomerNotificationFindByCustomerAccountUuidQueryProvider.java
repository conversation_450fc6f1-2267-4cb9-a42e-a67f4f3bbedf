package sk.spp.nzp.be.repository.notification.query;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.api.common.Paging;
import sk.spp.nzp.be.api.common.Sorting;
import sk.spp.nzp.be.api.common.SortingDirection;
import sk.spp.nzp.be.api.notification.CustomerNotificationSearchSort;
import sk.spp.nzp.be.api.notification.CustomerNotificationsSearch;
import sk.spp.nzp.commons.api.customersharing.enums.OwnershipType;
import sk.spp.nzp.commons.api.notification.enums.NotificationExecutionType;
import sk.spp.nzp.commons.api.notification.enums.NotificationTemplateType;
import sk.spp.nzp.commons.model.customerprofile.QContractAccountEntity;
import sk.spp.nzp.commons.model.customersharing.QContractAccountOwnershipEntity;
import sk.spp.nzp.commons.model.notification.CustomerNotificationEntity;
import sk.spp.nzp.commons.model.notification.QCustomerNotificationEntity;
import sk.spp.nzp.commons.model.notification.QNotificationTemplateEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

import javax.persistence.EntityManager;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

public class CustomerNotificationFindByCustomerAccountUuidQueryProvider implements QueryDslProvider<CustomerNotificationEntity> {

    private CustomerNotificationsSearch customerNotificationsSearch;
    private UUID customerUuid;

    public CustomerNotificationFindByCustomerAccountUuidQueryProvider(CustomerNotificationsSearch customerNotificationsSearch, UUID customerUuid) {
        this.customerNotificationsSearch = customerNotificationsSearch;
        this.customerUuid = customerUuid;
    }

    protected JPAQuery<CustomerNotificationEntity> findByBaseQuery(EntityManager entityManager, CustomerNotificationsSearch customerNotificationsSearch, UUID customerUuid) {

        QCustomerNotificationEntity qCustomerNotificationEntity = QCustomerNotificationEntity.customerNotificationEntity;
        QNotificationTemplateEntity notificationTemplateEntity = QNotificationTemplateEntity.notificationTemplateEntity;
        QContractAccountEntity contractAccountEntity = QContractAccountEntity.contractAccountEntity;
        QContractAccountOwnershipEntity contractAccountOwnershipEntity = QContractAccountOwnershipEntity.contractAccountOwnershipEntity;

        BooleanExpression customerExp = qCustomerNotificationEntity.customerAccount().id.eq(customerUuid).or(contractAccountOwnershipEntity.customerAccount().id.eq(customerUuid));
        BooleanExpression notDeletedExp = qCustomerNotificationEntity.deleted.isNull().or(qCustomerNotificationEntity.deleted.isFalse());
        BooleanExpression notCancelledExp = qCustomerNotificationEntity.cancelled.isNull().or(qCustomerNotificationEntity.cancelled.isFalse());
        BooleanExpression portalVisible = qCustomerNotificationEntity.header.isNotNull().and(qCustomerNotificationEntity.header.isNotEmpty());

        BooleanExpression statusesExp = null;
        if (customerNotificationsSearch.getStatuses() != null && !customerNotificationsSearch.getStatuses().isEmpty()) {
            statusesExp = qCustomerNotificationEntity.status.in(customerNotificationsSearch.getStatuses());
        }

        BooleanExpression readExp = null;
        if (customerNotificationsSearch.getRead() != null) {
            readExp = qCustomerNotificationEntity.read.eq(customerNotificationsSearch.getRead());
        }

        BooleanExpression timePeriodFromExp = null;
        if (customerNotificationsSearch.getCreatedAt() != null && customerNotificationsSearch.getCreatedAt().getFrom() != null) {
            timePeriodFromExp = qCustomerNotificationEntity.createdAt.goe(customerNotificationsSearch.getCreatedAt().getFrom());
        }

        BooleanExpression timePeriodToExp = null;
        if (customerNotificationsSearch.getCreatedAt() != null && customerNotificationsSearch.getCreatedAt().getTo() != null) {
            timePeriodToExp = qCustomerNotificationEntity.createdAt.lt(customerNotificationsSearch.getCreatedAt().getTo());
        }

        //default
        if (customerNotificationsSearch.getCreatedAt() == null) {
            LocalDateTime minusMonth = LocalDateTime.now().minusMonths(1);
            timePeriodFromExp = qCustomerNotificationEntity.createdAt.goe(minusMonth);
        }

        JPAQuery<CustomerNotificationEntity> query = new JPAQuery<CustomerNotificationEntity>(entityManager).from(qCustomerNotificationEntity)
                .join(notificationTemplateEntity).on(
                        notificationTemplateEntity.id.eq(qCustomerNotificationEntity.notificationTemplate().id)).fetchJoin()
                .leftJoin(contractAccountEntity).on(
                        qCustomerNotificationEntity.contractAccount().id.eq(contractAccountEntity.id))
                .leftJoin(contractAccountOwnershipEntity).on(
                        contractAccountOwnershipEntity.contractAccount().id.eq(contractAccountEntity.id)
                        .and(contractAccountOwnershipEntity.type.eq(OwnershipType.OWNER)))
                .where(customerExp, statusesExp, readExp, timePeriodFromExp, timePeriodToExp, notDeletedExp, portalVisible, notCancelledExp);

        orderBy(query, customerNotificationsSearch.getPaging(), qCustomerNotificationEntity);

        return query;
    }

    protected void orderBy(JPAQuery<CustomerNotificationEntity> query, Paging paging, QCustomerNotificationEntity qCustomerNotificationEntity) {
        if (paging == null || !paging.hasSorting()) {
            //default sort
            query.orderBy(qCustomerNotificationEntity.createdAt.desc());
        } else {
            for (Sorting sorting : paging.getSort()) {
                Optional<CustomerNotificationSearchSort> sortTypeOptional = CustomerNotificationSearchSort.fromString(sorting.getAttribute());

                if (sortTypeOptional.isPresent()) {
                    switch (sortTypeOptional.get()) {
                        case STATUS:
                            if (SortingDirection.ASC.equals(sorting.getDirection())) {
                                query.orderBy(qCustomerNotificationEntity.status.asc());
                            } else {
                                query.orderBy(qCustomerNotificationEntity.status.desc());
                            }
                            break;
                        case CREATED_AT:
                            if (SortingDirection.ASC.equals(sorting.getDirection())) {
                                query.orderBy(qCustomerNotificationEntity.createdAt.asc());
                            } else {
                                query.orderBy(qCustomerNotificationEntity.createdAt.desc());
                            }
                            break;
                    }
                }
            }
        }
    }

    @Override
    public JPAQuery<CustomerNotificationEntity> getQuery(EntityManager em) {
        return findByBaseQuery(em, customerNotificationsSearch, customerUuid);
    }
}
