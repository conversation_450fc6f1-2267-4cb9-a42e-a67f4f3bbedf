package sk.spp.nzp.be.assembler.notification.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.notification.NotificationTemplateI18N;
import sk.spp.nzp.be.assembler.notification.NotificationTemplateI18NAssembler;
import sk.spp.nzp.commons.model.notification.NotificationTemplateI18NEntity;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class NotificationTemplateI18NAssemblerImpl implements NotificationTemplateI18NAssembler {

    @Override
    public NotificationTemplateI18NEntity map(NotificationTemplateI18N input, NotificationTemplateI18NEntity output) {

        output.setEmailBody(input.getEmailBody());
        output.setEmailSubject(input.getEmailSubject());
        output.setHeader(input.getHeader());
        output.setHeaderUrl(input.getHeaderUrl());
        output.setSmsBody(input.getSmsBody());
        output.setPushTitle(input.getPushTitle());
        output.setPushBody(input.getPushBody());
        output.setPushText(input.getPushText());
        output.setPushRedirection(input.getPushRedirection());
        output.setLocale(input.getLocale());
        output.setStatus(input.getStatus());

        return output;
    }

    @Override
    public NotificationTemplateI18N map(NotificationTemplateI18NEntity input, NotificationTemplateI18N output) {

        output.setId(input.getId());
        output.setEmailBody(input.getEmailBody());
        output.setEmailSubject(input.getEmailSubject());
        output.setHeader(input.getHeader());
        output.setHeaderUrl(input.getHeaderUrl());
        output.setSmsBody(input.getSmsBody());
        output.setPushTitle(input.getPushTitle());
        output.setPushBody(input.getPushBody());
        output.setPushText(input.getPushText());
        output.setPushRedirection(input.getPushRedirection());
        output.setLocale(input.getLocale());
        output.setStatus(input.getStatus());

        return output;
    }

    @Override
    public List<NotificationTemplateI18N> map(Collection<NotificationTemplateI18NEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new NotificationTemplateI18N())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}
