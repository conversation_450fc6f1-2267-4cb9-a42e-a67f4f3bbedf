package sk.spp.nzp.be.assembler.customeraccess.impl;

import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customeraccess.CustomerAccount;
import sk.spp.nzp.be.api.customeraccess.Registration;
import sk.spp.nzp.be.api.customerprofile.CustomerSummary;
import sk.spp.nzp.be.api.employeeprofile.CustomerPreRegistration;
import sk.spp.nzp.be.api.sap.SAPCustomerAccount;
import sk.spp.nzp.be.api.sap.SAPCustomerAccountRegistration;
import sk.spp.nzp.be.assembler.customeraccess.CustomerAccountAssembler;
import sk.spp.nzp.be.assembler.customeraccess.CustomerApprovalAssembler;
import sk.spp.nzp.commons.api.customeraccess.enums.CustomerAccountStatus;
import sk.spp.nzp.commons.api.customeraccess.enums.CustomerAccountType;
import sk.spp.nzp.commons.converter.MsisdnNormalizer;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;
import sk.spp.nzp.commons.model.customeraccess.enums.CustomerAccountRegistrationSource;

import java.time.Clock;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class CustomerAccountAssemblerImpl implements CustomerAccountAssembler {

    private CustomerApprovalAssembler customerApprovalAssembler;
    private Clock clock;
    private PasswordEncoder passwordEncoder;

    public CustomerAccountAssemblerImpl(CustomerApprovalAssembler customerApprovalAssembler,
                                        Clock clock,
                                        PasswordEncoder passwordEncoder) {
        this.customerApprovalAssembler = customerApprovalAssembler;
        this.passwordEncoder = passwordEncoder;
        this.clock = clock;
    }

    @Override
    public CustomerAccountEntity map(CustomerAccount input, CustomerAccountEntity output) {
        output.setEmail(input.getEmail());
        output.setExternalId(input.getExternalId());
        output.setType(input.getType());
        output.setStatus(input.getStatus());
        output.setActivationAt(input.getActivationAt());
        output.setRegistrationAt(input.getRegistrationAt());
        output.setLockUntil(input.getLockUntil());
        output.setLoginSuccessAt(input.getLoginSuccessAt());
        output.setLoginUnsuccessAt(input.getLoginUnsuccessAt());
        output.setPasswordUpdatedAt(input.getPasswordUpdatedAt());
        output.setDeactivationReason(input.getDeactivationReason());
        output.setLocale(input.getLocale());
        output.setPhone(input.getPhone());
        output.setFirstName(input.getFirstName());
        output.setLastName(input.getLastName());
        output.setFacebookId(input.getFacebookId());
        output.setGoogleId(input.getGoogleId());
        output.setAppleId(input.getAppleId());
        output.setOtp(input.getOtp());
        return output;
    }

    @Override
    public CustomerAccount mapFull(CustomerAccountEntity input, CustomerAccount output) {
        map(input, output);
        output.setApprovals(customerApprovalAssembler.map(input.getApprovals()));
        return output;
    }

    @Override
    public CustomerAccount map(CustomerAccountEntity input, CustomerAccount output) {
        output.setId(input.getId());
        output.setEmail(input.getEmail());
        output.setExternalId(input.getExternalId());
        output.setType(input.getType());
        output.setStatus(input.getStatus());
        output.setActivationAt(input.getActivationAt());
        output.setRegistrationAt(input.getRegistrationAt());
        output.setLockUntil(input.getLockUntil());
        output.setLoginSuccessAt(input.getLoginSuccessAt());
        output.setLoginUnsuccessAt(input.getLoginUnsuccessAt());
        output.setPasswordUpdatedAt(input.getPasswordUpdatedAt());
        output.setDeactivationReason(input.getDeactivationReason());
        output.setLocale(input.getLocale());
        output.setPhone(input.getPhone());
        output.setFirstName(input.getFirstName());
        output.setLastName(input.getLastName());
        output.setFacebookId(input.getFacebookId());
        output.setFacebookUserName(input.getFacebookUserName());
        output.setFacebookLoginEnabled(input.getFacebookLoginEnabled());
        output.setGoogleId(input.getGoogleId());
        output.setGoogleUserName(input.getGoogleUserName());
        output.setGoogleLoginEnabled(input.getGoogleLoginEnabled());
        output.setAppleId(input.getAppleId());
        output.setAppleUserName(input.getAppleUserName());
        output.setAppleLoginEnabled(input.getAppleLoginEnabled());
        output.setOtp(input.getOtp());
        output.setQueueCategory(input.getQueueCategory());
        return output;
    }

    @Override
    public CustomerAccountEntity map(CustomerPreRegistration input, CustomerAccountEntity output) {
        output.setEmail(input.getEmail());
        output.setPhone(input.getPhone());
        output.setFirstName(input.getFirstName());
        output.setLastName(input.getLastName());
        output.setStatus(CustomerAccountStatus.PRE_REGISTRATION);
        output.setType(CustomerAccountType.CUSTOMER);
        output.setRegistrationSource(CustomerAccountRegistrationSource.OPERATOR);
        output.setRegistrationAt(LocalDateTime.now(clock));
        return output;
    }

    @Override
    public CustomerAccountEntity map(Registration input, CustomerAccountEntity output) {
        output.setEmail(input.getEmail());
        output.setFirstName(input.getFirstName());
        output.setLastName(input.getLastName());
        output.setOtp(false);
        output.setStatus(CustomerAccountStatus.UNVERIFIED);
        output.setType(CustomerAccountType.CUSTOMER);
        output.setRegistrationSource(CustomerAccountRegistrationSource.CUSTOMER);
        output.setRegistrationAt(LocalDateTime.now(clock));
        return output;
    }

    public List<CustomerAccount> mapToList(Iterable<CustomerAccountEntity> input, List<CustomerAccount> output) {
        input.forEach(account -> output.add(map(account, new CustomerAccount())));
        return output;
    }

    @Override
    public SAPCustomerAccount map(CustomerAccountEntity input, SAPCustomerAccount output) {
        output.setId(input.getId().toString());
        output.setEmail(input.getEmail());
        output.setPhone(input.getPhone());
        output.setFirstName(input.getFirstName());
        output.setLastName(input.getLastName());
        return output;
    }

    @Override
    public CustomerAccountEntity map(SAPCustomerAccountRegistration input, CustomerAccountEntity output) {
        output.setEmail(input.getEmail());
        output.setPhone(MsisdnNormalizer.normalizeToMsisdn(input.getPhone()));
        output.setFirstName(input.getFirstName());
        output.setLastName(input.getLastName());
        output.setOtp(false);
        output.setStatus(CustomerAccountStatus.PRE_REGISTRATION_SAP);
        output.setType(CustomerAccountType.CUSTOMER);
        output.setRegistrationSource(CustomerAccountRegistrationSource.SAP);
        output.setRegistrationAt(LocalDateTime.now(clock));
        return output;
    }

    @Override
    public CustomerSummary map(CustomerAccountEntity input, CustomerSummary output) {

        if (input == null) return null;

        output.setEmail(input.getEmail());
        output.setId(input.getId().toString());
        output.setFirstName(input.getFirstName());
        output.setLocale(input.getLocale());
        output.setPhone(input.getPhone());
        output.setLastName(input.getLastName());
        output.setQueueCategory(input.getQueueCategory());

        return output;
    }

    @Override
    public List<CustomerAccount> map(Collection<CustomerAccountEntity> input) {

        if(input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new CustomerAccount())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}
