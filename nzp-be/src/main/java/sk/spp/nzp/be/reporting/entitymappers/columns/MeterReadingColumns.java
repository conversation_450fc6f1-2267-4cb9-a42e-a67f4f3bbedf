package sk.spp.nzp.be.reporting.entitymappers.columns;

public enum MeterReadingColumns {

    ID,
    DEVICE_NUMBER,
    REGISTER,
    REGISTER_KIND_CODE,
    REGISTER_KIND_NAME,
    REASON_CODE,
    REASON_NAME,
    KIND_CODE,
    KIND_NAME,
    READ_AT,
    VALUE,
    VALUE_HIGH,
    UNITS,
    CATEGORY;

    public static final String GENERIC_CODE_LIST_TYPE = "METER_READING_EXPORT_COLUMN";

    public static MeterReadingColumns valueOfOrNull(String stringValue) {

        for(MeterReadingColumns value: MeterReadingColumns.values()) {

            if(value.name().equals(stringValue)) {
                return value;
            }
        }

        return null;
    }
}
