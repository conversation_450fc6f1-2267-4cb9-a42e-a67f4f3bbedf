package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerprofile.Contract;
import sk.spp.nzp.be.api.customerprofile.ContractAccount;
import sk.spp.nzp.be.api.customerprofile.ContractAccountSummary;
import sk.spp.nzp.be.api.customerprofile.ContractSummary;
import sk.spp.nzp.be.assembler.customerprofile.ContractAccountAssembler;
import sk.spp.nzp.be.assembler.customerprofile.ContractAssembler;
import sk.spp.nzp.be.service.codelist.CodeListProvider;
import sk.spp.nzp.be.service.codelist.model.CodeListQuery;
import sk.spp.nzp.be.service.codelist.provider.ProductCodeListProvider;
import sk.spp.nzp.be.service.customerprofile.TariffCountService;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.assembler.codelist.CodeListItemAssembler;
import sk.spp.nzp.commons.enums.CodeListType;
import sk.spp.nzp.commons.model.customerprofile.ContractEntity;
import sk.spp.nzp.commons.model.customerprofile.ContractVersionEntity;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Component
public class ContractAssemblerImpl implements ContractAssembler {

    private ContractAccountAssembler contractAccountAssembler;
    private CodeListItemAssembler codeListItemAssembler;
    private CodeListProvider productCodeListProvider;
    private TariffCountService tariffCountService;

    public ContractAssemblerImpl(
            ContractAccountAssembler contractAccountAssembler,
            CodeListItemAssembler codeListItemAssembler,
            ProductCodeListProvider productCodeListProvider,
            TariffCountService tariffCountService) {
        this.contractAccountAssembler = contractAccountAssembler;
        this.codeListItemAssembler = codeListItemAssembler;
        this.productCodeListProvider = productCodeListProvider;
        this.tariffCountService = tariffCountService;
    }

    @Override
    public Contract map(ContractEntity input, Contract output) {

        if (input == null) // external entities may not be imported during assembling
            return null;

        ContractVersionEntity currentContractVersion = input.getContractVersion();

        output.setId(input.getId());
        output.setExternalId(input.getExternalId());
        output.setStatus(input.getStatus());
        output.setSignatureAt(input.getSignatureAt());
        output.setEffectiveFrom(input.getEffectiveFrom());
        output.setEffectiveTo(input.getEffectiveTo());

        if (currentContractVersion != null) {

            output.setBindingTo(getEffectiveBinding(currentContractVersion));
            output.setNoticePeriod(currentContractVersion.getNoticePeriod());
            output.setEeTariffCount(tariffCountService.getTariffCount(currentContractVersion.getEeTariffCount()));
            output.setEeTariffValue(currentContractVersion.getEeTariffCount());
            output.setEffectiveFrom(currentContractVersion.getValidFrom());

            if (currentContractVersion.getPlannedValidTo() != null &&
                    currentContractVersion.getPlannedValidTo().isAfter(currentContractVersion.getValidFrom())){
                output.setEffectiveTo(currentContractVersion.getPlannedValidTo());
            }else if(currentContractVersion.getValidTo() != null){
                output.setEffectiveTo(currentContractVersion.getValidTo());
            }

            if (currentContractVersion.getBillCycle() != null) {
                output.setBillCycle(codeListItemAssembler.mapSummary(CodeListType.CONTRACT_BILL_CYCLE, currentContractVersion.getBillCycle(), new CodeListItem()));
            }

            if (currentContractVersion.getProduct() != null) {
                CodeListQuery codeListQuery = new CodeListQuery();
                codeListQuery.setCode(currentContractVersion.getProduct());
                output.setProduct(productCodeListProvider.getOrCode(CodeListType.PRODUCT.name(), codeListQuery, true));
            }
        }

        if (input.getContractAccount() != null) {
            output.setContractAccount(contractAccountAssembler.map(input.getContractAccount(), new ContractAccount()));
        }

        Optional.ofNullable(input.getAdvancePaymentPeriod())
                .map(period -> codeListItemAssembler.mapSummary(
                        CodeListType.CONTRACT_ADVANCE_PAYMENT_PERIOD, period, new CodeListItem()))
                .ifPresent(output::setAdvancePaymentPeriod);

        return output;
    }

    @Override
    public ContractSummary mapWithContractAccount(ContractEntity input, ContractSummary output, boolean includeFieldUpdates){
        output = map(input, output);
        output.setContractAccountSummary(contractAccountAssembler.mapSummary(input.getContractAccount(),  new ContractAccountSummary(), includeFieldUpdates));
        return output;
    }

    @Override
    public ContractSummary map(ContractEntity input, ContractSummary output) {
        ContractVersionEntity currentContractVersion = input.getContractVersion();

        output.setId(input.getId());
        output.setExternalId(input.getExternalId());
        output.setStatus(input.getStatus());

        if (currentContractVersion != null) {

            output.setNoticePeriod(currentContractVersion.getNoticePeriod());

            if (currentContractVersion.getBillCycle() != null) {
                output.setBillCycle(codeListItemAssembler.mapSummary(CodeListType.CONTRACT_BILL_CYCLE, currentContractVersion.getBillCycle(), new CodeListItem()));
            }

            if (currentContractVersion.getProduct() != null) {
                CodeListQuery codeListQuery = new CodeListQuery();
                codeListQuery.setCode(currentContractVersion.getProduct());
                output.setProduct(productCodeListProvider.getOrCode(CodeListType.PRODUCT.name(), codeListQuery, true));
            }
        }

        Optional.ofNullable(input.getAdvancePaymentPeriod())
                .map(period -> codeListItemAssembler.mapSummary(
                        CodeListType.CONTRACT_ADVANCE_PAYMENT_PERIOD, period, new CodeListItem()))
                .ifPresent(output::setAdvancePaymentPeriod);

        return output;
    }

    private LocalDate getEffectiveBinding(ContractVersionEntity currentContractVersion) {
        LocalDate now = LocalDate.now();

        List<LocalDate> bindings = new ArrayList<>();
        Optional.ofNullable(currentContractVersion.getBindingTo()).ifPresent(bindings::add);
        Optional.ofNullable(currentContractVersion.getBindingProlong1()).ifPresent(bindings::add);
        Optional.ofNullable(currentContractVersion.getBindingProlong2()).ifPresent(bindings::add);

        return bindings.stream()
                .filter(binding -> binding.isAfter(now))
                .min(Comparator.comparing((binding) -> ChronoUnit.DAYS.between(now, binding))).orElse(null);
    }
}
