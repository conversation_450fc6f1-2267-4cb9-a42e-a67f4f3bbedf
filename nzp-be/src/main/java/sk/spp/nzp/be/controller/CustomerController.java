package sk.spp.nzp.be.controller;

import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.Errors;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.annotation.paging.PagingAsQueryParams;
import sk.spp.nzp.be.api.common.Count;
import sk.spp.nzp.be.api.common.PagedResponse;
import sk.spp.nzp.be.api.common.QueryStringPaging;
import sk.spp.nzp.be.api.customeraccess.*;
import sk.spp.nzp.be.api.customeraccess.registration.PreRegistrationChallenge;
import sk.spp.nzp.be.api.customerprofile.*;
import sk.spp.nzp.be.api.customerprofile.invoicesummary.InvoicePaymentSummary;
import sk.spp.nzp.be.api.customerrequest.*;
import sk.spp.nzp.be.api.customersharing.SharingRequest;
import sk.spp.nzp.be.api.customersharing.SharingUnitedDeliveryPointRequest;
import sk.spp.nzp.be.api.epay.TransactionInitRequest;
import sk.spp.nzp.be.api.epay.TransactionInitResponse;
import sk.spp.nzp.be.api.notification.CustomerNotificationSummary;
import sk.spp.nzp.be.api.notification.CustomerNotificationsSearch;
import sk.spp.nzp.be.api.notification.setting.DeliveryPointNotificationSettingRequest;
import sk.spp.nzp.be.api.notification.setting.DeliveryPointNotificationSettingResponse;
import sk.spp.nzp.be.api.support.openapi.RegistrationClassic;
import sk.spp.nzp.be.api.support.openapi.RegistrationSocial;
import sk.spp.nzp.be.service.customeraccess.CustomerDeviceService;
import sk.spp.nzp.be.service.customeraccess.CustomerService;
import sk.spp.nzp.be.service.customeraccess.ReCaptchaTokenService;
import sk.spp.nzp.be.service.customeraccess.SocialNetworkService;
import sk.spp.nzp.be.service.customerprofile.BusinessPartnerService;
import sk.spp.nzp.be.service.customerprofile.InvoiceService;
import sk.spp.nzp.be.service.customerprofile.PairingService;
import sk.spp.nzp.be.service.customerprofile.UnitedDeliveryPointService;
import sk.spp.nzp.be.service.customerrequest.AnonymousCustomerService;
import sk.spp.nzp.be.service.customerrequest.CustomerRequestService;
import sk.spp.nzp.be.service.customersharing.CustomerSharingService;
import sk.spp.nzp.be.service.epay.EpayService;
import sk.spp.nzp.be.service.notification.CustomerNotificationService;
import sk.spp.nzp.be.service.notification.CustomerNotificationSettingsService;
import sk.spp.nzp.commons.annotation.ValidName;
import sk.spp.nzp.commons.api.customerrequest.enums.CompletionRejectReason;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.enums.HttpContextHeader;
import sk.spp.nzp.commons.enums.SocialNetwork;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.utils.ValidList;
import sk.spp.nzp.commons.validator.CustomerRequestChecks;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/customers")
@Validated
public class CustomerController {

    private CustomerService customerService;
    private UnitedDeliveryPointService unitedDeliveryPointService;
    private BusinessPartnerService businessPartnerService;
    private InvoiceService invoiceService;
    private PairingService pairingService;
    private CustomerNotificationService customerNotificationService;
    private CustomerRequestService customerRequestService;
    private CustomerNotificationSettingsService notificationSettingService;
    private CustomerSharingService customerSharingService;
    private EpayService epayService;
    private ReCaptchaTokenService reCaptchaTokenService;
    private AnonymousCustomerService anonymousCustomerService;
    private SocialNetworkService socialNetworkService;
    private CustomerDeviceService customerDeviceService;

    public CustomerController(
            CustomerService customerService,
            UnitedDeliveryPointService unitedDeliveryPointService,
            InvoiceService invoiceService,
            PairingService pairingService,
            CustomerNotificationService customerNotificationService,
            CustomerRequestService customerRequestService,
            CustomerNotificationSettingsService notificationSettingService,
            CustomerSharingService customerSharingService,
            EpayService epayService,
            ReCaptchaTokenService reCaptchaTokenService,
            AnonymousCustomerService anonymousCustomerService,
            BusinessPartnerService businessPartnerService,
            SocialNetworkService socialNetworkService,
            CustomerDeviceService customerDeviceService) {
        this.businessPartnerService = businessPartnerService;
        this.customerService = customerService;
        this.unitedDeliveryPointService = unitedDeliveryPointService;
        this.invoiceService = invoiceService;
        this.pairingService = pairingService;
        this.customerNotificationService = customerNotificationService;
        this.customerRequestService = customerRequestService;
        this.notificationSettingService = notificationSettingService;
        this.customerSharingService = customerSharingService;
        this.epayService = epayService;
        this.reCaptchaTokenService = reCaptchaTokenService;
        this.anonymousCustomerService = anonymousCustomerService;
        this.socialNetworkService = socialNetworkService;
        this.customerDeviceService = customerDeviceService;
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW'))))")
    @PutMapping(value = "/{customerUuid}/united-delivery-points/{unitedDeliveryPointUuid}/visibility", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public UnitedDeliveryPointSummary visibility(
            @LogParam("customerUuid") @PathVariable UUID customerUuid,
            @LogParam("unitedDeliveryPointUuid") @PathVariable UUID unitedDeliveryPointUuid,
            @LogParam("visibility ") @RequestBody DeliveryPointVisibility visibility) {

        return unitedDeliveryPointService.setVisibility(unitedDeliveryPointUuid, customerUuid, visibility);
    }

    @Log
    @LogParam
    @PostMapping(path = "/registration", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public void registerCustomer(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(schema = @Schema(implementation = RegistrationClassic.class)))
            @LogParam("registration")  @Validated(value = Registration.ClassicRegistration.class) @RequestBody Registration registration) {
        customerService.registerAccount(registration);
    }

    @Log
    @LogParam
    @PostMapping(path = "/registration-social", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public RegistrationResponse registerCustomerWithSocialLogin(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(schema = @Schema(implementation = RegistrationSocial.class)))
            @LogParam("registration") @Validated(value = Registration.SocialRegistration.class) @RequestBody Registration registration) {
        return customerService.registerAccountWithSocial(registration);
    }

    @Log
    @LogParam
    @PostMapping(path = "/re-captcha-token", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public ReCaptchaTokenResponse reCaptchaToken(
            HttpServletRequest request,
            @LogParam("reCaptchaTokenRequest") @Valid @RequestBody ReCaptchaTokenRequest reCaptchaTokenRequest) {

        final String reCaptchaCode = HttpContextHeader.RECAPTCHA_CODE.getFirstHeaderValue(request);
        return reCaptchaTokenService.getToken(reCaptchaTokenRequest, reCaptchaCode);
    }

    @Log
    @LogParam
    @PostMapping(path = "/check-email", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public CheckEmailResponse checkEmailExists(@LogParam("checkEmailRequest") @Valid @RequestBody CheckEmailRequest checkEmailRequest) {
        return customerService.checkEmail(checkEmailRequest);
    }

    @Log
    @LogParam
    @PostMapping(path = "/registration/resend", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public void resendCustomerRegistration(@LogParam("resendRegistration")  @Valid @RequestBody ResendRegistration resendRegistration) {
        customerService.resendCustomerRegistration(resendRegistration);
    }

    @Log
    @LogParam
    @PutMapping(path = "/registration/challenge", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void confirmRegistration(@LogParam("challenge") @Validated() @RequestBody Challenge challenge) {
        customerService.confirmRegistration(challenge.getCode(), challenge.getUuid());
    }

    @Log
    @LogParam
    @PutMapping(path = "/pre-registration/challenge", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Challenge confirmPreRegistrationChallenge(@LogParam("challenge") @Validated() @RequestBody PreRegistrationChallenge challenge) {
        return customerService.confirmPreRegistration(challenge);
    }

    @Log
    @LogParam
    @PostMapping(path = "/password/recovery", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public ChallengeValidity requestPasswordRecovery(@LogParam("request") @Valid @RequestBody PasswordRecovery request) {
        return customerService.requestPasswordRecovery(request);
    }

    @Log
    @LogParam
    @PutMapping(path = "/password/recovery/challenge", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void confirmPasswordRecovery(@LogParam("challenge") @Validated() @RequestBody PasswordChallenge challenge) {
        customerService.confirmPasswordRecovery(challenge.getCode(), challenge.getPassword(), challenge.getUuid());
    }

    @Log
    @LogParam
    @PostMapping(path = "/password/recovery/resend", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void resendPasswordRecovery(@LogParam("request") @Valid @RequestBody PasswordRecovery request) {
        customerService.resendPasswordRecovery(request);
    }

    @Log
    @LogParam
    @PutMapping(path = "/password/recovery/challenge/verify", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void passwordRecoveryChallengeValidity(@LogParam("request") @Validated() @RequestBody Challenge challenge) {
        customerService.passwordRecoveryChallengeValidity(challenge.getUuid(), challenge.getCode());
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PutMapping(path = "/{uuid}/password")
    public void changePassword(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("passwordChange") @Valid @RequestBody PasswordChange passwordChange) {

        customerService.changePassword(uuid, passwordChange);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PutMapping(path = "/{uuid}/name")
    public void changeName(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("firstName") @RequestParam @ValidName String firstName,
            @LogParam("lastName") @RequestParam(required = false) @ValidName String lastName) {

        customerService.changeName(uuid, firstName, lastName);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PostMapping(path = "/{uuid}/phone/login-token")
    public ResponseEntity<LoginTokenResponse> loginToken(
            @LogParam("uuid") @PathVariable UUID uuid) {

        return ResponseEntity.ok(customerService.loginToken(uuid));
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PutMapping(path = "/{uuid}/approvals")
    public void changeApprovals(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("approvals") @Valid @RequestBody ValidList<CustomerApprovalBase> approvals) {

        customerService.changeApprovals(uuid, approvals.getValues());
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))) and #uuid == authentication.customerId")
    @PostMapping("/{uuid}/notification-settings/search")
    public List<DeliveryPointNotificationSettingResponse> getNotificationSettings(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("unitedDeliveryPointFilter") @RequestBody(required = false) List<UUID> unitedDeliveryPointFilter) {

        return notificationSettingService.getSettings(uuid, unitedDeliveryPointFilter);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))) and #uuid == authentication.customerId")
    @PutMapping("/{uuid}/notification-settings")
    public void changeNotificationSettings(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("settingChange") @Valid @RequestBody DeliveryPointNotificationSettingRequest settingChange) {

        notificationSettingService.changeSettings(uuid, settingChange);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PutMapping("/{uuid}/login-settings/fb")
    public void changeFbLoginSettings(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("settingChange") @Valid @RequestBody SocialLoginSettingsChange settingChange) {

        socialNetworkService.setLoginOptionState(uuid, SocialNetwork.FACEBOOK, settingChange);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PutMapping("/{uuid}/login-settings/fb/unpair")
    public void unpairFbAccount(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("unpairRequest") @Valid @RequestBody UnpairSocialAccount unpairRequest) {

        socialNetworkService.unpairSocialAccount(uuid, SocialNetwork.FACEBOOK, unpairRequest);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PutMapping("/{uuid}/login-settings/google")
    public void changeGoogleLoginSettings(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("settingChange") @Valid @RequestBody SocialLoginSettingsChange settingChange) {

        socialNetworkService.setLoginOptionState(uuid, SocialNetwork.GOOGLE, settingChange);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PutMapping("/{uuid}/login-settings/google/unpair")
    public void unpairGoogleAccount(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("unpairRequest") @Valid @RequestBody UnpairSocialAccount unpairRequest) {

        socialNetworkService.unpairSocialAccount(uuid, SocialNetwork.GOOGLE, unpairRequest);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PutMapping("/{uuid}/login-settings/apple")
    public void changeAppleLoginSettings(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("settingChange") @Valid @RequestBody SocialLoginSettingsChange settingChange) {

        socialNetworkService.setLoginOptionState(uuid, SocialNetwork.APPLE, settingChange);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PutMapping("/{uuid}/login-settings/apple/unpair")
    public void unpairAppleAccount(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("unpairRequest") @Valid @RequestBody UnpairSocialAccount unpairRequest) {

        socialNetworkService.unpairSocialAccount(uuid, SocialNetwork.APPLE, unpairRequest);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PostMapping(path = "/{uuid}/phone", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<ChallengeResponse> requestPhoneChange(@LogParam("uuid") @PathVariable UUID uuid,
                                                                    @LogParam("request") @Valid @RequestBody PhoneChange request,
                                                                    Errors error) {
        if(error.hasErrors()){
            throw new ApiException(ErrorCode.CUSTOMER_ACCESS_INVALID_PHONE_NUMBER);
        }

        return ResponseEntity.ok(customerService.requestPhoneChange(request, uuid));
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #customerUuid == authentication.customerId")
    @PostMapping(path = "/{uuid}/phone/resend", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<ChallengeResponse> resendPhoneChallenge(
            @LogParam("customerUuid") @PathVariable(value = "uuid") UUID customerUuid,
            @LogParam("resendPhoneChallenge") @RequestBody @Valid ResendPhoneChallenge resendPhoneChallenge) {

        return ResponseEntity.ok(customerService.resendPhoneChallenge(resendPhoneChallenge, customerUuid));
    }


    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PostMapping(path = "/{uuid}/email", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void requestEmailChange(@LogParam("uuid") @PathVariable UUID uuid,
                                   @LogParam("request") @Valid @RequestBody EmailChange request) {

        customerService.requestEmailChange(request, uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PostMapping(path = "/{uuid}/email/resend")
    public void resendEmailChange(@LogParam("uuid") @PathVariable UUID uuid) {

        customerService.resendEmailChange(uuid);
    }

    @Log
    @LogParam
    @PutMapping(path = "/email/challenge", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void requestEmailChangeChallenge(@LogParam("request") @Validated() @RequestBody Challenge challenge) {

        customerService.requestEmailChangeChallenge(challenge.getCode(), challenge.getUuid());
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PutMapping(path = "/{uuid}/phone/challenge", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void requestPhoneChallenge(@LogParam("uuid") @PathVariable UUID uuid,
                                      @LogParam("challenge") @Validated() @RequestBody Challenge challenge) {

        customerService.requestPhoneChallenge(challenge.getCode(), challenge.getUuid(), uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #customerUuid == authentication.customerId")
    @PostMapping(path = "/{c_uuid}/epay/transaction/init", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<TransactionInitResponse> initEpayTransaction(@LogParam("customerUuid") @PathVariable(value = "c_uuid") UUID customerUuid,
                                                                       @LogParam("transactionInitReq") @Validated() @RequestBody TransactionInitRequest request){

        return ResponseEntity.ok(epayService.initTransaction(request, customerUuid));
    }


    // TODO: delete unnecessary code
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW')))) and #uuid == authentication.customerId")
//    @GetMapping(value = "/{uuid}/business-partners", produces = {MediaType.APPLICATION_JSON_VALUE})
//    @PagingAsQueryParams
//    public PagedResponse<BusinessPartner> getBusinessPartners(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("queryStringPaging ")  QueryStringPaging  queryStringPaging) {
//
//        return businessPartnerService.getByCustomerAccount(uuid, new BusinessPartnerSearch().setPaging(queryStringPaging.toPaging()));
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW')))) and #uuid == authentication.customerId")
//    @PostMapping(value = "/{uuid}/business-partners/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public PagedResponse<BusinessPartner> businessPartnerSearch(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("businessPartnerSearch ") @RequestBody BusinessPartnerSearch businessPartnerSearch) {
//
//        return businessPartnerService.getByCustomerAccount(uuid, businessPartnerSearch);
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('CUSTOMERS_VIEW')))) and #uuid == authentication.customerId")
//    @GetMapping(value = "/{uuid}/contract-accounts", produces = {MediaType.APPLICATION_JSON_VALUE})
//    @PagingAsQueryParams
//    public PagedResponse<ContractAccount> getContractAccounts(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("queryStringPaging ")  QueryStringPaging  queryStringPaging) {
//
//        return contractAccountService.getByCustomerAccount(uuid, new ContractAccountSearch().setPaging(queryStringPaging.toPaging()));
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('CUSTOMERS_VIEW')))) and #uuid == authentication.customerId")
//    @PostMapping(value = "/{uuid}/contract-accounts/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public PagedResponse<ContractAccount> contractAccountSearch(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("contractAccountSearch ") @RequestBody ContractAccountSearch contractAccountSearch) {
//
//        return contractAccountService.getByCustomerAccount(uuid, contractAccountSearch);
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('CUSTOMERS_VIEW')))) and #uuid == authentication.customerId")
//    @GetMapping(value = "/{uuid}/contracts", produces = {MediaType.APPLICATION_JSON_VALUE})
//    @PagingAsQueryParams
//    public PagedResponse<Contract> getContracts(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("queryStringPaging ")  QueryStringPaging  queryStringPaging) {
//
//        return contractService.getByCustomerAccount(uuid, new ContractSearch().setPaging(queryStringPaging.toPaging()));
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('CUSTOMERS_VIEW')))) and #uuid == authentication.customerId")
//    @PostMapping(value = "/{uuid}/contracts/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public PagedResponse<Contract> contractSearch(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("contractSearch ") @RequestBody ContractSearch contractSearch) {
//
//        return contractService.getByCustomerAccount(uuid, contractSearch);
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))) and #uuid == authentication.customerId")
//    @GetMapping(value = "/{uuid}/delivery-points", produces = {MediaType.APPLICATION_JSON_VALUE})
//    @PagingAsQueryParams
//    public PagedResponse<DeliveryPoint> getDeliveryPoints(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("queryStringPaging ")  QueryStringPaging  queryStringPaging) {
//
//        return deliveryPointService.getByCustomerAccount(uuid, new DeliveryPointSearch().setPaging(queryStringPaging.toPaging()));
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))) and #uuid == authentication.customerId")
//    @PostMapping(value = "/{uuid}/delivery-points/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public PagedResponse<DeliveryPoint> deliveryPointSearch(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("deliveryPointSearch ") @RequestBody DeliveryPointSearch deliveryPointSearch) {
//
//        return deliveryPointService.getByCustomerAccount(uuid, deliveryPointSearch);
//    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))) and #uuid == authentication.customerId")
    @GetMapping(value = "/{uuid}/united-delivery-points", produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<UnitedDeliveryPointSummary> getUnitedDeliveryPointsByCustomerAccount(@LogParam("uuid") @PathVariable UUID uuid,
                                                                             @LogParam("queryStringPaging ") QueryStringPaging  queryStringPaging) {

        return unitedDeliveryPointService.getUnitedPointsByCustomerAccount(
                uuid,
                new UnitedDeliveryPointSearch(queryStringPaging.toPaging()));
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))) and #uuid == authentication.customerId")
    @PostMapping(value = "/{uuid}/united-delivery-points/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<UnitedDeliveryPointSummary> searchUnitedDeliveryPoint(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("unitedDeliveryPointSearch ") @RequestBody UnitedDeliveryPointSearch unitedDeliveryPointSearch,
            @LogParam("fetch") @RequestParam(name = "fetch", required = false) List<DeliveryPointSummary.Fetch> fetches) {

        return unitedDeliveryPointService.getUnitedPointsByCustomerAccount(uuid, unitedDeliveryPointSearch, fetches);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))) and #uuid == authentication.customerId")
    @PostMapping(value = "/{uuid}/united-delivery-points/search/count", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public UnitedDeliveryCounts unitedDeliveryPointSearchCount(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("unitedDeliveryPointSearch ") @RequestBody UnitedDeliveryPointFullSearch unitedDeliveryPointSearch) {

        return unitedDeliveryPointService.getUnitedPointsCountByCustomerAccount(uuid, unitedDeliveryPointSearch);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))) and #uuid == authentication.customerId")
    @PostMapping(value = "/{uuid}/business-partner/united-delivery-points/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<BusinessPartnerUnitedDeliveryPointsSummary> searchBusinessPartners(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("businessPartnerUnitedDeliveryPointsSearch ") @RequestBody BusinessPartnerUnitedDeliveryPointsSearch businessPartnerUnitedDeliveryPointsSearch) {

        return businessPartnerService.getByCustomerAccount(uuid, businessPartnerUnitedDeliveryPointsSearch);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))) and #uuid == authentication.customerId")
    @GetMapping(value = "/{uuid}/invoices", produces = {MediaType.APPLICATION_JSON_VALUE})
    @PagingAsQueryParams
    public PagedResponse<InvoiceSummary> customerGetInvoices(@LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("queryStringPaging ")  QueryStringPaging  queryStringPaging) {

        return invoiceService.getInvoices(
                new InvoiceSearch()
                        .setPaging(queryStringPaging.toPaging())
                        .withCustomerAccountId(uuid)
        );
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))) and #uuid == authentication.customerId")
    @PostMapping(value = "/{uuid}/invoices/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<InvoiceSummary> customerInvoiceSearch(@LogParam("uuid") @PathVariable UUID uuid,
                                                       @LogParam("invoiceSearch ") @RequestBody InvoiceSearch invoiceSearch) {

        return invoiceService.getInvoices(invoiceSearch.withCustomerAccountId(uuid));
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))) and #uuid == authentication.customerId")
    @PostMapping(value = "/{uuid}/invoices/search/count", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Count invoiceSearchCount(@LogParam("uuid") @PathVariable UUID uuid,
                                    @LogParam("invoiceSearch ") @RequestBody InvoiceSearch invoiceSearch) {

        return invoiceService.getCount(invoiceSearch.withCustomerAccountId(uuid));
    }

    @Log
    @LogParam
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))) and #uuid == authentication.customerId")
    @PostMapping(value = "/{uuid}/invoices/search/summary", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public InvoicePaymentSummary invoiceSearchSummary(@LogParam("uuid") @PathVariable UUID uuid,
                                                      @LogParam("invoiceSearch ") @RequestBody InvoiceSearch invoiceSearch) {

        return invoiceService.getInvoiceSearchSummary(invoiceSearch.withCustomerAccountId(uuid));
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))) and #uuid == authentication.customerId")
    @GetMapping(value = "/{uuid}/invoices/summary", produces = {MediaType.APPLICATION_JSON_VALUE})
    public InvoicePaymentSummary invoicesSummary(@LogParam("uuid") @PathVariable UUID uuid) {

            return invoiceService.getInvoiceSummaryByCustomer(uuid);
    }

    // TODO: delete unnecessary code
//    @Log
//    @LogParam
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
//    @GetMapping(value = "/{uuid}/sharings/summaries", produces = {MediaType.APPLICATION_JSON_VALUE})
//    @PagingAsQueryParams
//    public PagedResponse<SharingSummary> getSharingsByCustomerUuid(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//                                                                   @LogParam("paging")  QueryStringPaging paging) {
//        return sharingSummariesService.getByCustomerUuid(uuid, paging.toPaging());
//    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))) and #customerUuid == authentication.customerId")
    @PostMapping(value = "/{customerUuid}/united-delivery-points/{unitedDeliveryPointUuid}/sharings")
    public void postSharing(
            @LogParam("customerUuid") @PathVariable UUID customerUuid,
            @LogParam("unitedDeliveryPointUuid") @PathVariable UUID unitedDeliveryPointUuid,
            @LogParam("email") @Valid @RequestBody SharingRequest email) {

        customerSharingService.createUnitedDeliveryPointSharing(customerUuid, unitedDeliveryPointUuid, email.getEmail());
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))) and #customerUuid == authentication.customerId")
    @DeleteMapping(value = "/{customerUuid}/united-delivery-points/{unitedDeliveryPointUuid}/sharings")
    public void deleteSharing(
            @LogParam("customerUuid") @PathVariable UUID customerUuid,
            @LogParam("unitedDeliveryPointUuid") @PathVariable UUID unitedDeliveryPointUuid,
            @LogParam("email") @RequestParam String email) {

        customerSharingService.removeUnitedDeliveryPointSharing(customerUuid, unitedDeliveryPointUuid, email);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('BUSINESS_PARTNERS_PAIRING')))) and #uuid == authentication.customerId")
    @PostMapping(value = "/{uuid}/business-partners/pair-request", produces = {MediaType.APPLICATION_JSON_VALUE})
    public PairBusinessPartnerResponse customerPairingBusinessPartner(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("businessPartnerPairingRequest") @RequestBody @Valid PairBusinessPartnerRequest pairBusinessPartnerRequest) {

        return pairingService.pairBusinessPartner(uuid, pairBusinessPartnerRequest);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('BUSINESS_PARTNERS_PAIRING')))) and #customerUuid == authentication.customerId")
    @PostMapping(value = "/{customerUuid}/business-partners/pair-request/resend", produces = {MediaType.APPLICATION_JSON_VALUE})
    public void resendBusinessPartnerPairRequest(
            @LogParam("customerUuid") @PathVariable UUID customerUuid,
            @LogParam("resendPhoneChallenge") @RequestBody @Valid ResendPhoneChallenge resendPhoneChallenge) {

        pairingService.resendBusinessPartnerPairRequest(customerUuid, resendPhoneChallenge);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('BUSINESS_PARTNERS_PAIRING')))) and #uuid == authentication.customerId")
    @PostMapping(value = "/{uuid}/business-partners/pair-challange", produces = {MediaType.APPLICATION_JSON_VALUE})
    public PairBusinessPartnerResponse pairChallenge(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("pairChallengeRequest") @RequestBody Challenge challenge) {

        return pairingService.pairChallenge(uuid, challenge);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))) and #uuid == authentication.customerId")
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/{uuid}/customer-requests")
    public CustomerRequest createCustomerRequestForCustomer(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("customerRequest") @Validated(CustomerRequestChecks.class) @RequestBody CustomerRequest customerRequest
    ) {
        return customerRequestService.createCustomerRequestForCustomer(uuid, customerRequest);
    }

    @Log
    @LogParam
    @PostMapping(path = "/anonymous/customer-requests/create", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public CustomerRequest createCustomerRequest(
            @LogParam("customerRequest") @RequestBody @Validated(CustomerRequestChecks.class) @Valid AnonymousCustomerRequest customerRequest) {

        return anonymousCustomerService.createCustomerRequest(customerRequest);
    }

    @Log
    @LogParam
    @PostMapping(path = "/anonymous/customer-requests/{uuid}/send", produces = {MediaType.APPLICATION_JSON_VALUE})
    public CustomerRequest sendAnonymousCustomerRequest(
            @LogParam("uuid") @PathVariable UUID uuid,
            @RequestPart(value = "attachment", required = false) MultipartFile... attachments) {

        return anonymousCustomerService.sendCustomerRequest(uuid, attachments);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))) and #customerUuid == authentication.customerId")
    @PostMapping("/{customerUuid}/customer-requests/{requestUuid}/completion/update")
    public CustomerRequest updateCustomerRequest(
            @LogParam("customerUuid") @PathVariable UUID customerUuid,
            @LogParam("requestUuid") @PathVariable UUID requestUuid,
            @LogParam("customerRequestUpdate") @Validated(CustomerRequestChecks.class) @RequestBody CustomerRequest customerRequest) {

        return customerRequestService.updateCustomerRequest(customerUuid, requestUuid, customerRequest);
    }

    @Log
    @LogParam
    @PagingAsQueryParams
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))) and #uuid == authentication.customerId")
    @GetMapping("/{uuid}/customer-requests")
    public PagedResponse<CustomerRequestSummary> getCustomerRequests(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("paging")  QueryStringPaging paging
    ) {
        CustomerRequestSearchQuery query = new CustomerRequestSearchQuery();
        query.setPaging(paging.toPaging());
        query.setCustomerUuid(uuid);

        return customerRequestService.fetchCustomerRequests(query);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))) and #customerUuid == authentication.customerId")
    @PostMapping("/{customerUuid}/customer-requests/{requestUuid}/completion/reject")
    public CustomerRequest rejectCustomerRequests(
            @LogParam("customerUuid") @PathVariable UUID customerUuid,
            @LogParam("requestUuid") @PathVariable UUID requestUuid,
            @LogParam("rejectReason") @RequestParam CompletionRejectReason rejectReason) {

        return customerRequestService.rejectCustomerRequest(requestUuid, rejectReason);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))) and #uuid == authentication.customerId")
    @PostMapping("/{uuid}/customer-requests/search")
    public PagedResponse<CustomerRequestSummary> customerSearchCustomerRequests(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("query") @RequestBody CustomerRequestSearchQuery query,
            @LogParam("APPEND_NOT_SYNCED") @RequestParam(defaultValue = "false",name = "APPEND_NOT_SYNCED") boolean appendNotSynced) {
        query.setCustomerUuid(uuid);

        return customerRequestService.fetchCustomerRequests(query, appendNotSynced);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))) and #uuid == authentication.customerId")
    @PostMapping("/{uuid}/customer-requests/search/count")
    public CustomerRequestCount getCountOfFilteredCustomerRequests(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("query") @RequestBody CustomerRequestSearchQuery query,
            @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId
    ) {
        query.setCustomerUuid(uuid);

        return customerRequestService.getCountOfFilteredRequests(query, businessPartnerId);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @GetMapping(value = "/{uuid}/notifications", produces = {MediaType.APPLICATION_JSON_VALUE})
    @PagingAsQueryParams
    public PagedResponse<CustomerNotificationSummary> getCustomerNotifications(@LogParam("uuid") @PathVariable UUID uuid,
                                                          @LogParam("queryStringPaging ")  QueryStringPaging  queryStringPaging) {

        CustomerNotificationsSearch customerNotificationsSearch = new CustomerNotificationsSearch();
        customerNotificationsSearch.setPaging(queryStringPaging.toPaging());
        return customerNotificationService.getByCustomerAccount(uuid, customerNotificationsSearch);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PostMapping(value = "/{uuid}/notifications/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<CustomerNotificationSummary> getCustomerNotificationsSearch(@LogParam("uuid") @PathVariable UUID uuid,
                                                                                  @LogParam("customerNotificationsSearch ") @RequestBody CustomerNotificationsSearch customerNotificationsSearch) {

        return customerNotificationService.getByCustomerAccount(uuid, customerNotificationsSearch);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @DeleteMapping(value = "/{uuid}/notifications")
    public void deleteAllCustomerVisibleNotifications(@LogParam("uuid") @PathVariable UUID uuid) {
        customerNotificationService.deleteAllSentVisibleNotificationsByCustomerId(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PostMapping(value = "/{uuid}/delete-account", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void verifyDeleteCustomerAccount(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("request") @RequestBody DeleteCustomerAccountRequest request
    ) {
        customerService.verifyDeleteCustomerAccount(uuid, request);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE')))) and #uuid == authentication.customerId")
    @PostMapping("/{uuid}/delete-account/resend")
    public void resendDeleteCustomerAccount(
            @LogParam("uuid") @PathVariable UUID uuid
    ) {
        customerService.resendDeleteCustomerAccount(uuid);
    }

    @Log
    @LogParam
    @PostMapping("/delete-account/challenge")
    public void deleteCustomerAccount(
            @LogParam("challenge") @RequestBody Challenge challenge
    ) {
        customerService.deleteCustomerAccount(challenge);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE'))")
    @PostMapping("/verifyPassword")
    public void verifyPasswordAccount(@LogParam("passwordCheck") @RequestBody PasswordCheck passwordCheck) {
        customerService.verifyPassword(passwordCheck);
    }

    @Log
    @LogParam
    @PostMapping(value = "/suspicious-activity", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void reportSuspiciousActivity(
            @LogParam("suspiciousActivity") @Valid @RequestBody SuspiciousActivity suspiciousActivity
    ) {
        customerService.reportSuspiciousActivity(suspiciousActivity);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))) and #customerUuid == authentication.customerId")
    @PostMapping("/{customerUuid}/united-delivery-points/sharings")
    public void createSharingDeliveryPoints(
                @LogParam("customerUuid") @PathVariable UUID customerUuid,
                @LogParam("sharingRequestDeliveryPoints")
                @Valid @NotNull @RequestBody SharingUnitedDeliveryPointRequest sharingUnitedDeliveryPointRequest
        ){
        customerSharingService.createUnitedDeliveryPointSharings(sharingUnitedDeliveryPointRequest,customerUuid);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))) and #customerUuid == authentication.customerId")
    @DeleteMapping("/{customerUuid}/united-delivery-points/sharings")
    public void removeSharingDeliveryPoints(
            @LogParam("customerUuid") @PathVariable UUID customerUuid,
            @LogParam("sharingRequestDeliveryPoints")
            @Valid @NotNull @RequestBody SharingUnitedDeliveryPointRequest sharingUnitedDeliveryPointRequest
    ){
        customerSharingService.removeUnitedDeliveryPointSharings(sharingUnitedDeliveryPointRequest,customerUuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and #customerUuid == authentication.customerId")
    @PostMapping("/{customerUuid}/mobile-app-token")
    public void createDevice(
            @LogParam("customerUuid") @PathVariable UUID customerUuid,
            @LogParam("customerDeviceCreateRequest") @Valid @NotNull @RequestBody CustomerDeviceCreateRequest customerDeviceCreateRequest
    ){
        customerDeviceService.createDevice(customerUuid,customerDeviceCreateRequest);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and #customerUuid == authentication.customerId")
    @DeleteMapping("/{customerUuid}/mobile-app-token")
    public void removeDevice(
            @LogParam("customerUuid") @PathVariable UUID customerUuid,
            @LogParam("customerDeviceDeleteRequest") @Valid @NotNull @RequestBody CustomerDeviceDeleteRequest customerDeviceDeleteRequest
    ){
        customerDeviceService.removeDevice(customerUuid,customerDeviceDeleteRequest);
    }
}
