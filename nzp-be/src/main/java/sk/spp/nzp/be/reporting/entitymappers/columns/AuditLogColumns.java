package sk.spp.nzp.be.reporting.entitymappers.columns;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public enum AuditLogColumns {

    CREATED_AT,
    CODE,
    ENTITY_TYPE,
    ENTITY_ITEM,
    ENTITY_REFERENCE,
    EMPLOYEE_LOGIN,
    EMPLOYEE_NAME,
    EMPLOYEE_EMAIL,
    EMPLOYEE_PRESENT,

    BUSINESS_PARTNER_NAME,
    BUSINESS_PARTNER_EXTERNAL_ID,
    RELATED_CUSTOMER_NAME,
    RELATED_CUSTOMER_EMAIL,
    LOGGED_CUSTOMER_NAME,
    LOGGED_CUSTOMER_EMAIL
    ;

    public static final String GENERIC_CODE_LIST_TYPE = "AUDIT_LOG_EXPORT_COLUMN";

    private static Map<String, AuditLogColumns> enumMap;

    static {
        enumMap = Arrays.stream(AuditLogColumns.values())
                .collect(Collectors.toMap(AuditLogColumns::name, Function.identity()));
    }

    public static AuditLogColumns valueOfOrNull(String stringValue) {
        return enumMap.getOrDefault(stringValue, null);
    }
}
