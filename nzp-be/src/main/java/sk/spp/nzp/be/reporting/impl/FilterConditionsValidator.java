package sk.spp.nzp.be.reporting.impl;

import sk.spp.nzp.commons.api.reporting.DataEntity;
import sk.spp.nzp.commons.api.reporting.FilterCondition;
import sk.spp.nzp.commons.api.reporting.FilterOperator;
import sk.spp.nzp.commons.api.reporting.PropertyDefinition;
import sk.spp.nzp.commons.api.reporting.ReportDefinition;
import sk.spp.nzp.commons.utils.DataModelHolder;
import sk.spp.nzp.commons.utils.ReportRegExpHolder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

public class FilterConditionsValidator {

    private List<String> aliasedFilterConditions;
    Map<String, DataEntity> allAliasedEntities;
    private ReportDefinition reportDefinition;
    private DataModelHolder dataModelHolder;

    private List<String> errors = new ArrayList<>();

    public FilterConditionsValidator(List<String> aliasedFilterConditions, Map<String, DataEntity> allAliasedEntities, ReportDefinition reportDefinition, DataModelHolder dataModelHolder) {
        this.aliasedFilterConditions = aliasedFilterConditions;
        this.allAliasedEntities = allAliasedEntities;
        this.reportDefinition = reportDefinition;
        this.dataModelHolder = dataModelHolder;
    }

    public void validate() {
        if (reportDefinition.getFilterConditions() != null) {
            for (FilterCondition filterCondition : reportDefinition.getFilterConditions()) {
                int sep = filterCondition.getProperty().indexOf('.');
                if (isValidCondition(filterCondition, sep)) {
                    String alias = filterCondition.getProperty().substring(0, sep);
                    String propertyName = filterCondition.getProperty().substring(sep + 1);
                    DataEntity dataEntity = allAliasedEntities.get(alias);

                    PropertyDefinition property = dataModelHolder.getProperty(dataEntity, propertyName);

                    if (property != null && !filterCondition.getOperator().isUnaryOperator()) {
                        switch (property.getType()) {
                            case UUID:
                            case STRING:
                                validateStringProperty(filterCondition);
                                validateId(filterCondition, property);
                                break;
                            case TIMESTAMP:
                                validateTimestampProperty(filterCondition);
                                break;
                            case ENUM:
                                validateEnumProperty(filterCondition, propertyName, dataEntity, property);
                                break;
                            case NUMBER:
                                validateNumberProperty(filterCondition);
                                break;
                            case INTEGER:
                                validateIntegerProperty(filterCondition);
                                break;
                            case OBJECT:
                                validateObjectProperty(filterCondition);
                                break;
                            case LIST:
                                validateObjectProperty(filterCondition);
                                break;
                            case BOOLEAN:
                                validateBooleanProperty(filterCondition);
                                break;
                            default:
                                errors.add("Unsupported property type: " + property.getType());
                        }
                    }
                }
            }
        }
    }

    private void validateId(FilterCondition filterCondition, PropertyDefinition propertyDefinition) {
        if(ReportRegExpHolder.ID.equals(propertyDefinition.getName())) {
            try{
                UUID.fromString(filterCondition.getValue());
            } catch (IllegalArgumentException e) {
                //do nothing
            }
        }
    }

    private void validateObjectProperty(FilterCondition filterCondition) {
        errors.add("Objects are not allowed to be used in binary filter conditions. Problematic filter condition is: " + filterCondition);
    }

    private void validateBooleanProperty(FilterCondition filterCondition) {
        if (filterCondition.getOperator() == FilterOperator.IS_BETWEEN) {
            errors.add("Between oprator is not allowed to be used for Boolean properties. Problematic filter condition is: " + filterCondition);
        } else if (filterCondition.getValue() == null || filterCondition.getValue().trim().isEmpty()) {
            errors.add("Missing value for filter condition. Problematic filter condition is: " + filterCondition);
        } else if (!filterCondition.getValue().matches(ReportRegExpHolder.BOOLEAN_REGEXP)) {
            errors.add("Boolean is expected but value '" + filterCondition.getValue() + "' is used. Problematic filter condition is: " + filterCondition);
        }
    }

    private void validateIntegerProperty(FilterCondition filterCondition) {
        if (filterCondition.getOperator() == FilterOperator.IS_BETWEEN) {
            if (filterCondition.getValue1() == null || filterCondition.getValue1().trim().isEmpty()) {
                errors.add("Missing first value for BETWEEN condition. Problematic filter condition is: " + filterCondition);
            } else if (filterCondition.getValue2() == null || filterCondition.getValue2().trim().isEmpty()) {
                errors.add("Missing second value for BETWEEN condition. Problematic filter condition is: " + filterCondition);
            } else if (!filterCondition.getValue1().matches(ReportRegExpHolder.INTEGER_REGEXP) || !filterCondition.getValue2().matches(ReportRegExpHolder.INTEGER_REGEXP)) {
                errors.add("For BETWEEN integer expression both values must be defined as integers. Problematic filter condition is: " + filterCondition);
            }
        } else if (filterCondition.getValue() == null || filterCondition.getValue().trim().isEmpty()) {
            errors.add("Missing value for filter condition. Problematic filter condition is: " + filterCondition);
        } else if (!filterCondition.getValue().matches(ReportRegExpHolder.INTEGER_REGEXP)) {
            errors.add("Integer is expected but value '" + filterCondition.getValue() + "' is used. Problematic filter condition is: " + filterCondition);
        }
    }

    private void validateNumberProperty(FilterCondition filterCondition) {
        if (filterCondition.getOperator() == FilterOperator.IS_BETWEEN) {
            if (filterCondition.getValue1() == null || filterCondition.getValue1().trim().isEmpty()) {
                errors.add("Missing first value for BETWEEN condition. Problematic filter condition is: " + filterCondition);
            } else if (filterCondition.getValue2() == null || filterCondition.getValue2().trim().isEmpty()) {
                errors.add("Missing second value for BETWEEN condition. Problematic filter condition is: " + filterCondition);
            } else if (!filterCondition.getValue1().matches(ReportRegExpHolder.NUMBER_REGEXP) || !filterCondition.getValue2().matches(ReportRegExpHolder.NUMBER_REGEXP)) {
                errors.add("For BETWEEN number expression both values must be defined as numbers. Problematic filter condition is: " + filterCondition);
            }
        } else if (filterCondition.getValue() == null || filterCondition.getValue().trim().isEmpty()) {
            errors.add("Missing value for filter condition. Problematic filter condition is: " + filterCondition);
        } else if (!filterCondition.getValue().matches(ReportRegExpHolder.NUMBER_REGEXP)) {
            errors.add("Number is expected but value '" + filterCondition.getValue() + "' is used. Problematic filter condition is: " + filterCondition);
        }
    }

    private void validateEnumProperty(FilterCondition filterCondition, String propertyName, DataEntity dataEntity, PropertyDefinition property) {
        if (filterCondition.getOperator() == FilterOperator.IS_BETWEEN) {
            errors.add("Between operator is not allowed to be used for Enum properties. Problematic filter condition is: " + filterCondition);
        } else if (filterCondition.getValue() == null || filterCondition.getValue().trim().isEmpty()) {
            errors.add("Missing value for filter condition. Problematic filter condition is: " + filterCondition);
        } else if (property.getOptions() == null) {
            errors.add("Options are missing in the data model for " + dataEntity.getName() + "." + propertyName);
        }
        if (property.getOptions().indexOf(filterCondition.getValue()) == -1) {
            errors.add("Option '" + filterCondition.getValue() + "' is not allowed by the data model. Problematic filter condition is: " + filterCondition);
        }
    }

    private void validateTimestampProperty(FilterCondition filterCondition) {
        if (filterCondition.getOperator() == FilterOperator.IS_BETWEEN) {
            if (filterCondition.getValue1() == null || filterCondition.getValue1().trim().isEmpty()) {
                errors.add("Missing first value for BETWEEN condition. Problematic filter condition is: " + filterCondition);
            } else if (filterCondition.getValue2() == null || filterCondition.getValue2().trim().isEmpty()) {
                errors.add("Missing second value for BETWEEN condition. Problematic filter condition is: " + filterCondition);
            } else if ((!filterCondition.getValue1().matches(ReportRegExpHolder.ISO_DATE_TIME_REGEXP) && !filterCondition.getValue1().matches(ReportRegExpHolder.ISO_DATE_TIME_REGEXP_MILLIS)) || (!filterCondition.getValue2().matches(ReportRegExpHolder.ISO_DATE_TIME_REGEXP) && !filterCondition.getValue2().matches(ReportRegExpHolder.ISO_DATE_TIME_REGEXP_MILLIS))) {
                errors.add("For BETWEEN date & time expression both values must be defined as ISO DATE & TIME. Problematic filter condition is: " + filterCondition);
            }
        } else {
            if (filterCondition.getValue() == null || filterCondition.getValue().trim().isEmpty()) {
                errors.add("Missing value for filter condition. Problematic filter condition is: " + filterCondition);
            } else if (filterCondition.getOperator() == FilterOperator.IS_OLDER_THAN || filterCondition.getOperator() == FilterOperator.IS_NOT_OLDER_THAN) {
                if (!filterCondition.getValue().matches(ReportRegExpHolder.TIME_INTERVAL_REGEXP)) {
                    errors.add("Time interval is expected but value '" + filterCondition.getValue() + "' is used. Problematic filter condition is: " + filterCondition);
                }
            } else if (!filterCondition.getValue().matches(ReportRegExpHolder.ISO_DATE_TIME_REGEXP) && !filterCondition.getValue().matches(ReportRegExpHolder.ISO_DATE_TIME_REGEXP_MILLIS)) {
                errors.add("ISO DATE & TIME is expected but value '" + filterCondition.getValue() + "' is used. Problematic filter condition is: " + filterCondition);
            }
        }
    }

    private void validateStringProperty(FilterCondition filterCondition) {
        if (filterCondition.getOperator() == FilterOperator.IS_BETWEEN) {
            errors.add("Between operator is not allowed to be used for String properties. Problematic filter condition is: " + filterCondition);
        } else if (filterCondition.getValue() == null || filterCondition.getValue().trim().isEmpty()) {
            errors.add("Missing value for filter condition. Problematic filter condition is: " + filterCondition);
        } else if (filterCondition.getOperator() == FilterOperator.IS_OLDER_THAN || filterCondition.getOperator() == FilterOperator.IS_NOT_OLDER_THAN) {
            errors.add("Invalid operator usage in filter condition: " + filterCondition);
        }
    }

    private boolean isValidCondition(FilterCondition filterCondition, int sep) {
        boolean isValid = true;
        if (!aliasedFilterConditions.contains(filterCondition.getProperty())) {
            errors.add("Invalid output property: " + filterCondition.getProperty());
            isValid = false;
        }
        if (sep == -1) {
            errors.add("Property '" + filterCondition.getProperty() + "' for filter condition '" + filterCondition + "' has invalid format");
            isValid = false;
        }
        return isValid;
    }

    public List<String> getErrors() {
        return errors.stream().distinct().collect(Collectors.toList());
    }
}
