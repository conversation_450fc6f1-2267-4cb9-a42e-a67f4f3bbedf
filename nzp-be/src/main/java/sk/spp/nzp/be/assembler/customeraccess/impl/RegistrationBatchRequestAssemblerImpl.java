package sk.spp.nzp.be.assembler.customeraccess.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customeraccess.registration.RegistrationBatchRequest;
import sk.spp.nzp.be.assembler.customeraccess.registration.RegistrationBatchRequestAssembler;
import sk.spp.nzp.commons.model.customeraccess.registration.RegistrationBatchRequestEntity;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class RegistrationBatchRequestAssemblerImpl implements RegistrationBatchRequestAssembler {

    @Override
    public List<RegistrationBatchRequest> map(Collection<RegistrationBatchRequestEntity> inputs) {
        return inputs.stream().map(v->map(v, new RegistrationBatchRequest())).collect(Collectors.toList());
    }

    @Override
    public RegistrationBatchRequest map(RegistrationBatchRequestEntity input, RegistrationBatchRequest output) {
        output.setId(input.getId());
        output.setCreatedAt(input.getCreatedAt());
        output.setCreatedBy(input.getCreatedBy());
        output.setUpdatedAt(input.getUpdatedAt());
        output.setUpdatedBy(input.getUpdatedBy());
        output.setScheduledAt(input.getScheduledAt());
        output.setStatus(input.getStatus());
        output.setFilename(input.getFilename());
        output.setFileSize(input.getFileSize());
        output.setFileType(input.getFileType());
        output.setSuccessCount(input.getSuccessCount());
        output.setFailedCount(input.getFailedCount());
        output.setReport(input.isReport());

        return output;
    }
}
