package sk.spp.nzp.be.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.help.ComponentHelp;
import sk.spp.nzp.be.service.help.ComponentHelpService;

import java.util.List;

@RestController
@RequestMapping("/component-helps")
public class ComponentHelpController {

    private ComponentHelpService componentHelpService;

    public ComponentHelpController(ComponentHelpService componentHelpService) {
        this.componentHelpService = componentHelpService;
    }

    @Log
    @LogParam
    @GetMapping( value = "", produces = {"application/json"})
    public List<ComponentHelp> getAllComponents(@LogParam("content") @RequestParam(required = false) Boolean content,
                                      @LogParam("screen") @RequestParam(value = "screen", required = false) List<String> screens,
                                      @LogParam("field") @RequestParam(value = "field", required = false) List<String> fields,
                                      @LogParam("locale") @RequestParam(required = false) String locale) {

        return componentHelpService.getAll(content, screens, fields, locale);
    }
}
