package sk.spp.nzp.be.reporting.entitymappers;

import com.querydsl.core.Tuple;
import com.querydsl.core.types.Expression;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.api.common.Sorting;
import sk.spp.nzp.be.api.common.SortingAttribute;
import sk.spp.nzp.be.api.customeraccess.CustomerAccountSearch;
import sk.spp.nzp.be.reporting.entitymappers.columns.CustomerAccountColumns;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;
import sk.spp.nzp.commons.model.customeraccess.QCustomerAccountEntity;
import sk.spp.nzp.commons.model.customerprofile.QBusinessPartnerConsentEntity;
import sk.spp.nzp.commons.model.customerprofile.QBusinessPartnerEntity;
import sk.spp.nzp.commons.model.customersharing.QBusinessPartnerOwnershipEntity;

import java.util.ArrayList;
import java.util.List;

public class CustomerAccountMapper extends AbstractMapper<CustomerAccountEntity> {

    private QCustomerAccountEntity qCustomerAccountEntity = QCustomerAccountEntity.customerAccountEntity;
    private QBusinessPartnerEntity qBusinessPartnerEntity = QBusinessPartnerEntity.businessPartnerEntity;
    private QBusinessPartnerOwnershipEntity qBusinessPartnerOwnershipEntity = QBusinessPartnerOwnershipEntity.businessPartnerOwnershipEntity;
    private QBusinessPartnerConsentEntity qBusinessPartnerConsentEntity = QBusinessPartnerConsentEntity.businessPartnerConsentEntity;

    private List<CustomerAccountColumns> columnsNames;
    private List<CustomerAccountColumns> orderByColumns; //becouse of disdinct query is used in provider

    public CustomerAccountMapper(List<String> columns, CustomerAccountSearch customerAccountSearch) {
        columnsNames = new ArrayList<>();
        orderByColumns = new ArrayList<>();

        for (String column : columns) {
            CustomerAccountColumns enumValue = CustomerAccountColumns.valueOfOrNull(column);
            if (enumValue != null) {
                columnsNames.add(enumValue);
            } else {
                throw new ApiException(ErrorCode.INVALID_COLUMN_NAME);
            }
        }
        if (customerAccountSearch.getPaging() == null || customerAccountSearch.getPaging().getSort() == null || customerAccountSearch.getPaging().getSort().isEmpty()) {
            if (!columnsNames.contains(CustomerAccountColumns.CREATED_AT)) {
                orderByColumns.add(CustomerAccountColumns.CREATED_AT);
            }
        } else {

            for (Sorting sorting : customerAccountSearch.getPaging().getSort()) {

                if (SortingAttribute.CUSTOMER_ACCOUNT_NAME.name().contentEquals(sorting.getAttribute()) || SortingAttribute.CUSTOMER_ACCOUNT_NAME.getCamelCaseName().contentEquals(sorting.getAttribute())) {

                    orderByColumns.add(CustomerAccountColumns.FIRST_NAME);
                    orderByColumns.add(CustomerAccountColumns.LAST_NAME);

                } else if (SortingAttribute.CUSTOMER_ACCOUNT_EMAIL.name().contentEquals(sorting.getAttribute()) || SortingAttribute.CUSTOMER_ACCOUNT_NAME.getCamelCaseName().contentEquals(sorting.getAttribute())) {

                    orderByColumns.add(CustomerAccountColumns.EMAIL);

                } else if (SortingAttribute.CUSTOMER_ACCOUNT_PHONE.name().contentEquals(sorting.getAttribute()) || SortingAttribute.CUSTOMER_ACCOUNT_NAME.getCamelCaseName().contentEquals(sorting.getAttribute())) {

                    orderByColumns.add(CustomerAccountColumns.PHONE);

                }
            }
        }
    }

    @Override
    public Object[] map(Tuple tuple) {
        objArr = new Object[columnsNames.size()];
        for (int i = 0; i < columnsNames.size(); i++) {
            CustomerAccountColumns customerAccountColumns = columnsNames.get(i);
            switch (customerAccountColumns) {
                case EMAIL:
                    objArr[i] = tuple.get(qCustomerAccountEntity.email);
                    break;
                case TYPE:
                    objArr[i] = tuple.get(qCustomerAccountEntity.type);
                    break;
                case STATUS:
                    objArr[i] = tuple.get(qCustomerAccountEntity.status);
                    break;
                case ACTIVATION_AT:
                    objArr[i] = tuple.get(qCustomerAccountEntity.activationAt);
                    break;
                case REGISTRATION_AT:
                    objArr[i] = tuple.get(qCustomerAccountEntity.registrationAt);
                    break;
                case LOCK_UNTIL:
                    objArr[i] = tuple.get(qCustomerAccountEntity.lockUntil);
                    break;
                case LOGIN_SUCCESS_AT:
                    objArr[i] = tuple.get(qCustomerAccountEntity.loginSuccessAt);
                    break;
                case LOGIN_UNSUCCESS_AT:
                    objArr[i] = tuple.get(qCustomerAccountEntity.loginUnsuccessAt);
                    break;
                case PASSWORD_UPDATED_AT:
                    objArr[i] = tuple.get(qCustomerAccountEntity.passwordUpdatedAt);
                    break;
                case DEACTIVATION_REASON:
                    objArr[i] = tuple.get(qCustomerAccountEntity.deactivationReason);
                    break;
                case LOCALE:
                    objArr[i] = tuple.get(qCustomerAccountEntity.locale);
                    break;
                case PHONE:
                    objArr[i] = tuple.get(qCustomerAccountEntity.phone);
                    break;
                case FIRST_NAME:
                    objArr[i] = tuple.get(qCustomerAccountEntity.firstName);
                    break;
                case LAST_NAME:
                    objArr[i] = tuple.get(qCustomerAccountEntity.lastName);
                    break;
                case CREATED_AT:
                    objArr[i] = tuple.get(qCustomerAccountEntity.createdAt);
                    break;
                case BP_QUEUE:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.queue);
                    break;
                case BP_COMPANY_REGISTRATION_NUMBER:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.companyRegistrationNumber);
                    break;
                case BP_TAX_ID_NUMBER:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.taxIdNumber);
                    break;
                case BP_VAT_REGISTRATION_NUMBER:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.vatRegistrationNumber);
                    break;
                case BP_NAME:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.name);
                    break;
                case BP_FIRST_NAME:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.firstName);
                    break;
                case BP_LAST_NAME:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.lastName);
                    break;
                case BP_EMAIL:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.email);
                    break;
                case BP_PHONE:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.phone);
                    break;
                case BP_SYNCHRONIZATION_AT:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.synchronizationAt);
                    break;
                case BP_PRIMARY_STREET:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.primaryAddress().street);
                    break;
                case BP_PRIMARY_STREET_NUMBER:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.primaryAddress().streetNumber);
                    break;
                case BP_PRIMARY_CITY:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.primaryAddress().city);
                    break;
                case BP_PRIMARY_ZIP_CODE:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.primaryAddress().zipCode);
                    break;
                case BP_PRIMARY_COUNTRY:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.primaryAddress().country);
                    break;
                case BP_ACCOUNT_MANAGER:
                    objArr[i] = tuple.get(qBusinessPartnerEntity.accountManager().phone);
                    break;
                case BP_CONSENT:
                    objArr[i] = tuple.get(qBusinessPartnerConsentEntity.consent);
                    break;
                case BP_CONSENT_COMMUNICATION_CHANNEL:
                    objArr[i] = tuple.get(qBusinessPartnerConsentEntity.consentCommunicationChannel);
                    break;
            }
        }
        return objArr;
    }

    @Override
    public void select(JPAQuery<CustomerAccountEntity> query) {
        List<CustomerAccountColumns> mergedNames = new ArrayList<>(columnsNames);
        orderByColumns.forEach(cac -> {
            if (!mergedNames.contains(cac)) {
                mergedNames.add(cac);
            }
        });

        Expression[] expressions = new Expression[mergedNames.size()];


        for (int i = 0; i < mergedNames.size(); i++) {
            CustomerAccountColumns customerAccountColumns = mergedNames.get(i);

            switch (customerAccountColumns) {
                case EMAIL:
                    expressions[i] = qCustomerAccountEntity.email;
                    break;
                case TYPE:
                    expressions[i] = qCustomerAccountEntity.type;
                    break;
                case STATUS:
                    expressions[i] = qCustomerAccountEntity.status;
                    break;
                case ACTIVATION_AT:
                    expressions[i] = qCustomerAccountEntity.activationAt;
                    break;
                case REGISTRATION_AT:
                    expressions[i] = qCustomerAccountEntity.registrationAt;
                    break;
                case LOCK_UNTIL:
                    expressions[i] = qCustomerAccountEntity.lockUntil;
                    break;
                case LOGIN_SUCCESS_AT:
                    expressions[i] = qCustomerAccountEntity.loginSuccessAt;
                    break;
                case LOGIN_UNSUCCESS_AT:
                    expressions[i] = qCustomerAccountEntity.loginUnsuccessAt;
                    break;
                case PASSWORD_UPDATED_AT:
                    expressions[i] = qCustomerAccountEntity.passwordUpdatedAt;
                    break;
                case DEACTIVATION_REASON:
                    expressions[i] = qCustomerAccountEntity.deactivationReason;
                    break;
                case LOCALE:
                    expressions[i] = qCustomerAccountEntity.locale;
                    break;
                case PHONE:
                    expressions[i] = qCustomerAccountEntity.phone;
                    break;
                case FIRST_NAME:
                    expressions[i] = qCustomerAccountEntity.firstName;
                    break;
                case LAST_NAME:
                    expressions[i] = qCustomerAccountEntity.lastName;
                    break;
                case CREATED_AT:
                    expressions[i] = qCustomerAccountEntity.createdAt;
                    break;
                case BP_QUEUE:
                    expressions[i] = qBusinessPartnerEntity.queue;
                    break;
                case BP_COMPANY_REGISTRATION_NUMBER:
                    expressions[i] = qBusinessPartnerEntity.companyRegistrationNumber;
                    break;
                case BP_TAX_ID_NUMBER:
                    expressions[i] = qBusinessPartnerEntity.taxIdNumber;
                    break;
                case BP_VAT_REGISTRATION_NUMBER:
                    expressions[i] = qBusinessPartnerEntity.vatRegistrationNumber;
                    break;
                case BP_NAME:
                    expressions[i] = qBusinessPartnerEntity.name;
                    break;
                case BP_FIRST_NAME:
                    expressions[i] = qBusinessPartnerEntity.firstName;
                    break;
                case BP_LAST_NAME:
                    expressions[i] = qBusinessPartnerEntity.lastName;
                    break;
                case BP_EMAIL:
                    expressions[i] = qBusinessPartnerEntity.email;
                    break;
                case BP_PHONE:
                    expressions[i] = qBusinessPartnerEntity.phone;
                    break;
                case BP_SYNCHRONIZATION_AT:
                    expressions[i] = qBusinessPartnerEntity.synchronizationAt;
                    break;
                case BP_PRIMARY_STREET:
                    expressions[i] = qBusinessPartnerEntity.primaryAddress().street;
                    break;
                case BP_PRIMARY_STREET_NUMBER:
                    expressions[i] = qBusinessPartnerEntity.primaryAddress().streetNumber;
                    break;
                case BP_PRIMARY_CITY:
                    expressions[i] = qBusinessPartnerEntity.primaryAddress().city;
                    break;
                case BP_PRIMARY_ZIP_CODE:
                    expressions[i] = qBusinessPartnerEntity.primaryAddress().zipCode;
                    break;
                case BP_PRIMARY_COUNTRY:
                    expressions[i] = qBusinessPartnerEntity.primaryAddress().country;
                    break;
                case BP_ACCOUNT_MANAGER:
                    expressions[i] = qBusinessPartnerEntity.accountManager().phone;
                    break;
                case BP_CONSENT:
                    expressions[i] = qBusinessPartnerConsentEntity.consent;
                    break;
                case BP_CONSENT_COMMUNICATION_CHANNEL:
                    expressions[i] = qBusinessPartnerConsentEntity.consentCommunicationChannel;
                    break;
            }
        }
        query.select(expressions)
                .leftJoin(qBusinessPartnerOwnershipEntity).on(qBusinessPartnerOwnershipEntity.customerAccount().eq(qCustomerAccountEntity))
                .leftJoin(qBusinessPartnerEntity).on(qBusinessPartnerEntity.eq(qBusinessPartnerOwnershipEntity.businessPartner()))
                .leftJoin(qBusinessPartnerConsentEntity).on(qBusinessPartnerEntity.id.eq(qBusinessPartnerConsentEntity.businessPartnerId));
    }
}
