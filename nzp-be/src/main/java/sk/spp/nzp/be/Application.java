package sk.spp.nzp.be;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import sk.isdd.common.logging.config.LoggingConfiguration;
import sk.spp.nzp.commons.configuration.HttpRestTemplateConfiguration;
import sk.spp.nzp.commons.configuration.NotificationConfiguration;
import sk.spp.nzp.ldap.EmployeeModuleConfiguration;

@SpringBootApplication
@ComponentScan({"sk.spp.nzp.commons","sk.spp.nzp.be"})
@EnableJpaRepositories({"sk.spp.nzp.commons","sk.spp.nzp.be"})
@EntityScan({"sk.spp.nzp.commons","sk.spp.nzp.be"})
@EnableScheduling
@EnableCaching
@Import({
        LoggingConfiguration.class,
        EmployeeModuleConfiguration.class,
        NotificationConfiguration.class,
        HttpRestTemplateConfiguration.class
})

public class Application extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(Application.class);
    }

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
