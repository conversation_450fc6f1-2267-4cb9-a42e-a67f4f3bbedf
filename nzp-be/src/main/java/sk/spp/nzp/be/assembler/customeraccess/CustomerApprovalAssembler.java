package sk.spp.nzp.be.assembler.customeraccess;

import sk.spp.nzp.be.api.customeraccess.CustomerApproval;
import sk.spp.nzp.commons.model.customeraccess.CustomerApprovalEntity;

import java.util.Collection;
import java.util.List;

public interface CustomerApprovalAssembler {

    CustomerApprovalEntity map(CustomerApproval input, CustomerApprovalEntity output);

    CustomerApproval map(CustomerApprovalEntity input, CustomerApproval output);

    List<CustomerApproval> map(Collection<CustomerApprovalEntity> input);

}
