package sk.spp.nzp.be.converters;

import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerconsumption.MeterReading;
import sk.spp.nzp.commons.api.customerconsumption.ConsumptionSearch;
import sk.spp.nzp.commons.api.customerconsumption.ConsumptionSearchResponse;
import sk.spp.nzp.commons.api.customerconsumption.ConsumptionType;
import sk.spp.nzp.commons.api.customerprofile.enums.DeliveryPointType;
import sk.spp.nzp.commons.api.customerprofile.enums.Units;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.model.customerprofile.DeliveryPointEntity;
import sk.spp.nzp.commons.service.codelist.ConfigParameterService;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;

@Component
public class MeterReadingUnitsConverterImpl implements MeterReadingUnitsConverter {

    private static final Map<DeliveryPointType, Units> DEFAULT_METER_READING_UNITS = Map.of(
            DeliveryPointType.EE, Units.KWH,
            DeliveryPointType.ZP, Units.M3
    );

    private static final Map<DeliveryPointType, Units> DEFAULT_METER_READING_UNITS_CONS = Map.of(
            DeliveryPointType.EE, Units.KW,
            DeliveryPointType.ZP, Units.M3
    );

    private static final String M3_KWH_COEFFICIENT_ID = "deliverypoint.zp.m3kwh.coefficient";

    @Autowired
    private ConfigParameterService configParameterService;

    @Override
    public void convert(MeterReading meterReading, DeliveryPointEntity deliveryPoint, Units units) {

        meterReading.setMeterReadingUnits(getDefaultUnits(deliveryPoint.getType()));

        if (validateConvertible(meterReading.getMeterReadingUnits(), deliveryPoint.getType(), deliveryPoint.getId(), units)) {
            BigDecimal m3KwhCoefficient = getCoefficient(M3_KWH_COEFFICIENT_ID);

            multiply(meterReading::getValue, meterReading::setValue, m3KwhCoefficient);

            meterReading.setMeterReadingUnits(units);
        }
    }

    @Override
    public void convert(ConsumptionSearchResponse consumption, DeliveryPointEntity deliveryPoint, ConsumptionSearch consumptionSearch) {

        Units units = consumptionSearch.getUnits();

        if(consumptionSearch.getType().equals(ConsumptionType.INTERVAL_METER_READING)){
            consumption.setUnits(getDefaultConsUnits(deliveryPoint.getType()));
        }else {
            consumption.setUnits(getDefaultUnits(deliveryPoint.getType()));
        }

        if (validateConvertible(consumption.getUnits(), deliveryPoint.getType(), deliveryPoint.getId(), units)) {
            BigDecimal m3KwhCoefficient = getCoefficient(M3_KWH_COEFFICIENT_ID);

            Optional.ofNullable(consumption.getTariffs()).ifPresent(tariffs -> tariffs.forEach(tariff -> {
                Optional.ofNullable(tariff.getValues()).ifPresent(values -> values.forEach(value -> {
                    multiply(value::getValue, value::setValue, m3KwhCoefficient);
                }));
            }));

            Optional.ofNullable(consumption.getStatistics()).ifPresent(stats -> {

                multiply(stats::getAverage, stats::setAverage, m3KwhCoefficient);
            });

            Optional.ofNullable(consumption.getReservedValues()).ifPresent(resVal -> {
                resVal.getMaximumDailyAmount().forEach( value ->
                        multiply(value::getValue, value::setValue, m3KwhCoefficient)
                );
                resVal.getReserveAmount().forEach( value ->
                        multiply(value::getValue, value::setValue, m3KwhCoefficient)
                );
                resVal.getMaximumReserveAmount().forEach( value ->
                        multiply(value::getValue, value::setValue, m3KwhCoefficient)
                );
            });

            consumption.setUnits(units);
        }
    }

    @Override
    public MutablePair<BigDecimal, Units> convert(BigDecimal value, DeliveryPointType type, Units units) {
        Units defaultUnits = getDefaultUnits(type);
        MutablePair<BigDecimal, Units> result = new MutablePair<>(value, defaultUnits);

        if (validateConvertible(defaultUnits, type, null, units)) {
            BigDecimal m3KwhCoefficient = getCoefficient(M3_KWH_COEFFICIENT_ID);

            multiply(() -> value, result::setLeft, m3KwhCoefficient);
            result.setRight(units);
        }
        return result;
    }

    @Override
    public Units getDefaultUnits(DeliveryPointType type) {
        return Optional.ofNullable(type)
                .map(DEFAULT_METER_READING_UNITS::get)
                .orElse(null);
    }

    public Units getDefaultConsUnits(DeliveryPointType type) {
        return Optional.ofNullable(type)
                .map(DEFAULT_METER_READING_UNITS_CONS::get)
                .orElse(null);
    }

    private boolean validateConvertible(Units defaultUnits, DeliveryPointType type, String id, Units units) {
        if (units == null) {
            return false; // No conversion
        }

        if (type == null) {
            throw new ApiException(ErrorCode.GENERIC_VALIDATION_ERROR,
                    String.format("Conversion error, delivery point type not specified [deliveryPointId=%s]", id));
        }

        if (defaultUnits == null) {
            throw new ApiException(ErrorCode.GENERIC_VALIDATION_ERROR,
                    String.format("Conversion error, default units not mapped [deliveryPointId=%s, deliveryPointType=%s]", id, type));
        }

        if (defaultUnits.equals(units)) {
            return false; // Same units requested
        }

        if (DeliveryPointType.EE.equals(type)) {
            throw new ApiException(ErrorCode.GENERIC_VALIDATION_ERROR,
                    String.format("Conversion error, conversion unsupported [deliveryPointId=%s, deliveryPointType=%s, units=%s]",
                            id, type, units));
        }

        return true;
    }

    private BigDecimal getCoefficient(String configParameterId) {
        return Optional.ofNullable(configParameterService.getDecimal(configParameterId, null))
                .orElseThrow(() -> new ApiException(ErrorCode.GENERIC_NOT_FOUND,
                        String.format("Conversion error, unable to fetch conversion coefficient [configParameterId=%s]", configParameterId)));
    }

    private void multiply(Supplier<BigDecimal> getter, Consumer<BigDecimal> setter, BigDecimal coefficient) {
        BigDecimal value = getter.get();

        if (value != null) {
            BigDecimal result = value.multiply(coefficient);
            setter.accept(result);
        }
    }
}