package sk.spp.nzp.be.assembler.customerrequest;

import sk.spp.nzp.be.api.customerconsumption.MeterReadingInfoRequest;
import sk.spp.nzp.commons.api.customerrequest.component.*;
import sk.spp.nzp.commons.api.customerrequest.enums.MeterReadingType;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;
import sk.spp.nzp.commons.model.customerprofile.*;

import java.math.BigDecimal;
import java.time.LocalDate;

public interface CustomerRequestComponentAssembler {

    Metadata mapMetadata(CustomerAccountEntity input, Metadata output);
    Metadata mapMetadata(BusinessPartnerEntity input, Metadata output);
    Metadata mapMetadata(ContractAccountEntity input, Metadata output);
    Metadata mapMetadata(ContractEntity input, Metadata output);
    Metadata mapMetadata(DeliveryPointEntity input, Metadata output);

    EntityReference mapEntityReference(ContractAccountEntity input, EntityReference output);
    EntityReference mapEntityReference(ContractEntity input, EntityReference output);
    EntityReference mapEntityReference(DeliveryPointEntity input, EntityReference output);

    MeterReading mapReading(MeterReadingInfoRequest meterReading, DeliveryPointEntity deliveryPoint, MeterReading output, MeterReadingType type);
    MeterReading mapGasReading(BigDecimal value, LocalDate date, MeterReading output);

    InvoiceDelivery mapEInvoiceDelivery(String email, InvoiceDelivery output);

    TariffEe mapTariff(TariffEntity tariff, LocalDate date, TariffEe output);
    TariffZp mapTariff(TariffEntity tariff, LocalDate date, TariffZp output);

}
