package sk.spp.nzp.be.assembler.customerprofile;

import sk.spp.nzp.be.api.customerprofile.InvoiceSummary;
import sk.spp.nzp.commons.model.customerprofile.InvoiceEntity;

import java.util.Collection;
import java.util.List;

public interface InvoiceSummaryAssembler {

    InvoiceSummary map(InvoiceEntity input, InvoiceSummary output);
    InvoiceSummary map(InvoiceEntity input, InvoiceSummary output, boolean onlyRefData);

    List<InvoiceSummary> map(Collection<InvoiceEntity> input);
}
