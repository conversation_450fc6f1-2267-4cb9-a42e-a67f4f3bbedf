package sk.spp.nzp.be.repository.customerprofile.query;

import java.util.UUID;

import javax.persistence.EntityManager;

import com.querydsl.jpa.impl.JPAQuery;

import sk.spp.nzp.be.models.customerprofile.ContractAccountSearch;
import sk.spp.nzp.commons.model.customerprofile.ContractAccountEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

// TODO: delete unnecessary code
public class ContractAccountFindByCustomerAccountUuidQueryProvider extends ContractAccountBaseFindByQueryProvider implements QueryDslProvider<ContractAccountEntity> {

    private UUID customerAccountUuid;
    private ContractAccountSearch contractAccountSearch;

    public ContractAccountFindByCustomerAccountUuidQueryProvider(
            UUID customerAccountUuid,
            ContractAccountSearch contractAccountSearch
    ) {
        this.customerAccountUuid = customerAccountUuid;
        this.contractAccountSearch = contractAccountSearch;
    }

    @Override
    public JPAQuery<ContractAccountEntity> getQuery(EntityManager em) {
        return findByBaseQuery(em, null, contractAccountSearch, customerAccountUuid, null, false);
    }
}
