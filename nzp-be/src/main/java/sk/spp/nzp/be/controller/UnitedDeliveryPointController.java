package sk.spp.nzp.be.controller;

import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.customerprofile.DeliveryPoint;
import sk.spp.nzp.be.api.customerprofile.EInvoiceRequest;
import sk.spp.nzp.be.api.customerprofile.UnitedDeliveryPoint;
import sk.spp.nzp.be.api.customerprofile.invoicesummary.InvoicePaymentSummary;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestSummary;
import sk.spp.nzp.be.service.customerprofile.ContractAccountService;
import sk.spp.nzp.be.service.customerprofile.InvoiceService;
import sk.spp.nzp.be.service.customerprofile.UnitedDeliveryPointService;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/united-delivery-points")
public class UnitedDeliveryPointController {

    private UnitedDeliveryPointService unitedDeliveryPointService;
    private ContractAccountService contractAccountService;
    private InvoiceService invoiceService;

    public UnitedDeliveryPointController(
            ContractAccountService contractAccountService,
            UnitedDeliveryPointService unitedDeliveryPointService,
            InvoiceService invoiceService) {

        this.unitedDeliveryPointService = unitedDeliveryPointService;
        this.contractAccountService = contractAccountService;
        this.invoiceService = invoiceService;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping(value = "/{uuid}/e-invoices/email")
    public List<CustomerRequestSummary> updateEInvoiceEmailUDP(@LogParam("uuid") @PathVariable UUID uuid,
                                                            @LogParam("emailRequest") @Valid @RequestBody EInvoiceRequest eInvoiceRequest) {

        return contractAccountService.updateEInvoiceEmailByUnitedDeliveryPoint(uuid, eInvoiceRequest.getEmail());
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping(value = "/{uuid}/e-invoices/activation")
    public List<CustomerRequestSummary> activateEInvoiceDeliveryPoint(@LogParam("uuid") @PathVariable UUID uuid,
                                 @LogParam("emailRequest") @Valid @RequestBody EInvoiceRequest eInvoiceRequest) {

        return contractAccountService.activateEInvoiceByUnitedDeliveryPoint(uuid, eInvoiceRequest.getEmail());
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))")
    @GetMapping(value = "/{uuid}/invoices/summary", produces = {MediaType.APPLICATION_JSON_VALUE})
    public InvoicePaymentSummary getInvoicesSummaryUdp(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("includeInactive") @RequestParam(defaultValue = "false") boolean includeInactive) {

        return invoiceService.getInvoiceSummaryByUnitedDeliveryPoint(uuid, includeInactive);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @GetMapping(value = "/{uuid}")
    public UnitedDeliveryPoint getUDPByUuid(@LogParam("uuid") @PathVariable UUID uuid,
                                         @LogParam("includeInactive") @RequestParam(defaultValue = "false") boolean includeInactive,
                                         @LogParam("fetch") @RequestParam(name = "fetch", required = false) List<DeliveryPoint.Fetch> fetches) {

        return unitedDeliveryPointService.getByUuid(uuid, includeInactive, fetches);
    }
}
