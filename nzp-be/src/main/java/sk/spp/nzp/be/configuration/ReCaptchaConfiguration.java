package sk.spp.nzp.be.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import sk.spp.nzp.be.service.common.ReCaptchaService;
import sk.spp.nzp.be.service.customeraccess.ReCaptchaTokenService;
import sk.spp.nzp.commons.context.holder.RequestContextHolder;
import sk.spp.nzp.be.web.filter.ReCaptchaV2Filter;

@Configuration
public class ReCaptchaConfiguration {

    private ObjectMapper objectMapper;
    private RequestContextHolder requestContextHolder;
    private ReCaptchaService reCaptchaService;
    private ReCaptchaTokenConfigurationHolder reCaptchaTokenConfigurationHolder;
    private ReCaptchaTokenService reCaptchaTokenService;

    @Value("${google.recaptcha.paths:/customers/registration}")
    String[] paths;

    @Value("${google.recaptcha.ignoreEmpty}")
    private Boolean ignoreEmpty;

    public ReCaptchaConfiguration(
            ObjectMapper objectMapper,
            RequestContextHolder requestContextHolder,
            ReCaptchaService reCaptchaService,
            ReCaptchaTokenConfigurationHolder reCaptchaTokenConfigurationHolder,
            ReCaptchaTokenService reCaptchaTokenService) {

        this.objectMapper = objectMapper;
        this.requestContextHolder = requestContextHolder;
        this.reCaptchaService = reCaptchaService;
        this.reCaptchaTokenConfigurationHolder = reCaptchaTokenConfigurationHolder;
        this.reCaptchaTokenService = reCaptchaTokenService;
    }

    @Bean
    public FilterRegistrationBean<ReCaptchaV2Filter> reCaptchaFilter(){
        FilterRegistrationBean<ReCaptchaV2Filter> registrationBean = new FilterRegistrationBean<>();

        ReCaptchaV2Filter reCaptchaFilter = new ReCaptchaV2Filter(
                reCaptchaService, objectMapper, requestContextHolder, ignoreEmpty, reCaptchaTokenConfigurationHolder, reCaptchaTokenService);

        registrationBean.setFilter(reCaptchaFilter);
        registrationBean.addUrlPatterns(paths);
        registrationBean.setOrder(2);

        return registrationBean;
    }

}
