package sk.spp.nzp.be.repository.locales;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sk.spp.nzp.commons.model.locales.LocaleEntryEntity;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface LocaleEntryEntityRepository extends JpaRepository<LocaleEntryEntity, UUID> {

    @Query("SELECT l FROM LocaleEntryEntity l " +
            "WHERE l.parent IS NULL " +
            "AND l.locale = LOWER(:locale)")
    Optional<LocaleEntryEntity> getRoot(
            @Param("locale") String locale);

    @Modifying
    @Query("DELETE FROM LocaleEntryEntity l " +
            "WHERE l.locale = LOWER(:locale)")
    int delete(
            @Param("locale") String locale);

}