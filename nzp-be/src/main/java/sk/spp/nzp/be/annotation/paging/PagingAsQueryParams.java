package sk.spp.nzp.be.annotation.paging;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ ElementType.METHOD, ElementType.ANNOTATION_TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Parameter(in = ParameterIn.QUERY
        , description = "Zero-based page index. Only page or offset can be present in request."
        , name = "page"
        , schema = @Schema(type = "integer"))
@Parameter(in = ParameterIn.QUERY
        , description = "Zero-based offset from which items will be returned. Only page or offset can be present in request."
        , name = "offset"
        , schema = @Schema(type = "integer"))
@Parameter(in = ParameterIn.QUERY
        , description = "The number of elements on single page."
        , name = "size"
        , schema = @Schema(type = "integer"))
@Parameter(in = ParameterIn.QUERY
        , description = "Sorting criteria in the format: property(,ASC|DESC)."
        , name = "sort"
        , array = @ArraySchema(schema = @Schema(type = "string")))
public @interface PagingAsQueryParams {
}
