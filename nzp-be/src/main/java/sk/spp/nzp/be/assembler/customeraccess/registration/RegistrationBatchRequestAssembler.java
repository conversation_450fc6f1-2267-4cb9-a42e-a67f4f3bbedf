package sk.spp.nzp.be.assembler.customeraccess.registration;

import sk.spp.nzp.be.api.customeraccess.registration.RegistrationBatchRequest;
import sk.spp.nzp.commons.model.customeraccess.registration.RegistrationBatchRequestEntity;

import java.util.Collection;
import java.util.List;

public interface RegistrationBatchRequestAssembler {

    /**
     *
     * @param input
     * @param output
     * @return
     */
    RegistrationBatchRequest map(RegistrationBatchRequestEntity input, RegistrationBatchRequest output);

    /**
     *
     * @param inputs
     * @return
     */
    List<RegistrationBatchRequest> map(Collection<RegistrationBatchRequestEntity> inputs);
}
