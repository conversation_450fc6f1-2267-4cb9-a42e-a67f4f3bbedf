package sk.spp.nzp.be.assembler.notification.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.notification.CustomerNotificationRenderStatus;
import sk.spp.nzp.be.assembler.notification.CustomerNotificationRenderStatusAssembler;
import sk.spp.nzp.commons.model.notification.CustomerNotificationRenderStatusEntity;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class CustomerNotificationRenderStatusAssemblerImpl implements CustomerNotificationRenderStatusAssembler {

    @Override
    public CustomerNotificationRenderStatusEntity map(CustomerNotificationRenderStatus input, CustomerNotificationRenderStatusEntity output) {

        output.setLockedBy(input.getLockedBy());
        output.setPriority(input.getPriority());
        output.setRetryCount(input.getRetryCount());

        return output;
    }

    @Override
    public CustomerNotificationRenderStatus map(CustomerNotificationRenderStatusEntity input, CustomerNotificationRenderStatus output) {
        output.setId(input.getId().toString());
        output.setLockedBy(input.getLockedBy());
        output.setPriority(input.getPriority());
        output.setRetryCount(input.getRetryCount());

        return output;
    }

    @Override
    public List<CustomerNotificationRenderStatus> map(Collection<CustomerNotificationRenderStatusEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new CustomerNotificationRenderStatus())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}