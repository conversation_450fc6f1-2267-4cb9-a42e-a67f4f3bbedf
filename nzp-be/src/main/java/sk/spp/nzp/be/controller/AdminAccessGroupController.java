package sk.spp.nzp.be.controller;

import java.util.Collection;
import java.util.UUID;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.commons.api.employeeaccess.AccessGroup;
import sk.spp.nzp.be.service.employeeaccess.AccessGroupService;

@RestController
@RequestMapping("/admin/access-groups")
public class AdminAccessGroupController {

    private AccessGroupService accessGroupService;
    
    public AdminAccessGroupController(AccessGroupService accessGroupService) {
        
        this.accessGroupService = accessGroupService;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('ACCESS_GROUPS_VIEW')")
    @GetMapping(value = "/")
    public Collection<AccessGroup> getAllAccessGroups() {

        return accessGroupService.getAllAccessGroups();
    }
    
    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('ACCESS_GROUPS_VIEW')")
    @GetMapping(value = "/{uuid}")
    public AccessGroup getAccessGroupById(@LogParam("uuid") @PathVariable UUID uuid) {

        return accessGroupService.getAccessGroupById(uuid);
    }
    
    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('ACCESS_GROUPS_EDIT')")
    @PostMapping(value = "/")
    public AccessGroup createAccessGroup(@LogParam("accessGroup") @RequestBody AccessGroup accessGroup) {

        return accessGroupService.createAccessGroup(accessGroup);
    }
    
    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('ACCESS_GROUPS_EDIT')")
    @PutMapping(value = "/{uuid}")
    public AccessGroup updateAccessGroup(@LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("accessGroup") @RequestBody AccessGroup accessGroup) {

        return accessGroupService.updateAccessGroup(uuid, accessGroup);
    }
    
    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('ACCESS_GROUPS_EDIT')")
    @DeleteMapping(value = "/{uuid}")
    public void deleteAccessGroup(@LogParam("uuid") @PathVariable UUID uuid) {

        accessGroupService.deleteAccessGroup(uuid);
    }
}
