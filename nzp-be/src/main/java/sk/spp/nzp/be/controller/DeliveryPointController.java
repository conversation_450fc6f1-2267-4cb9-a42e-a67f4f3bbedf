package sk.spp.nzp.be.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.annotation.paging.PagingAsQueryParams;
import sk.spp.nzp.be.api.common.PagedResponse;
import sk.spp.nzp.be.api.common.QueryStringPaging;
import sk.spp.nzp.be.api.customerconsumption.ConsumptionInfoResponse;
import sk.spp.nzp.be.api.customerconsumption.MeterReading;
import sk.spp.nzp.be.api.customerconsumption.MeterReadingInfoRequest;
import sk.spp.nzp.be.api.customerconsumption.MeterReadingSearch;
import sk.spp.nzp.be.api.customerprofile.DeliveryPoint;
import sk.spp.nzp.be.api.customerprofile.EInvoiceRequest;
import sk.spp.nzp.be.api.customerprofile.InvoiceSearch;
import sk.spp.nzp.be.api.customerprofile.InvoiceSummary;
import sk.spp.nzp.be.api.customerprofile.invoicesummary.InvoicePaymentSummary;
import sk.spp.nzp.be.api.customerrequest.*;
import sk.spp.nzp.be.api.notification.CustomerNotification;
import sk.spp.nzp.be.service.customerconsumption.ConsumptionService;
import sk.spp.nzp.be.service.customerconsumption.MeterReadingService;
import sk.spp.nzp.be.service.customerprofile.ContractAccountService;
import sk.spp.nzp.be.service.customerprofile.DeliveryPointService;
import sk.spp.nzp.be.service.customerprofile.InvoiceService;
import sk.spp.nzp.be.service.customerrequest.CustomerRequestAvailabilityService;
import sk.spp.nzp.be.service.customerrequest.CustomerRequestService;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.api.customerconsumption.ConsumptionSearch;
import sk.spp.nzp.commons.api.customerconsumption.ConsumptionSearchResponse;
import sk.spp.nzp.commons.api.customerrequest.component.AdvancePaymentPeriod;
import sk.spp.nzp.commons.api.customerrequest.component.PaymentMethod;
import sk.spp.nzp.commons.api.customerrequest.component.combined.AdvancePaymentAndPeriod;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.api.reporting.DataReportFormat;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.validator.CustomerRequestChecks;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

@RestController
@RequestMapping("/delivery-points")
public class DeliveryPointController {

    @Value("${reporting.export.filename}")
    private String fileName;

    private final DeliveryPointService deliveryPointService;
    private final InvoiceService invoiceService;
    private final MeterReadingService meterReadingService;
    private final CustomerRequestService customerRequestService;
    private final CustomerRequestAvailabilityService customerRequestAvailabilityService;
    private final ConsumptionService consumptionService;
    private final ContractAccountService contractAccountService;

    public DeliveryPointController(
            DeliveryPointService deliveryPointService,
            InvoiceService invoiceService,
            MeterReadingService meterReadingService,
            CustomerRequestService customerRequestService,
            CustomerRequestAvailabilityService customerRequestAvailabilityService,
            ConsumptionService consumptionService,
            ContractAccountService contractAccountService
    ) {
        this.deliveryPointService = deliveryPointService;
        this.invoiceService = invoiceService;
        this.meterReadingService = meterReadingService;
        this.customerRequestService = customerRequestService;
        this.customerRequestAvailabilityService = customerRequestAvailabilityService;
        this.consumptionService = consumptionService;
        this.contractAccountService = contractAccountService;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @GetMapping(value = "/contracts/{contractId}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public DeliveryPoint getByContractUuid(
            @LogParam("contractId") @PathVariable String contractId,
            @LogParam("token") @RequestParam(required = false) String aet) {

        return deliveryPointService.getByContractId(contractId, aet);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))")
    @GetMapping(value = "/contracts/{contractId}/invoices", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    @PagingAsQueryParams
    public PagedResponse<InvoiceSummary> getInvoicesByContract(@LogParam("contractId") @PathVariable String contractId,
            @LogParam("queryStringPaging ")  QueryStringPaging  queryStringPaging) {

        return invoiceService.getInvoices(
                new InvoiceSearch()
                        .setPaging(queryStringPaging.toPaging())
                        .withContractId(contractId)
        );
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))")
    @GetMapping(value = "/contracts/{contractId}/invoices/summary", produces = {MediaType.APPLICATION_JSON_VALUE})
    public InvoicePaymentSummary getInvoicesSummary(@LogParam("contractId") @PathVariable String contractId) {

        return invoiceService.getInvoiceSummaryByContractId(contractId, false);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))")
    @PostMapping(value = "/contracts/{contractId}/invoices/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<InvoiceSummary> invoiceSearchByContract(@LogParam("contractId") @PathVariable String contractId,
                                                       @LogParam("invoiceSearch ") @RequestBody InvoiceSearch invoiceSearch) {

        return invoiceService.getInvoices(invoiceSearch.withContractId(contractId));
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @GetMapping(value = "/contracts/{contractId}/meter-readings", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<MeterReading> getMeterReadings(@LogParam("contractId") @PathVariable String contractId,
            @LogParam("queryStringPaging ") QueryStringPaging  queryStringPaging) {

        return meterReadingService.getByContractId(contractId, new MeterReadingSearch().setPaging(queryStringPaging.toPaging()));
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping(value = "/contracts/{contractId}/meter-readings/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<MeterReading> meterReadingSearch(@LogParam("contractId") @PathVariable String contractId,
            @LogParam("meterReadingSearch ") @RequestBody MeterReadingSearch meterReadingSearch) {

        return meterReadingService.getByContractId(contractId, meterReadingSearch);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping(value = "/contracts/{contractId}/meter-readings/dates/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<LocalDate> meterReadingDateSearch(@LogParam("contractId") @PathVariable(value = "contractId") String contractId,
                                                                    @LogParam("meterReadingSearch ") @RequestBody MeterReadingSearch meterReadingSearch) {

        return meterReadingService.getDatesByContractId(contractId, meterReadingSearch);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping(value = "/contracts/{contractId}/meter-readings/search/download-export", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public void meterReadingSearchExport(@LogParam("contractId") @PathVariable String contractId,
            @LogParam("meterReadingSearch ") @RequestBody MeterReadingSearch meterReadingSearch,
            @LogParam("format") @RequestParam DataReportFormat format,
            @LogParam("columns") @RequestParam(required = false) List<String> columns,
            HttpServletResponse response) throws IOException {

        meterReadingService.getByContractIdExport(contractId, meterReadingSearch, format, columns, prepareOutputStream(response, format));
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping(value = "/contracts/{contractId}/meter-readings/info", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<List<MeterReading>> createMeterReadingInfo(@LogParam("contractId") @PathVariable String contractId,
                                                               @LogParam("meterReadingInfoRequest ") @RequestBody MeterReadingInfoRequest meterReadingInfoRequest) {

        List<MeterReading> output = meterReadingService.createMeterReadingInfo(contractId, meterReadingInfoRequest);
        return ResponseEntity.created(null).body(output);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping(value = "/contracts/{contractId}/meter-readings/customer", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<CustomerRequestSummary> createMeterReadingCustomer(@LogParam("contractId") @PathVariable String contractId,
            @LogParam("meterReadingInfoRequest ") @RequestBody MeterReadingInfoRequest meterReadingInfoRequest) {

        CustomerRequestSummary output = meterReadingService.createMeterReadingCustomer(contractId, meterReadingInfoRequest);

        return ResponseEntity.created(null).body(output);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))")
    @PagingAsQueryParams
    @GetMapping("/contracts/{contractId}/customer-requests")
    public PagedResponse<CustomerRequestSummary> getCustomerRequestsByContract(
            @LogParam("contractId") @PathVariable String contractId,
            @LogParam("paging")  QueryStringPaging paging) {
        CustomerRequestSearchQuery query = new CustomerRequestSearchQuery();
        query.setPaging(paging.toPaging());
        query.setContractId(contractId);
        return customerRequestService.fetchCustomerRequests(query);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))")
    @PostMapping("/contracts/{contractId}/customer-requests/search")
    public PagedResponse<CustomerRequestSummary> deliveryPointSearchCustomerRequests(
            @LogParam("contractId") @PathVariable String contractId,
            @LogParam("query") @RequestBody CustomerRequestSearchQuery query) {
        query.setContractId(contractId);
        return customerRequestService.fetchCustomerRequests(query);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping("/contracts/{contractId}/consumptions/search")
    public ConsumptionSearchResponse searchConsumptions(
            @LogParam("contractId") @PathVariable String contractId,
            @LogParam("consumptionSearch") @RequestBody ConsumptionSearch consumptionSearch
    ) {
        return consumptionService.getByContractId(contractId, consumptionSearch);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping(value = "/contracts/{contractId}/consumptions/search/download-export", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public void searchConsumptionsExport(
            @LogParam("contractId") @PathVariable String contractId,
            @LogParam("consumptionSearch") @RequestBody ConsumptionSearch consumptionSearch,
            @LogParam("format") @RequestParam DataReportFormat format,
            @LogParam("columns") @RequestParam(required = false) List<String> columns,
            HttpServletResponse response) throws IOException {

        consumptionService.getByContractIdExport(contractId, consumptionSearch, format, columns, prepareOutputStream(response, format));
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping("/contracts/{contractId}/customer-requests/tariff-rate")
    public CustomerRequestSummary changeTariff(
            @LogParam("contractId") @PathVariable String contractId,
            @LogParam("tariffChange") @RequestBody TariffChange tariffChange
    ) {
        return deliveryPointService.changeTariff(contractId, tariffChange);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping("/contracts/{contractId}/customer-requests/advance-payment")
    public CustomerRequestSummary changeAdvancePayment(
            @LogParam("contractId") @PathVariable String contractId,
            @LogParam("tariffChange") @Validated(CustomerRequestChecks.class) @RequestBody AdvancePaymentAndPeriod advancePayment
    ) {
        return deliveryPointService.changeAdvancePayment(contractId, advancePayment);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping("/contracts/{contractId}/customer-requests/payment-method")
    public CustomerRequestSummary changePaymentMethod(
            @LogParam("contractId") @PathVariable String contractId,
            @LogParam("paymentMethod") @Validated(CustomerRequestChecks.class) @RequestBody PaymentMethod paymentMethod
    ) {
        return deliveryPointService.changePaymentMethod(contractId, paymentMethod);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping("/contracts/customer-requests/payment-method")
    public Collection<CustomerRequestSummary> changePaymentMethodForMultipleContracts(
            @LogParam("bulkRequest") @Validated(CustomerRequestChecks.class) @RequestBody BulkPaymentMethodChangeRequest bulkRequest
    ) {
        return deliveryPointService.changePaymentMethodBulk(bulkRequest.getContractIds(), bulkRequest.getPaymentMethod());
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping("/contracts/{contractId}/customer-requests/bill-cycle-payment-method")
    public CustomerRequestSummary changeBillCycleAndPaymentMethod(
            @LogParam("contractId") @PathVariable(name = "contractId") String contractId,
            @LogParam("paymentMethod") @Validated(CustomerRequestChecks.class) @RequestBody PaymentMethod paymentMethod
    ) {
        return deliveryPointService.changeBillCycleToMonthlyAndPaymentMethod(contractId, paymentMethod);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping("/contracts/{contractId}/customer-requests/bill-cycle")
    public CustomerRequestSummary changeBillCycle(
            @LogParam("contractId") @PathVariable(name = "contractId") String contractId,
            @LogParam("paymentMethod") @Validated(CustomerRequestChecks.class) @RequestBody CodeListItem billCycle
    ) {
        return deliveryPointService.changeBillCycle(contractId, billCycle);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping("/contracts/{contractId}/customer-requests/sipo-number")
    public CustomerRequestSummary changeSipoNumber(
            @LogParam("contractId") @PathVariable String contractId,
            @LogParam("sipo") @Validated(CustomerRequestChecks.class) @RequestBody SipoNumberChange sipo
    ) {
        return deliveryPointService.changeSipoNumber(contractId, sipo);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping("/contracts/{contractId}/customer-requests/reserved-values")
    public CustomerNotification changeReservedValues(
            @LogParam("contractId") @PathVariable String contractId,
            @LogParam("reservedValues") @Validated(CustomerRequestChecks.class) @RequestBody ReservedValuesChange reservedValues) {

        return deliveryPointService.changeReservedValues(contractId, reservedValues);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @GetMapping("/contracts/{contractId}/customer-requests/availability")
    public CustomerRequestAvailabilityResponse getCustomerRequestAvailability(@LogParam("contractId") @PathVariable(name = "contractId") String contractId) {

        return customerRequestAvailabilityService.evaluateCustomerRequestAvailability(contractId);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping(value = "/contracts/{contractId}/e-invoices/email")
    public List<CustomerRequestSummary> updateEInvoiceEmailByContract(@LogParam("contractId") @PathVariable String contractId,
                                                            @LogParam("emailRequest") @Valid @RequestBody EInvoiceRequest eInvoiceRequest) {

        return contractAccountService.updateEInvoiceEmailByContractId(contractId, eInvoiceRequest.getEmail());
    }


    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @GetMapping(value = "/contracts/{contractId}/consumptions/info", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ConsumptionInfoResponse consumptionInfo(
            @LogParam("contractId") @PathVariable String contractId
    ){
        return deliveryPointService.getConsumptionInfoResponseByContractId(contractId);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping("/contracts/{contractId}/customer-requests/payment-period")
    public CustomerRequestSummary changePaymentPeriod(
            @LogParam("contractId") @PathVariable String contractId,
            @LogParam("paymentPeriod") @Validated(CustomerRequestChecks.class) @RequestBody AdvancePaymentPeriod paymentPeriod

    ){
        return deliveryPointService.changePaymentPeriod(contractId,paymentPeriod);
    }

    private OutputStream prepareOutputStream(HttpServletResponse response, DataReportFormat format)  throws IOException {
        switch (format) {

            case XLSX:
                response.setContentType("application/vnd.ms-excels");
                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, ContentDisposition.builder("attachment")
                        .filename(fileName + ".xlsx")
                        .build()
                        .toString());
                break;

            case CSV:
                response.setContentType("text/csv");
                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, ContentDisposition.builder("attachment")
                        .filename(fileName + ".csv")
                        .build()
                        .toString());
                break;

            default:
                throw new ApiException(ErrorCode.GENERIC_VALIDATION_ERROR,
                        String.format("Unsupported format [format=%s]", format));
        }

        return response.getOutputStream();
    }

}
