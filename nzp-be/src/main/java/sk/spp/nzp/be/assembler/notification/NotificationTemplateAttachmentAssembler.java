package sk.spp.nzp.be.assembler.notification;

import sk.spp.nzp.be.api.notification.NotificationTemplateAttachment;
import sk.spp.nzp.commons.model.notification.NotificationTemplateAttachmentEntity;

import java.util.Collection;
import java.util.List;

public interface NotificationTemplateAttachmentAssembler {

    NotificationTemplateAttachmentEntity map(NotificationTemplateAttachment input, NotificationTemplateAttachmentEntity output);

    NotificationTemplateAttachment map(NotificationTemplateAttachmentEntity input, NotificationTemplateAttachment output);

    List<NotificationTemplateAttachment> map(Collection<NotificationTemplateAttachmentEntity> input);

    NotificationTemplateAttachment mapWithContent(NotificationTemplateAttachmentEntity attachmentEntity, NotificationTemplateAttachment attachment);

    List<NotificationTemplateAttachment> mapWithContent(Collection<NotificationTemplateAttachmentEntity> input);
}
