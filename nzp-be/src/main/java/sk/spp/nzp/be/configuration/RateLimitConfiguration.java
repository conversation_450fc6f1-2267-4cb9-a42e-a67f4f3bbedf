package sk.spp.nzp.be.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import sk.spp.nzp.be.service.common.RateLimitService;
import sk.spp.nzp.be.web.filter.RateLimitFilter;
import sk.spp.nzp.commons.context.holder.RequestContextHolder;

import java.util.List;
import java.util.stream.Collectors;

@Configuration
@ConditionalOnProperty(value="restriction.policy.enabled", havingValue = "true")
public class RateLimitConfiguration {

    private RateLimitService rateLimitService;
    private RateLimitConfigurationHolder rateLimitConfigurationHolder;
    private RequestContextHolder requestContextHolder;
    private ObjectMapper objectMapper;

    public RateLimitConfiguration(RateLimitService rateLimitService, RateLimitConfigurationHolder rateLimitConfigurationHolder, RequestContextHolder requestContextHolder, ObjectMapper objectMapper) {
        this.rateLimitService = rateLimitService;
        this.rateLimitConfigurationHolder = rateLimitConfigurationHolder;
        this.requestContextHolder = requestContextHolder;
        this.objectMapper = objectMapper;
    }

    @Bean
    @ConditionalOnProperty(value="restriction.policy.enabled", havingValue = "true")
    public FilterRegistrationBean<RateLimitFilter> rateLimitFilter(){
        FilterRegistrationBean<RateLimitFilter> registrationBean = new FilterRegistrationBean<>();

        RateLimitFilter rateLimitFilter = new RateLimitFilter(
                rateLimitService,
                requestContextHolder,
                objectMapper
        );

        final List<String> limitedUrls = rateLimitConfigurationHolder
                .getEntries()
                .stream()
                .map(x -> x.getUrl())
                .collect(Collectors.toList());

        registrationBean.setFilter(rateLimitFilter);
        registrationBean.addUrlPatterns(limitedUrls.toArray(new String[0]));
        registrationBean.setOrder(2);

        return registrationBean;
    }

}
