package sk.spp.nzp.be.reporting.impl;

import com.mysema.commons.lang.CloseableIterator;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.reporting.IteratorWrapper;
import sk.spp.nzp.be.reporting.entitymappers.AbstractMapper;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

import javax.persistence.EntityManager;

public class CloseableIteratorWrapper<T> implements IteratorWrapper {

    private CloseableIterator<Tuple> closeableIterator;
    private EntityManager em;
    private Integer batchSize;
    private AbstractMapper<T> mapper;
    private int i = 0;
    private boolean empty = false;

    public CloseableIteratorWrapper(QueryDslProvider<T> provider, EntityManager em, Integer batchSize, AbstractMapper<T> mapper) {
        this.em = em;
        this.batchSize = batchSize;
        this.mapper = mapper;

        JPAQuery query = provider.getQuery(em);
        mapper.select(query);

        this.closeableIterator = query.iterate();
        if(!hasNext()){
            this.empty = true;
        }
    }

    @Override
    public boolean hasNext() {
        return closeableIterator.hasNext();
    }

    @Override
    public Object[] next() {
        i++;
        if (i % batchSize == 0) {
            em.clear();
        }
        if(empty){
            return new Object[0];
        }
        return  mapper.map(closeableIterator.next());
    }

    @Override
    public void close() {
        closeableIterator.close();
    }
}
