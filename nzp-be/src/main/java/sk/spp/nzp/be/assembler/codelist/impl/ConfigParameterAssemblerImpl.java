package sk.spp.nzp.be.assembler.codelist.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.codelist.ConfigParameter;
import sk.spp.nzp.be.assembler.codelist.ConfigParameterAssembler;
import sk.spp.nzp.commons.model.codelist.ConfigParameterEntity;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class ConfigParameterAssemblerImpl implements ConfigParameterAssembler {
    @Override
    public ConfigParameter map(ConfigParameterEntity input, ConfigParameter output) {
        output.setId(input.getId());
        output.setValue(input.getValue());
        output.setTarget(input.getTarget());
        output.setType(input.getType());
        return output;
    }

    @Override
    public List<ConfigParameter> map(List<ConfigParameterEntity> input) {
        if (input != null) {
            return input.stream()
                    .map(configParameterEntity -> map(configParameterEntity, new ConfigParameter()))
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}
