package sk.spp.nzp.be.reporting.impl;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import sk.spp.nzp.be.reporting.DataExporter;
import sk.spp.nzp.be.reporting.IteratorWrapper;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.model.audit.enums.AuditLogCode;
import sk.spp.nzp.commons.model.codelist.GenericCodeListEntity;
import sk.spp.nzp.commons.service.codelist.GenericCodeListEntityService;
import sk.spp.nzp.commons.service.common.LocaleResolver;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class DataExporterImpl implements DataExporter {

    private static final Logger logger = LoggerFactory.getLogger(DataExporterImpl.class);
    private static final int MAX_XLSX_ROWS = 1048575;

    @Value("${reporting.format.timestamp}")
    private String timestampFormat;
    @Value("${reporting.format.number}")
    private String numberFormat;
    @Value("${reporting.format.text:@}")
    private String textFormat;
    @Value("${reporting.export.sheetname}")
    private String sheetName;
    @Value("${reporting.export.randomAccessWindowSize}")
    private Integer randomAccessWindowSize;

    private DateFormat df;
    DateTimeFormatter dtf;
    private NumberFormat nf;

    @Autowired
    private GenericCodeListEntityService genericCodeListEntityService;

    @Autowired
    private LocaleResolver localeResolver;

    private String locale = LocaleContextHolder.getLocale().toString();

    @Override
    @Transactional(propagation = Propagation.MANDATORY)
    public void exportCsv(IteratorWrapper iterator, List<String> columnHeaders, OutputStream outputStream) {
        ErrorCode err = null;

        df = new SimpleDateFormat(timestampFormat);
        dtf = DateTimeFormatter.ofPattern(timestampFormat);
        nf = new DecimalFormat(numberFormat);

        try (outputStream; iterator) {

            String header = convertLineToCSV(columnHeaders.toArray()) + "\n";
            outputStream.write(header.getBytes(StandardCharsets.UTF_8));

            do {
                String s = convertLineToCSV(iterator.next()) + "\n";
                outputStream.write(s.getBytes(StandardCharsets.UTF_8));
            } while (iterator.hasNext());
        } catch (IOException e) {
            logger.error(String.format("Reporting: IO Exception: %s", e));
            err = ErrorCode.IO_EXCEPTION;
        } catch (SQLException e) {
            logger.error(String.format("Reporting: SQL Exception: %s", e));
            err = ErrorCode.INVALID_SQL_REQUEST;
        }
        if (err != null) {
            throw new ApiException(err);
        }
    }

    @Override
    public void exportXlsx(IteratorWrapper iterator, List<String> columnHeaders, OutputStream outputStream) {
        ErrorCode err = null;

        SXSSFWorkbook workbook = new SXSSFWorkbook();
        try (outputStream; iterator) {
            Map<String, CellStyle> cellStyleMap = prepareCellStyleMap(workbook);
            SXSSFSheet sheet = workbook.createSheet(sheetName);
            sheet.setRandomAccessWindowSize(randomAccessWindowSize);

            int rowCount = 0;
            int columnCount = 0;

            Row rowHeader = sheet.createRow(rowCount++);
            for (String header : columnHeaders) {
                createXlsCell(rowHeader, columnCount++, header, cellStyleMap);
            }

            do {
                Row row = sheet.createRow(rowCount++);

                columnCount = 0;
                for (Object obj : iterator.next()) {
                    createXlsCell(row, columnCount++, obj, cellStyleMap);
                }

            } while (iterator.hasNext() && rowCount < MAX_XLSX_ROWS);
            workbook.write(outputStream);
        } catch (IOException e) {
            workbook.dispose();
            logger.error(String.format("Reporting: IO Exception: %s", e));
            err = ErrorCode.IO_EXCEPTION;
        } catch (SQLException e) {
            workbook.dispose();
            logger.error(String.format("Reporting: SQL Exception: %s", e));
            err = ErrorCode.INVALID_SQL_REQUEST;
        }
        if (err != null) {
            throw new ApiException(err);
        }
    }

    private Map<String, CellStyle> prepareCellStyleMap(SXSSFWorkbook wb) {
        Map<String, CellStyle> cellStyleMap = new HashMap<>();
        cellStyleMap.put(timestampFormat, createCellStyle(wb, timestampFormat));
        cellStyleMap.put(textFormat, createCellStyle(wb, textFormat));
        return cellStyleMap;
    }

    private void createXlsCell(Row row, int columnCount, Object value, Map<String, CellStyle> cellStyleMap) {
        Cell cell = row.createCell(columnCount);
        if (value == null) {
            cell.setCellValue("");
        }else if (value instanceof AuditLogCode){
            Optional<GenericCodeListEntity> genericCodeListEntityOptional =
                    genericCodeListEntityService.getCurrentCodeListItem("AUDIT_LOG_CODE", ((AuditLogCode) value).name());
            if (genericCodeListEntityOptional.isPresent()){
                localeResolver.resolve(
                                Optional.ofNullable(genericCodeListEntityOptional.get().getTranslations()).orElse(Collections.emptyList()), locale)
                        .ifPresent(v -> {
                            cell.setCellValue(v.getName());
                        });
            }else {
                cell.setCellValue(((AuditLogCode) value).name());
            }
        } else if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof BigDecimal) {
            cell.setCellValue(((BigDecimal) value).doubleValue());
        } else if (value instanceof LocalDate) {
            cell.setCellValue((LocalDate) value);
            cell.setCellStyle(cellStyleMap.get(timestampFormat));
        } else if (value instanceof LocalDateTime) {
            cell.setCellValue((LocalDateTime) value);
            cell.setCellStyle(cellStyleMap.get(timestampFormat));
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else {
            cell.setCellValue(value.toString());
            cell.setCellStyle(cellStyleMap.get(textFormat));
        }
    }
    
    private CellStyle createCellStyle(SXSSFWorkbook wb, String format) {
        CellStyle cellStyle = wb.createCellStyle();
        
        cellStyle.setDataFormat(wb.getCreationHelper().createDataFormat().getFormat(format));
        
        return cellStyle;
    }

    private String convertLineToCSV(Object[] data) {
        return Stream.of(data)
                .map(this::mapToString)
                .map(this::escapeSpecialCharacters)
                .collect(Collectors.joining(","));
    }

    private String mapToString(Object value) {
        if (value == null) {
            return "";
        } else if (value instanceof Integer) {
            return value.toString();
        } else if (value instanceof Double) {
            return nf.format(value);
        } else if (value instanceof BigDecimal) {
            return nf.format(value);
        } else if (value instanceof LocalDate) {
            return ((LocalDate) value).format(dtf);
        } else if (value instanceof LocalDateTime) {
            return ((LocalDateTime) value).format(dtf);
        } else if (value instanceof Boolean) {
            return value.toString();
        } else {
            return value.toString();
        }
    }

    private String escapeSpecialCharacters(final String data) {
        String escapedData = data.replaceAll("\\R", " ");
        if (escapedData.matches("^[=+\\-@].*")) {    // values which starts with any of characters "+-=@"
            escapedData = "'" + escapedData;
        }
        if (escapedData.contains(",") || escapedData.contains("\"") || escapedData.contains("'")) {
            escapedData = escapedData.replace("\"", "\"\"");
            escapedData = "\"" + escapedData + "\"";
        }
        return escapedData;
    }

}
