package sk.spp.nzp.be.assembler.customerrequest.support;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import sk.spp.nzp.commons.model.customerprofile.enums.CompletionView;
import sk.spp.nzp.commons.api.customerrequest.request.base.CustomerRequestContent;

@Component
@Order // Lowest precedence
public class DefaultRequestContentVisibilityProcessor implements RequestContentVisibilityProcessor {

    @Override
    public boolean supports(CustomerRequestContent content) {
        return true;
    }

    @Override
    public CustomerRequestContent process(CustomerRequestContent dtoContent, CustomerRequestContent entityContent, CompletionView completionView) {
        return dtoContent;
    }

}