package sk.spp.nzp.be.controller;

import org.springframework.http.MediaType;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.identitymanagement.AccountInfo;
import sk.spp.nzp.be.service.identitymanagement.AccountInfoService;

@RestController
@RequestMapping("/me")
public class MeController {

    private AccountInfoService accountInfoService;

    public MeController(AccountInfoService accountInfoService) {
        this.accountInfoService = accountInfoService;
    }

    @Log
    @LogParam
    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
    @GetMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
    public AccountInfo getMe() {
        return accountInfoService.getAccountInfo();
    }
}
