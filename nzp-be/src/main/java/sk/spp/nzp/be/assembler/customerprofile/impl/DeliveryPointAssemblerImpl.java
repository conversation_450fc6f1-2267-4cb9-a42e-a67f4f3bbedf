package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import sk.spp.nzp.be.api.customerprofile.ReadingCycleInfo;
import sk.spp.nzp.be.api.customerprofile.*;
import sk.spp.nzp.be.assembler.customerprofile.AddressEmbedableAssembler;
import sk.spp.nzp.be.assembler.customerprofile.ContractAssembler;
import sk.spp.nzp.be.assembler.customerprofile.DeliveryPointAssembler;
import sk.spp.nzp.be.assembler.customerprofile.ReadingCycleInfoAssembler;
import sk.spp.nzp.be.service.codelist.CodeListProvider;
import sk.spp.nzp.be.service.codelist.model.CodeListQuery;
import sk.spp.nzp.be.service.codelist.provider.TariffCodeListProvider;
import sk.spp.nzp.be.service.customerrequest.FieldGroupUpdateService;
import sk.spp.nzp.commons.api.TimePeriod;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.api.customerconsumption.ConsumptionReservedValue;
import sk.spp.nzp.commons.api.customerconsumption.ConsumptionReservedValues;
import sk.spp.nzp.commons.api.customerconsumption.EeTariffValue;
import sk.spp.nzp.commons.api.customerprofile.enums.*;
import sk.spp.nzp.commons.assembler.codelist.CodeListItemAssembler;
import sk.spp.nzp.commons.context.DeliveryPointHistoryContext;
import sk.spp.nzp.commons.enums.CodeListType;
import sk.spp.nzp.commons.model.codelist.GenericCodeListEntity;
import sk.spp.nzp.commons.model.customerprofile.ContractEntity;
import sk.spp.nzp.commons.model.customerprofile.DeliveryPointEntity;
import sk.spp.nzp.commons.model.customerprofile.DeliveryPointFactEntity;
import sk.spp.nzp.commons.model.customerprofile.DeliveryPointVersionEntity;
import sk.spp.nzp.commons.repository.customerprofile.DeliveryPointVersionEntityRepository;
import sk.spp.nzp.commons.service.codelist.GenericCodeListEntityService;
import sk.spp.nzp.commons.service.customerconsumption.*;
import sk.spp.nzp.commons.utils.Expressions;

import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class DeliveryPointAssemblerImpl implements DeliveryPointAssembler {

    private final AddressEmbedableAssembler addressEmbedableAssembler;
    private final ContractAssembler contractAssembler;
    private final DeliveryPointVersionEntityRepository dpVersionRepository;
    private final CodeListProvider tariffCodeListProvider;
    private final CodeListItemAssembler codeListItemAssembler;
    private final FieldGroupUpdateService fieldGroupUpdateService;
    private final GenericCodeListEntityService genericCodeListEntityService;
    private final DeliveryPointInfoService deliveryPointInfoService;
    private final ReadingCycleInfoAssembler readingCycleInfoAssembler;

    public DeliveryPointAssemblerImpl(
            AddressEmbedableAssembler addressEmbedableAssembler,
            ContractAssembler contractAssembler,
            DeliveryPointVersionEntityRepository dpVersionRepository,
            TariffCodeListProvider tariffCodeListProvider,
            CodeListItemAssembler codeListItemAssembler,
            FieldGroupUpdateService fieldGroupUpdateService,
            GenericCodeListEntityService genericCodeListEntityService,
            DeliveryPointInfoService deliveryPointInfoService,
            ReadingCycleInfoAssembler readingCycleInfoAssembler) {

        this.addressEmbedableAssembler = addressEmbedableAssembler;
        this.contractAssembler = contractAssembler;
        this.dpVersionRepository = dpVersionRepository;
        this.tariffCodeListProvider = tariffCodeListProvider;
        this.fieldGroupUpdateService = fieldGroupUpdateService;
        this.codeListItemAssembler = codeListItemAssembler;
        this.genericCodeListEntityService = genericCodeListEntityService;
        this.deliveryPointInfoService = deliveryPointInfoService;
        this.readingCycleInfoAssembler = readingCycleInfoAssembler;
    }

    @Override
    public DeliveryPoint map(DeliveryPointEntity input, DeliveryPoint output, List<DeliveryPoint.Fetch> fetches, DeliveryPointHistoryContext historyContext) {
        return map(input, output, historyContext, fetches);
    }

    @Override
    public DeliveryPoint map(DeliveryPointEntity input, DeliveryPoint output, DeliveryPointHistoryContext historyContext) {
        return map(input, output, historyContext, null);
    }

    @Override
    public DeliveryPoint map(DeliveryPointEntity input, DeliveryPoint output, DeliveryPointHistoryContext historyContext, List<DeliveryPoint.Fetch> fetches) {
        if (input == null) // external entities may not be imported during assembling
            return null;
        Map<DeliveryPointFactOperand, DeliveryPointFactEntity> factsMap = deliveryPointInfoService.mapFacts(input, historyContext);
        Optional<MeteringIntervalType> meteringIntervalType = deliveryPointInfoService.getMeterReadingType(factsMap);
        output.setId(input.getId());
        output.setExternalId(input.getExternalId());
        output.setStatus(historyContext.hasActualValid() ? DeliveryPointStatus.ACTIVE : DeliveryPointStatus.INACTIVE);
        output.setType(input.getType());
        output.setEic(input.getEic());
        output.setPod(input.getPod());
        output.setDeviceNumber(input.getDeviceNumber());
        output.setDistributionArea(getDistributionArea(input));
        output.setMeteringInterval(deliveryPointInfoService.isIntervalReading(input, historyContext) || (meteringIntervalType.isPresent() && meteringIntervalType.get().isInterval()));
        DeliveryPointInfo dpInfo = deliveryPointInfoService.getDeliveryPointInfo(input, historyContext);
        output.setHasLimit(dpInfo.isNotificationLimit());
        if (input.getAddress() != null) {
            output.setAddress(addressEmbedableAssembler.map(input.getAddress(), new Address()));
        }

        ContractEntity contract = historyContext.getContract();
        Optional.ofNullable(contract.getContractAccount())
                .ifPresent(contractAccountEntity ->
                        output.setEInvoice(new EInvoice(contractAccountEntity.getEmail(), Optional.ofNullable(contractAccountEntity.geteInvoice()).orElse(Boolean.FALSE), contractAccountEntity.getId()))
                );

        if (fetches != null && !fetches.isEmpty()) {
            if (fetches.contains(DeliveryPoint.Fetch.CONTRACT)) {
                output.setContract(contractAssembler.map(contract, new Contract()));
            }
            if (fetches.contains(DeliveryPoint.Fetch.READING_CYCLE_INFO)) {
                MeterReadingInfo meterReadingInfo = deliveryPointInfoService.getMeterReadingInfo(input, historyContext);
                output.setReadingCycleInfo(readingCycleInfoAssembler.map(meterReadingInfo, new ReadingCycleInfo()));
            }
            if (fetches.contains(DeliveryPoint.Fetch.TARIFF_RATE)) {
                Optional.ofNullable(dpInfo.getTariffRate())
                        .map(this::toCodeListItem)
                        .ifPresent(output::setTariffRate);
            }
        }

        List<DeliveryPointVersionEntity> dpVersionList = dpVersionRepository.findByDeliveryPointId(input.getId());
        if (Expressions.notEmpty(dpVersionList)) {
            // validityService.getValid(dpVersionList, historyContext.getLastContract().getValidTo()).ifPresent(t -> versionMapper(output, t));
            historyContext.getFirstByValidDate(dpVersionList).ifPresent(t -> versionMapper(output, t));
        }

        if (historyContext.hasActualValid()) { //because for inactive UDP are this fields undesirable
            output.setFieldUpdates(fieldGroupUpdateService.getFieldUpdates(input));
        }

        return output;
    }

    @Override
    public DeliveryPoint mapFull(DeliveryPointEntity input, DeliveryPoint output, DeliveryPointHistoryContext historyContext) {
        if (input == null) // external entities may not be imported during assembling
            return null;

        map(input, output, historyContext);
        if (historyContext.getContract() != null) {
            output.setContract(contractAssembler.map(historyContext.getContract(), new Contract()));
        }

        DeliveryPointInfo operandInfo = deliveryPointInfoService.getDeliveryPointInfo(input, historyContext);
        return map(operandInfo, output);
    }

    @Override
    public DeliveryPointSummary map(DeliveryPointEntity input, DeliveryPointSummary output, DeliveryPointHistoryContext historyContext) {
        return map(input, output, null, historyContext);
    }

    @Override
    public DeliveryPointSummary map(DeliveryPointEntity input, DeliveryPointSummary output, List<DeliveryPointSummary.Fetch> dpsFetches, DeliveryPointHistoryContext historyContext) {
        return map(input, output, dpsFetches, null, historyContext);
    }

    @Override
    public DeliveryPointSummary map(DeliveryPointEntity input, DeliveryPointSummary output, List<DeliveryPointSummary.Fetch> dpsFetches,
                                    List<DeliveryPoint.Fetch> udpFetches, DeliveryPointHistoryContext historyContext) {
        if (input == null) // external entities may not be imported during assembling
            return null;

        output.setId(input.getId());
        output.setExternalId(input.getExternalId());
        if (dpsFetches != null && CollectionUtils.containsAny(dpsFetches, List.of(DeliveryPointSummary.Fetch.WITH_CONTRACT_ACCOUNT, DeliveryPointSummary.Fetch.WITH_CONTRACT_ACCOUNT_FIELD_UPDATES))) {
            output.setContract(contractAssembler.mapWithContractAccount(historyContext.getContract(), new ContractSummary(), dpsFetches.contains(DeliveryPointSummary.Fetch.WITH_CONTRACT_ACCOUNT_FIELD_UPDATES)));
            output.setUnitedDeliveryPointId(Expressions.tryGet(() -> input.getUnitedDeliveryPoint().getStringId()));
        }else {
            output.setContract(contractAssembler.map(historyContext.getContract(), new ContractSummary()));
        }
        output.setStatus(historyContext.hasActualValid() ? DeliveryPointStatus.ACTIVE : DeliveryPointStatus.INACTIVE);
        output.setType(input.getType());

        output.setEIC(input.getEic());
        output.setPOD(input.getPod());
        output.setDeviceNumber(input.getDeviceNumber());
        output.setDistributionArea(getDistributionArea(input));

        if (dpsFetches != null && dpsFetches.contains(DeliveryPointSummary.Fetch.E_INVOICE)) {
            Optional.ofNullable(historyContext.getContract().getContractAccount())
                    .ifPresent(contractAccountEntity ->
                            output.setEInvoice(new EInvoice(contractAccountEntity.getEmail(), Optional.ofNullable(contractAccountEntity.geteInvoice()).orElse(Boolean.FALSE), contractAccountEntity.getId()))
                    );
        }

        if (input.getAddress() != null) {
            output.setAddress(addressEmbedableAssembler.map(input.getAddress(), new Address()));
        }

        output.setHasLimit(deliveryPointInfoService.getDeliveryPointInfo(input, historyContext).isNotificationLimit());

        return output;
    }

    @Override
    public ConsumptionReservedValues map(DeliveryPointEntity input, ConsumptionReservedValues output, DeliveryPointHistoryContext historyContext) {

        String bpeId = historyContext.getContract().getBusinessPartnerId();

        List<ContractEntity> contracts = input.getContracts().stream()
                .filter(c -> bpeId.equals(c.getBusinessPartnerId()) && c.getValidTo() != null && c.getValidFrom() != null)
                .collect(Collectors.toList());

        DeliveryPointReservedValueIntervalsInfo intervalReservedValue = deliveryPointInfoService.getIntervalReservedValue(input, historyContext);
        map(intervalReservedValue, output);

        contracts.forEach(c -> {
            c.getVersions().stream().filter(cv -> cv.getValidFrom() != null && cv.getValidTo() != null).forEach(cv -> {

                EeTariffValue eeTariffValue = new EeTariffValue();
                eeTariffValue.setValid(new TimePeriod());
                eeTariffValue.getValid().setTo(cv.getValidTo().atTime(LocalTime.MAX));
                eeTariffValue.getValid().setFrom(cv.getValidFrom().atTime(LocalTime.MIN));
                eeTariffValue.setEeTariff(cv.getEeTariffCount());

                output.getTariffCount().add(eeTariffValue);
            });
        });

        return output;
    }

    @Override
    public DeliveryPoint map(DeliveryPointInfo input, DeliveryPoint output) {
        if (input == null) {
            return output;
        }

        Optional.ofNullable(input.getReserveAmount())
                .map(this::toReservedValue)
                .ifPresent(output::setReserveAmount);

        Optional.ofNullable(input.getMaximumReserveAmount())
                .map(this::toReservedValue)
                .ifPresent(output::setMaximumReserveAmount);

        Optional.ofNullable(input.getMaximumDailyAmount())
                .map(this::toReservedValue)
                .ifPresent(output::setMaximumDailyAmount);


        Optional.ofNullable(input.getTariffRate())
                .map(this::toCodeListItem)
                .ifPresent(output::setTariffRate);


        output.setHasLimit(input.isNotificationLimit());
        output.setMeteringIntervalType(input.getMeteringIntervalType());
        output.setDistributionTariff(input.getDistributionTariff());
        output.setCircuitBreaker(input.getCircuitBreaker());
        output.setCircuitPhaseCount(input.getCircuitPhaseCount());

        return output;
    }

    @Override
    public ConsumptionReservedValues map(DeliveryPointReservedValueIntervalsInfo input, ConsumptionReservedValues output) {
        if (input == null) {
            return output;
        }

        Optional.ofNullable(input.getReserveAmounts())
                .map(this::convertReservedValues)
                .ifPresent(output::setReserveAmount);

        Optional.ofNullable(input.getMaximumReserveAmounts())
                .map(this::convertReservedValues)
                .ifPresent(output::setMaximumReserveAmount);

        Optional.ofNullable(input.getMaximumDailyAmounts())
                .map(this::convertReservedValues)
                .ifPresent(output::setMaximumDailyAmount);

        return output;
    }

    private List<ConsumptionReservedValue> convertReservedValues(List<IntervalReservedValue> input) {
        return Optional.ofNullable(input).orElse(List.of()).stream()
                .map(amount -> toConsumptionReservedValue(amount, new ConsumptionReservedValue()))
                .collect(Collectors.toList());
    }

    private ConsumptionReservedValue toConsumptionReservedValue(IntervalReservedValue input, ConsumptionReservedValue output) {
        if (input == null) {
            return output;
        }

        Optional.ofNullable(input.getPeriod())
                .ifPresent(output::setValid);

        output.setValue(Optional.ofNullable(input.getConvertedAmount())
                .orElse(input.getAmount()));

        Optional.ofNullable(input.getType())
                .ifPresent(output::setType);

        return output;
    }

    private ReservedValueResponse toReservedValue(ConvertedReservedValue input) {
        return Optional.ofNullable(input)
                .map(val -> new ReservedValueResponse(val.getAmount(), val.getUnits(), val.getType()))
                .orElse(null);

    }

    private CodeListItem toCodeListItem(String tariffRate) {
        return Optional.ofNullable(tariffRate)
                .map(CodeListQuery::new)
                .map(query -> tariffCodeListProvider.getOrCode(CodeListType.TARIFF_RATE.name(), query, true))
                .orElse(null);
    }

    private void versionMapper(DeliveryPoint deliveryPoint, DeliveryPointVersionEntity version) {
        if (!Expressions.empty(version.getCategory())) {
            deliveryPoint.setCategory(DeliveryPointCategory.fromValue(version.getCategory()));
            deliveryPoint.setDeliveryCategory(deliveryPoint.getCategory().getDeliveryCategory());
        }

        deliveryPoint.setBillCycleLastUpdatedAt(version.getBillCycleLastUpdatedAt());

        Optional.ofNullable(version.getReadingCycleType())
                .map(type -> codeListItemAssembler.mapSummary(CodeListType.DELIVERY_POINT_READING_CYCLE_TYPE, version.getReadingCycleType(), new CodeListItem()))
                .ifPresent(deliveryPoint::setReadingCycleType);
    }

    private CodeListItem getDistributionArea(DeliveryPointEntity deliveryPoint) {
        if (deliveryPoint.getType() == DeliveryPointType.EE) {
            String eic = deliveryPoint.getEic();
            if (StringUtils.isBlank(eic)) {
                return null;
            }

            if (eic.startsWith("24")) {
                String distributionAreaCode = eic.substring(2, 5);
                Optional<GenericCodeListEntity> distributionArea = genericCodeListEntityService.getCurrentCodeListItem(CodeListType.DISTRIBUTION_AREA.name(), distributionAreaCode);

                if (distributionArea.isPresent()) {
                    return codeListItemAssembler.mapSummary(distributionArea.get(), new CodeListItem());
                } else {
                    CodeListItem codeListItem = new CodeListItem();
                    codeListItem.setCode(distributionAreaCode);
                    return codeListItem;
                }
            }
        } else {
            String pod = deliveryPoint.getPod();
            if (StringUtils.isBlank(pod)) {
                return null;
            }

            if (pod.startsWith("SK")) {
                String distributionAreaCode = pod.substring(2, 8);
                CodeListItem codeListItem = new CodeListItem();
                codeListItem.setCode(distributionAreaCode);
                return codeListItem;
            }
        }

        return null;
    }
}
