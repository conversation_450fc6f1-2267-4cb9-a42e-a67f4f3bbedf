package sk.spp.nzp.be.repository.customerconsumption.query;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

import javax.persistence.EntityManager;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.DateTimeExpression;
import com.querydsl.jpa.impl.JPAQuery;

import sk.spp.nzp.be.api.common.Paging;
import sk.spp.nzp.be.api.common.Sorting;
import sk.spp.nzp.be.api.common.SortingDirection;
import sk.spp.nzp.be.api.customerconsumption.MeterReadingSearch;
import sk.spp.nzp.be.api.customerconsumption.MeterReadingSearchSort;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.api.customerprofile.enums.RegisterKind;
import sk.spp.nzp.commons.context.DeliveryPointHistoryContext;
import sk.spp.nzp.commons.model.customerconsumption.MeterReadingEntity;
import sk.spp.nzp.commons.model.customerconsumption.QMeterReadingEntity;
import sk.spp.nzp.commons.model.customerprofile.QBusinessPartnerEntity;
import sk.spp.nzp.commons.model.customerprofile.QContractEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;
import sk.spp.nzp.commons.utils.DateTimeUtils;

public class MeterReadingFindByDeliveryPointUuidQueryProvider implements QueryDslProvider<MeterReadingEntity> {

    private String deliveryPointId;
    private MeterReadingSearch meterReadingSearch;
    private UUID customerUuid;
    private BusinessPartnerQueue businessPartnerQueue;
    private Boolean homeCategory;
    private DeliveryPointHistoryContext historyContext;

    public MeterReadingFindByDeliveryPointUuidQueryProvider(String deliveryPointId,
                                                            MeterReadingSearch meterReadingSearch,
                                                            UUID customerUuid, BusinessPartnerQueue businessPartnerQueue, Boolean homeCategory,
                                                            DeliveryPointHistoryContext historyContext) {
        
        this.deliveryPointId = deliveryPointId;
        this.meterReadingSearch = meterReadingSearch;
        this.customerUuid = customerUuid;
        this.businessPartnerQueue = businessPartnerQueue;
        this.homeCategory = homeCategory;
        this.historyContext = historyContext;
    }

    @Override
    public JPAQuery<MeterReadingEntity> getQuery(EntityManager em) {
        
        QMeterReadingEntity meterReadingEntity = QMeterReadingEntity.meterReadingEntity;
        QBusinessPartnerEntity bpEntity = QBusinessPartnerEntity.businessPartnerEntity;

        BooleanExpression deliveryPointExp = meterReadingEntity.deliveryPoint().id.eq(deliveryPointId);
        BooleanExpression homeCategoryExp = null;
        BooleanExpression typesExp = null;

        if(Boolean.TRUE.equals(homeCategory)){
            homeCategoryExp = meterReadingEntity.registerKind.isNull().or(meterReadingEntity.registerKind.ne(RegisterKind.ST.getExternalName()));
        }
        if(meterReadingSearch.getCategories() != null && !meterReadingSearch.getCategories().isEmpty()) {
            typesExp = meterReadingEntity.category.in(meterReadingSearch.getCategories());
        }
        
        BooleanExpression readingAtExp = null ;
        BooleanExpression reasonExp = null ;
        BooleanExpression historyContextReadAt = null ;

        // history
        if ( historyContext != null ) {
            historyContextReadAt = meterReadingEntity.readAt.between(
                    DateTimeUtils.startOfDay(historyContext.getValidPeriod().getValidFrom()),
                    DateTimeUtils.endOfDay(historyContext.getValidDate()));
        }

        if(meterReadingSearch.getReadingAt() != null && (meterReadingSearch.getReadingAt().getFrom() != null || meterReadingSearch.getReadingAt().getTo() != null)) {
            
            readingAtExp = meterReadingEntity.readAt.between(meterReadingSearch.getReadingAt().getFrom(), meterReadingSearch.getReadingAt().getTo());
        }

        if (meterReadingSearch.getReason() != null) {
            reasonExp = meterReadingEntity.reason.eq(meterReadingSearch.getReason().getCode());
        }

        BooleanExpression bpQueueTypeExp = null;
        if(businessPartnerQueue != null) {
            bpQueueTypeExp = bpEntity.queue.eq(businessPartnerQueue);
        }


        JPAQuery<MeterReadingEntity> query =  new JPAQuery<MeterReadingEntity>(em).from(meterReadingEntity)
                .where(deliveryPointExp, typesExp, readingAtExp, reasonExp, historyContextReadAt, homeCategoryExp, bpQueueTypeExp);

        if (businessPartnerQueue != null) {
            QContractEntity contractEntity = QContractEntity.contractEntity;
            query.join(contractEntity)
                 .on(
                        contractEntity.deliveryPointId.eq(meterReadingEntity.deliveryPointId),
                        DateTimeExpression.currentDate(LocalDate.class).between(contractEntity.effectiveFrom, contractEntity.effectiveTo)
                 );
            query.join(bpEntity).on(bpEntity.id.eq(contractEntity.businessPartnerId));
        }

        if (meterReadingSearch.getPaging() != null) {
            orderBy(query, meterReadingSearch.getPaging(), meterReadingEntity);
        }

        return query;
    }

    protected void orderBy(JPAQuery<MeterReadingEntity> query, Paging paging, QMeterReadingEntity meterReadingEntity) {
        if (!paging.hasSorting()) {
            //default sort
            query.orderBy(meterReadingEntity.readAt.desc());

        } else {
            for (Sorting sorting : paging.getSort()) {
                Optional<MeterReadingSearchSort> sortTypeOptional = MeterReadingSearchSort.fromString(sorting.getAttribute());

                if (sortTypeOptional.isPresent()) {
                    switch (sortTypeOptional.get()) {
                        case READ_AT:
                            if (SortingDirection.ASC.equals(sorting.getDirection())) {
                                query.orderBy(meterReadingEntity.readAt.asc());
                            } else {
                                query.orderBy(meterReadingEntity.readAt.desc());
                            }
                            break;
                    }
                }
            }
        }
    }
}
