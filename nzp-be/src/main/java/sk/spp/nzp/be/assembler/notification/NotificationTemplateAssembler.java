package sk.spp.nzp.be.assembler.notification;

import sk.spp.nzp.be.api.notification.*;
import sk.spp.nzp.commons.model.notification.NotificationTemplateEntity;
import sk.spp.nzp.commons.model.notification.NotificationTemplateVariableEntity;
import sk.spp.nzp.commons.service.notification.NotificationRenderJobData;

import java.util.Collection;
import java.util.List;

public interface NotificationTemplateAssembler {

    NotificationTemplateEntity map(NotificationTemplate input, NotificationTemplateEntity output);

    NotificationTemplate map(NotificationTemplateEntity input, NotificationTemplate output);

    NotificationTemplateSummary map(NotificationTemplateEntity input, NotificationTemplateSummary output);

    List<NotificationTemplateSummary> mapSummaries(Collection<NotificationTemplateEntity> input);

    NotificationTemplate mapRef(NotificationTemplateEntity input, NotificationTemplate output);

    NotificationTemplate mapFull(NotificationTemplateEntity input, NotificationTemplate output);

    List<NotificationTemplate> map(Collection<NotificationTemplateEntity> input);

    List<NotificationTemplate> mapFull(Collection<NotificationTemplateEntity> input);

    TemplateVariable map(NotificationTemplateVariableEntity input, TemplateVariable output);

    TemplateVariables mapTemplateVariables(Collection<NotificationTemplateVariableEntity> input, TemplateVariables output);

    NotificationTemplateTestResponse map(NotificationRenderJobData input, NotificationTemplateTestResponse output);

    NotificationTemplateTest map(NotificationTemplateI18N templateI18N, NotificationTemplate reportId);
}
