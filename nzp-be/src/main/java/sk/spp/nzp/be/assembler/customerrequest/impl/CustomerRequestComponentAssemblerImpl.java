package sk.spp.nzp.be.assembler.customerrequest.impl;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerconsumption.MeterReadingInfoRequest;
import sk.spp.nzp.be.assembler.customerrequest.CustomerRequestComponentAssembler;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.api.customerrequest.component.*;
import sk.spp.nzp.commons.api.customerrequest.enums.InvoiceDeliveryType;
import sk.spp.nzp.commons.api.customerrequest.enums.MeterReadingType;
import sk.spp.nzp.commons.api.customerrequest.enums.SelfReadReason;
import sk.spp.nzp.commons.assembler.codelist.CodeListItemAssembler;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;
import sk.spp.nzp.commons.model.customerprofile.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Component
public class CustomerRequestComponentAssemblerImpl implements CustomerRequestComponentAssembler {

    private final CodeListItemAssembler codeListItemAssembler;

    public CustomerRequestComponentAssemblerImpl(CodeListItemAssembler codeListItemAssembler) {
        this.codeListItemAssembler = codeListItemAssembler;
    }

    @Override
    public Metadata mapMetadata(CustomerAccountEntity input, Metadata output) {
        output.setCustomerAccountId(input.getId().toString());
        return output;
    }

    @Override
    public Metadata mapMetadata(BusinessPartnerEntity input, Metadata output) {
        output.setBusinessPartnerId(input.getId());
        return output;
    }

    @Override
    public Metadata mapMetadata(ContractAccountEntity input, Metadata output) {
        return input.getBusinessPartner() != null ? mapMetadata(input.getBusinessPartner(), output) : output;
    }

    @Override
    public Metadata mapMetadata(ContractEntity input, Metadata output) {
        return input.getContractAccount() != null ? mapMetadata(input.getContractAccount(), output) : output;
    }

    @Override
    public Metadata mapMetadata(DeliveryPointEntity input, Metadata output) {
        return input.getActualContract() != null ? mapMetadata(input.getActualContract(), output) : output;
    }

    @Override
    public EntityReference mapEntityReference(ContractAccountEntity input, EntityReference output) {
        output.setContractAccountId(input.getId());
        return output;
    }

    @Override
    public EntityReference mapEntityReference(ContractEntity input, EntityReference output) {
        output.setContractId(input.getId());
        return input.getContractAccount() != null ? mapEntityReference(input.getContractAccount(), output) : output;
    }

    @Override
    public EntityReference mapEntityReference(DeliveryPointEntity input, EntityReference output) {
        output.setDeliveryPointId(input.getId());
        return input.getActualContract() != null ? mapEntityReference(input.getActualContract(), output) : output;
    }

    @Override
    public MeterReading mapReading(MeterReadingInfoRequest meterReading, DeliveryPointEntity deliveryPoint, MeterReading output, MeterReadingType type) {
        output.setDate(meterReading.getReadAt().toLocalDate());
        output.setType(type);
        output.setDeviceNumber(deliveryPoint.getDeviceNumber());

        DeliveryPointVersionEntity deliveryPointVersionEntity = deliveryPoint.getDeliveryPointVersions().stream().filter(e -> e.getReadingCycleValue()!=0).findFirst().orElse(null);

        LocalDateTime from = deliveryPoint.getRequestMeterReadingFrom();
        LocalDateTime to = deliveryPoint.getRequestMeterReadingTo();
        int readingCycleMonth = deliveryPointVersionEntity.getReadingCycleValue();

        output.setSelfReadReason(SelfReadReason.READING_CYCLE);
        if (meterReading.getReadAt().getMonthValue() != readingCycleMonth && from != null && to != null) output.setSelfReadReason(SelfReadReason.CUSTOM);

        if (MeterReadingType.EE_2T.equals(type)) {
            output.setValueLow(meterReading.getValueLow());
            output.setValueHigh(meterReading.getValueHigh());
        }
        if (MeterReadingType.EE_1T.equals(type)) {
            output.setValue(meterReading.getValueHigh());
        }

        if (MeterReadingType.GAS.equals(type)){
            if (meterReading.getValueHigh() != null){
                output.setValue(meterReading.getValueHigh());
            }else{
                output.setValue(meterReading.getValueLow());
            }
        }

        return output;
    }

    @Override
    public MeterReading mapGasReading(BigDecimal value, LocalDate date, MeterReading output) {
        output.setType(MeterReadingType.GAS);
        output.setValue(value);
        output.setDate(date);

        return output;
    }

    @Override
    public InvoiceDelivery mapEInvoiceDelivery(String email, InvoiceDelivery output) {
        output.setType(InvoiceDeliveryType.EMAIL);
        output.setEmail(email);

        return output;
    }

    @Override
    public TariffEe mapTariff(TariffEntity tariff, LocalDate date, TariffEe output) {
        output.setTariff(codeListItemAssembler.mapSummary(tariff, new CodeListItem()));
        output.setChangeDate(date);

        return output;
    }

    @Override
    public TariffZp mapTariff(TariffEntity tariff, LocalDate date, TariffZp output) {
        output.setTariff(codeListItemAssembler.mapSummary(tariff, new CodeListItem()));
        output.setChangeDate(date);

        return output;
    }

}
