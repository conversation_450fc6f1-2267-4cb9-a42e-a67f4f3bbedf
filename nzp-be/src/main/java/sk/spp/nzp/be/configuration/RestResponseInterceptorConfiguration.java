package sk.spp.nzp.be.configuration;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import sk.spp.nzp.commons.context.holder.RequestContextHolder;
import sk.spp.nzp.commons.enums.HttpContextHeader;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Configuration
public class RestResponseInterceptorConfiguration implements WebMvcConfigurer {

    private class RestResponseInterceptor implements HandlerInterceptor {

        private final RequestContextHolder requestContextHolder;

        private RestResponseInterceptor(RequestContextHolder requestContextHolder) {
            this.requestContextHolder = requestContextHolder;
        }

        @Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
            addNotBlank(response, HttpContextHeader.REQUEST_ID, requestContextHolder.getRequestId());
            addNotBlank(response, HttpContextHeader.SESSION_ID, requestContextHolder.getSessionId());

            return true;
        }

        private void addNotBlank(HttpServletResponse response, HttpContextHeader key, String value) {
            if (StringUtils.isNotBlank(value)) {
                response.addHeader(key.getName(), value);
            }
        }
    }

    @Autowired
    private RequestContextHolder requestContextHolder;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new RestResponseInterceptor(requestContextHolder));
    }
}