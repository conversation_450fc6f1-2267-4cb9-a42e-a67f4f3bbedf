package sk.spp.nzp.be.controller;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.service.customerconsumption.MeterReadingService;

@RestController
@RequestMapping("/meter-readings")
public class MeterReadingController {

    private MeterReadingService meterReadingService;
    
    public MeterReadingController(MeterReadingService meterReadingService) {
        
        this.meterReadingService = meterReadingService;
    }

    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @DeleteMapping(value = "/info/{meterReadingId}")
    public void deleteMeterReadingInfo(@LogParam("meterReadingId") @PathVariable String meterReadingId) {

        meterReadingService.deleteMeterReadingInfo(meterReadingId);
    }
}
