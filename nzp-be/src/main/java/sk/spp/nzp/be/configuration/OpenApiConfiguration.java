package sk.spp.nzp.be.configuration;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfiguration {

    @Value("${openapi.title}")
    private String title;
    @Value("${openapi.description}")
    private String description;
    @Value("${openapi.version}")
    private String version;
    @Value("${openapi.tos_url}")
    private String tosUrl;
    @Value("${openapi.contact.name}")
    private String contactName;
    @Value("${openapi.contact.url}")
    private String contactUrl;
    @Value("${openapi.contact.email}")
    private String contactEmail;
    @Value("${openapi.license.name}")
    private String licenseName;
    @Value("${openapi.license.url}")
    private String licenseUrl;


    @Bean
    public OpenAPI customOpenAPI() {
        Contact contact = new Contact();
        contact.setName(contactName);
        contact.setEmail(contactEmail);
        contact.setUrl(contactUrl);

        License license = new License();
        license.setName(licenseName);
        license.setUrl(licenseUrl);

        return new OpenAPI()
                .components(new Components())
                .info(new Info()
                        .title(title)
                        .description(description)
                        .version(version)
                        .termsOfService(tosUrl)
                        .contact(contact)
                        .license(license));
    }
}
