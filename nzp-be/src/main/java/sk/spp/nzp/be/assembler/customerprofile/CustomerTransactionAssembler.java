package sk.spp.nzp.be.assembler.customerprofile;

import sk.spp.nzp.be.api.customerprofile.invoicesummary.CustomerTransactionSummary;
import sk.spp.nzp.commons.model.customerprofile.CustomerTransactionEntity;

import java.util.Collection;
import java.util.List;

public interface CustomerTransactionAssembler {

    CustomerTransactionSummary map(CustomerTransactionEntity input, CustomerTransactionSummary output);

    List<CustomerTransactionSummary> map(Collection<CustomerTransactionEntity> input);
}
