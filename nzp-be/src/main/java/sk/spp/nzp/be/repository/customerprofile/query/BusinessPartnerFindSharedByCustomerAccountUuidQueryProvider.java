package sk.spp.nzp.be.repository.customerprofile.query;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerSearch;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.api.customersharing.enums.OwnershipType;
import sk.spp.nzp.commons.model.customerprofile.BusinessPartnerEntity;
import sk.spp.nzp.commons.model.customerprofile.QBusinessPartnerEntity;
import sk.spp.nzp.commons.model.customerprofile.QUnitedDeliveryPointEntity;
import sk.spp.nzp.commons.model.customersharing.QBusinessPartnerOwnershipEntity;
import sk.spp.nzp.commons.model.customersharing.QUnitedDeliveryPointOwnershipEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

import javax.persistence.EntityManager;
import java.util.UUID;

public class BusinessPartnerFindSharedByCustomerAccountUuidQueryProvider implements QueryDslProvider<BusinessPartnerEntity> {

    private UUID customerAccountUuid;
    private BusinessPartnerQueue businessPartnerQueue;

    public BusinessPartnerFindSharedByCustomerAccountUuidQueryProvider(UUID customerAccountUuid, BusinessPartnerQueue businessPartnerQueue) {

        this.customerAccountUuid = customerAccountUuid;
        this.businessPartnerQueue = businessPartnerQueue;
    }

    public JPAQuery<BusinessPartnerEntity> getQuery(EntityManager em) {

        QBusinessPartnerEntity businessPartnerEntity = QBusinessPartnerEntity.businessPartnerEntity;
        QUnitedDeliveryPointOwnershipEntity unitedDeliveryPointOwnershipEntity = QUnitedDeliveryPointOwnershipEntity.unitedDeliveryPointOwnershipEntity;
        QUnitedDeliveryPointEntity unitedDeliveryPointEntity = QUnitedDeliveryPointEntity.unitedDeliveryPointEntity;
        QBusinessPartnerOwnershipEntity businessPartnerOwnershipEntity = QBusinessPartnerOwnershipEntity.businessPartnerOwnershipEntity;


        BooleanExpression ownerExp = null;

        BooleanExpression queueExp = null;
        if(businessPartnerQueue != null) {
            queueExp = businessPartnerEntity.queue.eq(businessPartnerQueue);
        }

        return new JPAQuery<BusinessPartnerEntity>(em).from(businessPartnerEntity)
                .join(unitedDeliveryPointEntity).on(unitedDeliveryPointEntity.businessPartner().eq(businessPartnerEntity))
                .join(unitedDeliveryPointOwnershipEntity).on(unitedDeliveryPointOwnershipEntity.customerAccount().id.eq(customerAccountUuid), unitedDeliveryPointOwnershipEntity.unitedDeliveryPoint().eq(unitedDeliveryPointEntity), unitedDeliveryPointOwnershipEntity.type.eq(OwnershipType.SHARING))
                .where(queueExp);
    }
}
