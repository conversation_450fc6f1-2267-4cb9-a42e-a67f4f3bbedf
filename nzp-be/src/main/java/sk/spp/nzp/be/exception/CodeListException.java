package sk.spp.nzp.be.exception;

import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.exception.ApiException;

public class CodeListException extends ApiException {

    protected CodeListException(ErrorCode code, String message) {
        super(code, message);
    }

    public static CodeListException ambigiousValue(String message) {
        return new CodeListException(ErrorCode.CODE_LIST_AMBIGIOUS_DATA, message);
    }

    public static CodeListException undefinedCodeListIdentifier() {
        return new CodeListException(
                ErrorCode.CODE_LIST_UNDEFINED_IDENTIFIER,
                "Undefined code list identifier. ID or CODE must be specified."
        );
    }

    public static CodeListException undefinedEnumCode() {
        return new CodeListException(
                ErrorCode.CODE_LIST_UNDEFINED_IDENTIFIER,
                "Undefined code list identifier, CODE must be specified."
        );
    }

    public static CodeListException valueForCodeNotFound(String type, String code) {
        return new CodeListException(
                ErrorCode.CODE_LIST_NOT_FOUND,
                String.format("Value for %s.%s was not found.", type, code)
        );
    }

    public static CodeListException valueForUuidNotFound(String type, String uuid) {
        return new CodeListException(
                ErrorCode.CODE_LIST_NOT_FOUND,
                String.format("Value for %s(%s) was not found.", type, uuid)
        );
    }

    public static CodeListException unsupportedLocale(String type, String locale) {
        return new CodeListException(
                ErrorCode.CODE_LIST_UNSUPPORTED_LOCALE,
                String.format("Locale '%s' is not supported for code list '%s'.", locale, type)
        );
    }

    public static CodeListException codeListNotFound(String type) {
        return new CodeListException(
                ErrorCode.CODE_LIST_NOT_FOUND,
                String.format("Code list with name '%s' was not found.", type)
        );
    }

    public static CodeListException wrongStatus(String status) {
        return new CodeListException(
                ErrorCode.CODE_LIST_WRONG_STATUS,
                String.format("Illegal code list status '%s'.", status)
        );
    }

    public static CodeListException unsupportedType(String type, String provider) {
        return new CodeListException(
                ErrorCode.CODE_LIST_UNSUPPORTED_TYPE,
                String.format("Provider '%s' does not support type '%s'", provider, type)
        );
    }

}
