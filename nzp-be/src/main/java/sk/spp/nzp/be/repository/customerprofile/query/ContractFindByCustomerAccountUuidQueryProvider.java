package sk.spp.nzp.be.repository.customerprofile.query;

import java.util.UUID;

import javax.persistence.EntityManager;

import com.querydsl.jpa.impl.JPAQuery;

import sk.spp.nzp.be.models.customerprofile.ContractSearch;
import sk.spp.nzp.commons.model.customerprofile.ContractEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

public class ContractFindByCustomerAccountUuidQueryProvider extends ContractBaseFindByQueryProvider implements QueryDslProvider<ContractEntity> {

    private UUID customerAccountUuid;
    private ContractSearch contractSearch;

    // TODO: delete unnecessary code
    public ContractFindByCustomerAccountUuidQueryProvider(UUID customerAccountUuid, ContractSearch contractSearch) {
        
        this.customerAccountUuid = customerAccountUuid;
        this.contractSearch = contractSearch;
    }

    @Override
    public JPAQuery<ContractEntity> getQuery(EntityManager em) {
        
        return findByBaseQuery(em, null, contractSearch, customerAccountUuid);
    }
}
