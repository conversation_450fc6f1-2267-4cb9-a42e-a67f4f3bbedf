package sk.spp.nzp.be.repository.customerprofile.query;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.DateExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.model.customerprofile.BusinessPartnerEntity;
import sk.spp.nzp.commons.model.customerprofile.QBusinessPartnerEntity;
import sk.spp.nzp.commons.model.customerprofile.QContractEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

import javax.persistence.EntityManager;
import java.time.LocalDate;

public class BusinessPartnerQueryProvider implements QueryDslProvider<BusinessPartnerEntity> {
    private final BusinessPartnerQueue businessPartnerQueue;
    private final String email;
    private final String phone;

    public BusinessPartnerQueryProvider(BusinessPartnerQueue businessPartnerQueue, String email, String phone) {
        this.businessPartnerQueue = businessPartnerQueue;
        this.email = email;
        this.phone = phone;
    }

    @Override
    public JPAQuery<BusinessPartnerEntity> getQuery(EntityManager em) {

        QBusinessPartnerEntity businessPartnerEntity = QBusinessPartnerEntity.businessPartnerEntity;
        QContractEntity contractEntity = QContractEntity.contractEntity;


        BooleanExpression queueExp = null;
        if (businessPartnerQueue != null) {
            queueExp = businessPartnerEntity.queue.eq(businessPartnerQueue);
        }

        BooleanExpression emailExp = null;
        if (email != null) {
            emailExp = businessPartnerEntity.email.eq(email);
        }

        BooleanExpression phoneExp = null;
        if (phone != null) {
            phoneExp = businessPartnerEntity.phone.eq(phone);
        }

        BooleanExpression activeContractExp = JPAExpressions.select().from(contractEntity).where(
                contractEntity.businessPartner().eq(businessPartnerEntity),
                DateExpression.currentDate(LocalDate.class).loe(QContractEntity.contractEntity.effectiveTo)
        ).exists();

        return new JPAQuery<BusinessPartnerEntity>(em).from(businessPartnerEntity)
                .where(queueExp, emailExp, phoneExp, activeContractExp);
    }
}
