package sk.spp.nzp.be.assembler.customerrequest.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestAttachment;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestAttachmentWithContent;
import sk.spp.nzp.be.assembler.customerrequest.CustomerRequestAttachmentAssembler;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestAttachmentEntity;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class CustomerRequestAttachmentAssemblerImpl implements CustomerRequestAttachmentAssembler {

    @Override
    public CustomerRequestAttachment map(CustomerRequestAttachmentEntity input, CustomerRequestAttachment output) {
        output.setUuid(input.getId().toString());
        output.setName(input.getName());
        output.setMimeType(input.getMimeType());
        output.setType(input.getType());
        output.setDraftMetadata(input.getDraftMetadata());

        return output;
    }

    @Override
    public List<CustomerRequestAttachment> map(List<CustomerRequestAttachmentEntity> input, List<CustomerRequestAttachment> output) {
        List<CustomerRequestAttachment> attachments = input.stream().map(entity -> map(entity, new CustomerRequestAttachment())).collect(Collectors.toList());
        output.addAll(attachments);

        return output;
    }

    @Override
    public CustomerRequestAttachmentWithContent map(CustomerRequestAttachmentEntity input, CustomerRequestAttachmentWithContent output) {
        output.setUuid(input.getId().toString());
        output.setName(input.getName());
        output.setMimeType(input.getMimeType());
        output.setContent(input.getContent());
        output.setType(input.getType());
        output.setDraftMetadata(input.getDraftMetadata());

        return output;
    }

}
