package sk.spp.nzp.be.assembler.customerrequest.support;

import sk.spp.nzp.commons.model.customerprofile.enums.CompletionView;
import sk.spp.nzp.commons.api.customerrequest.request.base.CustomerRequestContent;

public interface RequestContentVisibilityProcessor {

    boolean supports(CustomerRequestContent content);

    CustomerRequestContent process(CustomerRequestContent dtoContent, CustomerRequestContent entityContent, CompletionView completionView);

}