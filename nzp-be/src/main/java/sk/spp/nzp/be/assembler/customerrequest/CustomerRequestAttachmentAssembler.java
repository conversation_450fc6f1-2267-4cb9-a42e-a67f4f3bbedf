package sk.spp.nzp.be.assembler.customerrequest;

import sk.spp.nzp.be.api.customerrequest.CustomerRequestAttachment;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestAttachmentWithContent;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestAttachmentEntity;

import java.util.List;

public interface CustomerRequestAttachmentAssembler {

    CustomerRequestAttachment map(CustomerRequestAttachmentEntity input, CustomerRequestAttachment output);

    List<CustomerRequestAttachment> map(List<CustomerRequestAttachmentEntity> input, List<CustomerRequestAttachment> output);

    CustomerRequestAttachmentWithContent map(CustomerRequestAttachmentEntity input, CustomerRequestAttachmentWithContent output);

}
