package sk.spp.nzp.be.assembler.notification.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customeraccess.CustomerAccount;
import sk.spp.nzp.be.api.notification.CustomerNotification;
import sk.spp.nzp.be.api.notification.NotificationTemplate;
import sk.spp.nzp.be.assembler.customeraccess.CustomerAccountAssembler;
import sk.spp.nzp.be.assembler.notification.CustomerNotificationAssembler;
import sk.spp.nzp.be.assembler.notification.CustomerNotificationRenderStatusAssembler;
import sk.spp.nzp.be.assembler.notification.CustomerNotificationSendStatusAssembler;
import sk.spp.nzp.be.assembler.notification.NotificationTemplateAssembler;
import sk.spp.nzp.commons.model.notification.CustomerNotificationEntity;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class CustomerNotificationAssemblerImpl implements CustomerNotificationAssembler {

    private CustomerAccountAssembler customerAccountAssembler;
    private NotificationTemplateAssembler notificationTemplateAssembler;
    private CustomerNotificationRenderStatusAssembler customerNotificationRenderStatusAssembler;
    private CustomerNotificationSendStatusAssembler customerNotificationSendStatusAssembler;

    public CustomerNotificationAssemblerImpl(CustomerAccountAssembler customerAccountAssembler,
                                             NotificationTemplateAssembler notificationTemplateAssembler,
                                             CustomerNotificationRenderStatusAssembler customerNotificationRenderStatusAssembler,
                                             CustomerNotificationSendStatusAssembler customerNotificationSendStatusAssembler) {

        this.customerAccountAssembler = customerAccountAssembler;
        this.notificationTemplateAssembler = notificationTemplateAssembler;
        this.customerNotificationRenderStatusAssembler = customerNotificationRenderStatusAssembler;
        this.customerNotificationSendStatusAssembler = customerNotificationSendStatusAssembler;
    }

    @Override
    public CustomerNotificationEntity map(CustomerNotification input, CustomerNotificationEntity output) {

        output.setSource(input.getSource());
        output.setExternalId(input.getExternalId());
        output.setStatus(input.getStatus());
        output.setHeader(input.getHeader());
        output.setHeaderUrl(input.getHeaderUrl());
        output.setEmailBody(input.getEmailBody());
        output.setEmailSubject(input.getEmailSubject());
        output.setSmsBody(input.getSmsBody());
        output.setEntityType(input.getEntityType());
        output.setEntityId(input.getEntityId());
        output.setPhone(input.getPhone());
        output.setEmail(input.getEmail());
        output.setAttributes(input.getAttributes());
        output.setAttachments(input.getAttachments());
        output.setLocale(input.getLocale());
        output.setReadAt(input.getReadAt());

        return output;
    }

    @Override
    public CustomerNotification map(CustomerNotificationEntity input, CustomerNotification output) {

        output.setId(input.getId().toString());
        output.setSource(input.getSource());
        output.setExternalId(input.getExternalId());
        output.setStatus(input.getStatus());
        output.setHeader(input.getHeader());
        output.setHeaderUrl(input.getHeaderUrl());
        output.setEmailBody(input.getEmailBody());
        output.setEmailSubject(input.getEmailSubject());
        output.setSmsBody(input.getSmsBody());
        output.setEntityType(input.getEntityType());
        output.setEntityId(input.getEntityId());
        output.setPhone(input.getPhone());
        output.setEmail(input.getEmail());
        output.setAttributes(input.getAttributes());
        output.setAttachments(input.getAttachments());
        output.setLocale(input.getLocale());
        output.setReadAt(input.getReadAt());
        output.setEmailSentAt(input.getEmailSentAt());
        output.setSmsSentAt(input.getSmsSentAt());

        return output;
    }

    @Override
    public CustomerNotification mapRef(CustomerNotificationEntity input, CustomerNotification output) {

        output.setId(input.getId().toString());

        return output;
    }

    @Override
    public CustomerNotification mapFull(CustomerNotificationEntity input, CustomerNotification output) {

        map(input, output);

        output.setNotificationTemplate(notificationTemplateAssembler.map(input.getNotificationTemplate(), new NotificationTemplate()));
        output.setCustomerAccount(customerAccountAssembler.mapFull(input.getCustomerAccount(), new CustomerAccount()));
        if(input.getShareFrom()!=null)
            output.setShareFrom(customerAccountAssembler.mapFull(input.getShareFrom(), new CustomerAccount()));
        output.setRenderStatuses(new ArrayList<>(customerNotificationRenderStatusAssembler.map(input.getRenderStatuses())));
        output.setSendStatuses(new ArrayList<>(customerNotificationSendStatusAssembler.map(input.getSendStatuses())));

        return output;
    }

    @Override
    public List<CustomerNotification> map(Collection<CustomerNotificationEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new CustomerNotification())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public List<CustomerNotification> mapFull(Collection<CustomerNotificationEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> mapFull(i, new CustomerNotification())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

}
