package sk.spp.nzp.be.assembler.customeraccess.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customeraccess.CustomerApproval;
import sk.spp.nzp.be.assembler.customeraccess.CustomerApprovalAssembler;
import sk.spp.nzp.commons.model.customeraccess.CustomerApprovalEntity;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class CustomerApprovalAssemblerImpl implements CustomerApprovalAssembler {

    @Override
    public CustomerApprovalEntity map(CustomerApproval input, CustomerApprovalEntity output) {
        output.setType(input.getType());
        output.setApproval(input.getApproval());
        return output;
    }

    @Override
    public CustomerApproval map(CustomerApprovalEntity input, CustomerApproval output) {
        output.setId(input.getId());
        output.setType(input.getType());
        output.setApproval(input.getApproval());
        return output;
    }

    @Override
    public List<CustomerApproval> map(Collection<CustomerApprovalEntity> input) {

        if(input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new CustomerApproval())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}
