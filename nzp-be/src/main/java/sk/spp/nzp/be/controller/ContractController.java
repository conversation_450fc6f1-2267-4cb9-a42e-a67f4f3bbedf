package sk.spp.nzp.be.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.spp.nzp.be.service.customerprofile.ContractService;
import sk.spp.nzp.be.service.customerprofile.DeliveryPointService;
import sk.spp.nzp.be.service.customerprofile.InvoiceService;

@RestController
@RequestMapping("/contracts")
public class ContractController {

    private ContractService contractService;
    private DeliveryPointService deliveryPointService;
    private InvoiceService invoiceService;

    public ContractController(ContractService contractService,
                              DeliveryPointService deliveryPointService,
                              InvoiceService invoiceService) {
        
        this.contractService = contractService;
        this.deliveryPointService = deliveryPointService;
        this.invoiceService = invoiceService;
    }
}
