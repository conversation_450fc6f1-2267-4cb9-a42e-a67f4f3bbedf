package sk.spp.nzp.be.controller.sap;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.sap.SAPCustomerRegistrationResponse;
import sk.spp.nzp.be.service.sap.SAPCustomerRegistrationService;

@RestController
@RequestMapping("/sap")
public class SAPController {

    private SAPCustomerRegistrationService sapCustomerRegistrationService;

    public SAPController(SAPCustomerRegistrationService sapCustomerRegistrationService) {
        this.sapCustomerRegistrationService = sapCustomerRegistrationService;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_SAP')")
    @PostMapping(value = "/customers/registration", produces = {"application/json"})
    public ResponseEntity<SAPCustomerRegistrationResponse> customerRegistration (
            @LogParam("email") @RequestParam(value = "customerAccountEmail", required = true) String email,
            @LogParam("phone") @RequestParam(value = "customerAccountPhone", required = false) String phone,
            @LogParam("firstName") @RequestParam(value = "customerAccountFirstName", required = true) String firstName,
            @LogParam("lastName") @RequestParam(value = "customerAccountLastName", required = true) String lastName,
            @LogParam("businessPartnersExternalId") @RequestParam(required = true) String businessPartnersExternalId,
            @LogParam("SAPLogin") @RequestParam(value = "SAPLogin", required = true) String sapLogin) {
        return new ResponseEntity<>(sapCustomerRegistrationService.registerCustomerAccount(
                email,
                StringUtils.hasLength(phone) ? phone : null,
                firstName,
                lastName,
                businessPartnersExternalId,
                sapLogin), HttpStatus.OK);
    }
}
