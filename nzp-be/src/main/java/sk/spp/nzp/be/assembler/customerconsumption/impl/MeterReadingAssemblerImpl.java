package sk.spp.nzp.be.assembler.customerconsumption.impl;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import sk.spp.nzp.be.converters.MeterReadingUnitsConverter;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.be.api.customerconsumption.MeterReading;
import sk.spp.nzp.commons.api.customerprofile.enums.Units;
import sk.spp.nzp.commons.assembler.codelist.CodeListItemAssembler;
import sk.spp.nzp.be.assembler.customerconsumption.MeterReadingAssembler;
import sk.spp.nzp.commons.enums.CodeListType;
import sk.spp.nzp.commons.model.customerconsumption.MeterReadingEntity;

@Component
public class MeterReadingAssemblerImpl implements MeterReadingAssembler {

    private CodeListItemAssembler codeListItemAssembler;
    private MeterReadingUnitsConverter meterReadingUnitsConverter;

    public MeterReadingAssemblerImpl(
            CodeListItemAssembler codeListItemAssembler,
            MeterReadingUnitsConverter meterReadingUnitsConverter) {

        this.codeListItemAssembler = codeListItemAssembler;
        this.meterReadingUnitsConverter = meterReadingUnitsConverter;
    }

    @Override
    public MeterReading map(MeterReadingEntity input, MeterReading output) {
        return map(input, output, null);
    }

    @Override
    public MeterReading map(MeterReadingEntity input, MeterReading output, Units units) {

        if (input == null) // external entities may not be imported during assembling
            return null;

        output.setId(input.getId());
        output.setExternalId(input.getExternalId());
        output.setCategory(input.getCategory());
        output.setValue(input.getValue());
        output.setRegister(input.getRegister());
        output.setReadAt(input.getReadAt());
        output.setDescription(input.getDescription());
        output.setDeviceNumber(input.getDeviceNumber());
        output.setKindVariant(input.getKindVariant());

        if (input.getReason() != null) {
            output.setReason(codeListItemAssembler.mapSummary(CodeListType.METER_READING_REASON, input.getReason(), new CodeListItem()));
        }

        if (input.getKind() != null) {
            output.setKind(codeListItemAssembler.mapSummary(CodeListType.METER_READING_KIND, input.getKind(), new CodeListItem()));
        }


        if (input.getRegisterKind() != null) {
            output.setRegisterKind(codeListItemAssembler.mapSummary(CodeListType.METER_READING_REGISTER_KIND, input.getRegisterKind(), new CodeListItem()));
        }

        meterReadingUnitsConverter.convert(output, input.getDeliveryPoint(), units);

        return output;
    }
    
    @Override
    public List<MeterReading> map(Collection<MeterReadingEntity> input) {
        return map(input, null);
    }

    @Override
    public List<MeterReading> map(Collection<MeterReadingEntity> input, Units units) {

        if(input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new MeterReading(), units)).collect(Collectors.toList());
        }
        
        return Collections.emptyList();
    }
}
