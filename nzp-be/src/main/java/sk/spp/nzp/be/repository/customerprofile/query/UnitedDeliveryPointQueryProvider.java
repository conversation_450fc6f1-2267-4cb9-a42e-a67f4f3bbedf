package sk.spp.nzp.be.repository.customerprofile.query;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.DateExpression;
import com.querydsl.core.types.dsl.DateTimeExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import io.jsonwebtoken.lang.Collections;
import org.springframework.lang.NonNull;
import org.springframework.util.ObjectUtils;
import sk.spp.nzp.be.api.common.Sorting;
import sk.spp.nzp.be.api.common.SortingDirection;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerFilter;
import sk.spp.nzp.be.api.customerprofile.DeliveryPointFilter;
import sk.spp.nzp.be.api.customerprofile.UnitedDeliveryPointSearch;
import sk.spp.nzp.commons.api.customerprofile.enums.DeliveryPointType;
import sk.spp.nzp.commons.api.customerprofile.enums.PairingStatus;
import sk.spp.nzp.commons.model.customerprofile.*;
import sk.spp.nzp.commons.utils.FTSearchUtils;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.api.customerprofile.enums.UnitedDeliveryPointSearchSort;
import sk.spp.nzp.commons.api.customersharing.enums.OwnershipType;
import sk.spp.nzp.commons.exception.ForbiddenException;
import sk.spp.nzp.commons.model.customeraccess.QCustomerAccountEntity;
import sk.spp.nzp.commons.model.customersharing.QUnitedDeliveryPointOwnershipEntity;
import sk.spp.nzp.commons.repository.common.QueryDslCacheable;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

import javax.persistence.EntityManager;

import java.time.DateTimeException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public class UnitedDeliveryPointQueryProvider implements QueryDslProvider<UnitedDeliveryPointCommoditiesView>, QueryDslCacheable {
    private final UUID customerUuid;
    private final String businessPartnerId;
    private final UnitedDeliveryPointSearch unitedDeliveryPointSearch;
    private final BusinessPartnerQueue businessPartnerQueue;

    private static final String PERCENTAGE_CHAR = "%";

    public UnitedDeliveryPointQueryProvider(
            @NonNull UUID customerUuid,
            String businessPartnerId,
            UnitedDeliveryPointSearch unitedDeliveryPointSearch,
            BusinessPartnerQueue businessPartnerQueue) {

        this.customerUuid = customerUuid;
        this.businessPartnerId = businessPartnerId;
        this.unitedDeliveryPointSearch = unitedDeliveryPointSearch;
        this.businessPartnerQueue = businessPartnerQueue;
    }

    @Override
    public JPAQuery<UnitedDeliveryPointCommoditiesView> getQuery(EntityManager em) {
        return findByCustomerUuidAndBusinessPartnerUuidAndUnitedDeliveryPointSearch(em);
    }

    private JPAQuery<UnitedDeliveryPointCommoditiesView> findByCustomerUuidAndBusinessPartnerUuidAndUnitedDeliveryPointSearch(EntityManager em) {
        QBusinessPartnerEntity businessPartnerEntity = QBusinessPartnerEntity.businessPartnerEntity;
        QCustomerAccountEntity customerAccountEntity = QCustomerAccountEntity.customerAccountEntity;
        QUnitedDeliveryPointOwnershipEntity unitedDeliveryPointOwnershipEntity = QUnitedDeliveryPointOwnershipEntity.unitedDeliveryPointOwnershipEntity;
        QContractEntity contractEntity = QContractEntity.contractEntity;
        QDeliveryPointEntity deliveryPointEntity = QDeliveryPointEntity.deliveryPointEntity;
        QUnitedDeliveryPointCommoditiesView unitedDeliveryPointCommoditiesView = QUnitedDeliveryPointCommoditiesView.unitedDeliveryPointCommoditiesView;
        QUnitedDeliveryPointEntity unitedDeliveryPointEntity = unitedDeliveryPointCommoditiesView.unitedDeliveryPoint();


        // Business Partner UUID
        BooleanExpression businessPartnerUuidExp = null;
        if (!ObjectUtils.isEmpty(businessPartnerId)) {
            businessPartnerUuidExp = businessPartnerEntity.id.eq(businessPartnerId);
        }

        // shared
        BooleanExpression sharedExp = null;
        if (unitedDeliveryPointSearch.getShared() != null) {
            if (unitedDeliveryPointSearch.getShared()) {
                // if shared attribute was set and is true, only united delivery points that have sharing to customer account specified by UUID are returned
                sharedExp = unitedDeliveryPointOwnershipEntity.type.eq(OwnershipType.SHARING);
            } else { // if shared attribute was not set, or was set to false, only delivery points that are owned by customer account specified by UUID are returned
                sharedExp = unitedDeliveryPointOwnershipEntity.type.eq(OwnershipType.OWNER);
            }
        }

        // DeliveryPoint filter - ftAddress
        String ftAddress = Optional.ofNullable(unitedDeliveryPointSearch.getDeliveryPoint()).map(DeliveryPointFilter::getFtAddress).orElse(null);
        BooleanExpression deliveryPointFtAddressExp = FTSearchUtils.fulltextSearch(ftAddress, unitedDeliveryPointEntity.addressSearchable);

        // DeliveryPoint filter - hidden
        BooleanExpression hiddenExp = null;
        if (unitedDeliveryPointSearch.getDeliveryPoint() != null && unitedDeliveryPointSearch.getDeliveryPoint().getHidden() != null) {

            if(unitedDeliveryPointSearch.getDeliveryPoint().getHidden().booleanValue()) {

//                hiddenExp = unitedDeliveryPointEntity.hidden.eq(unitedDeliveryPointSearch.getDeliveryPoint().getHidden());
                hiddenExp = unitedDeliveryPointOwnershipEntity.hidden.eq(unitedDeliveryPointSearch.getDeliveryPoint().getHidden());

            } else {

//                hiddenExp = unitedDeliveryPointEntity.hidden.eq(unitedDeliveryPointSearch.getDeliveryPoint().getHidden()).or(unitedDeliveryPointEntity.hidden.isNull());
                hiddenExp = unitedDeliveryPointOwnershipEntity.hidden.eq(unitedDeliveryPointSearch.getDeliveryPoint().getHidden()).or(unitedDeliveryPointOwnershipEntity.hidden.isNull());
            }
        }

        // BusinessPartner filter - ft
        String ftNameSearchable = Optional.ofNullable(unitedDeliveryPointSearch.getBusinessPartner()).map(BusinessPartnerFilter::getFt).orElse(null);
        BooleanExpression businessPartnerFtExp = FTSearchUtils.fulltextSearch(ftNameSearchable, businessPartnerEntity.nameSearchable);

        // BusinessPartner filter - queue
        BooleanExpression queueExp = null;
        // if queue is defined by both security context and search request, it must be the same
        if (businessPartnerQueue != null
                && unitedDeliveryPointSearch.getBusinessPartner() != null
                && unitedDeliveryPointSearch.getBusinessPartner().getQueue() != null
                && businessPartnerQueue != unitedDeliveryPointSearch.getBusinessPartner().getQueue()
        ) {
            throw new ForbiddenException();
        }
        if (businessPartnerQueue != null) {
            queueExp = businessPartnerEntity.queue.eq(businessPartnerQueue);
        }
        if (queueExp == null && unitedDeliveryPointSearch.getBusinessPartner() != null && unitedDeliveryPointSearch.getBusinessPartner().getQueue() != null) {
            queueExp = businessPartnerEntity.queue.eq(unitedDeliveryPointSearch.getBusinessPartner().getQueue());
        }


        // includeInactive
        BooleanExpression validExp = null;
        if (!unitedDeliveryPointSearch.getIncludeInactive()) {
            
            validExp = unitedDeliveryPointCommoditiesView.activeCommodities.isNotNull();
        }

        // types
        BooleanExpression typesExp = null;
        if (unitedDeliveryPointSearch.getType() != null && unitedDeliveryPointSearch.getType().size() > 0) {

            String both = DeliveryPointType.EE.name() + DeliveryPointType.ZP.name();

            if(unitedDeliveryPointSearch.getType().size() == 1) {
                typesExp = unitedDeliveryPointSearch.getIncludeInactive() ?
                        unitedDeliveryPointCommoditiesView.allCommodities.in(unitedDeliveryPointSearch.getType().iterator().next().name(), both) :
                        unitedDeliveryPointCommoditiesView.activeCommodities.in(unitedDeliveryPointSearch.getType().iterator().next().name(), both);
            } else {
                typesExp = unitedDeliveryPointSearch.getIncludeInactive() ?
                        unitedDeliveryPointCommoditiesView.allCommodities.eq(both) :
                        unitedDeliveryPointCommoditiesView.activeCommodities.eq(both);
            }
        }

        // ft
        BooleanExpression ftExp = null;
        if (!ObjectUtils.isEmpty(unitedDeliveryPointSearch.getFt())) {
            BooleanExpression ftForBpExp = FTSearchUtils.fulltextSearch(unitedDeliveryPointSearch.getFt(), businessPartnerEntity.nameSearchable);

            String fullTextSearch = PERCENTAGE_CHAR + unitedDeliveryPointSearch.getFt().toLowerCase().trim() + PERCENTAGE_CHAR;

            BooleanExpression dpTypeExp = null;
            if(unitedDeliveryPointSearch.getType() != null && unitedDeliveryPointSearch.getType().size() == 1) {
                dpTypeExp = deliveryPointEntity.type.eq(unitedDeliveryPointSearch.getType().iterator().next());
            }

            BooleanExpression deliveryPointFtExp = JPAExpressions.select().from(contractEntity)
                    .join(deliveryPointEntity).on(contractEntity.deliveryPoint().eq(deliveryPointEntity), deliveryPointEntity.externalId.toLowerCase().trim().like(fullTextSearch))
                    .where(contractEntity.unitedDeliveryPoint().eq(unitedDeliveryPointEntity), getContractValidityExpression(contractEntity), dpTypeExp).exists();

            ftExp = unitedDeliveryPointEntity.street.toLowerCase().trim().like(fullTextSearch)
                    .or(unitedDeliveryPointEntity.streetNumber.toLowerCase().trim().like(fullTextSearch))
                    .or(unitedDeliveryPointEntity.city.toLowerCase().trim().like(fullTextSearch))
                    .or(unitedDeliveryPointEntity.country.toLowerCase().trim().like(fullTextSearch))
                    .or(unitedDeliveryPointEntity.zipCode.toLowerCase().trim().like(fullTextSearch))
                    .or(unitedDeliveryPointEntity.addressSearchable.like(fullTextSearch))
                    .or(deliveryPointFtExp)
                    .or(ftForBpExp);
        }

        BooleanBuilder deliveryPointFtExp = null;
        if (unitedDeliveryPointSearch.getDeliveryPoint() != null && !ObjectUtils.isEmpty(unitedDeliveryPointSearch.getDeliveryPoint().getFtId())) {

            String fullTextSearch = PERCENTAGE_CHAR + unitedDeliveryPointSearch.getDeliveryPoint().getFtId().toLowerCase().trim() + PERCENTAGE_CHAR;
            BooleanExpression deliveryPointFtIdExp = deliveryPointEntity.externalId.toLowerCase().trim().like(fullTextSearch)
                    .or(deliveryPointEntity.eic.toLowerCase().trim().like(fullTextSearch))
                    .or(deliveryPointEntity.pod.toLowerCase().trim().like(fullTextSearch));

            deliveryPointFtExp = new BooleanBuilder();

            if(unitedDeliveryPointSearch.getType() != null && unitedDeliveryPointSearch.getType().size() > 0) {

                for(DeliveryPointType dpt: unitedDeliveryPointSearch.getType()) {

                    BooleanExpression dpTypeExp = deliveryPointEntity.type.eq(dpt);

                    deliveryPointFtExp = deliveryPointFtExp.and(JPAExpressions.select().from(contractEntity)
                            .join(deliveryPointEntity).on(contractEntity.deliveryPoint().eq(deliveryPointEntity), deliveryPointFtIdExp)
                            .where(contractEntity.unitedDeliveryPoint().eq(unitedDeliveryPointEntity), getContractValidityExpression(contractEntity), dpTypeExp).exists());
                }
            } else {
                deliveryPointFtExp = deliveryPointFtExp.and(JPAExpressions.select().from(contractEntity)
                        .join(deliveryPointEntity).on(contractEntity.deliveryPoint().eq(deliveryPointEntity), deliveryPointFtIdExp)
                        .where(contractEntity.unitedDeliveryPoint().eq(unitedDeliveryPointEntity), getContractValidityExpression(contractEntity)).exists());
            }
        }

        // sorting
        List<OrderSpecifier> orderSpecifiers = new ArrayList<>();
        List<UnitedDeliveryPointSearchSort> sorts = null;
        if (unitedDeliveryPointSearch.getPaging() != null && !Collections.isEmpty(unitedDeliveryPointSearch.getPaging().getSort())) {
            sorts = createSorts();
            orderSpecifiers = createOrderSpecifiers(unitedDeliveryPointCommoditiesView, unitedDeliveryPointEntity, businessPartnerEntity, customerAccountEntity, unitedDeliveryPointOwnershipEntity);
        }
        // Ensure order
        orderSpecifiers.add(unitedDeliveryPointEntity.id.asc());

        // Pairing status filter
        BooleanExpression pairingDoneExp = null;
        if (unitedDeliveryPointSearch.isPairingDone()) {
            pairingDoneExp = unitedDeliveryPointEntity.pairingStatus.eq(PairingStatus.DONE).or(unitedDeliveryPointEntity.pairingStatus.isNull());
        }

        final BooleanExpression notEmptyExp = unitedDeliveryPointEntity.empty.isFalse();

        JPAQuery<UnitedDeliveryPointCommoditiesView> output = new JPAQuery<UnitedDeliveryPointCommoditiesView>(em).from(unitedDeliveryPointCommoditiesView)
                .join(unitedDeliveryPointOwnershipEntity).fetchJoin().on(unitedDeliveryPointOwnershipEntity.customerAccount().id.eq(customerUuid), unitedDeliveryPointOwnershipEntity.unitedDeliveryPoint().eq(unitedDeliveryPointEntity))
                .join(businessPartnerEntity).fetchJoin().on(businessPartnerEntity.id.eq(unitedDeliveryPointEntity.businessPartner().id))
                .where(notEmptyExp)
                .where(businessPartnerUuidExp)
                .where(validExp)
                .where(sharedExp)
                .where(ftExp)
                .where(businessPartnerFtExp)
                .where(queueExp)
                .where(hiddenExp)
                .where(pairingDoneExp)
                .where(typesExp)
                .where(deliveryPointFtAddressExp)
                .where(deliveryPointFtExp)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]));

        if (sorts != null && sorts.contains(UnitedDeliveryPointSearchSort.CUSTOMER_EMAIL)) {
            output = output.join(customerAccountEntity).fetchJoin().on(unitedDeliveryPointOwnershipEntity.customerAccount().eq(customerAccountEntity));
        }

        return output;
    }

    private BooleanExpression getContractValidityExpression(QContractEntity contractEntity) {

        if(!unitedDeliveryPointSearch.getIncludeInactive()) {
            return DateTimeExpression.currentDate(LocalDate.class).between(contractEntity.effectiveFrom, contractEntity.effectiveTo);
        }

        return null;
    }

    private List<UnitedDeliveryPointSearchSort> createSorts() {
        List<UnitedDeliveryPointSearchSort> sorts = new ArrayList<>();

        for (Sorting sorting : unitedDeliveryPointSearch.getPaging().getSort()) {
            Optional<UnitedDeliveryPointSearchSort> sortTypeOptional = UnitedDeliveryPointSearchSort.fromString(sorting.getAttribute());
            sortTypeOptional.ifPresent(sorts::add);
        }
        return sorts;
    }

    private List<OrderSpecifier> createOrderSpecifiers(QUnitedDeliveryPointCommoditiesView unitedDeliveryPointCommoditiesView, QUnitedDeliveryPointEntity unitedDeliveryPointEntity, QBusinessPartnerEntity businessPartnerEntity, QCustomerAccountEntity customerAccountEntity, QUnitedDeliveryPointOwnershipEntity unitedDeliveryPointOwnershipEntity) {
        List<OrderSpecifier> orderSpecifierList = new ArrayList<>();

        for (Sorting sorting : unitedDeliveryPointSearch.getPaging().getSort()) {
            OrderSpecifier orderSpecifier = null;
            Optional<UnitedDeliveryPointSearchSort> sortTypeOptional = UnitedDeliveryPointSearchSort.fromString(sorting.getAttribute());

            if (sortTypeOptional.isPresent()) {
                switch (sortTypeOptional.get()) {
                    case CITY:
                        if (sorting.getDirection() == SortingDirection.ASC) {
                            orderSpecifier = unitedDeliveryPointEntity.city.asc();
                        } else {
                            orderSpecifier = unitedDeliveryPointEntity.city.desc();
                        }
                        break;
                    case STREET:
                        if (sorting.getDirection() == SortingDirection.ASC) {
                            orderSpecifier = unitedDeliveryPointEntity.street.asc();
                        } else {
                            orderSpecifier = unitedDeliveryPointEntity.street.desc();
                        }
                        break;
                    case TYPE:
                        if(unitedDeliveryPointSearch.getIncludeInactive()) {
                            if (sorting.getDirection() == SortingDirection.ASC) {
                                orderSpecifier = unitedDeliveryPointCommoditiesView.allCommodities.asc();
                            } else {
                                orderSpecifier = unitedDeliveryPointCommoditiesView.allCommodities.desc();
                            }
                        } else {
                            if (sorting.getDirection() == SortingDirection.ASC) {
                                orderSpecifier = unitedDeliveryPointCommoditiesView.activeCommodities.asc();
                            } else {
                                orderSpecifier = unitedDeliveryPointCommoditiesView.activeCommodities.desc();
                            }
                        }
                        break;
                    case CUSTOMER_EMAIL:
                        if (sorting.getDirection() == SortingDirection.ASC) {
                            orderSpecifier = customerAccountEntity.email.asc();
                        } else {
                            orderSpecifier = customerAccountEntity.email.desc();
                        }
                        break;
                    case BUSINESS_PARTNER_EXTERNAL_ID:
                        if (sorting.getDirection() == SortingDirection.ASC) {
                            orderSpecifier = businessPartnerEntity.externalId.asc();
                        } else {
                            orderSpecifier = businessPartnerEntity.externalId.desc();
                        }
                        break;
                    case OWNERSHIP_TYPE:
                        if (sorting.getDirection() == SortingDirection.ASC) {
                            orderSpecifier = unitedDeliveryPointOwnershipEntity.type.asc();
                        } else {
                            orderSpecifier = unitedDeliveryPointOwnershipEntity.type.desc();
                        }
                        break;
                    case BUSINESS_PARTNER_NAME:
                        if (sorting.getDirection() == SortingDirection.ASC) {
                            orderSpecifier = businessPartnerEntity.nameSearchable.asc();
                        } else {
                            orderSpecifier = businessPartnerEntity.nameSearchable.desc();
                        }
                        break;
                }

                orderSpecifierList.add(orderSpecifier);
            }
        }
        return orderSpecifierList;
    }

    @Override
    public String getCacheRegion() {
        return "hibernate.query.unitedDeliveryPointQueryProvider";
    }
}
