package sk.spp.nzp.be.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.codelist.Address;
import sk.spp.nzp.be.api.customerprofile.AddressSearch;
import sk.spp.nzp.be.service.codelist.AddressService;

import java.util.List;

@RestController
@RequestMapping("/addresses")
public class AddressController {

    private AddressService addressService;

    public AddressController(AddressService addressService) {
        this.addressService = addressService;
    }

    @Log
    @LogParam
    @PostMapping("/search")
    public List<Address> searchAddresses(@LogParam("search") @RequestBody AddressSearch search) {
        return addressService.fetchAddresses(search);
    }

}
