package sk.spp.nzp.be.assembler.notification.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.notification.CustomerNotificationSendStatus;
import sk.spp.nzp.be.assembler.notification.CustomerNotificationSendStatusAssembler;
import sk.spp.nzp.commons.model.notification.CustomerNotificationSendStatusEntity;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class CustomerNotificationSendStatusAssemblerImpl implements CustomerNotificationSendStatusAssembler {

    @Override
    public CustomerNotificationSendStatusEntity map(CustomerNotificationSendStatus input, CustomerNotificationSendStatusEntity output) {

        output.setChannel(input.getChannel());
        output.setEmail(input.getEmail());
        output.setLockedBy(input.getLockedBy());
        output.setPhone(input.getPhone());
        output.setPriority(input.getPriority());
        output.setRetryCount(input.getRetryCount());

        return output;
    }

    @Override
    public CustomerNotificationSendStatus map(CustomerNotificationSendStatusEntity input, CustomerNotificationSendStatus output) {

        output.setId(input.getId().toString());
        output.setChannel(input.getChannel());
        output.setEmail(input.getEmail());
        output.setLockedBy(input.getLockedBy());
        output.setPhone(input.getPhone());
        output.setPriority(input.getPriority());
        output.setRetryCount(input.getRetryCount());

        return output;
    }

    @Override
    public List<CustomerNotificationSendStatus> map(Collection<CustomerNotificationSendStatusEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new CustomerNotificationSendStatus())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}