package sk.spp.nzp.be.configuration;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import sk.spp.nzp.be.ws.sso.SSOAuhtenticationService;
import sk.spp.nzp.be.ws.sso.impl.SSOAuthenticationServiceWSImpl;

@Configuration
public class WebserviceConfiguration {

    /**
     * Generated JAXB elements (/src/main/sso)
     * @return
     */
    @Bean("ssoJaxbMarshaller")
    public Jaxb2Marshaller getJaxb2Marshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPaths(
                "datatypes.v1.onetimetokenauthentication.sso.isdd.sk",
                "datatypes.v1.autologinauthentication.sso.isdd.sk",
                "datatypes.v1.faults.common.sso.isdd.sk",
                "datatypes.v1.common.sso.isdd.sk"
        );
        return marshaller;
    }

    @Bean
    public SSOAuhtenticationService getOneTimeTokenAuthenticationService(
            @Qualifier("ssoJaxbMarshaller") Jaxb2Marshaller jaxb2Marshaller
    ) {
        SSOAuthenticationServiceWSImpl service = new SSOAuthenticationServiceWSImpl();
        service.setMarshaller(jaxb2Marshaller);
        service.setUnmarshaller(jaxb2Marshaller);
        return service;
    }


}
