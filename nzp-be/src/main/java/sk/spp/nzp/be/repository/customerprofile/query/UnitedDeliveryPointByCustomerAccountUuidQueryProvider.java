package sk.spp.nzp.be.repository.customerprofile.query;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import org.springframework.lang.NonNull;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.model.customerprofile.*;
import sk.spp.nzp.commons.model.customersharing.QUnitedDeliveryPointOwnershipEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

import javax.persistence.EntityManager;
import java.util.UUID;

public class UnitedDeliveryPointByCustomerAccountUuidQueryProvider implements QueryDslProvider<UnitedDeliveryPointEntity> {
    private final UUID customerUuid;
    private final Boolean active;
    private final BusinessPartnerQueue businessPartnerQueue;


    public UnitedDeliveryPointByCustomerAccountUuidQueryProvider(@NonNull UUID customerUuid, BusinessPartnerQueue businessPartnerQueue, Boolean active) {
        this.customerUuid = customerUuid;
        this.businessPartnerQueue = businessPartnerQueue;
        this.active = active;
    }

    @Override
    public JPAQuery<UnitedDeliveryPointEntity> getQuery(EntityManager em) {
        return findByCustomerUuid(em);
    }

    private JPAQuery<UnitedDeliveryPointEntity> findByCustomerUuid(EntityManager em) {
        QBusinessPartnerEntity businessPartnerEntity = QBusinessPartnerEntity.businessPartnerEntity;
        QUnitedDeliveryPointOwnershipEntity unitedDeliveryPointOwnershipEntity = QUnitedDeliveryPointOwnershipEntity.unitedDeliveryPointOwnershipEntity;
        QContractEntity contractEntity = QContractEntity.contractEntity;
        QUnitedDeliveryPointEntity unitedDeliveryPointEntity = QUnitedDeliveryPointEntity.unitedDeliveryPointEntity;
        QUnitedDeliveryPointCommoditiesView unitedDeliveryPointCommoditiesView = QUnitedDeliveryPointCommoditiesView.unitedDeliveryPointCommoditiesView;
        BooleanExpression queueExp = businessPartnerQueue == null ? null : businessPartnerEntity.queue.eq(businessPartnerQueue);
        BooleanExpression activeUdp = null;

        final BooleanExpression notEmptyExp = unitedDeliveryPointEntity.empty.isFalse();

        if (Boolean.TRUE.equals(active)) {
            activeUdp = unitedDeliveryPointCommoditiesView.activeCommodities.isNotNull();
        }

        JPAQuery<UnitedDeliveryPointEntity> output = new JPAQuery<UnitedDeliveryPointEntity>(em)
                .select(unitedDeliveryPointCommoditiesView.unitedDeliveryPoint())
                .from(unitedDeliveryPointCommoditiesView)
                .join(unitedDeliveryPointCommoditiesView.unitedDeliveryPoint(), unitedDeliveryPointEntity)
                .join(unitedDeliveryPointOwnershipEntity).on(unitedDeliveryPointOwnershipEntity.customerAccount().id.eq(customerUuid), unitedDeliveryPointOwnershipEntity.unitedDeliveryPoint().eq(unitedDeliveryPointEntity))
                .join(businessPartnerEntity).on(businessPartnerEntity.id.eq(unitedDeliveryPointEntity.businessPartner().id))
                .where(queueExp,
                        notEmptyExp,
                        activeUdp);
        return output;
    }
}
