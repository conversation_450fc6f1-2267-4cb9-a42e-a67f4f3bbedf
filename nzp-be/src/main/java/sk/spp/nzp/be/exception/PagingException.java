package sk.spp.nzp.be.exception;

import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.exception.ApiException;

public class PagingException extends ApiException {

    protected PagingException(ErrorCode code, String message) {
        super(code, message);
    }

    public static PagingException wrongInput(String message) {
        return new PagingException(ErrorCode.PAGING_WRONG_INPUT, message);
    }

}
