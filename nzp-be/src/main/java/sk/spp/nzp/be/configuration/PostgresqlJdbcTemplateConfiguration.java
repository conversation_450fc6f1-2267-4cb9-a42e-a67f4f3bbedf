package sk.spp.nzp.be.configuration;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

@Configuration
public class PostgresqlJdbcTemplateConfiguration {

    @Autowired
    private DataSource ds;

    @Autowired
    @Qualifier("reportingDatasource")
    private DataSource reportingDs;

    @Bean(name = "postgreJdbcTemplate")
    public JdbcTemplate postgreJdbcTemplate() {

        JdbcTemplate template = new JdbcTemplate();
        template.setDataSource(ds);

        return template;
    }


    @Bean(name = "postgreJdbcTemplateReporting")
    @ConfigurationProperties(prefix = "reporting.jdbc.template")
    public JdbcTemplate postgreJdbcTemplateReporting() {

        JdbcTemplate template = new JdbcTemplate();
        template.setDataSource(reportingDs);

        return template;
    }
}
