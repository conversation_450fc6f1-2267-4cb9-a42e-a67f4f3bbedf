package sk.spp.nzp.be.converters;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import sk.spp.nzp.commons.api.customeraccess.enums.CustomerAccountStatus;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.api.employeeaccess.EmployeeAccount;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.context.holder.RequestContextThreadLocalHolder;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;
import sk.spp.nzp.commons.model.employeeaccess.AccessGroupEntity;
import sk.spp.nzp.commons.repository.customeraccess.CustomerAccountRepository;
import sk.spp.nzp.commons.security.NzpAuthenticationToken;
import sk.spp.nzp.commons.security.model.CustomerPrincipal;
import sk.spp.nzp.commons.security.model.EmployeePrincipal;
import sk.spp.nzp.commons.security.model.NzpAuthority;
import sk.spp.nzp.commons.security.model.enums.NzpAuthorityType;
import sk.spp.nzp.commons.utils.NzpSecurityHelper;
import sk.spp.nzp.ldap.mapper.employeeaccess.AccessGroupMapper;
import sk.spp.nzp.ldap.service.employeeaccess.EmployeeAccountService;

import java.time.Clock;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;

import static java.util.stream.Collectors.toSet;

@Component
public class NzpAuthenticationJwtConvertor implements Converter<Jwt, NzpAuthenticationToken> {

    private static final String PAYLOAD_ISSUER = "iss";
    private static final String PAYLOAD_AUDIENCE = "aud";
    private static final String PAYLOAD_PERSON_ID = "sub";
    private static final String PAYLOAD_PERSON_ON_BEHALF_ID = "act";
    private static final String PAYLOAD_PERSON_TYPE = "type";
    private static final String PAYLOAD_USER_SESSION_ID = "usid";
    private static final String TYPE_CUSTOMER = "cust";
    private static final String TYPE_EMPLOYEE = "emp";
    private static final String ROLE_CUSTOMER = "ROLE_CUSTOMER";
    private static final String ROLE_EMPLOYEE = "ROLE_EMPLOYEE";
    private static final String ROLE_ADMIN = "ROLE_ADMIN";
    private static final Integer EMP_ACCOUNT_CONTROL_ACTIVE = 512;

    @Value("${security.jwt.validation.issuer}")
    private String issuer;
    @Value("${security.jwt.validation.audience}")
    private List<String> allowedAudiences;
    @Value("${security.admin.nzpGroupCode}")
    private String nzpAdminGroupCode;

    @Autowired
    private CustomerAccountRepository customerRepository;
    @Autowired
    private EmployeeAccountService employeeAccountService;
    @Autowired
    private RequestContextThreadLocalHolder requestContextHolder;
    @Autowired
    private AccessGroupMapper accessGroupMapper;
    @Autowired
    private Clock clock;

    @Override
    @Transactional(readOnly = true)
    public NzpAuthenticationToken convert(Jwt source) {

        Map<String, Object> payload = source.getClaims();
        validateProperties(payload);

        NzpAuthenticationToken authentication;
        Set<NzpAuthority> authorities = new HashSet();

        String personId = (String) payload.get(PAYLOAD_PERSON_ID);
        String type = (String) payload.get(PAYLOAD_PERSON_TYPE);

        switch (type) {
            case TYPE_CUSTOMER:
                UUID customerId = toUUID(personId);
                authorities.add(getRole(ROLE_CUSTOMER));

                String onBehalfId = (String) payload.get(PAYLOAD_PERSON_ON_BEHALF_ID);

                CustomerPrincipal customer;

                if (onBehalfId != null) { // employee acts on behalf of customer
                    customer = loadCustomerDto(customerId, Arrays.asList(CustomerAccountStatus.ACTIVE, CustomerAccountStatus.PRE_REGISTRATION));

                    EmployeeAccount employeeOnBehalfEntity =
                            loadDto(onBehalfId,
                                    (login) -> employeeAccountService.findAccount(login)
                                            .filter(this::employeeAccountIsActive)
                                            .map(x -> {
                                                x.setNzpGroups(accessGroupMapper.map(x.getLdapGroups()));
                                                return x;
                                            }),
                                    entity -> entity,
                                    "employee on behalf");

                    authorities.addAll(getAuthorities(employeeOnBehalfEntity));

                    authentication = new NzpAuthenticationToken(authorities, source.getTokenValue(), (String) payload.get(PAYLOAD_USER_SESSION_ID));
                    authentication.setEmployee(convert(employeeOnBehalfEntity));

                } else { // standard customer
                    customer = loadCustomerDto(customerId, Collections.singletonList(CustomerAccountStatus.ACTIVE));

                    authentication = new NzpAuthenticationToken(authorities, source.getTokenValue(), (String) payload.get(PAYLOAD_USER_SESSION_ID)); // customer doesn't have roles / rights
                }
                authentication.setCustomer(customer);
                MDC.put("customerId", customerId.toString());
                break;

            case TYPE_EMPLOYEE:
                EmployeeAccount employeeEntity =
                        loadDto(personId,
                                (login) -> employeeAccountService.findAccount(login)
                                        .filter(this::employeeAccountIsActive)
                                        .map(x -> {
                                            x.setNzpGroups(accessGroupMapper.map(x.getLdapGroups()));
                                            return x;
                                        }),
                                entity -> entity,
                                "employee");

                authorities.addAll(getAuthorities(employeeEntity));

                authentication = new NzpAuthenticationToken(authorities, source.getTokenValue(), (String) payload.get(PAYLOAD_USER_SESSION_ID));
                authentication.setEmployee(convert(employeeEntity));
                MDC.put("employeeLogin", employeeEntity.getLogin());
                break;

            default:
                throw getInvalidTokenException("Unknown type: " + type);
        }

        authentication.setDetails(source);
        authentication.setAuthenticated(true);

        setRequestContextSessionId((String) payload.get(PAYLOAD_USER_SESSION_ID));

        return authentication;
    }

    private CustomerPrincipal loadCustomerDto(UUID customerId, List<CustomerAccountStatus> allowedStatuses) {
        return loadDto(customerId,
                (id) -> customerRepository.findById(id).filter(x -> allowedStatuses.contains(x.getStatus())),
                this::convert,
                "customer");
    }

    private boolean employeeAccountIsActive(EmployeeAccount employeeAccount) {
        return EMP_ACCOUNT_CONTROL_ACTIVE.equals(employeeAccount.getUserAccountControl())
                && (employeeAccount.getAccountExpires() == null || employeeAccount.getAccountExpires().isAfter(LocalDateTime.now(clock)));
    }

    private void setRequestContextSessionId(String sessionId) {

        if(requestContextHolder.getContext() != null && requestContextHolder.getContext().getSessionId() == null && sessionId != null) {
            requestContextHolder.getContext().setSessionId(sessionId);
            requestContextHolder.setContext(requestContextHolder.getContext());
        }
    }

    private void validateProperties(Map<String, Object> payload) {
        if (!Objects.equals(payload.get(PAYLOAD_ISSUER), issuer)) {
            throw getInvalidTokenException("Invalid issuer");
        }
        if (payload.get(PAYLOAD_AUDIENCE) == null || !audienceIsValid((List) payload.get(PAYLOAD_AUDIENCE))) {
            throw getInvalidTokenException("Invalid audience");
        }
    }

    private boolean audienceIsValid(List audClaimValues) {
        return !Collections.disjoint(audClaimValues, allowedAudiences);
    }

    private Set<NzpAuthority> getAuthorities(EmployeeAccount account) {
        Set<NzpAuthority> result = new HashSet();

        result.add(getRole(ROLE_EMPLOYEE));
        if (isAdmin(account)) {
            result.add(getRole(ROLE_ADMIN));
        }

        if (account.getNzpGroups() != null) {
            convertGroupAccessesToAuthorities(account, result);
        }
        return result;
    }

    private void convertGroupAccessesToAuthorities(EmployeeAccount account, Set<NzpAuthority> authorities) {
        NzpSecurityHelper.getMapOfGrantedRightsAndQueues(account.getNzpGroups()).entrySet().stream()
                .map(entry -> convert(entry.getKey(), entry.getValue().size() == 1 ? entry.getValue().iterator().next() : null))
                .forEach(authorities::add);
    }

    private <E, D> D loadDto(UUID id, Function<UUID, Optional<E>> getter, Function<E, D> converter, String idName) {
        E entity = getter.apply(id)
                .orElseThrow(() -> getInvalidTokenException("Unknown or invalid (expired, inactive) " + idName + " account"));

        return converter.apply(entity);
    }

    private <E, D> D loadDto(String id, Function<String, Optional<E>> getter, Function<E, D> converter, String idName) {
        E entity = getter.apply(id)
                .orElseThrow(() -> getInvalidTokenException("Unknown or invalid (expired, inactive) " + idName + " account"));

        return converter.apply(entity);
    }

    private AuthenticationException getInvalidTokenException(String message) {
        return new CustomSppAuthenticationException(message, ErrorCode.ACCOUNT_INACTIVE.getCode());
    }

    private NzpAuthority getRole(String role) {
        return new NzpAuthority(
                role,
                NzpAuthorityType.ROLE,
                null);
    }

    private CustomerPrincipal convert(CustomerAccountEntity entity) {
        return new CustomerPrincipal(entity.getId(), entity.getType())
                .setEmail(entity.getEmail())
                .setFirstName(entity.getFirstName())
                .setLastName(entity.getLastName())
                .setPhone(entity.getPhone());
    }

    private EmployeePrincipal convert(EmployeeAccount account) {
        Set<String> nzpGroupCodes = Optional.ofNullable(account.getNzpGroups())
                .orElse(Set.of()).stream()
                .map(AccessGroupEntity::getCode)
                .collect(toSet());

        return new EmployeePrincipal(
                account.getId(),
                account.getEmail(),
                account.getLogin(),
                account.getFirstName(),
                account.getLastName(),
                account.getLdapGroups(),
                nzpGroupCodes);
    }

    private boolean isAdmin(EmployeeAccount account) {
        return account.getNzpGroups().stream().anyMatch(x -> nzpAdminGroupCode.equals(x.getCode()));
    }

    private NzpAuthority convert(String accessRightCode, BusinessPartnerQueue queue) {
        return new NzpAuthority(
                accessRightCode,
                NzpAuthorityType.ACCESS_RIGHT,
                queue);
    }

    private UUID toUUID(Object id) {
        if (id == null) {
            return null;
        }
        return UUID.fromString((String) id);
    }

    public static class CustomSppAuthenticationException extends AuthenticationException {

        private int code;

        public CustomSppAuthenticationException(String message, int errorCode) {
            super(message);
            this.code = errorCode;
        }

        public int getCode() {
            return code;
        }
    }
}
