package sk.spp.nzp.be.repository.customerprofile.query;

import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.service.codelist.model.CodeListQuery;
import sk.spp.nzp.be.service.codelist.model.CodeListStatus;
import sk.spp.nzp.commons.api.customerprofile.enums.TariffStatus;
import sk.spp.nzp.commons.model.customerprofile.TariffEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;
import sk.spp.nzp.commons.repository.customerprofile.query.TariffFindAllQueryProvider;

import javax.persistence.EntityManager;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class TariffCodeListQueryProvider implements QueryDslProvider<TariffEntity> {

    private CodeListQuery query;

    public TariffCodeListQueryProvider(CodeListQuery query) {
        this.query = query;
    }

    @Override
    public JPAQuery<TariffEntity> getQuery(EntityManager em) {
        return new TariffFindAllQueryProvider()
                .setStatuses(mapStatuses(query.getStatuses()))
                .setCode(query.getCode())
                .getQuery(em);
    }

    private Set<TariffStatus> mapStatuses(List<CodeListStatus> statuses) {
        Set<TariffStatus> productStatuses = new HashSet<>();

        statuses.forEach(s -> {
            if (s == CodeListStatus.ACTIVE) {
                productStatuses.add(TariffStatus.ACTIVE);
            } else if (s == CodeListStatus.INACTIVE) {
                productStatuses.add(TariffStatus.INACTIVE);
            }
        });

        return productStatuses;
    }

}
