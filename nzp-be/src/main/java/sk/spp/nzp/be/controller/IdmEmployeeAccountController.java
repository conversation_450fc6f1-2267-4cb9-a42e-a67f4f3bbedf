package sk.spp.nzp.be.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.commons.api.employeeaccess.Employee;
import sk.spp.nzp.be.api.identitymanagement.EmployeeAuthReq;
import sk.spp.nzp.commons.api.identitymanagement.EmployeeAccountSearch;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.ldap.service.employeeaccess.EmployeeAccountService;

import javax.validation.Valid;
import java.util.ArrayList;

@RestController
@RequestMapping("/idm/employee-accounts")
public class IdmEmployeeAccountController {

    private EmployeeAccountService employeeService;

    public IdmEmployeeAccountController(EmployeeAccountService employeeService) {
        this.employeeService = employeeService;
    }

    @Log
    @LogParam
    @PostMapping("/authenticate")
    public void authenticate(@LogParam("authReq") @Valid @RequestBody EmployeeAuthReq authReq) {
        employeeService.authenticate(authReq.getLogin(), authReq.getPassword());
    }

    @Log
    @LogParam
    @PostMapping("/search")
    public Iterable<Employee> search(@LogParam("searchReq") @Valid @RequestBody EmployeeAccountSearch searchReq) {
        try {
            return employeeService.search(searchReq, true);
        } catch (ApiException ex) {
            return new ArrayList<>();
        }
    }

}

