package sk.spp.nzp.be.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.*;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.Errors;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.common.Count;
import sk.spp.nzp.be.api.common.FileContent;
import sk.spp.nzp.be.api.common.PagedResponse;
import sk.spp.nzp.be.api.customeraccess.*;
import sk.spp.nzp.be.api.customeraccess.registration.RegistrationBatchRequest;
import sk.spp.nzp.be.api.customeraccess.registration.RegistrationBatchRequestSearch;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerSearch;
import sk.spp.nzp.be.api.customerprofile.*;
import sk.spp.nzp.be.api.customerprofile.invoicesummary.InvoicePaymentSummary;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestSearchQuery;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestSummary;
import sk.spp.nzp.be.api.employeeprofile.BusinessPartnerPairingAction;
import sk.spp.nzp.be.api.employeeprofile.CustomerDeactivation;
import sk.spp.nzp.be.api.employeeprofile.CustomerPreRegistration;
import sk.spp.nzp.be.service.customeraccess.CustomerService;
import sk.spp.nzp.be.service.customeraccess.RegistrationBatchRequestService;
import sk.spp.nzp.be.service.customerprofile.BusinessPartnerService;
import sk.spp.nzp.be.service.customerprofile.InvoiceService;
import sk.spp.nzp.be.service.customerprofile.PairingService;
import sk.spp.nzp.be.service.customerrequest.CustomerRequestService;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.api.reporting.DataReportFormat;
import sk.spp.nzp.commons.exception.ApiException;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/admin/customers")
public class AdminCustomerController {

    @Value("${reporting.export.filename}")
    private String fileName;

    private CustomerService customerService;
    private RegistrationBatchRequestService registrationBatchRequestService;
    private PairingService pairingService;
    private BusinessPartnerService businessPartnerService;
    private InvoiceService invoiceService;
    private CustomerRequestService customerRequestService;

    public AdminCustomerController(CustomerService customerService,
                                   PairingService pairingService,
                                   BusinessPartnerService businessPartnerService,
                                   InvoiceService invoiceService,
                                   CustomerRequestService customerRequestService,
                                   RegistrationBatchRequestService registrationBatchRequestService) {

        this.customerService = customerService;
        this.pairingService = pairingService;
        this.businessPartnerService = businessPartnerService;
        this.invoiceService = invoiceService;
        this.customerRequestService = customerRequestService;
        this.registrationBatchRequestService = registrationBatchRequestService;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTIVATION')")
    @PostMapping(value = "/{uuid}/activate")
    public void customerActivation (@LogParam("uuid") @PathVariable UUID uuid) {

        customerService.activateCustomerAccount(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_DEACTIVATION')")
    @PostMapping(value = "/{uuid}/deactivate")
    public void customerDeactivation (@LogParam("uuid") @PathVariable UUID uuid,
                                      @LogParam("customerDeactivation") @Valid @RequestBody CustomerDeactivation customerDeactivation) {

        customerService.deactivateCustomerAccount(uuid, customerDeactivation);
    }

    @Secured("ROLE_EMPLOYEE")
    @PreAuthorize("hasAuthority('CUSTOMERS_VIEW')")
    @PostMapping("/search")
    public PagedResponse<CustomerAccount> adminSearchCustomerRequests(@LogParam("customerAccountSearch") @RequestBody CustomerAccountSearch customerAccountSearch) {

        return customerService.getByCustomerAccountSearch(customerAccountSearch);
    }

    @Secured("ROLE_EMPLOYEE")
    @PreAuthorize("hasAuthority('CUSTOMERS_VIEW')")
    @GetMapping("/{uuid}")
    public CustomerAccount adminGetCustomerRequest(@LogParam("uuid") @PathVariable UUID uuid) {

        return customerService.getByUuid(uuid);
    }

    @Log
    @LogParam
    @Secured("ROLE_EMPLOYEE")
    @PreAuthorize("hasAuthority('CUSTOMERS_VIEW')")
    @PostMapping(value = "/search/download-export", consumes = {"application/json"})
    public void searchCustomerRequestsDownloadExport(@LogParam("customerAccountSearch") @RequestBody CustomerAccountSearch customerAccountSearch,
                                       @LogParam("format") @RequestParam DataReportFormat format,
                                       @LogParam("columns") @RequestParam(required = false) List<String> columns,
                                       HttpServletResponse response) throws IOException {

        ContentDisposition contentDisposition;
        if (DataReportFormat.CSV.equals(format)) {
            contentDisposition = ContentDisposition.builder("attachment")
                    .filename(fileName + ".csv")
                    .build();

            response.setContentType("text/csv");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
        } else {
            contentDisposition = ContentDisposition.builder("attachment")
                    .filename(fileName + ".xlsx")
                    .build();

            response.setContentType("application/vnd.ms-excels");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
        }

        customerService.getByCustomerAccountSearchExport(customerAccountSearch, format, columns, response.getOutputStream());
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('BUSINESS_PARTNERS_UNPAIRING') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW')")
    @PostMapping(value = "/{customerUuid}/business-partners/{businessPartnerId}/unpair-request")
    public void unpairingBusinessPartner (
            @LogParam("customerUuid") @PathVariable UUID customerUuid, @LogParam("businessPartnerId") @PathVariable String businessPartnerId) {

        pairingService.unpairBusinessPartner(customerUuid, businessPartnerId);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('BUSINESS_PARTNERS_PAIRING') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW')")
    @PostMapping(value = "/{customerUuid}/business-partners/{businessPartnerExternalId}/pair-request")
    public PairBusinessPartnerResponse pairingBusinessPartner(
            @LogParam("customerUuid") @PathVariable UUID customerUuid, @LogParam("businessPartnerExternalId") @PathVariable String businessPartnerExternalId) {

        return pairingService.pairBusinessPartner(customerUuid, businessPartnerExternalId);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_REGISTRATION')")
    @PostMapping(value = "/registration")
    public CustomerAccount customerRegistration (@LogParam("customerPreRegistration") @Valid @RequestBody CustomerPreRegistration customerPreRegistration) {
        return customerService.preRegisterAccount(customerPreRegistration);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_BATCH_IMPORT')")
    @PostMapping(value = "/registration/batch")
    public RegistrationBatchRequest createCustomerBatchRegistration (
            @RequestParam MultipartFile file,
            @LogParam("scheduledAt") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime scheduledAt) {
        return registrationBatchRequestService.createRegistrationBatchRequest(file, scheduledAt);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_BATCH_IMPORT')")
    @PutMapping(value = "/registration/batch/{id}/schedule")
    public RegistrationBatchRequest updateCustomerBatchRegistrationPlannedAt (
            @LogParam("id") @PathVariable UUID id,
            @LogParam("scheduledAt") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime scheduledAt) {
        return registrationBatchRequestService.updateRegistrationBatchRequestSchedule(id, scheduledAt);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_BATCH_IMPORT')")
    @PutMapping(value = "/registration/batch/{id}/cancel")
    public RegistrationBatchRequest cancelCustomerBatchRegistration (
            @LogParam("id") @PathVariable UUID id,
            @LogParam("request") @RequestBody RegistrationBatchRequest registrationBatchRequest) {
        return registrationBatchRequestService.cancelReqistrationBatchRequest(id);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_BATCH_IMPORT')")
    @GetMapping(value = "/registration/batch/{id}")
    public RegistrationBatchRequest getCustomerBatchRegistration (
            @LogParam("id") @PathVariable UUID id) {
        return registrationBatchRequestService.getReqistrationBatchRequest(id);
    }


    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_BATCH_IMPORT')")
    @GetMapping(value = "/registration/batch/{id}/report/download")
    public ResponseEntity<Resource> downloadCustomerBatchRegistrationReport (
            @LogParam("id") @PathVariable UUID id) {

        // fetch attachment
        FileContent fileContent = registrationBatchRequestService.getRegistrationBatchRequestReport(id);

        ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                .filename(fileContent.getFilename())
                .build();

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, fileContent.getMimetype());
        headers.add(HttpHeaders.CONTENT_LENGTH, "" + fileContent.getLength());
        headers.add(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());

        return new ResponseEntity<>(fileContent.getContentAsResource(), headers, HttpStatus.OK);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_BATCH_IMPORT')")
    @GetMapping(value = "/registration/batch/{id}/download")
    public ResponseEntity<Resource> downloadCustomerBatchRegistration (
            @LogParam("id") @PathVariable UUID id) {

        // fetch attachment
        FileContent fileContent = registrationBatchRequestService.getRegistrationBatchRequestContent(id);

        ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                .filename(fileContent.getFilename())
                .build();

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, fileContent.getMimetype());
        headers.add(HttpHeaders.CONTENT_LENGTH, "" + fileContent.getLength());
        headers.add(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());

        return new ResponseEntity<>(fileContent.getContentAsResource(), headers, HttpStatus.OK);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_BATCH_IMPORT')")
    @PostMapping(value = "/registration/batch/search")
    public PagedResponse<RegistrationBatchRequest> findRegisterBatchRequests (
            @LogParam("search") @RequestBody RegistrationBatchRequestSearch search) {
        return registrationBatchRequestService.searchRegistrationBatchRequest(search);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_PASSWORD_RECOVERY')")
    @PostMapping(value = "/{customerUuid}/password/recovery")
    public void customerPasswordRecovery (@LogParam("customerUuid") @PathVariable UUID customerUuid) {

        customerService.customerPasswordRecovery(customerUuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_REGISTRATION')")
    @PostMapping(value = "/{customerUuid}/registration-confirm")
    public void adminConfirmRegistration(@LogParam("customerUuid") @PathVariable UUID customerUuid) {

        customerService.confirmRegistration(customerUuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('BUSINESS_PARTNERS_PAIRING') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW')")
    @PutMapping(value = "/{customerUuid}/business-partners/{businessPartnerId}/pair-approval")
    public void approveBusinessPartnerPairing(@LogParam("customerUuid") @PathVariable UUID customerUuid,
            @LogParam("businessPartnerId") @PathVariable String businessPartnerId,
            @LogParam("businessPartnerPairingAction") @Valid @RequestBody BusinessPartnerPairingAction businessPartnerPairingAction) {

        pairingService.approveBusinessPartnerPairing(customerUuid, businessPartnerId, businessPartnerPairingAction);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_VIEW')  and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW')")
    @PostMapping(value = "/{customerUuid}/business-partners/search")
    public PagedResponse<BusinessPartnerPairing> businessPartnerSearch(@LogParam("customerUuid") @PathVariable UUID customerUuid,
            @LogParam("businessPartnerSearch ") @RequestBody BusinessPartnerSearch businessPartnerSearch) {

        return businessPartnerService.getByCustomerAccountAlsoWithPairing(customerUuid, businessPartnerSearch);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_DELETE_ACCOUNT')")
    @PostMapping(value = "/{uuid}/delete-account")
    public void adminDeleteCustomerAccount(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("request") @RequestBody DeleteCustomerAccountRequest request) {
        
        customerService.deleteCustomerAccountAsAdmin(uuid, request);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_REGISTRATION')")
    @PostMapping(value = "/{customerUuid}/send-invite")
    public void sendInvite (@LogParam("customerUuid") @PathVariable UUID customerUuid) {

        customerService.sendInvite(customerUuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_EDIT')")
    @PostMapping(path = "/{uuid}/phone", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void adminRequestPhoneChange(@LogParam("uuid") @PathVariable UUID uuid,
                                                                @LogParam("request") @Valid @RequestBody AdminPhoneChange request,
                                                                Errors error) {
        if(error.hasErrors()){
            throw new ApiException(ErrorCode.CUSTOMER_ACCESS_INVALID_PHONE_NUMBER);
        }

        customerService.requestPhoneChange(request, uuid);
    }


    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_EDIT')")
    @PostMapping(path = "/{uuid}/email", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void adminRequestEmailChange(@LogParam("uuid") @PathVariable UUID uuid,
                                   @LogParam("request") @Valid @RequestBody AdminEmailChange request) {

        customerService.requestEmailChange(request, uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_VIEW') and hasAuthority('ENTITY_INVOICES_VIEW')")
    @PostMapping(value = "/{uuid}/invoices/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<InvoiceSummary> invoiceSearch(@LogParam("uuid") @PathVariable UUID uuid,
                                                @LogParam("invoiceSearch ") @RequestBody InvoiceSearch invoiceSearch) {

        return invoiceService.getInvoicesAsEmployee(invoiceSearch.withCustomerAccountId(uuid));
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_VIEW') and hasAuthority('ENTITY_INVOICES_VIEW')")
    @PostMapping(value = "/{uuid}/invoices/search/count", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Count adminInvoiceSearchCount(@LogParam("uuid") @PathVariable UUID uuid,
                                                   @LogParam("invoiceSearch ") @RequestBody InvoiceSearch invoiceSearch) {

        return invoiceService.getInvoicesCountAsEmployee(invoiceSearch.withCustomerAccountId(uuid));
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_VIEW') and hasAuthority('ENTITY_INVOICES_VIEW')")
    @GetMapping(value = "/{uuid}/invoices/summary", produces = {MediaType.APPLICATION_JSON_VALUE})
    public InvoicePaymentSummary adminInvoicesSummary(@LogParam("uuid") @PathVariable UUID uuid) {

        return invoiceService.getInvoiceSummaryByCustomer(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_VIEW')  and hasAuthority('ENTITY_REQUESTS_VIEW')")
    @PostMapping("/{uuid}/customer-requests/search")
    public PagedResponse<CustomerRequestSummary> searchCustomerRequests(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("query") @RequestBody CustomerRequestSearchQuery query
    ) {
        query.setCustomerUuid(uuid);

        return customerRequestService.fetchCustomerRequestsAsEmployee(query);
    }
}
