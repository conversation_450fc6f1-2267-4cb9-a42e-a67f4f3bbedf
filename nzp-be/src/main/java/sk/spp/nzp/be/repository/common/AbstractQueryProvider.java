package sk.spp.nzp.be.repository.common;

import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.ComparableExpressionBase;

import sk.spp.nzp.be.api.common.Sorting;
import sk.spp.nzp.be.api.common.SortingDirection;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

public abstract class AbstractQueryProvider<S> implements QueryDslProvider<S>{

    public <T extends Comparable<?>> OrderSpecifier<T> getOrderSpecifier(Sorting sorting, ComparableExpressionBase<T> expression) {
        
        return sorting.getDirection().equals(SortingDirection.ASC) ? expression.asc() : expression.desc();
    }
}
