package sk.spp.nzp.be.repository.notification;

import org.springframework.data.jpa.repository.JpaRepository;
import sk.spp.nzp.commons.api.notification.enums.NotificationExecutionType;
import sk.spp.nzp.commons.model.notification.NotificationTemplateVariableEntity;

import java.util.List;
import java.util.UUID;

public interface NotificationTemplateVariableEntityRepository extends JpaRepository<NotificationTemplateVariableEntity, UUID> {

    List<NotificationTemplateVariableEntity> findAllByNotificationTemplate_Id(UUID notificationTemplateId);

    List<NotificationTemplateVariableEntity> findAllByNotificationTemplateExecutionType(NotificationExecutionType notificationExecutionType);
}
