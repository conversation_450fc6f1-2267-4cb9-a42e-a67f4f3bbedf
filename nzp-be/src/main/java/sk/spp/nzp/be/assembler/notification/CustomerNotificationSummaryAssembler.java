package sk.spp.nzp.be.assembler.notification;

import sk.spp.nzp.be.api.notification.CustomerNotificationSummary;
import sk.spp.nzp.commons.model.notification.CustomerNotificationEntity;

import java.util.Collection;
import java.util.List;

public interface CustomerNotificationSummaryAssembler {

    CustomerNotificationSummary map(CustomerNotificationEntity input, CustomerNotificationSummary output);

    List<CustomerNotificationSummary> map(Collection<CustomerNotificationEntity> customerNotifications);
}
