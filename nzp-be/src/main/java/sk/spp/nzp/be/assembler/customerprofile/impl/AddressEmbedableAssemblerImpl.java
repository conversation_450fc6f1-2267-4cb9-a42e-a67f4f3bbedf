package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.springframework.stereotype.Component;

import sk.spp.nzp.be.api.customerprofile.Address;
import sk.spp.nzp.be.assembler.customerprofile.AddressEmbedableAssembler;
import sk.spp.nzp.commons.model.customerprofile.AddressEmbeddable;

@Component
public class AddressEmbedableAssemblerImpl implements AddressEmbedableAssembler {

    @Override
    public AddressEmbeddable map(Address input, AddressEmbeddable output) {
        
        output.setStreet(input.getStreet());
        output.setStreetNumber(input.getStreetNumber());
        output.setCity(input.getCity());
        output.setZipCode(input.getZipCode());
        output.setCountry(input.getCountry());
        
        return output;
    }
    
    @Override
    public Address map(AddressEmbeddable input, Address output) {
        
        output.setStreet(input.getStreet());
        output.setStreetNumber(input.getStreetNumber());
        output.setCity(input.getCity());
        output.setZipCode(input.getZipCode());
        output.setCountry(input.getCountry());
        
        return output;
    }
}
