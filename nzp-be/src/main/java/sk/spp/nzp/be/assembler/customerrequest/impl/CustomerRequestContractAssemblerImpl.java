package sk.spp.nzp.be.assembler.customerrequest.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerprofile.Address;
import sk.spp.nzp.be.api.customerprofile.ContractAccountSummary;
import sk.spp.nzp.be.api.customerprofile.ContractSummary;
import sk.spp.nzp.be.api.customerprofile.DeliveryPointSummary;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestContract;
import sk.spp.nzp.be.assembler.customerprofile.AddressEmbedableAssembler;
import sk.spp.nzp.be.assembler.customerprofile.ContractAccountAssembler;
import sk.spp.nzp.be.assembler.customerprofile.ContractAssembler;
import sk.spp.nzp.be.assembler.customerprofile.DeliveryPointAssembler;
import sk.spp.nzp.be.assembler.customerrequest.CustomerRequestContractAssembler;
import sk.spp.nzp.commons.model.customerprofile.ContractAccountEntity;
import sk.spp.nzp.commons.model.customerprofile.ContractEntity;
import sk.spp.nzp.commons.model.customerprofile.DeliveryPointEntity;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestContractEntity;
import sk.spp.nzp.commons.service.common.HistoryContextProvider;
import sk.spp.nzp.commons.service.common.impl.HistoryPeriod;
import sk.spp.nzp.commons.utils.Expressions;

import java.util.ArrayList;
import java.util.List;

@Component
public class CustomerRequestContractAssemblerImpl implements CustomerRequestContractAssembler {

    private final DeliveryPointAssembler deliveryPointAssembler;
    private final HistoryContextProvider historyContextProvider;
    private final ContractAccountAssembler contractAccountAssembler;
    private final ContractAssembler contractAssembler;
    private final AddressEmbedableAssembler addressEmbedableAssembler;

    public CustomerRequestContractAssemblerImpl(
            DeliveryPointAssembler deliveryPointAssembler,
            HistoryContextProvider historyContextProvider,
            ContractAccountAssembler contractAccountAssembler,
            ContractAssembler contractAssembler,
            AddressEmbedableAssembler addressEmbedableAssembler
    ) {
        this.deliveryPointAssembler = deliveryPointAssembler;
        this.historyContextProvider = historyContextProvider;
        this.contractAccountAssembler = contractAccountAssembler;
        this.contractAssembler = contractAssembler;
        this.addressEmbedableAssembler = addressEmbedableAssembler;
    }

    @Override
    public CustomerRequestContract map(CustomerRequestContractEntity input, CustomerRequestContract output) {

        if (input.getDeliveryPoint() != null) {
            DeliveryPointSummary deliveryPointSummary = new DeliveryPointSummary();

            output.setDeliveryPoint(deliveryPointAssembler.map(
                    input.getDeliveryPoint(),
                    deliveryPointSummary,
                    historyContextProvider.createDeliveryPointHistoryContext(Expressions.tryGet(input::getContract), HistoryPeriod.CURRENT))
            );
        }

        if (input.getContractAccount() != null) {
            ContractAccountEntity contractAccountEntity = input.getContractAccount();
            ContractAccountSummary contractAccountSummary = new ContractAccountSummary();
            output.setContractAccount(contractAccountAssembler.mapSummary(contractAccountEntity, contractAccountSummary, false));
        }

        if (input.getContract() != null){
            ContractSummary contractSummary = new ContractSummary();
            output.setContract(contractAssembler.map(input.getContract(),contractSummary));
        }

        return output;
    }

    @Override
    public List<CustomerRequestContract> map(List<CustomerRequestContractEntity> input) {
        if (input == null) {
            return null;
        }

        List<CustomerRequestContract> customerRequestContracts = new ArrayList<>();

        input.forEach(i -> customerRequestContracts.add(map(i, new CustomerRequestContract())));

        return customerRequestContracts;
    }

    @Override
    public CustomerRequestContract mapBrief(CustomerRequestContractEntity input, CustomerRequestContract output) {

        if (input.getDeliveryPoint() != null) {
            DeliveryPointEntity inputDeliveryPoint = input.getDeliveryPoint();

            DeliveryPointSummary deliveryPointSummary = new DeliveryPointSummary();
            deliveryPointSummary.setId(inputDeliveryPoint.getId());
            deliveryPointSummary.setType(inputDeliveryPoint.getType());

            if (inputDeliveryPoint.getAddress() != null) {
                deliveryPointSummary.setAddress(addressEmbedableAssembler.map(inputDeliveryPoint.getAddress(), new Address()));
            }

            output.setDeliveryPoint(deliveryPointSummary);
        }

        if (input.getContractAccount() != null) {
            ContractAccountEntity contractAccountEntity = input.getContractAccount();
            output.setContractAccount(contractAccountAssembler.mapBrief(contractAccountEntity, new ContractAccountSummary()));
        }

        if (input.getContract() != null){
            ContractEntity contractEntity = input.getContract();
            output.setContract(contractAssembler.map(contractEntity,new ContractSummary()));
        }

        return output;
    }

    @Override
    public List<CustomerRequestContract> mapBrief(List<CustomerRequestContractEntity> input) {
        if (input == null) {
            return null;
        }

        List<CustomerRequestContract> customerRequestContracts = new ArrayList<>();

        input.forEach(i -> customerRequestContracts.add(mapBrief(i, new CustomerRequestContract())));

        return customerRequestContracts;
    }

}
