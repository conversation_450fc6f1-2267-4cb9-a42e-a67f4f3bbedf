package sk.spp.nzp.be.controller;

import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.customeraccess.login.PreLoginChallenge;
import sk.spp.nzp.be.api.customeraccess.login.PreLoginToken;
import sk.spp.nzp.be.service.login.PreLoginService;

import javax.validation.Valid;

@RestController
@RequestMapping("/customers/login")
@Validated
public class CustomerLoginController {

    private PreLoginService preLoginService;

    public CustomerLoginController(PreLoginService preLoginService) {
        this.preLoginService = preLoginService;
    }

    @Log
    @LogParam
    @PostMapping(value = "/prelogin", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public PreLoginToken preLogin(@LogParam("request") @Valid @RequestBody PreLoginChallenge preLoginChallenge) {
        return preLoginService.preLogin(preLoginChallenge);
    }
}
