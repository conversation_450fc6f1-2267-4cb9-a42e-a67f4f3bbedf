package sk.spp.nzp.be.reporting.entitymappers;

import sk.spp.nzp.be.reporting.entitymappers.columns.ConsumptionColumns;
import sk.spp.nzp.commons.api.customerconsumption.ConsumptionValue;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.model.customerprofile.DeliveryPointEntity;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class ConsumptionMapper implements AbstractDtoMapper<ConsumptionValue> {

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("dd.MM.yyyy");
    private static final DateTimeFormatter TIME_FORMAT = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm:ss");

    private final List<ConsumptionColumns> columnsNames;

    private final DeliveryPointEntity deliveryPoint;

    public ConsumptionMapper(List<String> columns, DeliveryPointEntity deliveryPoint) {
        this.columnsNames = columns.stream()
                .map(this::getColumn)
                .collect(Collectors.toList());

        this.deliveryPoint = deliveryPoint;
    }

    private ConsumptionColumns getColumn(String name) {
        return Optional.ofNullable(name)
                .map(ConsumptionColumns::getByName)
                .orElseThrow(() -> new ApiException(ErrorCode.INVALID_COLUMN_NAME));
    }

    public Object[] map(ConsumptionValue consumption) {
        Object[] output = new Object[columnsNames.size()];

        for (int i = 0; i < columnsNames.size(); i++) {
            switch (columnsNames.get(i)) {

                case VALUE:
                case POWER:
                    output[i] = consumption.getValue();
                    break;

                case DATE:
                    output[i] = getPeriod(consumption, true, DATE_FORMAT);
                    break;

                case TIME_FROM:
                    output[i] = getPeriod(consumption, true, TIME_FORMAT);
                    break;

                case EIC_OOM:
                    output[i] = deliveryPoint.getEic();
                    break;

                case FROM:
                    output[i] = getPeriod(consumption, true, DATE_TIME_FORMAT);
                    break;

                case TO:
                    output[i] = getPeriod(consumption, false, DATE_TIME_FORMAT);
                    break;

                case UPDATED:
                    output[i] = Optional.ofNullable(consumption.getUpdatedAt())
                            .map(DATE_TIME_FORMAT::format)
                            .orElse(null);
                    break;
            }
        }
        return output;
    }

    private String getPeriod(ConsumptionValue consumption, boolean from, DateTimeFormatter formatter) {
        return Optional.ofNullable(consumption.getPeriod())
                .map(period -> from ? period.getFrom() : period.getTo())
                .map(formatter::format)
                .orElse(null);
    }

}