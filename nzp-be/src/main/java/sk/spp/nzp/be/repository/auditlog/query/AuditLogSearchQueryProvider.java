package sk.spp.nzp.be.repository.auditlog.query;

import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.api.auditlog.AuditLogSearch;
import sk.spp.nzp.be.api.common.Sorting;
import sk.spp.nzp.be.api.common.SortingAttribute;
import sk.spp.nzp.be.repository.common.AbstractQueryProvider;
import sk.spp.nzp.commons.model.audit.AuditLogEntity;
import sk.spp.nzp.commons.utils.Expressions;
import sk.spp.nzp.commons.utils.FTSearchUtils;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.List;

import static sk.spp.nzp.commons.model.audit.QAuditLogEntity.auditLogEntity;

public class AuditLogSearchQueryProvider extends AbstractQueryProvider<AuditLogEntity> {

    private AuditLogSearch adminAuditLogSearch;
    private boolean employeeLogs;
    private boolean customerLogs;
    private boolean isEmployeeVisible;
    private String employeePrincipalLogin;
    
    public AuditLogSearchQueryProvider(AuditLogSearch adminAuditLogSearch, boolean customerLogs, boolean employeeLogs, boolean isEmployeeVisible, String employeePrincipalLogin) {
        
        this.adminAuditLogSearch = adminAuditLogSearch;
        this.employeeLogs = employeeLogs;
        this.customerLogs = customerLogs;
        this.isEmployeeVisible = isEmployeeVisible;
        this.employeePrincipalLogin = employeePrincipalLogin;
    }

    @Override
    public JPAQuery<AuditLogEntity> getQuery(EntityManager em) {
        
        JPAQuery<AuditLogEntity> query = new JPAQuery<AuditLogEntity>(em).from(auditLogEntity);
        
        
        BooleanExpression createdAtExp = null;
        
        if(adminAuditLogSearch.getCreatedAt()!= null) {
            createdAtExp = auditLogEntity.createdAt.between(adminAuditLogSearch.getCreatedAt().getFrom(), adminAuditLogSearch.getCreatedAt().getTo());
        }
        
        
        BooleanExpression codesExp = null;
        
        if(adminAuditLogSearch.getCodes() != null) {
            codesExp = auditLogEntity.code.in(adminAuditLogSearch.getCodes());
        }

        BooleanExpression createdByExp = getCreatedByExp();
        BooleanExpression relatedCustomerExp = getRelatedCustomerExp();
        BooleanExpression businessPartnerExp = getBusinessPartnerExp();
        BooleanExpression entityExp = getEntityExp();
        BooleanExpression kindExp = getKindExp();
        BooleanExpression permissionExp = getPermissionExp();

        query = query.where(permissionExp, createdAtExp, codesExp, createdByExp, businessPartnerExp, entityExp, relatedCustomerExp, kindExp).orderBy(getOrderBy());
        
        return query;
    }

    private BooleanExpression getPermissionExp() {
        if (customerLogs || employeeLogs) {
            if(customerLogs && employeeLogs) {
                return null;
            }
            BooleanExpression expr = auditLogEntity.employeeLogin.eq(employeePrincipalLogin);
            if(!customerLogs) {
                return expr.or(auditLogEntity.relatedCustomerAccount().isNull());
            }
            return expr.or(auditLogEntity.relatedCustomerAccount().isNotNull());
        }
        return com.querydsl.core.types.dsl.Expressions.TRUE.isFalse();
    }

    private BooleanExpression getKindExp() {
        if(adminAuditLogSearch.getKind() == null) {
            return null;
        }
        BooleanExpression output = null;
        switch (adminAuditLogSearch.getKind()){
            case CUSTOMER:
                output = auditLogEntity.relatedCustomerAccount().isNotNull();
                break;
            case CUSTOMER_ONLY:
                output = auditLogEntity.relatedCustomerAccount().isNotNull().and(
                        auditLogEntity.employeeLogin.isNull()
                );
                break;
            case CUSTOMER_EMPLOYEE:
                output = auditLogEntity.relatedCustomerAccount().isNotNull().and(
                        auditLogEntity.employeeLogin.isNotNull()
                );
                break;
            case EMPLOYEE:
                output = auditLogEntity.employeeLogin.isNotNull();
                break;
            case EMPLOYEE_ONLY:
                output = auditLogEntity.employeeLogin.isNotNull().and(
                        auditLogEntity.relatedCustomerAccount().isNull()
                );
                break;
        }
        return output;
    }

    private BooleanExpression getEntityExp() {
        if(adminAuditLogSearch.getEntityFt() != null && Expressions.notEmpty(adminAuditLogSearch.getEntityFt().getFt())) {
            String searchable = FTSearchUtils.createSearchable(adminAuditLogSearch.getEntityFt().getFt());
            return auditLogEntity.entityReference.startsWith(searchable).or(auditLogEntity.entityItem.startsWithIgnoreCase(searchable));
        }
        return null;
    }

    private BooleanExpression getBusinessPartnerExp() {
        if(adminAuditLogSearch.getBusinessPartnerFt() != null && Expressions.notEmpty(adminAuditLogSearch.getBusinessPartnerFt().getFt())) {
            String searchable = FTSearchUtils.createSearchable(adminAuditLogSearch.getBusinessPartnerFt().getFt());
            return auditLogEntity.businessPartnerExternalId.eq(searchable).or(
                    auditLogEntity.businessPartnerFt.startsWith(searchable)
            );
        }
        return null;
    }

    private BooleanExpression getRelatedCustomerExp() {
        if (adminAuditLogSearch.getRelatedCustomerAccountUuid() != null){
            return auditLogEntity.relatedCustomerAccount().id.eq(adminAuditLogSearch.getRelatedCustomerAccountUuid());
        }else{
            if(adminAuditLogSearch.getRelatedCustomerAccountFt() != null && Expressions.notEmpty(adminAuditLogSearch.getRelatedCustomerAccountFt().getFt())) {
                String searchableOrderSwitched = null;
                // user can enter firstName+lastName and also lastName+firstName
                if (adminAuditLogSearch.getRelatedCustomerAccountFt().getFt().contains(" ")) {
                    String[] searchStrings = adminAuditLogSearch.getRelatedCustomerAccountFt().getFt().strip().split(" ", 2);
                    // if the search string would not contain 2 strings after splitting, no need to switch order of strings
                    if (searchStrings.length == 2) {
                        StringBuilder searchableSwitchedOrderBuilder = new StringBuilder();
                        searchableSwitchedOrderBuilder.append(searchStrings[1]);
                        searchableSwitchedOrderBuilder.append(" ");
                        searchableSwitchedOrderBuilder.append(searchStrings[0]);
                        searchableOrderSwitched = FTSearchUtils.createSearchable(searchableSwitchedOrderBuilder.toString());
                    }
                }

                String searchable = FTSearchUtils.createSearchable(adminAuditLogSearch.getRelatedCustomerAccountFt().getFt());
                BooleanExpression output = auditLogEntity.relatedCustomerAccountEmail.startsWithIgnoreCase(searchable).or(
                        auditLogEntity.relatedCustomerAccountFt.startsWith(searchable)
                );

                if (searchableOrderSwitched != null) {
                    output = output.or(auditLogEntity.relatedCustomerAccountFt.startsWith(searchableOrderSwitched).or(
                            auditLogEntity.relatedCustomerAccountEmail.startsWithIgnoreCase(searchableOrderSwitched)));
                }

                return output;
            }
        }
        return null;
    }

    private BooleanExpression getCreatedByExp() {
        if(adminAuditLogSearch.getCreatedByFt() != null && Expressions.notEmpty(adminAuditLogSearch.getCreatedByFt().getFt())) {
            String searchableOrderSwitched = null;
            // user can enter firstName+lastName and also lastName+firstName
            if (adminAuditLogSearch.getCreatedByFt().getFt().contains(" ")) {
                String[] searchStrings = adminAuditLogSearch.getCreatedByFt().getFt().strip().split(" ", 2);
                // if the search string would not contain 2 strings after splitting, no need to switch order of strings
                if (searchStrings.length == 2) {
                    StringBuilder searchableSwitchedOrderBuilder = new StringBuilder();
                    searchableSwitchedOrderBuilder.append(searchStrings[1]);
                    searchableSwitchedOrderBuilder.append(" ");
                    searchableSwitchedOrderBuilder.append(searchStrings[0]);
                    searchableOrderSwitched = FTSearchUtils.createSearchable(searchableSwitchedOrderBuilder.toString());
                }
            }

            String searchable = FTSearchUtils.createSearchable(adminAuditLogSearch.getCreatedByFt().getFt());
            BooleanExpression output = auditLogEntity.loggedCustomerAccountFt.startsWith(searchable).or(
                    auditLogEntity.loggedCustomerAccountEmail.startsWithIgnoreCase(searchable)
            );

            if (searchableOrderSwitched != null) {
                output = output.or(auditLogEntity.loggedCustomerAccountFt.startsWith(searchableOrderSwitched).or(
                        auditLogEntity.loggedCustomerAccountEmail.startsWithIgnoreCase(searchableOrderSwitched)));
            }

            if(isEmployeeVisible || employeeLogs || employeePrincipalLogin.equalsIgnoreCase(searchable)){
               BooleanExpression employee= auditLogEntity.employeeFt.startsWith(searchable)
                        .or(auditLogEntity.employeeEmail.startsWithIgnoreCase(searchable))
                        .or(auditLogEntity.employeeLogin.startsWithIgnoreCase(searchable));
               return output.or(employee);
            }

            return output;
        }
        return null;
    }
/*
    private BooleanExpression getCreatedByExp() {
        
        BooleanExpression output = null;
        
        if(adminAuditLogSearch.getCreatedBy() == null) {
            return output;
        }
            
        if(AuditLogKind.CUSTOMER.equals(adminAuditLogSearch.getCreatedBy().getType())
                || AuditLogKind.CUSTOMER_ONLY.equals(adminAuditLogSearch.getCreatedBy().getType())) {
            
            if(StringUtils.isNotBlank(adminAuditLogSearch.getCreatedBy().getUuid())) {
                
                output = auditLogEntity.relatedCustomerAccount().id.eq(UUID.fromString(adminAuditLogSearch.getCreatedBy().getUuid()));
                
            } else {
                
                output = auditLogEntity.relatedCustomerAccount().isNotNull();
                
            }

            // only customers (not 'assistence with employee')
            if ( AuditLogKind.CUSTOMER_ONLY.equals(adminAuditLogSearch.getCreatedBy().getType())) {
                output = output.and(auditLogEntity.employee().login.isNull());
            }
        } else if(AuditLogKind.EMPLOYEE.equals(adminAuditLogSearch.getCreatedBy().getType())
            || AuditLogKind.EMPLOYEE_ONLY.equals(adminAuditLogSearch.getCreatedBy().getType())) {

            boolean hasPermission = isAdmin || (StringUtils.isNotBlank(adminAuditLogSearch.getCreatedBy().getUuid()) && adminAuditLogSearch.getCreatedBy().getUuid().equals(employeePrincipalLogin));

            if(StringUtils.isNotBlank(adminAuditLogSearch.getCreatedBy().getUuid())) {

                output = auditLogEntity.employee().login.eq(adminAuditLogSearch.getCreatedBy().getUuid());

            } else {

                output = auditLogEntity.employee().login.isNotNull();
            }

            if(!hasPermission) {

                output = output.and(auditLogEntity.loggedCustomerAccount().isNotNull());

            } else if (AuditLogKind.EMPLOYEE_ONLY.equals(adminAuditLogSearch.getCreatedBy().getType())) {

                output = output.and(auditLogEntity.loggedCustomerAccount().id.isNull());
            }
            
        } else {
            
            if(StringUtils.isNotBlank(adminAuditLogSearch.getCreatedBy().getUuid())) {
                BooleanExpression customerUuidExp = null;

                if(isUUID(adminAuditLogSearch.getCreatedBy().getUuid())) {
                    customerUuidExp = auditLogEntity.relatedCustomerAccount().id.eq(UUID.fromString(adminAuditLogSearch.getCreatedBy().getUuid()));
                }
                BooleanExpression employeeLoginExp = auditLogEntity.employee().login.eq(adminAuditLogSearch.getCreatedBy().getUuid());
                BooleanExpression notAdminExp = auditLogEntity.loggedCustomerAccount().isNotNull();

                output = isAdmin || adminAuditLogSearch.getCreatedBy().getUuid().equals(employeePrincipalLogin)
                        ? employeeLoginExp.or(customerUuidExp)
                        : (employeeLoginExp.and(notAdminExp)).or(customerUuidExp);
            }
            
        }
        
        return output;
    }

    private boolean isUUID(String string) {
        try {
            UUID.fromString(string);
            return true;
        } catch (Exception ex) {
            return false;
        }
    }
    
    private BooleanExpression getEntityByExp() {
        
        BooleanExpression output = null;
        
        if(adminAuditLogSearch.getEntity() == null) {
            return output;
        }
        
        if(StringUtils.isNotBlank(adminAuditLogSearch.getEntity().getId())) {
            
            output = auditLogEntity.entityId.eq(adminAuditLogSearch.getEntity().getId());
            
        }
        
        if(adminAuditLogSearch.getEntity().getType() != null) {
            
            if(output == null) {
                
                output = auditLogEntity.entityType.eq(adminAuditLogSearch.getEntity().getType());
                
            } else {
                
                output = output.and(auditLogEntity.entityType.eq(adminAuditLogSearch.getEntity().getType()));
                
            }
        }
        
        return output;
    }*/

    private OrderSpecifier<?>[] getOrderBy() {
        
        if(adminAuditLogSearch.getPaging() == null || adminAuditLogSearch.getPaging().getSort() == null || adminAuditLogSearch.getPaging().getSort().isEmpty()) {

            return getDefaultOrderBy();
        }
        
        List<OrderSpecifier<?>> sortingList = new ArrayList<>();
        
        for(Sorting sorting: adminAuditLogSearch.getPaging().getSort()) {
            
            if(SortingAttribute.AUDIT_LOG_CREATED_AT.name().contentEquals(sorting.getAttribute()) || SortingAttribute.AUDIT_LOG_CREATED_AT.getCamelCaseName().contentEquals(sorting.getAttribute())) {
                
                sortingList.add(getOrderSpecifier(sorting, auditLogEntity.createdAt));
                
            } else if(SortingAttribute.AUDIT_LOG_TYPE.name().contentEquals(sorting.getAttribute()) || SortingAttribute.AUDIT_LOG_CREATED_AT.getCamelCaseName().contentEquals(sorting.getAttribute())) {
                
                sortingList.add(getOrderSpecifier(sorting, auditLogEntity.entityType));
                
            } 
        }
        
        if(sortingList.isEmpty()) {
            
            return getDefaultOrderBy();
        }
        
        OrderSpecifier<?>[] output = new OrderSpecifier<?>[sortingList.size()];
        
        return sortingList.toArray(output);
    }
    
    private OrderSpecifier<?>[] getDefaultOrderBy() {
        
        OrderSpecifier<?>[] output = new OrderSpecifier<?>[1];
        output[0] = auditLogEntity.createdAt.asc();
        
        return output;
    }

}
