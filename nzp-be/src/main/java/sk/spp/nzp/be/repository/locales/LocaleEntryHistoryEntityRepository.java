package sk.spp.nzp.be.repository.locales;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sk.spp.nzp.commons.model.locales.LocaleEntryHistoryEntity;

import java.util.UUID;
import java.util.stream.Stream;

@Repository
public interface LocaleEntryHistoryEntityRepository extends JpaRepository<LocaleEntryHistoryEntity, UUID> {

    @Query("SELECT COUNT(h) FROM LocaleEntryHistoryEntity h " +
            "WHERE h.locale = LOWER(:locale)")
    int count(
            @Param("locale") String locale);

    @Query("SELECT h FROM LocaleEntryHistoryEntity h " +
            "WHERE h.locale = LOWER(:locale) " +
            "ORDER BY h.createdAt ASC")
    Stream<LocaleEntryHistoryEntity> streamAll(
            @Param("locale") String locale);

    @Modifying
    @Query("DELETE FROM LocaleEntryHistoryEntity h " +
            "WHERE h.locale = LOWER(:locale)")
    int delete(
            @Param("locale") String locale);

}