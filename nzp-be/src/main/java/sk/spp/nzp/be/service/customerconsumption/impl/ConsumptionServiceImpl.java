package sk.spp.nzp.be.service.customerconsumption.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.assembler.customerprofile.DeliveryPointAssembler;
import sk.spp.nzp.be.converters.MeterReadingUnitsConverter;
import sk.spp.nzp.be.reporting.DataExporter;
import sk.spp.nzp.be.reporting.IteratorWrapper;
import sk.spp.nzp.be.reporting.entitymappers.ConsumptionMapper;
import sk.spp.nzp.be.reporting.entitymappers.columns.ConsumptionColumns;
import sk.spp.nzp.be.reporting.impl.CollectionIteratorWrapper;
import sk.spp.nzp.be.service.codelist.model.CodeListQuery;
import sk.spp.nzp.be.service.codelist.provider.GenericCodeListProvider;
import sk.spp.nzp.be.service.customerconsumption.ConsumptionAggregationService;
import sk.spp.nzp.be.service.customerconsumption.ConsumptionService;
import sk.spp.nzp.be.service.customerprofile.DeliveryPointFactConsumptionService;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.api.customerconsumption.*;
import sk.spp.nzp.commons.api.customerprofile.enums.DeliveryPointType;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.api.reporting.DataReportFormat;
import sk.spp.nzp.commons.context.DeliveryPointHistoryContext;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.exception.NotFoundException;
import sk.spp.nzp.commons.model.customerprofile.ContractEntity;
import sk.spp.nzp.commons.model.customerprofile.DeliveryPointEntity;
import sk.spp.nzp.commons.repository.customerconsumption.IntervalMeterReadingEeConsumptionRepository;
import sk.spp.nzp.commons.repository.customerprofile.ContractEntityRepository;
import sk.spp.nzp.commons.service.common.HistoryContextProvider;
import sk.spp.nzp.commons.service.common.impl.HistoryPeriod;
import sk.spp.nzp.commons.service.customerconsumption.DeliveryPointInfoService;
import sk.spp.nzp.commons.service.customerconsumption.IntervalMeterReadingConsumptionService;
import sk.spp.nzp.commons.service.security.SecurityChecker;

import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.AbstractMap.SimpleEntry;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ConsumptionServiceImpl implements ConsumptionService {

    private ContractEntityRepository contractEntityRepository;
    private DeliveryPointAssembler deliveryPointAssembler;
    private IntervalMeterReadingConsumptionService intervalMeterReadingConsumptionService;
    private IntervalMeterReadingEeConsumptionRepository intervalMeterReadingEeConsumptionRepository;
    private SecurityChecker securityChecker;
    private MeterReadingUnitsConverter meterReadingUnitsConverter;
    private DeliveryPointInfoService deliveryPointInfoService;
    private DeliveryPointFactConsumptionService deliveryPointFactConsumptionService;
    private HistoryContextProvider historyContextProvider;
    private DataExporter dataExporter;
    private GenericCodeListProvider genericCodeListProvider;
    private ConsumptionAggregationService consumptionAggregationService;

    public ConsumptionServiceImpl(DeliveryPointAssembler deliveryPointAssembler,
                                  IntervalMeterReadingConsumptionService intervalMeterReadingConsumptionService,
                                  IntervalMeterReadingEeConsumptionRepository intervalMeterReadingEeConsumptionRepository,
                                  SecurityChecker securityChecker,
                                  MeterReadingUnitsConverter meterReadingUnitsConverter,
                                  DeliveryPointInfoService deliveryPointInfoService,
                                  DeliveryPointFactConsumptionService deliveryPointFactConsumptionService,
                                  HistoryContextProvider historyContextProvider,
                                  ContractEntityRepository contractEntityRepository,
                                  DataExporter dataExporter,
                                  GenericCodeListProvider genericCodeListProvider,
                                  ConsumptionAggregationService consumptionAggregationService) {

        super();
        this.deliveryPointAssembler = deliveryPointAssembler;
        this.intervalMeterReadingConsumptionService = intervalMeterReadingConsumptionService;
        this.intervalMeterReadingEeConsumptionRepository = intervalMeterReadingEeConsumptionRepository;
        this.securityChecker = securityChecker;
        this.meterReadingUnitsConverter = meterReadingUnitsConverter;
        this.deliveryPointInfoService = deliveryPointInfoService;
        this.deliveryPointFactConsumptionService = deliveryPointFactConsumptionService;
        this.historyContextProvider = historyContextProvider;
        this.contractEntityRepository = contractEntityRepository;
        this.dataExporter = dataExporter;
        this.genericCodeListProvider = genericCodeListProvider;
        this.consumptionAggregationService = consumptionAggregationService;
    }

    @Log
    @LogParam
    @Override
    @Transactional
    @SuppressWarnings("unchecked")
    public void getByContractIdExport(
            @LogParam("contractId") String contractId,
            @LogParam("consumptionSearch") ConsumptionSearch consumptionSearch,
            @LogParam("format") DataReportFormat format,
            @LogParam("columnNames") List<String> columnNames,
            OutputStream outputStream) {

        final ContractEntity contract = contractEntityRepository.findById(contractId).orElseThrow(NotFoundException::new);
        final DeliveryPointHistoryContext historyContext = historyContextProvider.createDeliveryPointHistoryContext(contract, HistoryPeriod.CURRENT);
        final DeliveryPointEntity deliveryPoint = historyContext.getDeliveryPoint();

        // Process columns names
        if (columnNames == null || columnNames.isEmpty()) {
            final boolean isEE = ConsumptionType.INTERVAL_METER_READING.equals(consumptionSearch.getType())
                    && DeliveryPointType.EE.equals(deliveryPoint.getType())
                    && StringUtils.isNotBlank(deliveryPoint.getEic());

            if (isEE) {
                columnNames = toList(ConsumptionColumns.EIC_OOM, ConsumptionColumns.FROM, ConsumptionColumns.TO, ConsumptionColumns.POWER, ConsumptionColumns.UPDATED);

            } else {
                columnNames = toList(ConsumptionColumns.DATE, ConsumptionColumns.VALUE);
            }
        }

        // Get consumption
        consumptionSearch.setStatistics(false);

        final ConsumptionSearchResponse consumptions = getByContractId(contractId, consumptionSearch);
        final List<ConsumptionValue> values = Optional.of(consumptions)
                .map(ConsumptionSearchResponse::getTariffs)
                .map(tarrifs -> tarrifs.get(0))
                .map(ConsumptionTariff::getValues)
                .orElse(List.of());

        // Export consumption
        final IteratorWrapper iterator = new CollectionIteratorWrapper<>(values, new ConsumptionMapper(columnNames, deliveryPoint));

        columnNames = processColumnNames(columnNames,
                new SimpleEntry<>(ConsumptionColumns.POWER, val -> String.format("%s (%s)", val, consumptions.getUnits().getId())));

        switch (format) {
            case XLSX:
                dataExporter.exportXlsx(iterator, columnNames, outputStream);
                break;

            case CSV:
                dataExporter.exportCsv(iterator, columnNames, outputStream);
                break;

            default:
                throw new ApiException(ErrorCode.GENERIC_VALIDATION_ERROR,
                        String.format("Unsupported format [format=%s]", format));
        }
    }

    private List<String> toList(Enum<?> ... enums) {
        return Arrays.stream(enums)
                .map(Enum::name)
                .collect(Collectors.toList());
    }

    private List<String> processColumnNames(List<String> columnNames, Entry<ConsumptionColumns, Function<String, String>> ... alterFunctions) {

        final Map<ConsumptionColumns, Function<String, String>> functionsMap = Arrays.stream(alterFunctions)
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));

        List<String> resultList = new ArrayList<>();

        for (String columnName : columnNames) {
            final ConsumptionColumns consumptionColumn = ConsumptionColumns.getByName(columnName);
            final String localized = getCodeListItemByName(consumptionColumn.name()).getName();

            resultList.add(Optional.ofNullable(functionsMap.get(consumptionColumn))
                    .map(func -> func.apply(localized))
                    .orElse(localized));
        }

        return resultList;
    }

    private CodeListItem getCodeListItemByName(String name) {
        CodeListQuery query = new CodeListQuery();
        query.setCode(name);
        return genericCodeListProvider.get(ConsumptionColumns.GENERIC_CODE_LIST_TYPE, query);
    }

    @Log
    @LogParam
    @Transactional
    @Override
    public ConsumptionSearchResponse getByContractId(
            @LogParam("contractId") String contractId,
            @LogParam("consumptionSearch") ConsumptionSearch consumptionSearch) {

        final ContractEntity contract = contractEntityRepository.findById(contractId).orElseThrow(NotFoundException::new);
        final DeliveryPointHistoryContext historyContext = historyContextProvider.createDeliveryPointHistoryContext(contract, HistoryPeriod.FULL);
        final DeliveryPointEntity deliveryPoint = historyContext.getDeliveryPoint();

        securityChecker.checkAccess(contract);

        ConsumptionSearchResponse output = new ConsumptionSearchResponse();
        output.setInterval(consumptionSearch.getInterval());
        output.setReadAt(consumptionSearch.getReadAt());
        output.setReservedValues(deliveryPointAssembler.map(deliveryPoint, new ConsumptionReservedValues(), historyContext));

        if(ConsumptionType.INTERVAL_METER_READING.equals(consumptionSearch.getType())
                && DeliveryPointType.ZP.equals(deliveryPoint.getType())) {

            List<ConsumptionValue> consumptionValues = intervalMeterReadingConsumptionService.getByConsumptionSearch(consumptionSearch, deliveryPoint, historyContext);
            output.addTariff(new ConsumptionTariff(consumptionValues));

            if(consumptionSearch.isStatistics()) {
                output.setStatistics(getStatistics(output.getTariffs()));
            }
            meterReadingUnitsConverter.convert(output, deliveryPoint, consumptionSearch);

        } else if(ConsumptionType.INTERVAL_METER_READING.equals(consumptionSearch.getType())
                && DeliveryPointType.EE.equals(deliveryPoint.getType())
                && StringUtils.isNotBlank(deliveryPoint.getEic())) {

            List<ConsumptionValue> consumptionValues = intervalMeterReadingEeConsumptionRepository.getByConsumptionSearch(consumptionSearch, deliveryPoint, historyContext);
            output.addTariff(new ConsumptionTariff(consumptionValues));

            if(consumptionSearch.isStatistics()) {
                output.setStatistics(intervalMeterReadingEeConsumptionRepository.getConsumptionStatistics(consumptionSearch, deliveryPoint, historyContext));
            }
            meterReadingUnitsConverter.convert(output, deliveryPoint, consumptionSearch);

        } else if(ConsumptionType.DELIVERY_POINT_FACT.equals(consumptionSearch.getType())) {

            output.setTariffs(deliveryPointFactConsumptionService.getConsumption(consumptionSearch, deliveryPoint, historyContext));

            if(consumptionSearch.isStatistics()) {
                output.setStatistics(getStatistics(output.getTariffs()));
            }
        }

        output = consumptionAggregationService.aggregate(output, consumptionSearch);

        if (!output.isResultsAggregated()) {
            sortTariffsAsc(output);
        }
        return output;
    }

    private ConsumptionStatistics getStatistics(List<ConsumptionTariff> values) {

        BigDecimal total = BigDecimal.ZERO;

        ConsumptionStatistics output = new ConsumptionStatistics();
        output.setAverage(total);

        if(values == null || values.isEmpty()) {

            return output;
        }

        int count = 0;

        for(ConsumptionTariff ct: values) {
            for(ConsumptionValue v : ct.getValues()) {

                count++;
                total = total.add(v.getValue() == null ? BigDecimal.ZERO : v.getValue());
            }
        }

        output.setAverage(total.divide(BigDecimal.valueOf(count), 2, RoundingMode.HALF_UP));

        return output;
    }

    private void sortTariffsAsc(ConsumptionSearchResponse output) {
        if (output.getTariffs() != null) {
            Comparator<ConsumptionValue> comparePeriodFrom = Comparator.comparing(o -> o.getPeriod().getFrom());

            output.getTariffs().stream()
                    .map(ConsumptionTariff::getValues)
                    .forEach(t -> t.sort(comparePeriodFrom));
        }
    }

}