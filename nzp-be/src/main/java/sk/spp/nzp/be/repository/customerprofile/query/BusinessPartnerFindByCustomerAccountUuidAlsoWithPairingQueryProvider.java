package sk.spp.nzp.be.repository.customerprofile.query;

import java.util.UUID;
import javax.persistence.EntityManager;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerSearch;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.api.customersharing.enums.OwnershipType;
import sk.spp.nzp.commons.model.customerprofile.BusinessPartnerExtendedOwnershipViewEntity;
import sk.spp.nzp.commons.model.customerprofile.QBusinessPartnerExtendedOwnershipViewEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

public class BusinessPartnerFindByCustomerAccountUuidAlsoWithPairingQueryProvider implements QueryDslProvider<BusinessPartnerExtendedOwnershipViewEntity> {

    private final UUID customerAccountUuid;
    private final BusinessPartnerSearch businessPartnerSearch;
    private final BusinessPartnerQueue businessPartnerQueue;
    
    public BusinessPartnerFindByCustomerAccountUuidAlsoWithPairingQueryProvider(
            UUID customerAccountUuid, BusinessPartnerSearch businessPartnerSearch, BusinessPartnerQueue businessPartnerQueue) {
        
        this.customerAccountUuid = customerAccountUuid;
        this.businessPartnerSearch = businessPartnerSearch;
        this.businessPartnerQueue = businessPartnerQueue;
    }

    @Override
    public JPAQuery<BusinessPartnerExtendedOwnershipViewEntity> getQuery(EntityManager em) {
        return findByCustomerAccountUuidQuery(em);
    }
    
    public JPAQuery<BusinessPartnerExtendedOwnershipViewEntity> findByCustomerAccountUuidQuery(EntityManager em) {
        QBusinessPartnerExtendedOwnershipViewEntity bpExtendedOwnershipView = QBusinessPartnerExtendedOwnershipViewEntity.businessPartnerExtendedOwnershipViewEntity;

        BooleanExpression customerExp = null;
        if (customerAccountUuid != null) {
            customerExp = bpExtendedOwnershipView.customerAccount().id.eq(customerAccountUuid);
        }

        BooleanExpression ownerExp = null;
        if (!Boolean.TRUE.equals(businessPartnerSearch.getShared())) {

            ownerExp = bpExtendedOwnershipView.type.isNull()
                    .or(bpExtendedOwnershipView.type.eq(OwnershipType.OWNER));
        }

        BooleanExpression queueExp = null;
        if (businessPartnerQueue != null) {
            queueExp = bpExtendedOwnershipView.businessPartner().queue.eq(businessPartnerQueue);
        }

        return new JPAQuery<BusinessPartnerExtendedOwnershipViewEntity>(em)
                .from(bpExtendedOwnershipView)
                .where(customerExp, ownerExp, queueExp);
    }
}