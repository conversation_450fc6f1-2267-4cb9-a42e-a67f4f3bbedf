package sk.spp.nzp.be.assembler.notification;

import sk.spp.nzp.be.api.notification.CustomerNotificationSendStatus;
import sk.spp.nzp.commons.model.notification.CustomerNotificationSendStatusEntity;

import java.util.Collection;
import java.util.List;

public interface CustomerNotificationSendStatusAssembler {

    CustomerNotificationSendStatusEntity map(CustomerNotificationSendStatus input, CustomerNotificationSendStatusEntity output);

    CustomerNotificationSendStatus map(CustomerNotificationSendStatusEntity input, CustomerNotificationSendStatus output);

    List<CustomerNotificationSendStatus> map(Collection<CustomerNotificationSendStatusEntity> input);

}