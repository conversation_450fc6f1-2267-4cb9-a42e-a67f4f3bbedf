package sk.spp.nzp.be.assembler.customerrequest.impl;

import org.apache.commons.lang3.SerializationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import sk.spp.nzp.commons.model.customerprofile.enums.CompletionView;
import sk.spp.nzp.be.service.customerrequest.CustomerRequestProcessorService;
import sk.spp.nzp.be.assembler.customerrequest.CustomerRequestContentAssembler;
import sk.spp.nzp.be.assembler.customerrequest.support.RequestContentVisibilityProcessor;
import sk.spp.nzp.commons.api.customerrequest.request.base.CustomerRequestContent;
import sk.spp.nzp.commons.context.holder.RequestContextHolder;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestEntity;

import java.util.List;
import java.util.Optional;

@Component
public class CustomerRequestContentAssemblerImpl implements CustomerRequestContentAssembler {

    @Autowired
    private List<RequestContentVisibilityProcessor> visibilityProcessors;

    @Autowired
    private CustomerRequestProcessorService customerRequestProcessorService;

    @Autowired
    private RequestContextHolder requestContextHolder;

    @Override
    public CustomerRequestContent getFilteredContent(CustomerRequestContent entityContent, CompletionView completionView) {

        if (entityContent == null) {
            return null;
        }

        final CustomerRequestContent dtoContent = Optional.of(entityContent)
                .map(SerializationUtils::clone)
                .map(this::wrapWithEntity)
                .map(e -> customerRequestProcessorService.updateContentCodeListsToLocale(e, requestContextHolder.getLocale()))
                .map(CustomerRequestEntity::getContent)
                .orElseThrow();

        final RequestContentVisibilityProcessor applicableProcessor = getProcessor(dtoContent);
        return applicableProcessor.process(dtoContent, null, completionView);
    }

    @Override
    public CustomerRequestContent filterUpdateContent(CustomerRequestContent dtoContent, CustomerRequestContent currentEntityContent, CompletionView completionView) {

        final RequestContentVisibilityProcessor applicableProcessor = getProcessor(dtoContent);
        return applicableProcessor.process(dtoContent, currentEntityContent, completionView);
    }

    private CustomerRequestEntity wrapWithEntity(CustomerRequestContent content) {

        CustomerRequestEntity entity = new CustomerRequestEntity();
        entity.setContent(content);

        return entity;
    }

    private RequestContentVisibilityProcessor getProcessor(CustomerRequestContent content) {
        return visibilityProcessors.stream()
                .filter(p -> p.supports(content))
                .findFirst()
                .orElseThrow(); // Default processor is always supported
    }

}