package sk.spp.nzp.be.assembler.customeraccess.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customeraccess.ReCaptchaTokenResponse;
import sk.spp.nzp.be.assembler.customeraccess.ReCaptchaTokenAssembler;
import sk.spp.nzp.commons.model.customeraccess.ReCaptchaTokenEntity;

@Component
public class ReCaptchaTokenAssemblerImpl implements ReCaptchaTokenAssembler {


    @Override
    public ReCaptchaTokenResponse map(ReCaptchaTokenEntity input, ReCaptchaTokenResponse output) {
        if (input == null) {
            return output;
        }

        output.setToken(input.getId());
        output.setValidUntil(input.getValidUntil());
        output.setRemainingUsesCount(input.getRemainingUsesCount());

        return output;
    }
}
