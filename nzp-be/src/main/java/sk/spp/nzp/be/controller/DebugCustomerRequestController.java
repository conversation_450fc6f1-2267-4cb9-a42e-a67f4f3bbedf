package sk.spp.nzp.be.controller;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.swagger.v3.oas.annotations.Hidden;
import org.apache.commons.lang3.SerializationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.*;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.customerrequest.CustomerRequest;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestTemplate;
import sk.spp.nzp.be.repository.customerprofile.query.BusinessPartnerEntityRepository;
import sk.spp.nzp.be.repository.customerrequest.CustomerRequestTemplateRepository;
import sk.spp.nzp.be.service.customerrequest.CustomerRequestService;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.api.customerprofile.enums.TariffStatus;
import sk.spp.nzp.commons.api.customerprofile.enums.TariffType;
import sk.spp.nzp.commons.api.customerrequest.component.*;
import sk.spp.nzp.commons.api.customerrequest.enums.*;
import sk.spp.nzp.commons.api.customerrequest.request.*;
import sk.spp.nzp.commons.api.customerrequest.request.base.CustomerRequestContent;
import sk.spp.nzp.commons.enums.CodeListType;
import sk.spp.nzp.commons.enums.EntityType;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.model.audit.AuditLogEntity;
import sk.spp.nzp.commons.model.audit.enums.AuditLogCode;
import sk.spp.nzp.commons.model.codelist.GenericCodeListEntity;
import sk.spp.nzp.commons.model.customerprofile.TariffEntity;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestEntity;
import sk.spp.nzp.commons.repository.audit.AuditLogEntityRepository;
import sk.spp.nzp.commons.repository.customerprofile.ContractEntityRepository;
import sk.spp.nzp.commons.repository.customerprofile.DeliveryPointEntityRepository;
import sk.spp.nzp.commons.repository.customerprofile.TariffEntityRepository;
import sk.spp.nzp.commons.repository.customerrequest.CustomerRequestRepository;
import sk.spp.nzp.commons.service.codelist.GenericCodeListEntityService;
import sk.spp.nzp.commons.utils.Expressions;

import javax.annotation.PostConstruct;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

@Hidden
@RestController
@RequestMapping("/admin/debug/customer-requests")
@ConditionalOnProperty(value = "development.controller", havingValue = "true", matchIfMissing = false)
public class DebugCustomerRequestController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DebugCustomerRequestController.class);

    @Autowired
    private CustomerRequestService customerRequestService;

    @Autowired
    private CustomerRequestRepository customerRequestRepository;

    @Autowired
    private CustomerRequestTemplateRepository customerRequestTemplateRepository;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private BusinessPartnerEntityRepository businessPartnerEntityRepository;

    @Autowired
    private ContractEntityRepository contractEntityRepository;

    @Autowired
    private GenericCodeListEntityService codeListEntityService;

    @Autowired
    private DeliveryPointEntityRepository deliveryPointEntityRepository;

    @Autowired
    private TariffEntityRepository tariffEntityRepository;

    @Autowired
    private AuditLogEntityRepository auditLogEntityRepository;

    private ObjectMapper objectMapper;

    @PostConstruct
    public void init() {
        objectMapper = new ObjectMapper();
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    /**
     *
     */
    private TransactionTemplate createTransactionTemplate() {
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        return transactionTemplate;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER')")
    @PostMapping(value = "/test")
    public Map<String, Object> test(@LogParam("request") @RequestBody Request request,
                                    @RequestParam(defaultValue = "false", required = false) boolean skipValidations) {
        List<AbstractRequestContentDebugger> debuggers = new ArrayList<>();
        Map<String, Object> results = new LinkedHashMap<>();

        // register
        debuggers.add(new ZZU_ADEFRequestContentDebug(request));
        debuggers.add(new ZOP_ZPRequestContentDebug(request));
        debuggers.add(new ZOP_ZOUARequestContentDebug(request));
        debuggers.add(new ZOP_ZBURequestContentDebug(request));
        debuggers.add(new ZOP_USRequestContentDebug(request));
        debuggers.add(new ZOP_SRequestContentDebug(request));
        debuggers.add(new ZOP_PRequestContentDebug(request));
        debuggers.add(new ZOP_RSRequestContentDebug(request));
        debuggers.add(new ZOM_ZVPPRequestContentDebug(request));
        debuggers.add(new ZOM_ZVAPPPRequestContentDebug(request));
        debuggers.add(new ZOM_ZTPRequestContentDebug(request));
        debuggers.add(new ZOM_ZSPAPPPRequestContentDebug(request));
        debuggers.add(new ZOM_ZSERequestContentDebug(request));
        debuggers.add(new ZOM_ZOZODZPRequestContentDebug(request));
        debuggers.add(new ZOM_ZOVDFRequestContentDebug(request));
        debuggers.add(new ZOM_ZOUZRequestContentDebug(request));
        debuggers.add(new ZOM_ZOUSMRequestContentDebug(request));
        debuggers.add(new ZOM_ZOFMCRequestContentDebug(request));
        debuggers.add(new ZOM_ZODSRequestContentDebug(request));
        debuggers.add(new ZOM_ZAOMRequestContentDebug(request));
        debuggers.add(new ZOM_RPVFRequestContentDebug(request));
        debuggers.add(new ZOM_RFRequestContentDebug(request));
        debuggers.add(new ZOM_POZVRequestContentDebug(request));
        debuggers.add(new ZOM_POVZRequestContentDebug(request));
        debuggers.add(new ZOM_ORequestContentDebug(request));
        debuggers.add(new ZOM_DOSRequestContentDebug(request));
        debuggers.add(new ZOPZOUA_PRIVATE_RequestContentDebug(request));
        debuggers.add(new ZOPZOUA_COMPANY_RequestContentDebug(request));
        debuggers.add(new ZOM_PRequestContentDebug(request));
        debuggers.add(new ZOM_CERequestContentDebug(request));
        debuggers.add(new ZOM_ZODERequestContentDebug(request));
        debuggers.add(new ZOM_ZODPRequestContentDebug(request));
        debuggers.add(new ZOM_ZOPAOORequestContentDebug(request));

        // filter
        debuggers
                .removeIf(v -> request.getRequestCodes() != null && !request.getRequestCodes().contains(v.getRequestCode().name()));

        try {
            // prepare
            createTransactionTemplate().executeWithoutResult(tx -> {
                boolean result = debuggers
                        .stream()
                        .map(v->{
                            results.put(v.getName(), v.getResultMap());
                            v.prepare(skipValidations);
                            return v.hasError();
                        })
                        .reduce((r,e)->r |= e)
                        .orElse(false);

//                if ( result ) {
//                    throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR);
//                }
            });

            // process
            debuggers.stream()
                    .filter(v -> !v.hasError())
                    .forEach(AbstractRequestContentDebugger::process);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }

        results.put("hasError", debuggers.stream().anyMatch(AbstractRequestContentDebugger::hasError));
        results.put("complete", debuggers.stream().allMatch(v -> v.getResultMap().containsKey("sap-request") && v.getResultMap().containsKey("sap-response")));
        return results;
    }

    /**
     *
     */
    private interface RequestContentDebugger {
        <T extends RequestContentDebugger> T prepare(boolean skipValidations) throws Exception;
        <T extends RequestContentDebugger> T process() throws Exception;
    }

    /**
     *
     */
    private abstract class AbstractRequestContentDebugger implements RequestContentDebugger {

        // origin request
        protected Request request;

        // create CustomeRequest
        protected CustomerRequest customerRequest;

        // result map with request/response
        private final Map<String, Object> resultMap = new LinkedHashMap<>();

        public AbstractRequestContentDebugger(Request requestContentDebuggerRequest) {
            this.request = requestContentDebuggerRequest;

            // initialize
            this.resultMap.put("code", getRequestCode());
            this.resultMap.put("errors", new HashMap<>());
        }

        protected String getName() {
            return getClass().getSimpleName();
        }

        /**
         *
         */
        protected abstract CustomerRequestCode getRequestCode();
        protected abstract CustomerRequestContent createRequestContent();

        /**
         *
         */
        protected CustomerRequestContent prepareRequestContent(CustomerRequestContent customerRequestContent) {
            customerRequestContent.setContact(createContact());
            customerRequestContent.setNote("TEST_NOTE");
            customerRequestContent.setMetadata(createMetaData());
            customerRequestContent.addEntityReference(createEntityReference());
            return customerRequestContent;
        }

        /**
         *
         */
        protected EntityReference createEntityReference() {
            EntityReference entityReference = new EntityReference();
            entityReference.setContractAccountId(request.getCaId());
            entityReference.setContractId(request.getcId());
            entityReference.setDeliveryPointId(request.getDpId());

            return entityReference;
        }

        /**
         *
         */
        protected Metadata createMetaData() {
            Metadata result = new Metadata();
            result.setBusinessPartnerId(request.getBpId());
            result.setCustomerAccountId(request.getCustomerId().toString());
            return result;
        }


        /**
         *
         */
        protected Address createAddress() {
            Address result = new Address();
            result.setCity("CITY");
            result.setStreet("STREET");
            result.setNumber("1");
            result.setFloor("1");
            result.setFlatNumber("1");
            result.setPostalCode("000000");
            result.setCountry(getCodeListItem(CodeListType.COUNTRY, "SK"));
            return result;
        }

        /**
         *
         */
        protected ContractTerminationReason createContractTerminationReason() {
            ContractTerminationReason result = new ContractTerminationReason();
            result.setMeterRemoval(Boolean.TRUE);
            result.setNotOwnerOfPropertyDeclaration(Boolean.TRUE);
            result.setReason(getCodeListItem(CodeListType.CONTRACT_TERMINATION_REASON));
            return result;
        }

        /**
         *
         */
        protected TariffEe createTariffEe() {
            TariffEe result = new TariffEe();
            result.setChangeDate(LocalDate.now());
            result.setTariff(getTariff(TariffType.EE));
            return result;
        }


        /**
         *
         */
        protected TariffZp createTariffZp() {
            TariffZp result = new TariffZp();
            result.setChangeDate(LocalDate.now());
            result.setTariff(getTariff(TariffType.ZP));
            return result;
        }


        /**
         *
         */
        protected AdvancePayment createAdvancePayment() {
            AdvancePayment result = new AdvancePayment();
            result.setAmount(BigDecimal.ONE);
            result.setDate(LocalDate.now());
            return result;
        }

        /**
         *
         */
        protected CodeListItem createCarbonStopProductLevel() {
            return getCodeListItem(CodeListType.CARBON_STOP_LEVEL);
        }

        /**
         *
         */
        protected AdvancePaymentPeriod createAdvancePaymentPeriod() {
            AdvancePaymentPeriod result = new AdvancePaymentPeriod();
            result.setPeriod(getCodeListItem(CodeListType.ADVANCE_PAYMENT_PERIOD));
            return result;
        }

        /**
         *
         */
        protected ConnectionDetailEe createConnnectionDetailEe() {
            ConnectionDetailEe result = new ConnectionDetailEe();
            result.setCircuitPhaseCount(2);
            result.setMainCircuitBreakerValue(new BigDecimal("34"));
            return result;
        }


        /**
         *
         */
        protected AdvancePaymentPeriod createContractAdvancePaymentPeriod() {
            AdvancePaymentPeriod result = new AdvancePaymentPeriod();
            result.setPeriod(getCodeListItem(CodeListType.CONTRACT_ADVANCE_PAYMENT_PERIOD));
            return result;
        }

        protected AssumedConsumptionEe createAssumedConsumptionEe() {
            AssumedConsumptionEe result = new AssumedConsumptionEe();
            result.setAdvancePayment(BigDecimal.ONE);
            result.setDate(LocalDate.now());
            result.setValue(new BigDecimal("1"));
            result.setValueHigh(new BigDecimal("2"));
            result.setValueLow(new BigDecimal("3"));

            return result;
        }

        protected ContractDelivery createContractDelivery() {
            ContractDelivery result = new ContractDelivery();
            result.setAddress(createAddress());
            result.setType(ContractDeliveryType.COURIER);

            return result;
        }

        protected AssumedConsumptionZp createAssumedConsumptionZp() {
            AssumedConsumptionZp result = new AssumedConsumptionZp();
            result.setAdvancePayment(BigDecimal.ONE);
            result.setDate(LocalDate.now());
            result.setValue(new BigDecimal("1"));

            return result;
        }

        protected DeliveryPointRecordEE createDeliveryPointRecordEe(DeliveryPoint deliveryPoint,
                                                                    MeterReading meterReading,
                                                                    TariffEe tariffEe,
                                                                    AssumedConsumptionEe assumedConsumptionEe,
                                                                    AdvancePaymentPeriod advancePaymentAndPeriod) {
            DeliveryPointRecordEE record = new DeliveryPointRecordEE();
            record.setDeliveryPointEe(deliveryPoint);
            record.setMeterReadingEe(meterReading);
            record.setTariffEe(tariffEe);
            record.setAssumedConsumptionEe(assumedConsumptionEe);
            record.setAdvancePaymentPeriodEe(advancePaymentAndPeriod);
            return record;
        }

        protected DeliveryPointRecordZP createDeliveryPointRecordZp(DeliveryPoint deliveryPoint,
                                                                    MeterReading meterReading,
                                                                    TariffZp tariffEe,
                                                                    AssumedConsumptionZp assumedConsumptionEe,
                                                                    AdvancePaymentPeriod advancePaymentAndPeriod) {
            DeliveryPointRecordZP record = new DeliveryPointRecordZP();
            record.setDeliveryPointZp(deliveryPoint);
            record.setMeterReadingZp(meterReading);
            record.setTariffZp(tariffEe);
            record.setAssumedConsumptionZp(assumedConsumptionEe);
            record.setAdvancePaymentPeriodZp(advancePaymentAndPeriod);
            return record;
        }

        /**
         *
         */
        protected PaymentMethod createPaymentMethod() {
            PaymentMethod result = new PaymentMethod();
            // result.setBicSwift("BICSWIFT");
            result.setIban("************************");
            result.setPaymentType(getCodeListItem(CodeListType.PAYMENT_TYPE, "P"));
            result.setSipoNumber("**********");
            return result;
        }

        /**
         *
         */
        protected DealAmountEe createDealAmountEe() {
            DealAmountEe result = new DealAmountEe();
            result.setMaximumReserveAmount(BigDecimal.ONE);
            result.setReserveAmount(BigDecimal.ONE);
            return result;
        }


        /**
         *
         */
        protected DealAmountZp createDealAmountZp() {
            DealAmountZp result = new DealAmountZp();
            result.setDealAmount(BigDecimal.ONE);
            result.setMaximumDailyAmount(BigDecimal.ONE);
            return result;
        }

        /**
         *
         */
        protected DeliveryPoint createDeliveryPoint() {
            DeliveryPoint result = new DeliveryPoint();
            result.setAddress(createAddress());
            result.setContractNumber(Expressions.tryGet(() -> contractEntityRepository.findById(request.getcId()).get().getExternalId()));
            result.setDeliveryPointNumber(Expressions.tryGet(() -> deliveryPointEntityRepository.findById(request.getDpId()).get().getExternalId()));
            result.setMeterNumber("METER_NUMBER");
            return result;
        }

        /**
         *
         */
        protected DeliveryPointConnectionEe createDeliveryPointConnectionEe() {
            DeliveryPointConnectionEe result = new DeliveryPointConnectionEe();
            result.setContractNumber("12345678");
            result.setBindingTo(LocalDate.now());
            result.setBusinessPartnerId("12345678");
            result.setContractTermination(true);
            result.setCurrentDistributor("DIST");
            result.setDistributionArea(DistributionArea.ZSS);
            result.setNoticePeriod(1);
            result.setType(DeliveryPointEeConnectionType.NEW);

            return result;
        }

        /**
         *
         */
        protected DeliveryPointConnectionZp createDeliveryPointConnectionZp() {
            DeliveryPointConnectionZp result = new DeliveryPointConnectionZp();
            result.setBindingTo(LocalDate.now());
            result.setContractTermination(true);
            result.setCurrentDistributor("DIST");
            result.setNoticePeriod(1);
            result.setType(DeliveryPointZpConnectionType.NEW);

            return result;
        }

        /**
         *
         */
        protected Approvals createApprovals() {
            Approvals result = new Approvals();
            result.setConditions(Boolean.TRUE);
            result.setMarketing(Boolean.TRUE);
            result.setPersonalInfoProcessing(Boolean.TRUE);
            return result;
        }

        /**
         *
         */
        protected BankConnection createBankConnection() {
            BankConnection result = new BankConnection();
            // result.setBicSwift("BICSWIFT");
            result.setIban("************************");
            return result;
        }

        /**
         *
         */
        protected OverpaymentSettlement createOverpaymentSettlement() {
            OverpaymentSettlement result = new OverpaymentSettlement();
            result.setAddress(createAddress());
            result.setBankConnection(createBankConnection());
            result.setOverpaymentSettlementType(getCodeListItem(CodeListType.OVERPAYMENT_SETTLEMENT_TYPE, "BANK_ACCOUNT"));
            return result;
        }

        /**
         *
         */
        protected List<InvoiceNumber> createInvoiceNumbers() {
            InvoiceNumber result = new InvoiceNumber();
            result.setNumber("**********");
            return Arrays.asList(result);
        }


        /**
         *
         */
        protected InvoiceDelivery createInvoiceDelivery() {
            InvoiceDelivery result = new InvoiceDelivery();
            result.setAddress(createAddress());
            result.setEmail("<EMAIL>");
            result.setType(InvoiceDeliveryType.ADDRESS);
            return result;
        }

        /**
         *
         */
        protected Invoice createInvoice() {
            Invoice result = new Invoice();
            result.setBillingPeriod(new BillingPeriod());
            result.getBillingPeriod().setFrom(LocalDate.now());
            result.getBillingPeriod().setTo(LocalDate.now());
            result.setIssueDate(LocalDate.now());
            result.setVs("123456890");
            return result;
        }

        /**
         *
         */
        protected List<ComplainedAdvance> createComplainedAdvances() {
            ComplainedAdvance result = new ComplainedAdvance();
            result.setAmount(BigDecimal.ONE);
            result.setDate(LocalDate.now());
            result.setPaymentType(getCodeListItem(CodeListType.PAYMENT_TYPE));
            result.setVs("123456890");
            return List.of(result);
        }

        /**
         *
         */
        protected List<AdditionalService> createAdditionalServices() {
            AdditionalService result = new AdditionalService();
            result.setName("SERVICE_NAME");
            return List.of(result);
        }

        /**
         * Get First by type
         */
        protected CodeListItem getTariff(TariffType type) {
            CodeListItem item = new CodeListItem();
            TariffEntity entity = null;
            switch (type) {
                case EE:
                    entity = tariffEntityRepository.findByCodeAndStatus("DD1", TariffStatus.ACTIVE).get();
                    break;
                case ZP:
                    entity = tariffEntityRepository.findByCodeAndStatus("D1", TariffStatus.ACTIVE).get();
                    break;
            }

            item.setUuid(entity.getId().toString());
            item.setCode(entity.getCode());
            item.setType(type.name());
            return item;
        }

        /**
         * Get First by type
         */
        protected CodeListItem getCodeListItem(CodeListType type) {
            return getCodeListItem(type, null);
        }

        /**
         * Get First by type
         */
        protected CodeListItem getCodeListItem(CodeListType type, String code) {
            CodeListItem item = new CodeListItem();
            GenericCodeListEntity entity;
            entity = codeListEntityService.getCurrentCodeListItems(type.name()).stream()
                    .filter(v -> code == null || code.equals(v.getCode())).findAny().get();
            item.setUuid(entity.getId().toString());
            item.setCode(entity.getCode());
            item.setType(type.name());

            return item;
        }

        /**
         *
         */
        protected ComplainedInvoiceProblemDescription createComplainedInvoiceProblemDescription() {
            ComplainedInvoiceProblemDescription result = new ComplainedInvoiceProblemDescription();
            result.setProblemDescription(getCodeListItem(CodeListType.COMPLAINED_INVOICE_PROBLEM));
            result.setComplainedMeterReading(BigDecimal.ONE);
            result.setCurrentMeterReading(BigDecimal.ONE);
            result.setCurrentReadingDate(LocalDate.now());
            result.setDescription("DESCRIPTION");
            result.setMeterNumber("**********");
            result.setReadingDate(LocalDate.now());
            result.setRequiredMeterReading(BigDecimal.ONE);
            result.setRequiredReadingDate(LocalDate.now());
            return result;
        }

        /**
         *
         */
        protected MeterReading createMeterReading() {
            MeterReading result = new MeterReading();
            result.setDate(LocalDate.now());
            result.setType(MeterReadingType.GAS);
            result.setValue(BigDecimal.ONE);
            result.setValueHigh(BigDecimal.ONE);
            result.setValueLow(BigDecimal.ONE);
            return result;
        }

        /**
         *
         */
        protected UnderchargeSettlementInvoice createUnderchargeSettlementInvoice() {
            UnderchargeSettlementInvoice result = new UnderchargeSettlementInvoice();
            result.setAmount(BigDecimal.ONE);
            result.setInvoiceNumber("**********");
            result.setRepaymentCount(1);
            return result;
        }

        /**
         *
         */
        protected Contact createContact() {
            Contact result = new Contact();
            result.setEmail("<EMAIL>");
            result.setPhoneNumber("+421907123456");
            return result;
        }

        /**
         *
         */
        protected BusinessPartner createBusinessPartner(CustomerType type) {
            BusinessPartner result = new BusinessPartner();
            result.setType(type);

            // default
            result.setBpNumber(businessPartnerEntityRepository.findById(request.getBpId()).get().getExternalId());
            switch (type) {
                case COMPANY:
                    result.setCompanyRegistrationNumber("ICO");
                    result.setStatutoryName("STATUTORY_NAME");
                    result.setTaxIdNumber("DIC");
                    result.setVatRegistrationNumber("ICDPH");
                    result.setName("COMPANY_NAME");
                    result.setLegalForm("Company");
                    break;
                case PRIVATE_PERSON:
                    result.setFirstName("FIRST_NAME");
                    result.setLastName("LAST_NAME");
                    result.setBirthDate(LocalDate.now());
                    result.setNationality("SK");
                    result.setTitleBehind(null);
                    result.setTitleFront(null);
                    break;
            }
            return result;
        }

        protected CustomerRequest createCustomerRequest() {
            CustomerRequest customerRequest = new CustomerRequest();

            // template
            customerRequest.setCustomerRequestTemplate(new CustomerRequestTemplate());
            customerRequest.getCustomerRequestTemplate().setUuid(customerRequestTemplateRepository.findByCodeAndStatus(getRequestCode(),
                    CustomerRequestTemplateStatus.ACTIVE).get().getId().toString());

            // content
            customerRequest.setContent(prepareRequestContent(createRequestContent()));
            return customerRequest;
        }

        /**
         *
         */
        protected void write(Map<String, Object> content, String payloadName) throws Exception {
            resultMap.put(payloadName, content);
        }

        /**
         *
         */
        protected String toJsonString(Object value) {
            try {
                return objectMapper.writeValueAsString(value);
            } catch (Exception e) {
                LOGGER.error(String.format("Searilization JSON failed; value=[%s], msg=[%s]", value, e.getMessage()), e);
                return null;
            }
        }

        /**
         *
         */
        protected Map<String, Object> toJsonMap(Object value) {
            try {
                String content = objectMapper.writeValueAsString(value);
                return objectMapper.readValue(content, Map.class);

            } catch (Exception e) {
                LOGGER.error(String.format("Searilization JSON failed; value=[%s], msg=[%s]", value, e.getMessage()), e);
                return null;
            }
        }

        /**
         *
         */
        protected Map<String, Object> toJsonMap(String value) {
            try {
                return objectMapper.readValue(value, Map.class);

            } catch (Exception e) {
                LOGGER.error(String.format("Searilization JSON failed; value=[%s], msg=[%s]", value, e.getMessage()), e);
                return null;
            }
        }

        /**
         *
         */
        private void processAuditLog(AuditLogEntity auditLog) throws Exception {
            String request = auditLog.getAttributes().get("serviceCallRequest").replace("\\\"", "\"");
            String reponse = auditLog.getAttributes().get("serviceCallResponse").replace("\\\"", "\"");
            String status = auditLog.getAttributes().get("serviceCallResponseStatus").replace("\\\"", "\"");

            // write result
            write(toJsonMap(request), "sap-request");
            write(toJsonMap(reponse), "sap-response");

            Map<String, Object> resultMap = new LinkedHashMap<>();
            resultMap.put("httpStatus", status);
            write(resultMap, "sap-result");
        }

        @Override
        public RequestContentDebugger process() {
            int retry = 80;
            boolean result = false;

            try {
                // try find auditlog
                while (retry >= 0 && (!result)) {
                    LOGGER.info(String.format("Checking AuditLog, name=[%s], retry=[%s], customerRequest.id=[%s]",
                            getName(),
                            retry,
                            customerRequest.getUuid()));

                    result = createTransactionTemplate().execute(tx -> {
                        List<AuditLogEntity> results = null;
                        results = auditLogEntityRepository.findAll(
                                EntityType.CUSTOMER_REQUEST,
                                customerRequest.getUuid(),
                                AuditLogCode.SERVICE_CALL_CUSTOMER_REQUEST);

                        //
                        // process
                        if (results != null && results.size() > 0) {
                            try {
                                processAuditLog(results.get(0));
                            } catch (Exception e) {
                                throw new RuntimeException(e.getMessage(), e);
                            }

                            return true;
                        } else {
                            return false;
                        }
                    });

                    Thread.sleep(2000);
                    retry--;
                }

                //
                // process
                if (!result) {
                    throw new Exception(String.format("AuditLog not found, customerRequest.id=[%s]",
                            customerRequest.getUuid()));
                }

            } catch (Exception e) {
                Map<String, String> errorMessage = new HashMap<>();
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);

                errorMessage.put("exception", e.getClass().getName());
                errorMessage.put("message", e.getMessage());
                errorMessage.put("stacktrace", sw.toString());

                resultMap.put("errors", errorMessage);
                LOGGER.error(String.format("Process failed; msg=[%s]", errorMessage));
            }

            return this;
        }

        protected void send(boolean skipValidations) {
            // send
            customerRequestService.sendCustomerRequest(UUID.fromString(customerRequest.getUuid()));
        }

        protected void create(boolean skipValidations) {
            customerRequest = customerRequestService.createCustomerRequestForCustomer(
                    request.getCustomerId(),
                    customerRequest,
                    skipValidations);
        }

        @Override
        public RequestContentDebugger prepare(boolean skipValidations) {
            createTransactionTemplate().executeWithoutResult(tx -> {
                try {
                    // create request
                    customerRequest = createCustomerRequest();
                    LOGGER.info(String.format("Prepare CustomerRequest INIT, name=[%s], customerRequest=[%s]",
                            getName(),
                            toJsonString(customerRequest)));

                    // write again
                    write(toJsonMap(customerRequest), "nzp-request");

                    // create
                    create(skipValidations);

                    // send
                    send(skipValidations);

                    LOGGER.info(String.format("Prepare CustomerRequest CREATE, name=[%s], customerRequest=[%s]",
                            getName(),
                            toJsonString(customerRequest)));

                    // write again
                    write(toJsonMap(customerRequest), "nzp-response");

                } catch (Exception e) {
                    StringWriter sw = new StringWriter();
                    PrintWriter pw = new PrintWriter(sw);
                    e.printStackTrace(pw);
                    Map<String, String> errorMessage = new HashMap<>();
                    errorMessage.put("request", toJsonString(customerRequest));
                    errorMessage.put("exception", e.getClass().getName());
                    errorMessage.put("message", e.getMessage());
                    errorMessage.put("validations", Expressions.tryGet(() -> ((ApiException) e).getErrors().toString()));
                    errorMessage.put("stacktrace", sw.toString());
                    resultMap.put("errors", errorMessage);
                    LOGGER.error(String.format("Prepare failed; errors=[%s]", errorMessage.toString()));
                }
            });

            return this;
        }

        /**
         *
         */
        public CustomerRequest getCustomerRequest() {
            return customerRequest;
        }

        /**
         *
         */
        public Map<String, Object> getResultMap() {
            return resultMap;
        }

        /**
         *
         */
        public boolean hasError() {
            return ((Map<String, String>) resultMap.get("errors")).size() > 0;
        }
    }

    /**
     *
     */
    public static class Request {

        private String bpId;
        private String caId;
        private String cId;
        private String dpId;
        private UUID customerId;
        private List<String> requestCodes;

        public String getBpId() {
            return bpId;
        }

        public Request setBpId(String bpId) {
            this.bpId = bpId;
            return this;
        }

        public String getCaId() {
            return caId;
        }

        public Request setCaId(String caId) {
            this.caId = caId;
            return this;
        }

        public String getcId() {
            return cId;
        }

        public Request setcId(String cId) {
            this.cId = cId;
            return this;
        }

        public String getDpId() {
            return dpId;
        }

        public Request setDpId(String dId) {
            this.dpId = dId;
            return this;
        }

        public UUID getCustomerId() {
            return customerId;
        }

        public Request setCustomerId(UUID customerId) {
            this.customerId = customerId;
            return this;
        }

        public List<String> getRequestCodes() {
            return requestCodes;
        }

        public Request setRequestCodes(List<String> requestCodes) {
            this.requestCodes = requestCodes;
            return this;
        }
    }

    /***********************************************************
     * DEBUGGER *
     ************************************************************/

    /**
     *
     */
    private class ZZU_ADEFRequestContentDebug extends AbstractRequestContentDebugger {

        public ZZU_ADEFRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZZU_ADEF;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZZUADEFRequestContent requestContent = new ZZUADEFRequestContent();

            // components
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setInvoiceDelivery(createInvoiceDelivery());
            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOP_ZPRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOP_ZPRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOP_ZP;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOPZPRequestContent requestContent = new ZOPZPRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));

            // new data
            requestContent.setLastNameChangeReason(LastNameChangeReason.MARRIAGE);
            requestContent.setNewLastName("LASTNAME");

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOP_ZOUARequestContentDebug extends AbstractRequestContentDebugger {

        public ZOP_ZOUARequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOP_ZOUA;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOPZOUARequestContent requestContent = new ZOPZOUARequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));

            // new data
            requestContent.setNewBusinessPartnerData(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setNewAddress(createAddress());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOP_ZBURequestContentDebug extends AbstractRequestContentDebugger {

        public ZOP_ZBURequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOP_ZBU;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOPZBURequestContent requestContent = new ZOPZBURequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());

            // new data
            requestContent.setNewBankConnection(createBankConnection());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOP_USRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOP_USRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOP_US;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOPUSRequestContent requestContent = new ZOPUSRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());

            // new data
            requestContent.setPaymentMethod(createPaymentMethod());
            requestContent.setAdvancePaymentPeriod(createAdvancePaymentPeriod());
            requestContent.setProductLevel(createCarbonStopProductLevel());
            requestContent.setApprovals(createApprovals());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOP_SRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOP_SRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOP_S;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOPSRequestContent requestContent = new ZOPSRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());

            // new data
            requestContent.setComplaint("COMPLAINT");

            return requestContent;
        }
    }
    /**
     *
     */
    private class ZOP_PRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOP_PRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOP_P;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOPPRequestContent requestContent = new ZOPPRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());

            // new data
            requestContent.setComplaint("COMPLAINT");

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOP_RSRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOP_RSRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOP_RS;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOPRSRequestContent requestContent = new ZOPRSRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());

            // new data
            requestContent.setBankConnection(createBankConnection());
            requestContent.setApprovals(createApprovals());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZVPPRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZVPPRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZVPP;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZVPPRequestContent requestContent = new ZOMZVPPRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setAdvancePayment(createAdvancePayment());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZVAPPPRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZVAPPPRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZVAPPP;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZVAPPPRequestContent requestContent = new ZOMZVAPPPRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setAdvancePayment(createAdvancePayment());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZTPRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZTPRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZTP;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZTPRequestContent requestContent = new ZOMZTPRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setTariffZp(createTariffZp());
            requestContent.setMeterReadingZp(createMeterReading());
            requestContent.setDealAmountZp(createDealAmountZp());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZSPAPPPRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZSPAPPPRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZSPAPPP;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZSPAPPPRequestContent requestContent = new ZOMZSPAPPPRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setPaymentMethod(createPaymentMethod());
            requestContent.setAdvancePaymentPeriod(createContractAdvancePaymentPeriod());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZSERequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZSERequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZSE;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZSERequestContent requestContent = new ZOMZSERequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setTariffEe(createTariffEe());
            requestContent.setDealAmountEe(createDealAmountEe());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZOZODZPRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZOZODZPRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZOZODZP;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZOZODZPRequestContent requestContent = new ZOMZOZODZPRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setStatement(Boolean.TRUE);

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZOVDFRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZOVDFRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZOVDF;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZOVDFRequestContent requestContent = new ZOMZOVDFRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setInvoiceNumbers(createInvoiceNumbers());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZOUZRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZOUZRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZOUZ;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZOUZRequestContent requestContent = new ZOMZOUZRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setContractTerminationReason(createContractTerminationReason());
            requestContent.setContractTerminationDate(LocalDate.now());
            requestContent.setInvoiceDelivery(createInvoiceDelivery());
            requestContent.setOverpaymentSettlement(createOverpaymentSettlement());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZOUSMRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZOUSMRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZOUSM;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZOUSMRequestContent requestContent = new ZOMZOUSMRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setStatement(Boolean.TRUE);

            return requestContent;
        }
    }


    /**
     *
     */
    private class ZOM_ZOFMCRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZOFMCRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZOFMC;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZOFMCRequestContent requestContent = new ZOMZOFMCRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setMeterReading(createMeterReading());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZODSRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZODSRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZODS;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZODSRequestContent requestContent = new ZOMZODSRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setAdditionalServices(createAdditionalServices());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZAOMRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZAOMRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZAOM;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZAOMRequestContent requestContent = new ZOMZAOMRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setNewDeliveryPointAddress(createAddress());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_RPVFRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_RPVFRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_RPVF;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMRPVFRequestContent requestContent = new ZOMRPVFRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setComplainedInvoice(createInvoice());
            requestContent.setProblemDescription("DESCRIPTION");
            requestContent.setComplainedAdvances(createComplainedAdvances());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_RFRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_RFRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_RF;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMRFRequestContent requestContent = new ZOMRFRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setComplainedInvoice(createInvoice());
            requestContent.setComplainedInvoiceProblemDescription(createComplainedInvoiceProblemDescription());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_POZVRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_POZVRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_POZV;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMPOZVRequestContent requestContent = new ZOMPOZVRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setStatement(Boolean.TRUE);

            return requestContent;
        }
    }


    /**
     *
     */
    private class ZOM_POVZRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_POVZRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_POVZ;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMPOVZRequestContent requestContent = new ZOMPOVZRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setDate(LocalDate.now());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ORequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ORequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_O;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMORequestContent requestContent = new ZOMORequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setMeterReading(createMeterReading());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_DOSRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_DOSRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_DOS;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMDOSRequestContent requestContent = new ZOMDOSRequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());

            // new data
            requestContent.setUnderchargeSettlementInvoice(createUnderchargeSettlementInvoice());
            requestContent.setInvoiceNumbers(createInvoiceNumbers());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_PRequestContentDebug extends AbstractRequestContentDebugger {

        private CustomerRequestContent requestContent;

        public ZOM_PRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_P;
        }

        @Override
        protected void send(boolean skipValidations) {
            // set status manually
            CustomerRequestEntity cre = customerRequestRepository.findById(UUID.fromString(customerRequest.getUuid())).get();
            cre.setStatus(CustomerRequestStatus.CREATED);
        }

        @Override
        protected void create(boolean skipValidations) {
            this.requestContent = SerializationUtils.clone(this.customerRequest.getContent());

            // create
            super.create(skipValidations);

            // set metadata
            requestContent.setMetadata(customerRequest.getContent().getMetadata());

            // assemble manually
            CustomerRequestEntity cre = customerRequestRepository.findById(UUID.fromString(customerRequest.getUuid())).get();
            cre.setContent(requestContent);
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMPRequestContent requestContent = new ZOMPRequestContent();

            // old
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setLastInvoice(createInvoiceDelivery());
            requestContent.setOverpaymentSettlement(createOverpaymentSettlement());

            // new data
            requestContent.setNewBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setNewBusinessPartnerAddress(createAddress());
            requestContent.setNewBusinessPartnerCorrespondenceAddress(createAddress());
            requestContent.setInvoiceDelivery(createInvoiceDelivery());
            requestContent.setPaymentMethod(createPaymentMethod());
            requestContent.setApprovals(createApprovals());


            // deliverypoints
            requestContent.setDeliveryPointsEe(Arrays.asList(
                    createDeliveryPointRecordEe(
                            createDeliveryPoint(),
                            createMeterReading(),
                            createTariffEe(),
                            createAssumedConsumptionEe(),
                            createContractAdvancePaymentPeriod()))
            );

            requestContent.setDeliveryPointsZp(Arrays.asList(
                    createDeliveryPointRecordZp(
                            createDeliveryPoint(),
                            createMeterReading(),
                            createTariffZp(),
                            createAssumedConsumptionZp(),
                            createContractAdvancePaymentPeriod()))
            );

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZODERequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZODERequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZODE;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZODERequestContent requestContent = new ZOMZODERequestContent();

            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setAddress(createAddress());
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());
            requestContent.setDeliveryPointConnectionEe(createDeliveryPointConnectionEe());
            requestContent.setInvoiceDelivery(createInvoiceDelivery());
            requestContent.setOwner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setOwnerAddress(createAddress());
            requestContent.setOwnerContact(createContact());
            requestContent.setOwnerCorrespondenceAddress(createAddress());
            requestContent.setPaymentMethod(createPaymentMethod());
            requestContent.setAdvancePaymentPeriod(createAdvancePaymentPeriod());
            requestContent.setTariffEe(createTariffEe());
            requestContent.setConnectionDetailEe(createConnnectionDetailEe());
            requestContent.setDealAmountEe(createDealAmountEe());
            requestContent.setAssumedConsumptionEe(createAssumedConsumptionEe());
            requestContent.setContractDelivery(createContractDelivery());
            requestContent.setApprovals(createApprovals());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZODPRequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZODPRequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZODP;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZODPRequestContent requestContent = new ZOMZODPRequestContent();

            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setAddress(createAddress());
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());
            requestContent.setDeliveryPointConnectionZp(createDeliveryPointConnectionZp());
            requestContent.setInvoiceDelivery(createInvoiceDelivery());
            requestContent.setOwner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setOwnerAddress(createAddress());
            requestContent.setOwnerContact(createContact());
            requestContent.setOwnerCorrespondenceAddress(createAddress());
            requestContent.setPaymentMethod(createPaymentMethod());
            requestContent.setAdvancePaymentPeriod(createAdvancePaymentPeriod());
            requestContent.setTariffZp(createTariffZp());
            requestContent.setAssumedConsumptionZp(createAssumedConsumptionZp());
            requestContent.setContractDelivery(createContractDelivery());
            requestContent.setApprovals(createApprovals());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_CERequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_CERequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_CE;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMCERequestContent requestContent = new ZOMCERequestContent();

            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setDeliveryPoint(createDeliveryPoint());
            requestContent.setInvoiceDelivery(createInvoiceDelivery());
            requestContent.setPaymentMethod(createPaymentMethod());
            requestContent.setAdvancePaymentPeriod(createAdvancePaymentPeriod());
            requestContent.setApprovals(createApprovals());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOM_ZOPAOORequestContentDebug extends AbstractRequestContentDebugger {

        public ZOM_ZOPAOORequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return getRequestCode().name();
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOM_ZOPAOO;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOMZOPAOORequestContent requestContent = new ZOMZOPAOORequestContent();

            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setAddress(createAddress());
            requestContent.setCorrespondenceAddress(createAddress());
            requestContent.setDeliveryPoint(createDeliveryPoint());
            requestContent.setDate(LocalDate.now());
            requestContent.setReason(getCodeListItem(CodeListType.ZOM_ZOPAOO_REASON));
            requestContent.setFinalInvoiceDelivery(createInvoiceDelivery());
            requestContent.setOverpaymentSettlement(createOverpaymentSettlement());

            // old
            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOPZOUA_PRIVATE_RequestContentDebug extends AbstractRequestContentDebugger {

        public ZOPZOUA_PRIVATE_RequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return String.format("%s_PRIVATE", getRequestCode().name());
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOP_ZOUA;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOPZOUARequestContent requestContent = new ZOPZOUARequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.PRIVATE_PERSON));

            // new data
            requestContent.setNewAddress(createAddress());
            requestContent.setNewBusinessPartnerData(createBusinessPartner(CustomerType.PRIVATE_PERSON));
            requestContent.setNewCorrespondenceAddress(createAddress());

            return requestContent;
        }
    }

    /**
     *
     */
    private class ZOPZOUA_COMPANY_RequestContentDebug extends AbstractRequestContentDebugger {

        public ZOPZOUA_COMPANY_RequestContentDebug(Request requestContentDebuggerRequest) {
            super(requestContentDebuggerRequest);
        }

        @Override
        protected String getName() {
            return String.format("%s_COMPANY", getRequestCode().name());
        }

        @Override
        protected CustomerRequestCode getRequestCode() {
            return CustomerRequestCode.ZOP_ZOUA;
        }

        @Override
        protected CustomerRequestContent createRequestContent() {
            ZOPZOUARequestContent requestContent = new ZOPZOUARequestContent();

            // components
            requestContent.setAddress(createAddress());
            requestContent.setBusinessPartner(createBusinessPartner(CustomerType.COMPANY));

            // new data
            requestContent.setNewAddress(createAddress());
            requestContent.setNewBusinessPartnerData(createBusinessPartner(CustomerType.COMPANY));
            requestContent.setNewCorrespondenceAddress(createAddress());

            return requestContent;
        }
    }
}

