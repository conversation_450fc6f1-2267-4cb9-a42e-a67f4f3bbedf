package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerConsent;
import sk.spp.nzp.be.assembler.customerprofile.BusinessPartnerConsentAssembler;
import sk.spp.nzp.commons.model.customerprofile.BusinessPartnerConsentEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class BusinessPartnerConsentAssemblerImpl implements BusinessPartnerConsentAssembler {

    @Override
    public BusinessPartnerConsentEntity map(BusinessPartnerConsent input, BusinessPartnerConsentEntity output) {

        output.setConsentCommunicationChannel(input.getConsentCommunicationChannel());
        output.setConsent(input.getConsent());
        output.setStatus(input.getStatus());
        output.setValidTo(input.getValidTo());
        output.setPartnerConsent(input.getPartnersConsent());

        return output;
    }

    @Override
    public BusinessPartnerConsent map(BusinessPartnerConsentEntity input, BusinessPartnerConsent output) {

        output.setId(input.getId().toString());
        output.setConsentCommunicationChannel(input.getConsentCommunicationChannel());
        output.setConsent(input.getConsent());
        output.setStatus(input.getStatus());
        output.setValidTo(input.getValidTo());
        output.setPartnersConsent(input.getPartnerConsent());

        return output;
    }

    @Override
    public List<BusinessPartnerConsent> map(Collection<BusinessPartnerConsentEntity> input) {

        if (input != null && !input.isEmpty()) {
            return input.stream()
                    .filter(i -> !i.getValidFrom().isAfter(LocalDateTime.now())
                    && (!i.getValidTo().isBefore(LocalDateTime.now())|| i.getValidTo().toLocalDate().equals(LocalDate.now()))  )
                    .map(i -> map(i, new BusinessPartnerConsent())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}
