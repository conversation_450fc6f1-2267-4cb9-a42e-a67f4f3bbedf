package sk.spp.nzp.be.controller;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.annotation.paging.PagingAsQueryParams;
import sk.spp.nzp.be.api.common.PagedResponse;
import sk.spp.nzp.be.api.common.QueryStringPaging;
import sk.spp.nzp.be.api.help.ComponentHelp;
import sk.spp.nzp.be.service.help.ComponentHelpService;

import java.util.UUID;

@RestController
@RequestMapping("/admin/component-helps")
public class AdminComponentHelpController {

    private ComponentHelpService componentHelpService;

    public AdminComponentHelpController(ComponentHelpService componentHelpService) {
        this.componentHelpService = componentHelpService;
    }

    @Log
    @LogParam
    @GetMapping(value = "", produces = {"application/json"})
    @PagingAsQueryParams
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('COMPONENT_HELP_VIEW')")
    public PagedResponse<ComponentHelp> getAll(@LogParam("paging") QueryStringPaging paging) {

        return componentHelpService.getAllPaged(paging.toPaging());
    }

    @Log
    @LogParam
    @GetMapping(value = "/{uuid}", produces = {"application/json"})
    @PagingAsQueryParams
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('COMPONENT_HELP_VIEW')")
    public ComponentHelp getById(@LogParam("uuid") @PathVariable UUID uuid) {

        return componentHelpService.getById(uuid);
    }

    @Log
    @LogParam
    @PostMapping(value = "", produces = {"application/json"}, consumes = {"application/json"})
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('COMPONENT_HELP_EDIT')")
    public ComponentHelp create(@LogParam("componentHelp") @RequestBody ComponentHelp componentHelp) {

        return componentHelpService.create(componentHelp);
    }

    @Log
    @LogParam
    @PutMapping(value = "/{uuid}", produces = {"application/json"}, consumes = {"application/json"})
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('COMPONENT_HELP_EDIT')")
    public ComponentHelp update(@LogParam("uuid") @PathVariable UUID uuid,
                                            @LogParam("componentHelp") @RequestBody ComponentHelp componentHelp) {

        return componentHelpService.update(uuid, componentHelp);
    }

    @Log
    @LogParam
    @DeleteMapping(value = "/{uuid}")
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('COMPONENT_HELP_EDIT')")
    public void delete(@LogParam("uuid") @PathVariable UUID uuid) {

        componentHelpService.delete(uuid);
    }
}
