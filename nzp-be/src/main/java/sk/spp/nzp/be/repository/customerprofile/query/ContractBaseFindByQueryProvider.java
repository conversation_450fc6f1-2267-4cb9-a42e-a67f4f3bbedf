package sk.spp.nzp.be.repository.customerprofile.query;

import java.time.LocalDate;
import java.util.Set;
import java.util.UUID;

import javax.persistence.EntityManager;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;

import sk.spp.nzp.be.models.customerprofile.ContractSearch;
import sk.spp.nzp.commons.api.customerprofile.enums.ContractStatus;
import sk.spp.nzp.commons.api.customersharing.enums.OwnershipType;
import sk.spp.nzp.commons.model.customerprofile.ContractEntity;
import sk.spp.nzp.commons.model.customerprofile.QContractEntity;
import sk.spp.nzp.commons.model.customersharing.QContractOwnershipEntity;

public class ContractBaseFindByQueryProvider {

    // TODO: delete unnecessary code
    protected JPAQuery<ContractEntity> findByBaseQuery(EntityManager entityManager, BooleanExpression parentEntityExpression, ContractSearch contractSearch, UUID sharedCustomerUuid) {
        
        QContractEntity contractEntity = QContractEntity.contractEntity;
        QContractOwnershipEntity contractOwnershipEntity = QContractOwnershipEntity.contractOwnershipEntity;
        
        BooleanExpression statusesExp = statusFilter(contractSearch.getStatuses());
        
        if(sharedCustomerUuid == null) {//temporary hack because signed customer (sharedCustomerId) can not be obtain from principal.
            
            return new JPAQuery<ContractEntity>(entityManager).from(contractEntity).where(statusesExp, parentEntityExpression); 
        } 
        
        BooleanExpression ownerExp = null;
        
        if(contractSearch.getShared() == null || !contractSearch.getShared()) {
            ownerExp = contractOwnershipEntity.type.eq(OwnershipType.OWNER);
        }
            
        return new JPAQuery<ContractEntity>(entityManager).from(contractEntity)
                .join(contractOwnershipEntity).on(contractOwnershipEntity.customerAccount().id.eq(sharedCustomerUuid), contractOwnershipEntity.contract().eq(contractEntity))
                .where(statusesExp, parentEntityExpression, ownerExp);
    }

    private BooleanExpression statusFilter(Set<ContractStatus> statuses) {
        if (statuses == null || statuses.containsAll(Set.of(ContractStatus.ACTIVE, ContractStatus.INACTIVE))) {
            return null;
        }

        if (statuses.contains(ContractStatus.ACTIVE)) {
            return getActiveItems(LocalDate.now());
        }

        if (statuses.contains(ContractStatus.INACTIVE)) {
            return getInactiveItems(LocalDate.now());
        }

        return null;
    }

    protected BooleanExpression getActiveItems(LocalDate forTime) {
        QContractEntity contractEntity = QContractEntity.contractEntity;

        BooleanExpression validFromIsLessThanNow = contractEntity.effectiveFrom.loe(forTime)
                .or(contractEntity.effectiveFrom.isNull());
        BooleanExpression validToIsGreaterThanNow = contractEntity.effectiveTo.goe(forTime)
                .or(contractEntity.effectiveTo.isNull());

        return validFromIsLessThanNow.and(validToIsGreaterThanNow);
    }

    protected BooleanExpression getInactiveItems(LocalDate forTime) {
        return getActiveItems(forTime).not();
    }
}
