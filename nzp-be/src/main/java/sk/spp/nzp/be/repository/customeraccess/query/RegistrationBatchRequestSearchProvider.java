package sk.spp.nzp.be.repository.customeraccess.query;

import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import org.springframework.util.StringUtils;
import sk.spp.nzp.be.api.common.Sorting;
import sk.spp.nzp.be.api.common.SortingAttribute;
import sk.spp.nzp.be.api.customeraccess.registration.RegistrationBatchRequestSearch;
import sk.spp.nzp.be.repository.common.AbstractQueryProvider;
import sk.spp.nzp.commons.model.customeraccess.registration.RegistrationBatchRequestEntity;
import sk.spp.nzp.commons.utils.Expressions;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.List;

import static sk.spp.nzp.commons.model.customeraccess.registration.QRegistrationBatchRequestEntity.registrationBatchRequestEntity;

public class RegistrationBatchRequestSearchProvider extends AbstractQueryProvider<RegistrationBatchRequestEntity>{

    private RegistrationBatchRequestSearch search;

    public RegistrationBatchRequestSearchProvider(RegistrationBatchRequestSearch search) {
        this.search = search;
    }

    @Override
    public JPAQuery<RegistrationBatchRequestEntity> getQuery(EntityManager em) {
        JPAQuery<RegistrationBatchRequestEntity> query = new JPAQuery<RegistrationBatchRequestEntity>(em).from(registrationBatchRequestEntity);
        BooleanExpression createdAtExp = null;
        BooleanExpression scheduledAtExp = null;
        BooleanExpression createdByExp = null;
        BooleanExpression statusExp = null;

        // createdAt
        if ( search.getCreatedAt() != null ) {
            createdAtExp = registrationBatchRequestEntity.createdAt.between(search.getCreatedAt().getFrom(), search.getCreatedAt().getTo());
        }

        // scheduledAtExp
        if ( search.getScheduledAt() != null ) {
            scheduledAtExp = registrationBatchRequestEntity.scheduledAt.between(search.getScheduledAt().getFrom(), search.getScheduledAt().getTo());
        }

        // createdBy
        if ( StringUtils.hasLength(search.getCreatedBy()) ) {
            createdByExp = registrationBatchRequestEntity.createdBy.eq(search.getCreatedBy());
        }

        // statusExp
        if ( search.getStatus() != null ) {
            statusExp = registrationBatchRequestEntity.status.eq(search.getStatus());
        }

        // where
        query = query.where(scheduledAtExp, createdAtExp, createdByExp, statusExp).orderBy(getOrderBy()).distinct();
        return query;
    }

    private OrderSpecifier<?>[] getOrderBy() {
        List<OrderSpecifier<?>> sortingList = new ArrayList<>();
        if( !Expressions.value(Expressions.tryGet(()-> search.getPaging().getSort().isEmpty()), Boolean.TRUE)) {

            // fetch sorting and assbemle orderby
            for(Sorting sorting: search.getPaging().getSort()) {
                if (StringUtils.hasLength(sorting.getAttribute())) {

                    // find sortring attribute
                    SortingAttribute sortingAttribute = SortingAttribute.find(sorting.getAttribute(),
                            SortingAttribute.REGISTRATION_BATCH_REQUEST_SCHEDULED_AT,
                            SortingAttribute.REGISTRATION_BATCH_REQUEST_CREATED_AT,
                            SortingAttribute.REGISTRATION_BATCH_REQUEST_CREATED_BY,
                            SortingAttribute.REGISTRATION_BATCH_REQUEST_STATUS);

                    if ( sortingAttribute != null ) {
                        sortingList.add(getOrderSpecifier(sorting, sortingAttribute.toComparableExpression(registrationBatchRequestEntity)));
                    }
                }
            }
        }

        return sortingList.isEmpty() ? getDefaultOrderBy() : sortingList.toArray(new OrderSpecifier[sortingList.size()]);
    }

    private OrderSpecifier<?>[] getDefaultOrderBy() {
        return new OrderSpecifier<?>[] {registrationBatchRequestEntity.createdAt.desc()};
    }
}
