package sk.spp.nzp.be.assembler.reporting;

import sk.spp.nzp.commons.api.reporting.Report;
import sk.spp.nzp.commons.api.reporting.ReportDefinition;
import sk.spp.nzp.commons.model.reporting.ReportEntity;
import sk.spp.nzp.commons.utils.DataModelHolder;

import java.util.Collection;
import java.util.List;

public interface ReportAssembler {

    ReportEntity map (Report input, ReportEntity output);

    List<Report> map(Collection<ReportEntity> input);

    List<Report> mapForNotificationTemplate(Collection<ReportEntity> input, DataModelHolder dataModelHolder);

    ReportEntity mapDefinitionOnly (ReportDefinition input, ReportEntity output);

    Report map (ReportEntity input, Report output);
}
