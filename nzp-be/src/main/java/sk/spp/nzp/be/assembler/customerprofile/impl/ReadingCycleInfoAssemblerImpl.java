package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.commons.service.customerconsumption.MeterReadingInfo;
import sk.spp.nzp.be.api.customerprofile.ReadingCycleInfo;
import sk.spp.nzp.be.assembler.customerprofile.ReadingCycleInfoAssembler;

@Component
public class ReadingCycleInfoAssemblerImpl implements ReadingCycleInfoAssembler {

    @Override
    public ReadingCycleInfo map(MeterReadingInfo input, ReadingCycleInfo output) {
        if (input == null) {
            return output;
        }

        return output
                .setPeriod(input.getReadingCycleInfo().getReadingCycleMonth())
                .setRequested(input.getReadingCycleInfo().isMeterReadingAllowed())
                .setLastLowValue(input.getLastConsumptionLow())
                .setLastHighValue(input.getLastConsumptionHigh())
                .setReadingCycleFrom(input.getReadingCycleInfo().getReadingCycleFrom())
                .setReadingCycleTo(input.getReadingCycleInfo().getReadingCycleTo());
    }
}