package sk.spp.nzp.be.repository.customerprofile.query;

import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.service.codelist.model.CodeListQuery;
import sk.spp.nzp.be.service.codelist.model.CodeListStatus;
import sk.spp.nzp.commons.api.customerprofile.enums.ProductStatus;
import sk.spp.nzp.commons.model.customerprofile.ProductEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;
import sk.spp.nzp.commons.repository.customerprofile.query.ProductFindAllQueryProvider;

import javax.persistence.EntityManager;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ProductCodeListQueryProvider implements QueryDslProvider<ProductEntity> {

    private CodeListQuery query;

    public ProductCodeListQueryProvider(CodeListQuery query) {
        this.query = query;
    }

    @Override
    public JPAQuery<ProductEntity> getQuery(EntityManager em) {
        return new ProductFindAllQueryProvider()
                .setStatuses(mapStatuses(query.getStatuses()))
                .setCode(query.getCode())
                .getQuery(em);
    }

    private Set<ProductStatus> mapStatuses(List<CodeListStatus> statuses) {
        Set<ProductStatus> productStatuses = new HashSet<>();

        statuses.forEach(s -> {
            if (s == CodeListStatus.ACTIVE) {
                productStatuses.add(ProductStatus.ACTIVE);
            } else if (s == CodeListStatus.INACTIVE) {
                productStatuses.add(ProductStatus.INACTIVE);
            }
        });

        return productStatuses;
    }
}
