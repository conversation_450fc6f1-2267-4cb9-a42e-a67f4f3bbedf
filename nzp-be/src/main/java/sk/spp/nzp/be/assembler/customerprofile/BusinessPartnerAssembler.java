package sk.spp.nzp.be.assembler.customerprofile;

import sk.spp.nzp.be.api.customerprofile.BusinessPartner;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerPairing;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerSummary;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerUnitedDeliveryPointsSummary;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.model.customerprofile.BusinessPartnerEntity;
import sk.spp.nzp.commons.model.customerprofile.BusinessPartnerExtendedOwnershipViewEntity;

import java.util.Collection;
import java.util.List;
import java.util.UUID;

public interface BusinessPartnerAssembler {

    BusinessPartner map(BusinessPartnerEntity input, BusinessPartner output);

    BusinessPartner mapRef(BusinessPartnerEntity input, BusinessPartner output);

    BusinessPartner mapFull(BusinessPartnerEntity input, BusinessPartner output);

    List<BusinessPartner> map(Collection<BusinessPartnerEntity> input);

    List<BusinessPartnerSummary> mapSummaries(Collection<BusinessPartnerEntity> input);

    List<BusinessPartnerUnitedDeliveryPointsSummary> mapUnitedSummaries(Collection<BusinessPartnerEntity> input);

    List<BusinessPartner> mapFull(Collection<BusinessPartnerEntity> input);

    BusinessPartnerUnitedDeliveryPointsSummary map(BusinessPartnerEntity input, BusinessPartnerUnitedDeliveryPointsSummary output);

    BusinessPartnerSummary map(BusinessPartnerEntity input, BusinessPartnerSummary output);

    BusinessPartnerPairing map(BusinessPartnerExtendedOwnershipViewEntity input, BusinessPartnerPairing output, UUID customerAccountUuid, BusinessPartnerQueue bpQueueSecurity);

    List<BusinessPartnerPairing> map(Collection<BusinessPartnerExtendedOwnershipViewEntity> input, UUID customerAccountUuid, BusinessPartnerQueue bpQueueSecurity);
}
