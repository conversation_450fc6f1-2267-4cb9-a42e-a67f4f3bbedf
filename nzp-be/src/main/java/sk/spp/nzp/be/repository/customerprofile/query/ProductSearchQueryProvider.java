package sk.spp.nzp.be.repository.customerprofile.query;

import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.api.customerprofile.ProductsSearch;
import sk.spp.nzp.commons.model.customerprofile.ProductEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;
import sk.spp.nzp.commons.repository.customerprofile.query.ProductFindAllQueryProvider;

import javax.persistence.EntityManager;

public class ProductSearchQueryProvider implements QueryDslProvider<ProductEntity> {

    private ProductsSearch query;

    public ProductSearchQueryProvider(ProductsSearch query) {
        this.query = query;
    }

    @Override
    public JPAQuery<ProductEntity> getQuery(EntityManager em) {
        ProductFindAllQueryProvider provider = new ProductFindAllQueryProvider()
                .setStatus(query.getStatus())
                .setExcludeOtherProductTypes(query.getExcludeOtherTypes());

        query.getTypes().forEach(s -> {
            switch (s) {
                case EE:
                    provider.setEe(true);
                    break;
                case ZP:
                    provider.setZp(true);
                    break;
                case N:
                    provider.setNotCommodity(true);
                    break;
            }
        });

        return provider.getQuery(em);
    }

}
