package sk.spp.nzp.be.assembler.notification;

import sk.spp.nzp.be.api.notification.CustomerNotification;
import sk.spp.nzp.commons.model.notification.CustomerNotificationEntity;

import java.util.Collection;
import java.util.List;

public interface CustomerNotificationAssembler {

    CustomerNotificationEntity map(CustomerNotification input, CustomerNotificationEntity output);

    CustomerNotification map(CustomerNotificationEntity input, CustomerNotification output);

    CustomerNotification mapRef(CustomerNotificationEntity input, CustomerNotification output);

    CustomerNotification mapFull(CustomerNotificationEntity input, CustomerNotification output);

    List<CustomerNotification> map(Collection<CustomerNotificationEntity> input);

    List<CustomerNotification> mapFull(Collection<CustomerNotificationEntity> input);

}