package sk.spp.nzp.be.reporting.entitymappers;

import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import org.apache.commons.lang3.tuple.MutablePair;
import sk.spp.nzp.be.converters.MeterReadingUnitsConverter;
import sk.spp.nzp.be.reporting.entitymappers.columns.MeterReadingColumns;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.api.customerprofile.enums.DeliveryPointType;
import sk.spp.nzp.commons.api.customerprofile.enums.Units;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.enums.CodeListType;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.model.customerconsumption.MeterReadingEntity;
import sk.spp.nzp.commons.model.customerconsumption.QMeterReadingEntity;
import sk.spp.nzp.commons.utils.Expressions;

import java.math.BigDecimal;
import java.util.*;

public class MeterReadingMapper extends AbstractMapper<MeterReadingEntity> {

    private QMeterReadingEntity qMeterReadingEntity = QMeterReadingEntity.meterReadingEntity;

    private List<MeterReadingColumns> columnsNames;

    private MeterReadingUnitsConverter unitsConverter;
    private final Units units;
    private final DeliveryPointType type;
    private final Map<CodeListType, Map<String, CodeListItem>> codelistMap;

    public MeterReadingMapper(List<String> columns,
                              MeterReadingUnitsConverter unitsConverter,
                              Units units,
                              DeliveryPointType type,
                              Map<CodeListType, Map<String, CodeListItem>> codelistMap) {
        columnsNames = new ArrayList<>();

        for (String column : columns) {
            MeterReadingColumns enumValue = MeterReadingColumns.valueOfOrNull(column);
            if (enumValue != null) {
                columnsNames.add(enumValue);
            } else {
                throw new ApiException(ErrorCode.INVALID_COLUMN_NAME);
            }
        }

        this.unitsConverter = unitsConverter;
        this.units = units;
        this.type = type;
        this.codelistMap = codelistMap;
    }

    public Object[] map(Tuple tuple) {
        MeterReadingEntity mre;
        objArr = new Object[columnsNames.size()];
        for (int i = 0; i < columnsNames.size(); i++) {
            MeterReadingColumns meterReadingColumns = columnsNames.get(i);
            switch (meterReadingColumns) {
                case ID:
                    objArr[i] = tuple.get(0, MeterReadingEntity.class).getId();
                    break;
                case DEVICE_NUMBER:
                    objArr[i] = tuple.get(0, MeterReadingEntity.class).getDeviceNumber();
                    break;
                case REGISTER:
                    objArr[i] = tuple.get(0, MeterReadingEntity.class).getRegister();
                    break;
                case REGISTER_KIND_CODE:
                    mre = tuple.get(0, MeterReadingEntity.class);
                    objArr[i] = mre.getRegisterKind() != null ? mre.getRegisterKind() : null;
                    break;
                case REGISTER_KIND_NAME:
                    mre = tuple.get(0, MeterReadingEntity.class);
                    objArr[i] = mre.getRegisterKind() != null ? getGlcName(CodeListType.METER_READING_REGISTER_KIND, mre.getRegisterKind()) : null;
                    break;
                case REASON_CODE:
                    mre = tuple.get(0, MeterReadingEntity.class);
                    objArr[i] = mre.getReason() != null ? mre.getReason() : null;
                    break;
                case REASON_NAME:
                    mre = tuple.get(0, MeterReadingEntity.class);
                    objArr[i] = mre.getReason() != null ? getGlcName(CodeListType.METER_READING_REASON, mre.getReason()) : null;
                    break;
                case KIND_CODE:
                    mre = tuple.get(0, MeterReadingEntity.class);
                    objArr[i] = mre.getKind() != null ? mre.getKind() : null;
                    break;
                case KIND_NAME:
                    mre = tuple.get(0, MeterReadingEntity.class);
                    objArr[i] = mre.getKind() != null ? getGlcName(CodeListType.METER_READING_KIND, mre.getKind()) : null;
                    break;
                case READ_AT:
                    objArr[i] = tuple.get(0, MeterReadingEntity.class).getReadAt();
                    break;
                case VALUE:
                    objArr[i] = getConverted(tuple.get(0, MeterReadingEntity.class).getValue()).getLeft();
                    break;
                case UNITS:
                    if (units == null) {
                        objArr[i] = unitsConverter.getDefaultUnits(type);
                    } else {
                        objArr[i] = units;
                    }
                    break;
                case CATEGORY:
                    objArr[i] = tuple.get(0, MeterReadingEntity.class).getCategory();
                    break;
            }
        }
        return objArr;
    }

    private Object getGlcName(CodeListType type, String code) {
        return Expressions.tryGet(()->codelistMap.get(type).get(code).getName());
    }

    @Override
    public void select(JPAQuery<MeterReadingEntity> query) {

        QMeterReadingEntity meterReadingEntity = QMeterReadingEntity.meterReadingEntity;

        query.select(meterReadingEntity, meterReadingEntity.id);
    }

    private MutablePair<BigDecimal, Units> getConverted(BigDecimal value) {

        return unitsConverter.convert(value, type, units);
    }
}
