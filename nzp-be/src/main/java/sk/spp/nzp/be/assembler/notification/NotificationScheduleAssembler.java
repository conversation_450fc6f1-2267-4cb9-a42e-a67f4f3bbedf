package sk.spp.nzp.be.assembler.notification;

import sk.spp.nzp.be.api.notification.CustomerNotificationCounts;
import sk.spp.nzp.be.api.notification.NotificationSchedule;
import sk.spp.nzp.be.api.notification.NotificationScheduleSummary;
import sk.spp.nzp.commons.model.notification.NotificationScheduleEntity;

import java.util.Collection;
import java.util.List;

public interface NotificationScheduleAssembler {

    NotificationScheduleEntity map(NotificationSchedule input, NotificationScheduleEntity output);

    NotificationSchedule map(NotificationScheduleEntity input, NotificationSchedule output);

    NotificationScheduleSummary map(NotificationScheduleEntity input, NotificationScheduleSummary output);

    List<NotificationSchedule> map(Collection<NotificationScheduleEntity> input);

    List<NotificationScheduleSummary>  mapSummary(List<NotificationScheduleEntity> notificationScheduleList);

    CustomerNotificationCounts mapCounts(sk.spp.nzp.commons.model.notification.CustomerNotificationCounts input, CustomerNotificationCounts output);
}