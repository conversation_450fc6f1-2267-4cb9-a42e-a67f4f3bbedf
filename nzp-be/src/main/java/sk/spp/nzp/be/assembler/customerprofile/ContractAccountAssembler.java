package sk.spp.nzp.be.assembler.customerprofile;

import java.util.Collection;
import java.util.List;

import sk.spp.nzp.be.api.customerprofile.ContractAccount;
import sk.spp.nzp.be.api.customerprofile.ContractAccountSummary;
import sk.spp.nzp.commons.model.customerprofile.ContractAccountEntity;

public interface ContractAccountAssembler {

    ContractAccount map(ContractAccountEntity input, ContractAccount output);

    List<ContractAccount> map(Collection<ContractAccountEntity> input);

    ContractAccount mapRef(ContractAccountEntity contractAccount, ContractAccount contractAccount2);

    ContractAccountSummary mapSummary(ContractAccountEntity input, ContractAccountSummary output, boolean includeFieldUpdates);

    ContractAccountSummary mapBrief(ContractAccountEntity input, ContractAccountSummary output);
}
