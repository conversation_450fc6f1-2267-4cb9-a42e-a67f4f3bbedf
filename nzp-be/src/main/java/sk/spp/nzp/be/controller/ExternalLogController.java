package sk.spp.nzp.be.controller;

import org.springframework.http.MediaType;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.isdd.common.logging.Marker;
import sk.spp.nzp.be.api.log.LogRequest;

@RestController
@RequestMapping("/external/logs")
public class ExternalLogController {

    @PostMapping(consumes = {MediaType.APPLICATION_JSON_VALUE})
    @Log(value = Marker.EXTERNAL, end = false)
    public void logs(@RequestBody @LogParam("log") LogRequest logRequest) {
        // it's logged based on @Log/@LogParam
    }
}
