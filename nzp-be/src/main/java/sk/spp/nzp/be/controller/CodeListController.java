package sk.spp.nzp.be.controller;

import org.springframework.web.bind.annotation.*;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.annotation.paging.PagingAsQueryParams;
import sk.spp.nzp.be.api.common.PagedResponse;
import sk.spp.nzp.be.api.common.QueryStringPaging;
import sk.spp.nzp.be.service.codelist.CodeListService;
import sk.spp.nzp.be.service.codelist.model.CodeListQuery;
import sk.spp.nzp.commons.api.codelist.CodeListItem;

import java.util.List;

@RestController
@RequestMapping("/code-lists")
public class CodeListController {

    private CodeListService codeListService;

    public CodeListController(CodeListService codeListService) {
        this.codeListService = codeListService;
    }

    @Log
    @LogParam
    @GetMapping("/{type}")
    @PagingAsQueryParams
    public PagedResponse<CodeListItem> list(
            @LogParam("type") @PathVariable String type,
            @LogParam("parent") @RequestParam(required = false) String parent,
            @LogParam("statuses") @RequestParam(name = "status", required = false) List<String> statuses,
            @LogParam("paging")  QueryStringPaging paging
    ) {
        return codeListService.list(type, buildListQuery(parent, statuses, paging));
    }

    private CodeListQuery buildListQuery(String parent, List<String> statuses, QueryStringPaging paging) {
        CodeListQuery query = buildBasicQuery(paging);
        query.setParent(parent);
        query.setStatusesFromString(statuses);

        return query;
    }

    private CodeListQuery buildBasicQuery(QueryStringPaging paging) {
        CodeListQuery query = new CodeListQuery();

        if (paging != null) {
            query.setPaging(paging.toPaging());
        }

        return query;
    }

    @Log
    @LogParam
    @GetMapping("/{type}/uuid/{uuid}")
    public CodeListItem detailByUuid(
            @LogParam("type") @PathVariable String type,
            @LogParam("uuid") @PathVariable String uuid
    ) {
        return codeListService.detail(type, buildDetailByUuidQuery(uuid));
    }

    private CodeListQuery buildDetailByUuidQuery(String uuid) {
        CodeListQuery query = buildBasicQuery(null);
        query.setUuid(uuid);

        return query;
    }

    @Log
    @LogParam
    @GetMapping("/{type}/code/{code}")
    public CodeListItem detailByCode(
            @LogParam("type") @PathVariable String type,
            @LogParam("code") @PathVariable String code
    ) {
        return codeListService.detail(type, buildDetailByCodeQuery(code));
    }

    private CodeListQuery buildDetailByCodeQuery(String code) {
        CodeListQuery query = buildBasicQuery(null);
        query.setCode(code);

        return query;
    }

}
