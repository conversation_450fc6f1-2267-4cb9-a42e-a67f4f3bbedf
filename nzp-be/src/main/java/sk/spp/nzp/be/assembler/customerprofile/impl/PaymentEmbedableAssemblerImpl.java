package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerprofile.Payment;
import sk.spp.nzp.be.assembler.customerprofile.PaymentEmbedableAssembler;
import sk.spp.nzp.be.service.codelist.model.CodeListQuery;
import sk.spp.nzp.be.service.codelist.provider.GenericCodeListProvider;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.enums.CodeListType;
import sk.spp.nzp.commons.model.customerprofile.PaymentEmbeddable;

import java.time.LocalDate;
import java.util.Optional;

@Component
public class PaymentEmbedableAssemblerImpl implements PaymentEmbedableAssembler {

    private GenericCodeListProvider genericCodeListProvider;

    public PaymentEmbedableAssemblerImpl(
            GenericCodeListProvider genericCodeListProvider
    ) {
        this.genericCodeListProvider = genericCodeListProvider;
    }

    @Override
    public Payment map(PaymentEmbeddable input, Payment output, LocalDate executeAt) {
        output.setAmount(input.getAmount());
        output.setExternalId(input.getExternalId());
        output.setType(input.getType());
        output.setVs(input.getVs());
        output.setExecuteAt(executeAt);

        Optional.ofNullable(input.getType()).ifPresent(paymentType -> setPaymentCodeList(input, output));

        return output;
    }

    private void setPaymentCodeList(PaymentEmbeddable input, Payment output) {
        CodeListQuery codeListQuery = new CodeListQuery();
        codeListQuery.setCode(input.getType().getExternalName());
        CodeListItem codeListItem = genericCodeListProvider.get(CodeListType.PAYMENT_TYPE.toString(), codeListQuery);
        output.setPaymentType(codeListItem);
    }
}