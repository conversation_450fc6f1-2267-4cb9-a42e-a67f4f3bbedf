package sk.spp.nzp.be.assembler.reporting.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import sk.spp.nzp.be.assembler.reporting.ReportAssembler;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.api.reporting.DataEntity;
import sk.spp.nzp.commons.api.reporting.PropertyDefinition;
import sk.spp.nzp.commons.api.reporting.Report;
import sk.spp.nzp.commons.api.reporting.ReportDefinition;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.model.reporting.ReportEntity;
import sk.spp.nzp.commons.utils.AliasResolver;
import sk.spp.nzp.commons.utils.DataModelHolder;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class ReportAssemblerImpl implements ReportAssembler {

    @Value("${reporting.customerAccountTable}")
    private String customerAccountTableName;

    @Override
    public ReportEntity map(Report input, ReportEntity output) {
        output.setName(input.getName());
        output.setDefinition(mapDefinition(input.getDefinition()));
        return output;
    }

    @Override
    public ReportEntity mapDefinitionOnly(ReportDefinition input, ReportEntity output) {
        output.setDefinition(mapDefinition(input));
        return output;
    }

    @Override
    public Report map(ReportEntity input, Report output) {
        output.setId(input.getId());
        output.setVersion(input.getVersion());
        output.setCreateAt(input.getCreatedAt());
        output.setCreatedBy(input.getCreatedBy());
        output.setUpdateAt(input.getUpdatedAt());
        output.setUpdateBy(input.getUpdatedBy());
        output.setName(input.getName());
        output.setDefinition(mapDefinition(input));
        return output;
    }

    private Report mapForNotificationTemplate(ReportEntity input, Report output, DataModelHolder dataModelHolder) {
        output.setId(input.getId());
        output.setName(input.getName());
        output.setDefinition(mapDefinitionForNotificationTemplate(input, dataModelHolder));
        return output;
    }

    @Override
    public List<Report> map(Collection<ReportEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new Report())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public List<Report> mapForNotificationTemplate(Collection<ReportEntity> input, DataModelHolder dataModelHolder) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> mapForNotificationTemplate(i, new Report(), dataModelHolder)).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    private String mapDefinition(ReportDefinition definition) {
        String stringDefinition = null;
        if (definition != null) {
            definition.setFormat(null);
            ObjectMapper mapper = new ObjectMapper();

            try {
                stringDefinition = mapper.writeValueAsString(definition);
            } catch (JsonProcessingException e) {
                throw new ApiException(ErrorCode.INVALID_REPORT_DEFINITION_JSON);
            }
        }
        return stringDefinition;
    }

    private ReportDefinition mapDefinition(ReportEntity input) {
        ReportDefinition reportDefinition = null;
        if (input.getDefinition() != null) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                reportDefinition = mapper.readValue(input.getDefinition(), ReportDefinition.class);
            } catch (JsonProcessingException e) {
                throw new ApiException(ErrorCode.INVALID_REPORT_DEFINITION_JSON);
            }
        }
        return reportDefinition;
    }

    private ReportDefinition mapDefinitionForNotificationTemplate(ReportEntity input, DataModelHolder dataModelHolder) {
        ReportDefinition reportDefinition = null;
        AliasResolver aliasResolver = null;
        if (input.getDefinition() != null) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                reportDefinition = mapper.readValue(input.getDefinition(), ReportDefinition.class);
                aliasResolver = new AliasResolver(dataModelHolder, reportDefinition);
            } catch (JsonProcessingException e) {
                throw new ApiException(ErrorCode.INVALID_REPORT_DEFINITION_JSON);
            }
        }

        List<String> filteredProps = new ArrayList<>();

        if (reportDefinition != null) {
            for (String property : reportDefinition.getOutputProps()) {
                String[] splitProperty = property.split("\\.");
                if (splitProperty.length == 2) {
                    DataEntity dataEntity = aliasResolver.getAllAliasedEntities().get(splitProperty[0]);
                    if (dataEntity != null) {
                        PropertyDefinition propertyDefinition = dataModelHolder.getProperty(dataEntity, splitProperty[1]);
                        if (Optional.ofNullable(propertyDefinition).map(PropertyDefinition::getPrimaryKey).orElse(false)
                                && customerAccountTableName.equals(dataEntity.getTableName())) {
                            filteredProps.add(property);
                        }
                    }
                }
            }
            reportDefinition.setOutputProps(filteredProps);

            reportDefinition.setAlias(null);
            reportDefinition.setFormat(null);
            reportDefinition.setDataEntity(null);
            reportDefinition.setJoins(null);
            reportDefinition.setFilterConditions(null);
        }
        return reportDefinition;
    }
}
