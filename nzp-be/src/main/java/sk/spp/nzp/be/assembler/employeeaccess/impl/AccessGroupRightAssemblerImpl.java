package sk.spp.nzp.be.assembler.employeeaccess.impl;

import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import sk.spp.nzp.commons.api.employeeaccess.AccessRight;
import sk.spp.nzp.be.assembler.employeeaccess.AccessGroupRightAssembler;
import sk.spp.nzp.commons.exception.NotFoundException;
import sk.spp.nzp.commons.model.employeeaccess.AccessGroupRightEntity;
import sk.spp.nzp.commons.repository.employeeaccess.AccessRightEntityRepository;

@Component
public class AccessGroupRightAssemblerImpl implements AccessGroupRightAssembler {

    private AccessRightEntityRepository accessRightEntityRepository;
    
    public AccessGroupRightAssemblerImpl(AccessRightEntityRepository accessRightEntityRepository) {
        
        this.accessRightEntityRepository = accessRightEntityRepository;
    }

    @Override
    public AccessGroupRightEntity map(AccessRight input, AccessGroupRightEntity output) {
        
        output.setOperation(input.getOperation());
        output.setQueue(input.getQueue());
        output.setAccessRight(accessRightEntityRepository.findByCode(input.getCode()).orElseThrow(NotFoundException::new));
        
        return output;
    }
    
    @Override
    public AccessRight map(AccessGroupRightEntity input, AccessRight output) {
        
        output.setOperation(input.getOperation());
        output.setQueue(input.getQueue());
        
        output.setAdmin(input.getAccessRight().getAdmin());
        output.setCode(input.getAccessRight().getCode());
        output.setName(input.getAccessRight().getName());
        output.setDescription(input.getAccessRight().getDescription());
        output.setOptionQueue(input.getAccessRight().getOptionQueue());
        
        return output;
    }
    
    @Override
    public List<AccessRight> map(Collection<AccessGroupRightEntity> input) {

        if(input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new AccessRight()))
                    .sorted(Comparator.comparing(AccessRight::getName)).collect(Collectors.toList());
        }
        
        return Collections.emptyList();
    }
}
