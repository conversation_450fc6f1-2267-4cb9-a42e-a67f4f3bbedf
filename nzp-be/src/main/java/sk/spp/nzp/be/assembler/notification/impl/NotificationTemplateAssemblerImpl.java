package sk.spp.nzp.be.assembler.notification.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.employeeprofile.NotificationTemplateSearch;
import sk.spp.nzp.be.api.notification.*;
import sk.spp.nzp.be.assembler.notification.NotificationTemplateAssembler;
import sk.spp.nzp.be.assembler.notification.NotificationTemplateAttachmentAssembler;
import sk.spp.nzp.be.assembler.notification.NotificationTemplateI18NAssembler;
import sk.spp.nzp.commons.api.notification.enums.NotificationScheduleStatus;
import sk.spp.nzp.commons.exception.NotFoundException;
import sk.spp.nzp.commons.model.notification.NotificationTemplateEntity;
import sk.spp.nzp.commons.model.notification.NotificationTemplateVariableEntity;
import sk.spp.nzp.commons.model.reporting.ReportEntity;
import sk.spp.nzp.commons.repository.notification.NotificationScheduleEntityRepository;
import sk.spp.nzp.commons.repository.reporting.ReportEntityRepository;
import sk.spp.nzp.commons.service.notification.NotificationRenderJobData;

import java.time.Clock;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static sk.spp.nzp.commons.api.notification.enums.NotificationScheduleStatus.*;

@Component
public class NotificationTemplateAssemblerImpl implements NotificationTemplateAssembler {

    private final NotificationTemplateI18NAssembler notificationTemplateI18NAssembler;
    private final NotificationTemplateAttachmentAssembler notificationTemplateAttachmentAssembler;
    private final ReportEntityRepository reportEntityRepository;
    private final NotificationScheduleEntityRepository notificationScheduleRepository;
    private final Clock clock;;

    private static final Set<NotificationScheduleStatus> scheduledStatuses = Set.of(CREATED, IN_PROGRESS);

    public NotificationTemplateAssemblerImpl(NotificationTemplateI18NAssembler notificationTemplateI18NAssembler,
                                             NotificationTemplateAttachmentAssembler notificationTemplateAttachmentAssembler,
                                             ReportEntityRepository reportEntityRepository,
                                             NotificationScheduleEntityRepository notificationScheduleRepository,
                                             Clock clock) {

        this.notificationTemplateI18NAssembler = notificationTemplateI18NAssembler;
        this.notificationTemplateAttachmentAssembler = notificationTemplateAttachmentAssembler;
        this.reportEntityRepository = reportEntityRepository;
        this.notificationScheduleRepository = notificationScheduleRepository;
        this.clock = clock;
    }

    @Override
    public NotificationTemplateEntity map(NotificationTemplate input, NotificationTemplateEntity output) {

        output.setAttributes(input.getAttributes());
        output.setCode(input.getCode());
        output.setDescription(input.getDescription());
        output.setCategory(input.getCategory());
        output.setName(input.getName());
        output.setStatus(input.getStatus());
        output.setType(input.getType());
        output.setEnableEmail(input.isEnableEmail());
        output.setEnablePortal(input.isEnablePortal());
        output.setEnableSms(input.isEnableSms());
        output.setEnablePush(input.isEnablePush());
        output.setPriority(input.getPriority());

        output.setExecutionType(input.getExecutionType());
        output.setReportCustomerColumn(input.getReportCustomerColumn());

        if (input.getReportId() != null) {
            ReportEntity reportEntity = reportEntityRepository.findById(input.getReportId())
                    .orElseThrow(NotFoundException::new);
            output.setReport(reportEntity);
        }

        return output;
    }

    @Override
    public NotificationTemplate map(NotificationTemplateEntity input, NotificationTemplate output) {

        output.setId(input.getId());
        output.setAttributes(input.getAttributes());
        output.setCode(input.getCode());
        output.setDescription(input.getDescription());
        output.setCategory(input.getCategory());
        output.setName(input.getName());
        output.setPriority(input.getPriority());
        output.setStatus(input.getStatus());
        output.setType(input.getType());
        output.setGroup(input.getGroup());
        output.setExecutionType(input.getExecutionType());
        output.setReportCustomerColumn(input.getReportCustomerColumn());
        output.setDeleted(input.isDeleted());
        output.setEnableEmail(input.isEnableEmail());
        output.setEnablePortal(input.isEnablePortal());
        output.setEnableSms(input.isEnableSms());
        output.setEnablePush(input.isEnablePush());

        if (input.getReport() != null) {
            output.setReportId(input.getReport().getId());
        }

        return output;
    }

    @Override
    public NotificationTemplateSummary map(NotificationTemplateEntity input, NotificationTemplateSummary output) {

        output.setId(input.getId().toString());
        output.setCode(input.getCode());
        output.setDescription(input.getDescription());
        output.setCategory(input.getCategory());
        output.setName(input.getName());
        output.setPriority(input.getPriority());
        output.setStatus(input.getStatus());
        output.setType(input.getType());
        output.setGroup(input.getGroup());
        output.setExecutionType(input.getExecutionType());
        output.setReportCustomerColumn(input.getReportCustomerColumn());
        output.setDeleted(input.isDeleted());
        output.setEnableEmail(input.isEnableEmail());
        output.setEnablePortal(input.isEnablePortal());
        output.setEnableSms(input.isEnableSms());
        output.setEnablePush(input.isEnablePush());
        if (input.getReport() != null) {
            output.setReportId(input.getReport().getId());
        }
        output.setScheduled(notificationScheduleRepository.existsByNotificationTemplateIdAndStatusInAndScheduledAtIsAfter(input.getId(), scheduledStatuses, LocalDateTime.now(clock)));

        return output;
    }

    @Override
    public List<NotificationTemplateSummary> mapSummaries(Collection<NotificationTemplateEntity> input) {
        if (input != null && !input.isEmpty()) {
            return input.stream().map(templateEntity -> map(templateEntity, new NotificationTemplateSummary())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public NotificationTemplate mapRef(NotificationTemplateEntity input, NotificationTemplate output) {
        output.setId(input.getId());

        return output;
    }

    @Override
    public NotificationTemplate mapFull(NotificationTemplateEntity input, NotificationTemplate output) {
        map(input, output);

        output.setAttachments(new ArrayList<>(notificationTemplateAttachmentAssembler.map(input.getAttachments())));
        output.setI18ns(new ArrayList<>(notificationTemplateI18NAssembler.map(input.getI18ns())));

        return output;
    }

    @Override
    public List<NotificationTemplate> map(Collection<NotificationTemplateEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new NotificationTemplate())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public List<NotificationTemplate> mapFull(Collection<NotificationTemplateEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> mapFull(i, new NotificationTemplate())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public TemplateVariable map(NotificationTemplateVariableEntity input, TemplateVariable output) {
        output.setVariable(input.getVariable());
        output.setName(input.getName());
        output.setDescription(input.getDescription());
        return output;
    }

    @Override
    public TemplateVariables mapTemplateVariables(Collection<NotificationTemplateVariableEntity> input, TemplateVariables output) {

        if (input != null && !input.isEmpty()) {
            output.setVariables(input.stream().map(notificationTemplateVariableEntity -> map(notificationTemplateVariableEntity, new TemplateVariable())).collect(Collectors.toList()));
        }

        return output;
    }


    @Override
    public NotificationTemplateTestResponse map(NotificationRenderJobData input, NotificationTemplateTestResponse output) {
        output.setHeader(input.getHeader());
        output.setHeaderUrl(input.getHeaderUrl());
        output.setEmailBody(input.getEmailBody());
        output.setEmailSubject(input.getEmailSubject());
        output.setSmsBody(input.getSmsBody());
        output.setPushTitle(input.getPushTitle());
        output.setPushBody(input.getPushBody());
        output.setPushText(input.getPushText());
        output.setPushRedirection(input.getPushRedirection());

        return output;
    }

    @Override
    public NotificationTemplateTest map(NotificationTemplateI18N templateI18N, NotificationTemplate notificationTemplate) {
        NotificationTemplateTest template = new NotificationTemplateTest();
        template.setHeader(templateI18N.getHeader());
        template.setHeaderUrl(templateI18N.getHeaderUrl());
        template.setEmailBody(templateI18N.getEmailBody());
        template.setEmailSubject(templateI18N.getEmailSubject());
        template.setSmsBody(templateI18N.getSmsBody());
        template.setLocale(templateI18N.getLocale());
        template.setReportId(notificationTemplate.getReportId());
        return template;
    }

}
