package sk.spp.nzp.be.reporting.entitymappers;

import com.querydsl.core.Tuple;
import com.querydsl.core.types.Expression;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.reporting.entitymappers.columns.AuditLogColumns;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.model.audit.AuditLogEntity;
import sk.spp.nzp.commons.model.audit.QAuditLogEntity;
import sk.spp.nzp.commons.utils.Expressions;

import java.util.ArrayList;
import java.util.List;

public class AuditLogMapper extends AbstractMapper<AuditLogEntity> {

    private List<AuditLogColumns> columnsNames;
    private QAuditLogEntity qAuditLogEntity = QAuditLogEntity.auditLogEntity;
    private Boolean isEmployeeVisible;

    public AuditLogMapper(List<String> columns, Boolean isEmployeeVisible) {
        this.isEmployeeVisible = isEmployeeVisible;
        columnsNames = new ArrayList<>();

        for (String column : columns) {
            AuditLogColumns enumValue = AuditLogColumns.valueOfOrNull(column);
            if (enumValue != null) {
                columnsNames.add(enumValue);
            } else {
                throw new ApiException(ErrorCode.INVALID_COLUMN_NAME);
            }
        }
    }

    @Override
    public Object[] map(Tuple tuple) {
        objArr = new Object[columnsNames.size()];
        for (int i = 0; i < columnsNames.size(); i++) {
            AuditLogColumns auditLogColumns = columnsNames.get(i);
            switch (auditLogColumns) {
                case CREATED_AT:
                    objArr[i] = tuple.get(qAuditLogEntity.createdAt);
                    break;
                case CODE:
                    objArr[i] = tuple.get(qAuditLogEntity.code);
                    break;
                case ENTITY_TYPE:
                    objArr[i] = tuple.get(qAuditLogEntity.entityType);
                    break;
                case ENTITY_ITEM:
                    objArr[i] = tuple.get(qAuditLogEntity.entityItem);
                    break;
                case ENTITY_REFERENCE:
                    objArr[i] = tuple.get(qAuditLogEntity.entityReference);
                    break;
                case EMPLOYEE_LOGIN:
                    objArr[i] = isEmployeeVisible? tuple.get(qAuditLogEntity.employeeLogin) : "";
                    break;
                case EMPLOYEE_PRESENT:
                    String employeeLogin = tuple.get(qAuditLogEntity.employeeLogin);
                    objArr[i] = Expressions.notEmpty(employeeLogin);
                    break;
                case EMPLOYEE_NAME:
                    objArr[i] = isEmployeeVisible? tuple.get(qAuditLogEntity.employeeName) : "";
                    break;
                case EMPLOYEE_EMAIL:
                    objArr[i] = isEmployeeVisible? tuple.get(qAuditLogEntity.employeeEmail) : "";
                    break;
                case BUSINESS_PARTNER_NAME:
                    objArr[i] = tuple.get(qAuditLogEntity.businessPartnerName);
                    break;
                case BUSINESS_PARTNER_EXTERNAL_ID:
                    objArr[i] = tuple.get(qAuditLogEntity.businessPartnerExternalId);
                    break;
                case RELATED_CUSTOMER_NAME:
                    objArr[i] = tuple.get(qAuditLogEntity.relatedCustomerAccountName);
                    break;
                case RELATED_CUSTOMER_EMAIL:
                    objArr[i] = tuple.get(qAuditLogEntity.relatedCustomerAccountEmail);
                    break;
                case LOGGED_CUSTOMER_NAME:
                    objArr[i] = tuple.get(qAuditLogEntity.loggedCustomerAccountName);
                    break;
                case LOGGED_CUSTOMER_EMAIL:
                    objArr[i] = tuple.get(qAuditLogEntity.loggedCustomerAccountEmail);
                    break;
            }
        }
        return objArr;
    }

    @Override
    public void select(JPAQuery<AuditLogEntity> query) {
        Expression[] expressions = new Expression[columnsNames.size()];
        for (int i = 0; i < columnsNames.size(); i++) {
            AuditLogColumns auditLogColumns = columnsNames.get(i);

            switch (auditLogColumns) {
                case CREATED_AT:
                    expressions[i] = qAuditLogEntity.createdAt;
                    break;
                case CODE:
                    expressions[i] = qAuditLogEntity.code;
                    break;
                case ENTITY_TYPE:
                    expressions[i] = qAuditLogEntity.entityType;
                    break;
                case ENTITY_ITEM:
                    expressions[i] = qAuditLogEntity.entityItem;
                    break;
                case ENTITY_REFERENCE:
                    expressions[i] = qAuditLogEntity.entityReference;
                    break;
                case EMPLOYEE_LOGIN:
                    expressions[i] = qAuditLogEntity.employeeLogin;
                    break;
                case EMPLOYEE_NAME:
                    expressions[i] = qAuditLogEntity.employeeName;
                    break;
                case EMPLOYEE_EMAIL:
                    expressions[i] = qAuditLogEntity.employeeEmail;
                    break;
                case EMPLOYEE_PRESENT:
                    expressions[i] = qAuditLogEntity.employeeLogin;
                    break;
                case BUSINESS_PARTNER_NAME:
                    expressions[i] = qAuditLogEntity.businessPartnerName;
                    break;
                case BUSINESS_PARTNER_EXTERNAL_ID:
                    expressions[i] = qAuditLogEntity.businessPartnerExternalId;
                    break;
                case RELATED_CUSTOMER_NAME:
                    expressions[i] = qAuditLogEntity.relatedCustomerAccountName;
                    break;
                case RELATED_CUSTOMER_EMAIL:
                    expressions[i] = qAuditLogEntity.relatedCustomerAccountEmail;
                    break;
                case LOGGED_CUSTOMER_NAME:
                    expressions[i] = qAuditLogEntity.loggedCustomerAccountName;
                    break;
                case LOGGED_CUSTOMER_EMAIL:
                    expressions[i] = qAuditLogEntity.loggedCustomerAccountEmail;
                    break;
            }
        }
        query.select(expressions);
    }
}
