package sk.spp.nzp.be.assembler.customerprofile;

import sk.spp.nzp.be.api.customerprofile.*;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.model.customerprofile.UnitedDeliveryPointEntity;

import java.util.List;
import java.util.UUID;

public interface UnitedDeliveryPointAssembler {

    UnitedDeliveryPoint map(UnitedDeliveryPointEntity unitedDeliveryPointEntity, UnitedDeliveryPoint unitedDeliveryPoint, UUID customerId, boolean includeInactive, List<DeliveryPoint.Fetch> fetches);

    UnitedDeliveryPointSummary map(UnitedDeliveryPointEntity unitedDeliveryPointEntity, UnitedDeliveryPointSummary unitedDeliveryPointSummary, UUID customerId, boolean includeInactive);
    UnitedDeliveryPointSummary map(UnitedDeliveryPointEntity unitedDeliveryPointEntity, UnitedDeliveryPointSummary unitedDeliveryPointSummary, UUID customerId, boolean includeInactive, List<DeliveryPointSummary.Fetch> fetches);

    UnitedDeliveryPointSummary map(UnitedDeliveryPointEntity unitedDeliveryPointEntity, UnitedDeliveryPointSummary unitedDeliveryPointSummary, UUID customerId, List<DeliveryPointSummary.Fetch> fetches, boolean includeInactive, BusinessPartnerQueue bpQueueSecurity);

    UnitedDeliveryPointSummary mapWithoutBP(UnitedDeliveryPointEntity input, UnitedDeliveryPointSummary output, UUID customerId, List<DeliveryPointSummary.Fetch> fetches, boolean includeInactive, BusinessPartnerQueue bpQueueSecurity, boolean includeDp);

    UnitedDeliveryPointAddress map(UnitedDeliveryPointEntity input, UnitedDeliveryPointAddress output);
}
