package sk.spp.nzp.be.controller;

import org.springframework.web.bind.annotation.*;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.customeraccess.CustomerAccount;
import sk.spp.nzp.be.api.customeraccess.PasswordChange;
import sk.spp.nzp.be.api.identitymanagement.CustomerAuthReq;
import sk.spp.nzp.be.service.customeraccess.CustomerService;
import sk.spp.nzp.commons.api.identitymanagement.AccountSearch;
import sk.spp.nzp.commons.exception.ApiException;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.UUID;

@RestController
@RequestMapping("/idm/customer-accounts")
public class IdmCustomerAccountController {

    private CustomerService customerService;

    public IdmCustomerAccountController(CustomerService customerService) {
        this.customerService = customerService;
    }

    @Log
    @LogParam
    @PostMapping("/authenticate")
    public CustomerAccount authenticateIdm(@LogParam("authReq") @Valid @RequestBody CustomerAuthReq authReq) {
        return customerService.authenticate(authReq);
    }

    @Log
    @LogParam
    @PostMapping("/search")
    public Iterable<CustomerAccount> findAccount(@LogParam("searchReq") @Valid @RequestBody AccountSearch searchReq) {
        try {
            return customerService.findAccountBySearch(searchReq);
        } catch (ApiException ex) {
            return new ArrayList<>();
        }
    }

    @Log
    @LogParam
    @PutMapping("/{customerUuid}/password")
    public void changePasswordIdm(@Valid @RequestBody PasswordChange passChangeReq,
                               @LogParam("customerUuid") @PathVariable UUID customerUuid) {
        customerService.changePassword(customerUuid, passChangeReq, false);
    }

}
