package sk.spp.nzp.be.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.identitymanagement.Event;
import sk.spp.nzp.be.service.identitymanagement.IdmEventService;

import javax.validation.Valid;

@RestController
@RequestMapping("/idm")
public class IdmEventController {

    private IdmEventService idmEventService;

    public IdmEventController(IdmEventService idmEventService) {
        this.idmEventService = idmEventService;
    }

    @Log
    @LogParam
    @PostMapping("/events")
    public void reportEvent(@LogParam("event") @Valid @RequestBody Event event) {
        idmEventService.reportEvent(event);
    }

}

