package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerprofile.UnitedDeliveryPointFullSearch;
import sk.spp.nzp.be.api.customerprofile.UnitedDeliveryPointSearch;
import sk.spp.nzp.be.assembler.customerprofile.UnitedDeliveryPointSearchAssembler;

@Component
public class UnitedDeliveryPointSearchAssemblerImpl implements UnitedDeliveryPointSearchAssembler {

    @Override
    public UnitedDeliveryPointSearch map(
            UnitedDeliveryPointFullSearch input, UnitedDeliveryPointSearch output) {

        if (input == null) {
            return output;
        }

        output.setType(input.getType());
        output.setFt(input.getFt());
        output.setBusinessPartner(input.getBusinessPartner());
        output.setDeliveryPoint(input.getDeliveryPoint());

        return output;
    }
}