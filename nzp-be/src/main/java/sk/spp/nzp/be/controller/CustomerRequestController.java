package sk.spp.nzp.be.controller;

import org.apache.tomcat.util.http.parser.Authorization;
import org.springframework.core.io.Resource;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.customerrequest.*;
import sk.spp.nzp.be.service.customerrequest.CustomerRequestService;
import sk.spp.nzp.commons.security.NzpAuthenticationToken;

import java.util.UUID;

@RestController
@RequestMapping("/customer-requests")
public class CustomerRequestController {

    private final CustomerRequestService customerRequestService;

    public CustomerRequestController(CustomerRequestService customerRequestService) {
        this.customerRequestService = customerRequestService;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))")
    @GetMapping("/{uuid}")
    public CustomerRequest getCustomerRequest(@LogParam("uuid") @PathVariable UUID uuid) {
        return customerRequestService.getCustomerRequest(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))")
    @PostMapping("/{uuid}/cancel")
    public CustomerRequest cancelCustomerRequest(@LogParam("uuid") @PathVariable UUID uuid) {
        return customerRequestService.cancelCustomerRequest(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))")
    @GetMapping("/attachments/{uuid}/content")
    public ResponseEntity<Resource> getAttachmentContent(@LogParam("uuid") @PathVariable UUID uuid) {
        CustomerRequestAttachmentWithContent attachment = customerRequestService.getAttachmentWithContent(uuid);
        return getAttachmentResponseEntity(attachment);
    }

    private ResponseEntity<Resource> getAttachmentResponseEntity(CustomerRequestAttachmentWithContent attachment) {
        ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                .filename(attachment.getName())
                .build();

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, attachment.getMimeType());
        headers.add(HttpHeaders.CONTENT_LENGTH, "" + attachment.getLength());
        headers.add(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());

        return new ResponseEntity<>(attachment.getContentAsResource(), headers, HttpStatus.OK);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))")
    @PostMapping("/{uuid}/attachments")
    public CustomerRequestAttachment uploadAttachment(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("draftMetadata") @RequestParam(required = false) String draftMetadata,
            @RequestParam MultipartFile file
    ) {
        return customerRequestService.uploadAttachment(uuid, draftMetadata, file);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))")
    @DeleteMapping("/attachments/{uuid}")
    @ResponseStatus(HttpStatus.ACCEPTED)
    public void deleteAttachment(@LogParam("uuid") @PathVariable UUID uuid) {
        customerRequestService.deleteAttachment(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))")
    @PostMapping("/{uuid}/send")
    public CustomerRequest sendCustomerRequest(@LogParam("uuid") @PathVariable UUID uuid) {
        return customerRequestService.sendCustomerRequest(uuid);
    }

    @Log
    @LogParam
    @PostMapping("/{uuid}/customer-confirm")
    public CustomerRequest confirmRequest(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("customerRequestConfirmation") @RequestBody CustomerRequestConfirmation customerRequestConfirmation) {
        return customerRequestService.confirmCustomerRequest(uuid, customerRequestConfirmation);
    }

    @Log
    @LogParam
    @GetMapping("/submit-info")
    public CustomerRequestSubmitInfo submitInfo(@LogParam("code") @RequestBody CustomerRequestSubmitInfoRequest request) {
        return customerRequestService.getCustomerRequestInfo(request);
    }

}
