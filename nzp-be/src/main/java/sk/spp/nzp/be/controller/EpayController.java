package sk.spp.nzp.be.controller;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.epay.TransactionChangeRequest;
import sk.spp.nzp.be.service.epay.EpayService;

import java.util.UUID;

@RestController
@RequestMapping("/epay")
public class EpayController {

    private EpayService epayService;

    public EpayController(EpayService epayService){
        this.epayService = epayService;
    }

    @Log
    @LogParam
    @PostMapping(value = "/{transactionId}/change-status", produces = {MediaType.APPLICATION_JSON_VALUE})
    public void getByUuid(@LogParam("transactionId") @PathVariable UUID transactionId,
                          @LogParam("transactionChangeRequest") @RequestBody TransactionChangeRequest changeRequest) {

        epayService.updateTransaction(transactionId, changeRequest);
    }
}
