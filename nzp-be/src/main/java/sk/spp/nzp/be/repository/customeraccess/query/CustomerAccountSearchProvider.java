package sk.spp.nzp.be.repository.customeraccess.query;

import static sk.spp.nzp.commons.model.customeraccess.QCustomerAccountEntity.customerAccountEntity;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.EntityManager;

import org.apache.commons.lang3.StringUtils;

import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQuery;

import sk.spp.nzp.be.api.common.Sorting;
import sk.spp.nzp.be.api.common.SortingAttribute;
import sk.spp.nzp.be.api.customeraccess.CustomerAccountSearch;
import sk.spp.nzp.be.api.customeraccess.CustomerApprovalSearch;
import sk.spp.nzp.be.repository.common.AbstractQueryProvider;
import sk.spp.nzp.commons.utils.FTSearchUtils;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;
import sk.spp.nzp.commons.model.customeraccess.QCustomerAccountEntity;
import sk.spp.nzp.commons.model.customeraccess.QCustomerApprovalEntity;
import sk.spp.nzp.commons.model.customerprofile.QBusinessPartnerEntity;
import sk.spp.nzp.commons.model.customersharing.QBusinessPartnerOwnershipEntity;

public class CustomerAccountSearchProvider extends AbstractQueryProvider<CustomerAccountEntity>{

    CustomerAccountSearch customerAccountSearch;
    
    public CustomerAccountSearchProvider(CustomerAccountSearch customerAccountSearch) {
        
        this.customerAccountSearch = customerAccountSearch;
    }

    @Override
    public JPAQuery<CustomerAccountEntity> getQuery(EntityManager em) {
        
        JPAQuery<CustomerAccountEntity> query = new JPAQuery<CustomerAccountEntity>(em).from(customerAccountEntity);
        
        BooleanExpression nameExp = FTSearchUtils.fulltextSearch(customerAccountSearch.getName(), customerAccountEntity.fullName);
        
        BooleanExpression emailExp = null;
        
        if(!StringUtils.isBlank(customerAccountSearch.getEmail())) {
            emailExp = customerAccountEntity.email.containsIgnoreCase(customerAccountSearch.getEmail());
        }
        
        
        BooleanExpression phoneExp = null;
        
        if(!StringUtils.isBlank(customerAccountSearch.getPhone())) {
            phoneExp = customerAccountEntity.phone.containsIgnoreCase(customerAccountSearch.getPhone());
        }
        
        
        BooleanExpression registrationAtExp = null;
        
        if(customerAccountSearch.getRegistrationAt() != null) {
            registrationAtExp = customerAccountEntity.registrationAt.between(customerAccountSearch.getRegistrationAt().getFrom(), customerAccountSearch.getRegistrationAt().getTo());
        }
        
        
        BooleanExpression loginSuccessAtExp = null;
        
        if(customerAccountSearch.getLoginSuccessAt() != null) {
            loginSuccessAtExp = customerAccountEntity.loginSuccessAt.between(customerAccountSearch.getLoginSuccessAt().getFrom(), customerAccountSearch.getLoginSuccessAt().getTo());
        }
        
        
        BooleanExpression statusesExp = null;
        
        if(customerAccountSearch.getStatuses() != null) {
            statusesExp = customerAccountEntity.status.in(customerAccountSearch.getStatuses());
        }
        
        
        BooleanExpression approvalsExp = getApprovalsExp(customerAccountEntity);
        
        
        query = createBusinessPartnerConditions(query, customerAccountEntity);
        
        
        
        query = query.where(nameExp, emailExp, phoneExp, registrationAtExp, loginSuccessAtExp, statusesExp, approvalsExp).orderBy(getOrderBy()).distinct();
        
        return query;
    }

    private BooleanExpression getApprovalsExp(QCustomerAccountEntity customerAccountEntity) {
        
        BooleanExpression output = null;
        
        if(customerAccountSearch.getApprovals() != null) {
            
            QCustomerApprovalEntity customerApprovalEntity = QCustomerApprovalEntity.customerApprovalEntity;
            
            for(CustomerApprovalSearch cam: customerAccountSearch.getApprovals()) {
                
                BooleanExpression typeExp = null;
                
                if(cam.getType() != null) {
                    
                    typeExp = customerApprovalEntity.type.eq(cam.getType());
                }
                
                BooleanExpression approvalExp = null;
                BooleanExpression missingApprovalExp = null;

                if(cam.getApproval() != null) {

                    approvalExp = customerApprovalEntity.approval.eq(cam.getApproval());

                    if (cam.getApproval().equals(Boolean.FALSE)) {
                        missingApprovalExp = JPAExpressions.select().from(customerApprovalEntity).where(customerApprovalEntity.customer().eq(customerAccountEntity), typeExp).notExists();
                    }
                }
                
                BooleanExpression subQueryExp = JPAExpressions.select().from(customerApprovalEntity).where(customerApprovalEntity.customer().eq(customerAccountEntity), approvalExp, typeExp).exists();

                if (missingApprovalExp != null) {
                    subQueryExp = subQueryExp.or(missingApprovalExp);
                }

                if(output == null) {
                    
                    output = subQueryExp;
                    
                } else {
                    
                    output = output.and(subQueryExp);
                    
                }
            }
        }
        
        return output;
    }
    
    private JPAQuery<CustomerAccountEntity> createBusinessPartnerConditions(JPAQuery<CustomerAccountEntity> query, QCustomerAccountEntity customerAccountEntity) {
        
        if(customerAccountSearch.getBusinessPartner() != null && (customerAccountSearch.getBusinessPartner().getExternalId() != null || customerAccountSearch.getBusinessPartner().getQueue() != null)) {
            
            QBusinessPartnerOwnershipEntity businessPartnerOwnershipEntity = QBusinessPartnerOwnershipEntity.businessPartnerOwnershipEntity;
            QBusinessPartnerEntity businessPartnerEntity = QBusinessPartnerEntity.businessPartnerEntity;
            
            BooleanExpression filterExp = null;
            
            if(customerAccountSearch.getBusinessPartner().getExternalId() != null) {
                filterExp = businessPartnerEntity.externalId.containsIgnoreCase(customerAccountSearch.getBusinessPartner().getExternalId());
            }
            
            if(customerAccountSearch.getBusinessPartner().getQueue() != null) {
                
                if(filterExp == null) {
                    
                    filterExp = businessPartnerEntity.queue.eq(customerAccountSearch.getBusinessPartner().getQueue());
                    
                } else {
                    
                    filterExp = filterExp.and(businessPartnerEntity.queue.eq(customerAccountSearch.getBusinessPartner().getQueue()));
                    
                }
            }
            
            query = query.join(businessPartnerOwnershipEntity).on(businessPartnerOwnershipEntity.customerAccount().eq(customerAccountEntity))
                    .join(businessPartnerEntity).on(businessPartnerEntity.eq(businessPartnerOwnershipEntity.businessPartner()), filterExp);
        }
        
        return query;
    }

    private OrderSpecifier<?>[] getOrderBy() {
        
        if(customerAccountSearch.getPaging() == null || customerAccountSearch.getPaging().getSort() == null || customerAccountSearch.getPaging().getSort().isEmpty()) {

            return getDefaultOrderBy();
        }
        
        List<OrderSpecifier<?>> sortingList = new ArrayList<>();
        
        for(Sorting sorting: customerAccountSearch.getPaging().getSort()) {
            
            if(SortingAttribute.CUSTOMER_ACCOUNT_NAME.name().contentEquals(sorting.getAttribute()) || SortingAttribute.CUSTOMER_ACCOUNT_NAME.getCamelCaseName().contentEquals(sorting.getAttribute())) {
                
                sortingList.add(getOrderSpecifier(sorting, customerAccountEntity.lastName));
                sortingList.add(getOrderSpecifier(sorting, customerAccountEntity.firstName)); 
                
            } else if(SortingAttribute.CUSTOMER_ACCOUNT_EMAIL.name().contentEquals(sorting.getAttribute()) || SortingAttribute.CUSTOMER_ACCOUNT_NAME.getCamelCaseName().contentEquals(sorting.getAttribute())) {
                
                sortingList.add(getOrderSpecifier(sorting, customerAccountEntity.email));
                
            } else if(SortingAttribute.CUSTOMER_ACCOUNT_PHONE.name().contentEquals(sorting.getAttribute()) || SortingAttribute.CUSTOMER_ACCOUNT_NAME.getCamelCaseName().contentEquals(sorting.getAttribute())) {
                
                sortingList.add(getOrderSpecifier(sorting, customerAccountEntity.phone));
                
            }
        }
        
        if(sortingList.isEmpty()) {
            
            return getDefaultOrderBy();
        }
        
        OrderSpecifier<?>[] output = new OrderSpecifier<?>[sortingList.size()];
        
        return sortingList.toArray(output);
    }
    
    private OrderSpecifier<?>[] getDefaultOrderBy() {
        
        OrderSpecifier<?>[] output = new OrderSpecifier<?>[1];
        output[0] = customerAccountEntity.createdAt.asc();
        
        return output;
    }
}
