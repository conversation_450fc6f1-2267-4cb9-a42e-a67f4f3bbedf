package sk.spp.nzp.be.repository.customerprofile.query;

import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.api.notification.setting.NotificationSettingsView;
import sk.spp.nzp.commons.model.notification.QCustomerNotificationSettingEntity;
import sk.spp.nzp.commons.model.notification.QNotificationTemplateEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

import javax.persistence.EntityManager;
import java.util.UUID;

public class NotificationSettingsFindQueryProvider implements QueryDslProvider<NotificationSettingsView> {

    private UUID customerId;
    private UUID unitedDeliveryPointId;

    public NotificationSettingsFindQueryProvider(UUID customerId, UUID unitedDeliveryPointId) {
        this.customerId = customerId;
        this.unitedDeliveryPointId = unitedDeliveryPointId;
    }

    @Override
    public JPAQuery<NotificationSettingsView> getQuery(EntityManager em) {
        return findByCustomerAndUnitedDeliveryPoint(em);
    }

    public JPAQuery<NotificationSettingsView> findByCustomerAndUnitedDeliveryPoint(EntityManager em) {
        
        QNotificationTemplateEntity notificationTemplate = QNotificationTemplateEntity.notificationTemplateEntity;
        QCustomerNotificationSettingEntity notificationSetting = QCustomerNotificationSettingEntity.customerNotificationSettingEntity;

        return new JPAQuery<NotificationSettingsView>(em).from(notificationTemplate)
                .select(Projections.constructor(NotificationSettingsView.class,
                        notificationTemplate,
                        notificationSetting))
                .leftJoin(notificationSetting).on(
                        notificationTemplate.id.eq(notificationSetting.notificationTemplate().id)
                        .and(notificationSetting.customerAccount().id.eq(customerId)
                        .and(notificationSetting.unitedDeliveryPoint().id.eq(unitedDeliveryPointId))));
    }
}