package sk.spp.nzp.be.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.annotation.paging.PagingAsQueryParams;
import sk.spp.nzp.be.api.common.PagedResponse;
import sk.spp.nzp.be.api.common.QueryStringPaging;
import sk.spp.nzp.be.service.locales.LocaleHistoryService;
import sk.spp.nzp.be.service.locales.LocaleService;
import sk.spp.nzp.commons.api.locales.LocaleEditRequest;
import sk.spp.nzp.commons.api.locales.LocaleHistoryExport;
import sk.spp.nzp.commons.api.locales.LocaleHistoryImportReport;
import sk.spp.nzp.commons.api.locales.LocaleHistorySearchResponse;

import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;
import java.util.Map;

@RestController
@RequestMapping("/admin/locales")
public class LocaleController {

    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Autowired
    private LocaleService localeService;

    @Autowired
    private LocaleHistoryService localeHistoryService;

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('LOCALE_FULL_IMPORT')")
    @PutMapping(value = "/{locale}/full-import", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public void fullImport(
            @PathVariable String locale,
            @RequestParam MultipartFile file) {

        localeService.fullImport(new Locale(locale), file);
    }

    @Log
    @LogParam
    @GetMapping(value = "/{locale}/export", produces = {MediaType.APPLICATION_JSON_VALUE})
    public Map<String, Object> export(
            @PathVariable String locale) {

        return localeService.export(locale);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('LOCALE_EDIT')")
    @PutMapping(value = "/{locale}/edit-entry", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void editEntry(
            @PathVariable String locale,
            @RequestBody @Valid LocaleEditRequest editRequest) {

        localeService.editEntry(new Locale(locale), editRequest);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('LOCALE_HISTORY_IMPORT')")
    @PostMapping(value = "/{locale}/history/import/analyze", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public LocaleHistoryImportReport historyImportAnalyze(
            @PathVariable String locale,
            @RequestParam MultipartFile file) {

        return localeService.historyImportAnalyze(new Locale(locale), file);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('LOCALE_HISTORY_IMPORT')")
    @PutMapping(value = "/{locale}/history/import", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public void historyImport(
            @PathVariable String locale,
            @RequestParam(required = false) List<String> confirmedPathsToOverride,
            @RequestParam MultipartFile file) {

        localeService.historyImport(new Locale(locale), confirmedPathsToOverride, file);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('LOCALE_HISTORY_IMPORT')")
    @GetMapping(value = "/{locale}/history/export", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Resource> exportHistory(
            @PathVariable String locale) throws JsonProcessingException {

        LocaleHistoryExport localeHistory = localeHistoryService.exportChanges(new Locale(locale));
        return getAsResponseEntity(localeHistory);
    }

    @Log
    @LogParam
    @PagingAsQueryParams
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('LOCALE_EDIT')")
    @PostMapping(value = "/{locale}/history/search", produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<LocaleHistorySearchResponse> searchHistory(
            @PathVariable String locale,
            @LogParam("paging") QueryStringPaging paging) {

        return localeHistoryService.search(new Locale(locale), paging);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('LOCALE_EDIT')")
    @PostMapping(value = "/{locale}/history/{path}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public List<LocaleHistorySearchResponse> searchHistoryByPath(
            @PathVariable String locale,
            @PathVariable String path) {

        return localeHistoryService.search(new Locale(locale), path);
    }

    private ResponseEntity<Resource> getAsResponseEntity(Object response) throws JsonProcessingException {

        final ContentDisposition contentDisposition = ContentDisposition.builder("json")
                .filename(String.format("HistoryExport_%s.json", DATE_TIME_FORMAT.format(LocalDateTime.now())))
                .build();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentDisposition(contentDisposition);

        final ObjectMapper mapper = new ObjectMapper();

        final byte[] responseBytes = mapper.writeValueAsBytes(response);
        InputStreamResource resource = new InputStreamResource(new ByteArrayInputStream(responseBytes));

        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(responseBytes.length)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

}