package sk.spp.nzp.be.repository.customerprofile.query;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.DateTimeExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.api.customersharing.enums.OwnershipType;
import sk.spp.nzp.commons.model.customerprofile.ContractAccountEntity;
import sk.spp.nzp.commons.model.customerprofile.QContractAccountEntity;
import sk.spp.nzp.commons.model.customerprofile.QContractEntity;
import sk.spp.nzp.commons.model.customersharing.QContractAccountOwnershipEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

import javax.persistence.EntityManager;
import java.time.LocalDate;
import java.util.UUID;

public class ContractAccountFindByUnitedDeliveryPointUuidQueryProvider implements QueryDslProvider<ContractAccountEntity> {

    private UUID unitedDeliveryPointUuid;
    private UUID customerUuid;
    private Boolean eInvoiceActive;
    private final BusinessPartnerQueue businessPartnerQueue;

    public ContractAccountFindByUnitedDeliveryPointUuidQueryProvider(UUID unitedDeliveryPointUuid, UUID customerUuid, Boolean eInvoiceActive, BusinessPartnerQueue businessPartnerQueue) {
        this.unitedDeliveryPointUuid = unitedDeliveryPointUuid;
        this.customerUuid = customerUuid;
        this.eInvoiceActive = eInvoiceActive;
        this.businessPartnerQueue = businessPartnerQueue;
    }

    @Override
    public JPAQuery<ContractAccountEntity> getQuery(EntityManager em) {

        QContractAccountEntity contractAccountEntity = QContractAccountEntity.contractAccountEntity;
        QContractEntity contractEntity = QContractEntity.contractEntity;
        QContractAccountOwnershipEntity contractAccountOwnershipEntity = QContractAccountOwnershipEntity.contractAccountOwnershipEntity;

        BooleanExpression contractAccountOwnerExp = contractAccountOwnershipEntity.type.eq(OwnershipType.OWNER);
        JPQLQuery<String> caFromUdp =JPAExpressions.selectDistinct(contractEntity.contractAccountId)
                .from(contractEntity)
                .where(
                    contractEntity.unitedDeliveryPoint().id.eq(unitedDeliveryPointUuid).and(
                        DateTimeExpression.currentDate(LocalDate.class).between(contractEntity.effectiveFrom, contractEntity.effectiveTo)
                    )
                );

        BooleanExpression udpExp = contractAccountEntity.id.in(caFromUdp);

        BooleanExpression eInvoiceActiveExp = null;
        if (eInvoiceActive != null) {
            eInvoiceActiveExp = contractAccountEntity.eInvoice.eq(eInvoiceActive);
        }

        BooleanExpression queueExp = null;
        if (businessPartnerQueue != null) {
            queueExp = contractAccountEntity.businessPartner().queue.eq(businessPartnerQueue);
        }

        return new JPAQuery<ContractAccountEntity>(em).from(contractAccountEntity)
                .join(contractAccountOwnershipEntity).on(contractAccountOwnershipEntity.customerAccount().id.eq(customerUuid), contractAccountOwnershipEntity.contractAccount().id.eq(contractAccountEntity.id))
                .where(udpExp, contractAccountOwnerExp)
                .where(eInvoiceActiveExp)
                .where(queueExp)
                .distinct();
    }
}