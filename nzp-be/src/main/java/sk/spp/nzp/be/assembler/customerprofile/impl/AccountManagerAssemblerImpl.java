package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerprofile.AccountManager;
import sk.spp.nzp.be.assembler.customerprofile.AccountManagerAssembler;

@Component
public class AccountManagerAssemblerImpl implements AccountManagerAssembler {

    @Override
    public AccountManager map(sk.spp.nzp.commons.model.customerprofile.AccountManager input, AccountManager output) {
        output.setEmail(input.getEmail());
        output.setExternalId(input.getExternalId());
        output.setFirstName(input.getFirstName());
        output.setLastName(input.getLastName());
        output.setPhone(input.getPhone());
        return output;
    }
}
