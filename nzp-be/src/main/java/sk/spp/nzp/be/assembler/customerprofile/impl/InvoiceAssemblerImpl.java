package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.apache.commons.lang3.RegExUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.common.Sharing;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerSummary;
import sk.spp.nzp.be.api.customerprofile.ContractAccountSummary;
import sk.spp.nzp.be.api.customerprofile.DeliveryPointSummary;
import sk.spp.nzp.be.api.customerprofile.Invoice;
import sk.spp.nzp.be.assembler.customerprofile.*;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceStatus;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceTypeGroup;
import sk.spp.nzp.commons.assembler.codelist.CodeListItemAssembler;
import sk.spp.nzp.commons.enums.CodeListType;
import sk.spp.nzp.commons.enums.InvoiceTypeCode;
import sk.spp.nzp.commons.model.customerprofile.InvoiceEntity;
import sk.spp.nzp.commons.model.customerprofile.InvoiceRawEntity;
import sk.spp.nzp.commons.service.common.HistoryContextProvider;
import sk.spp.nzp.commons.service.common.impl.HistoryPeriod;
import sk.spp.nzp.commons.utils.Expressions;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class InvoiceAssemblerImpl implements InvoiceAssembler {

    public final static String LEADING_ZERO_REGEX = "^0+";

    private DeliveryPointAssembler deliveryPointAssembler;
    private CodeListItemAssembler genericCodeListAssembler;
    private InvoiceItemAssembler invoiceItemAssembler;
    private BusinessPartnerAssembler businessPartnerAssembler;
    private ContractAccountAssembler contractAccountAssembler;
    private HistoryContextProvider historyContextProvider;

    public InvoiceAssemblerImpl(DeliveryPointAssembler deliveryPointAssembler,
                                CodeListItemAssembler genericCodeListAssembler,
                                InvoiceItemAssembler invoiceItemAssembler,
                                BusinessPartnerAssembler businessPartnerAssembler,
                                ContractAccountAssembler contractAccountAssembler,
                                HistoryContextProvider historyContextProvider) {

        this.deliveryPointAssembler = deliveryPointAssembler;
        this.genericCodeListAssembler = genericCodeListAssembler;
        this.invoiceItemAssembler = invoiceItemAssembler;
        this.businessPartnerAssembler = businessPartnerAssembler;
        this.contractAccountAssembler = contractAccountAssembler;
        this.historyContextProvider = historyContextProvider;
    }

    @Override
    public Invoice map(InvoiceEntity input, Invoice output) {

        if (input == null) // external entities may not be imported during assembling
            return null;

        output.setId(input.getId());
        output.setExternalId(RegExUtils.replaceFirst(input.getExternalId(), LEADING_ZERO_REGEX,""));
        output.setReference(RegExUtils.replaceFirst(input.getReference(), LEADING_ZERO_REGEX,""));
        output.setStatus(input.getStatus());
        output.setAmount(input.getAmount());
        output.setUnpaid(input.getUnpaid());
        output.setOverpaid(input.getOverpaid());
        output.setFileArchiveId(input.getFileArchiveId());
        output.setTypeGroup(input.getTypeGroup());
        output.setDueAt(input.getDueAt());
        output.setIssueAt(input.getIssueAt());
        output.setVs(RegExUtils.replaceFirst(input.getVs(), LEADING_ZERO_REGEX,""));
        output.setContainsPaymentPlan(input.getContainsPaymentPlan());
        output.setContainsPaymentRequest(input.getContainsPaymentRequest());

        if(input.getType() != null) {
            output.setType(genericCodeListAssembler.mapSummary(CodeListType.INVOICE_TYPE, input.getType(), new CodeListItem()));
        }

        Optional.ofNullable(input.getSubType())
                .map(subType -> genericCodeListAssembler.mapSummary(CodeListType.INVOICE_SUB_TYPE, subType.name(), new CodeListItem()))
                .ifPresent(output::setSubType);

        if ( input.getInvoiceContractList() != null ) {
            output.setDeliveryPoints(
                    input.getInvoiceContractList()
                            .stream()
                            .filter(v->Expressions.tryGet(()->v.getContract().getDeliveryPoint()) != null)
                            .map(v->deliveryPointAssembler.map(
                                    v.getContract().getDeliveryPoint(),
                                    new DeliveryPointSummary(),
                                    historyContextProvider.createDeliveryPointHistoryContext(v.getContract(), HistoryPeriod.CURRENT)))
                            .collect(Collectors.toList())
                    );
        }

        if (input.getBusinessPartner() != null) {
            output.setBusinessPartner(businessPartnerAssembler.map(input.getBusinessPartner(), new BusinessPartnerSummary()));
        }

        if (input.getContractAccount() != null) {
            output.setContractAccount(contractAccountAssembler.mapSummary(input.getContractAccount(), new ContractAccountSummary(), false));
        }

        setInstallmentVisibility(output);

        return output;
    }
    
    @Override
    public Invoice mapFull(InvoiceEntity input, Invoice output) {

        if (input == null) // external entities may not be imported during assembling
            return null;

        map(input, output);

        Set<InvoiceRawEntity> invoiceRawSet = new HashSet<>();
        List<InvoiceRawEntity> invoiceRawEntities = Optional.ofNullable(input.getInvoiceRawList()).orElse(new ArrayList<>());
        invoiceRawSet.addAll(invoiceRawEntities);
        List<InvoiceRawEntity> invoiceRawFAEntities = Optional.ofNullable(input.getInvoiceRawFaList()).orElse(new ArrayList<>());
        invoiceRawSet.addAll(invoiceRawFAEntities);

        if(Expressions.notEmpty(invoiceRawSet)) {
            output.setInvoiceItems(invoiceItemAssembler.mapFull(invoiceRawSet));
        }
        return output;
    }
    
    @Override
    public List<Invoice> map(Collection<InvoiceEntity> input) {

        if(input != null && !input.isEmpty()) {
            
            return input.stream().map(i -> map(i, new Invoice())).collect(Collectors.toList());
        }
        
        return Collections.emptyList();
    }

    private void setInstallmentVisibility(Invoice input) {

        boolean isInvoiceGroup = InvoiceTypeGroup.INVOICE.equals(input.getTypeGroup())
                || InvoiceTypeGroup.OTHERS.equals(input.getTypeGroup());

        boolean isUnpaid = InvoiceStatus.UNPAID.equals(input.getStatus())
                || InvoiceStatus.PARTIALLY_PAID.equals(input.getStatus());

        boolean sharedToEmpty = Optional.ofNullable(input.getSharing())
                .map(Sharing::getTo)
                .map(List::isEmpty)
                .orElse(true);

        boolean isInstallmentEligible = Optional.ofNullable(input.getType())
                .map(CodeListItem::getCode)
                .flatMap(InvoiceTypeCode::fromString)
                .map(InvoiceTypeCode::isInstallmentEligible)
                .orElse(false);

        input.setInstallmentVisibility(isInvoiceGroup && isUnpaid && sharedToEmpty && isInstallmentEligible);
    }

}
