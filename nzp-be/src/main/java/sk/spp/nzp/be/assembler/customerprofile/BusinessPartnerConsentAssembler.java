package sk.spp.nzp.be.assembler.customerprofile;

import sk.spp.nzp.be.api.customerprofile.BusinessPartnerConsent;
import sk.spp.nzp.commons.model.customerprofile.BusinessPartnerConsentEntity;

import java.util.Collection;
import java.util.List;

public interface BusinessPartnerConsentAssembler {

    BusinessPartnerConsentEntity map(BusinessPartnerConsent input, BusinessPartnerConsentEntity output);

    BusinessPartnerConsent map(BusinessPartnerConsentEntity input, BusinessPartnerConsent output);

    List<BusinessPartnerConsent> map(Collection<BusinessPartnerConsentEntity> input);

}
