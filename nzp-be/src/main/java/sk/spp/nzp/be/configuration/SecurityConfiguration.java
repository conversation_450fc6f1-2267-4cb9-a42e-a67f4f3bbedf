package sk.spp.nzp.be.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration;

import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.provisioning.UserDetailsManager;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import sk.spp.nzp.be.converters.NzpAuthenticationJwtConvertor;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.context.holder.RequestContextHolder;
import sk.spp.nzp.commons.exception.ErrorResponse;
import sk.spp.nzp.commons.exception.ErrorResponseBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(
        securedEnabled = true,
        prePostEnabled = true
)
public class SecurityConfiguration extends GlobalMethodSecurityConfiguration {

    private static String ROLE_SAP          = "SAP";

    @Value("${spring.security.cors.path:/**}")
    private String corsPath;

    @Value("${spring.security.cors.path:/**}")
    private String basicAuthentifications;

    @Value("${sap.security.basicauth.username}")
    private String sapSecurityBasicAuthUserName;

    @Value("${sap.security.basicauth.password}")
    private String sapSecurityBasicAuthPassword;

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        super.configure(auth);
    }

    @Bean
    SecurityFilterChain filterChain(HttpSecurity http,
                                    ObjectMapper mapper,
                                    RequestContextHolder requestContextHolder,
                                    NzpAuthenticationJwtConvertor converter) throws Exception {
        http
                .cors().and()
                .csrf().disable()
                .formLogin().disable()
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);

        // HTTP
        http.httpBasic();

        // any request
        http.authorizeRequests().anyRequest().permitAll();

        http
                .oauth2ResourceServer(oauth2ResourceServer -> oauth2ResourceServer
                        .jwt(jwt -> jwt.jwtAuthenticationConverter(converter))
                        .authenticationEntryPoint(new CustomSppAuthEntryPoint(requestContextHolder, mapper)));
        return http.build();

    }

    @Bean
    public UserDetailsManager getSapUserDetailsManager(PasswordEncoder passwordEncoder) {
        return new InMemoryUserDetailsManager(User
                .withUsername(sapSecurityBasicAuthUserName)
                .password(passwordEncoder.encode(sapSecurityBasicAuthPassword))
                .roles(ROLE_SAP)
                .build());
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.security.cors")
    CorsConfiguration corsConfiguration() {
        return new CorsConfiguration();
    }

    @Bean
    CorsConfigurationSource corsConfigurationSource(CorsConfiguration corsConfiguration) {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration(corsPath, corsConfiguration);
        return source;
    }

    class CustomSppAuthEntryPoint implements AuthenticationEntryPoint {

        private RequestContextHolder requestContextHolder;
        private ObjectMapper mapper;

        public CustomSppAuthEntryPoint(RequestContextHolder requestContextHolder, ObjectMapper objectMapper) {
            this.requestContextHolder = requestContextHolder;
            this.mapper = objectMapper;
        }

        @Override
        public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException {
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);

            ErrorResponse responseJson = new ErrorResponseBuilder()
                    .setCode(ErrorCode.GENERIC_UNAUTHORIZED.getCode())
                    .setRequestId(requestContextHolder.getRequestId())
                    .createErrorResponse();

            if(authException instanceof NzpAuthenticationJwtConvertor.CustomSppAuthenticationException) {
                responseJson.setCode(((NzpAuthenticationJwtConvertor.CustomSppAuthenticationException) authException).getCode());
            }

            response.getWriter().write(mapper.writeValueAsString(responseJson));
        }
    }
}
