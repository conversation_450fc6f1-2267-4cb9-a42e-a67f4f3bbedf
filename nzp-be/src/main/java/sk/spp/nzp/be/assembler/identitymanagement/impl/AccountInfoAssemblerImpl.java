package sk.spp.nzp.be.assembler.identitymanagement.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customeraccess.CustomerApproval;
import sk.spp.nzp.be.api.identitymanagement.AccountInfo;
import sk.spp.nzp.be.api.identitymanagement.Customer;
import sk.spp.nzp.commons.api.employeeaccess.Employee;
import sk.spp.nzp.commons.assembler.EmployeeAccountAssembler;
import sk.spp.nzp.be.assembler.identitymanagement.AccountInfoAssembler;
import sk.spp.nzp.commons.api.employeeaccess.EmployeeAccount;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;
import sk.spp.nzp.commons.model.customeraccess.CustomerApprovalEntity;

import java.util.stream.Collectors;

@Component
public class AccountInfoAssemblerImpl implements AccountInfoAssembler {

    @Autowired
    private EmployeeAccountAssembler employeeAccountAssembler;

    @Override
    public AccountInfo map(CustomerAccountEntity inputCustomerAccount, EmployeeAccount inputEmployeeAccount, AccountInfo output) {
        if (inputCustomerAccount != null) {
            Customer customer = new Customer();
            customer.setId(inputCustomerAccount.getId());
            customer.setEmail(inputCustomerAccount.getEmail());
            customer.setType(inputCustomerAccount.getType());
            customer.setStatus(inputCustomerAccount.getStatus());
            customer.setActivationAt(inputCustomerAccount.getActivationAt());
            customer.setRegistrationAt(inputCustomerAccount.getRegistrationAt());
            customer.setLockUntil(inputCustomerAccount.getLockUntil());
            customer.setLoginSuccessAt(inputCustomerAccount.getLoginSuccessAt());
            customer.setDeactivationReason(inputCustomerAccount.getDeactivationReason());
            customer.setLocale(inputCustomerAccount.getLocale());
            customer.setPhone(inputCustomerAccount.getPhone());
            customer.setFirstName(inputCustomerAccount.getFirstName());
            customer.setLastName(inputCustomerAccount.getLastName());
            customer.setApprovals(
                    inputCustomerAccount.getApprovals()
                            .stream()
                            .map(this::map)
                            .collect(Collectors.toList())
            );
            customer.setFacebookUserName(inputCustomerAccount.getFacebookUserName());
            customer.setHasFacebookId(StringUtils.isNotEmpty(inputCustomerAccount.getFacebookId()));
            customer.setFacebookLoginEnabled(inputCustomerAccount.getFacebookLoginEnabled());
            customer.setGoogleUserName(inputCustomerAccount.getGoogleUserName());
            customer.setHasGoogleId(StringUtils.isNotEmpty(inputCustomerAccount.getGoogleId()));
            customer.setGoogleLoginEnabled(inputCustomerAccount.getGoogleLoginEnabled());
            customer.setAppleUserName(inputCustomerAccount.getAppleUserName());
            customer.setHasAppleId(StringUtils.isNotEmpty(inputCustomerAccount.getAppleId()));
            customer.setAppleLoginEnabled(inputCustomerAccount.getAppleLoginEnabled());
            customer.setHasPassword(StringUtils.isNotEmpty(inputCustomerAccount.getPassword()));

            output.setCustomer(customer);
        }

        if (inputEmployeeAccount != null) {
            Employee employee = employeeAccountAssembler.mapFullFromSecurityContext(inputEmployeeAccount, new Employee());
            output.setEmployee(employee);
        }

        return output;
    }

    public CustomerApproval map(CustomerApprovalEntity customerApproval) {
        CustomerApproval approval = new CustomerApproval();
        approval.setType(customerApproval.getType());
        approval.setApproval(customerApproval.getApproval());
        return approval;
    }
}
