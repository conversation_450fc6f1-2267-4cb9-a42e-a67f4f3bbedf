package sk.spp.nzp.be.assembler.customerrequest.support;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import sk.spp.nzp.commons.model.customerprofile.enums.CompletionView;
import sk.spp.nzp.commons.api.customerrequest.component.DeliveryPointRecordEE;
import sk.spp.nzp.commons.api.customerrequest.component.DeliveryPointRecordZP;
import sk.spp.nzp.commons.api.customerrequest.request.ZOMPRequestContent;
import sk.spp.nzp.commons.api.customerrequest.request.base.CustomerRequestContent;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ZOMPRequestContentVisibilityProcessor implements RequestContentVisibilityProcessor {

    public boolean supports(CustomerRequestContent content) {
        return content instanceof ZOMPRequestContent;
    }

    @Override
    public CustomerRequestContent process(CustomerRequestContent dtoContent, CustomerRequestContent entityContent, CompletionView completionView) {
        if (completionView == null) {
            return dtoContent;
        }

        processContent((ZOMPRequestContent) dtoContent, (ZOMPRequestContent) entityContent, completionView);
        return dtoContent;
    }

    private void processContent(ZOMPRequestContent newContent, ZOMPRequestContent originalContent, CompletionView completionView) {

        switch (completionView) {

            case OWNER:
                updateValue(originalContent, ZOMPRequestContent::getNewBusinessPartner, newContent::setNewBusinessPartner);
                updateValue(originalContent, ZOMPRequestContent::getNewBusinessPartnerAddress, newContent::setNewBusinessPartnerAddress);
                updateValue(originalContent, ZOMPRequestContent::getNewBusinessPartnerCorrespondenceAddress, newContent::setNewBusinessPartnerCorrespondenceAddress);
                updateValue(originalContent, ZOMPRequestContent::getNewBusinessPartnerContact, newContent::setNewBusinessPartnerContact);
                updateValue(originalContent, ZOMPRequestContent::getPaymentMethod, newContent::setPaymentMethod);
                updateValue(originalContent, ZOMPRequestContent::getInvoiceDelivery, newContent::setInvoiceDelivery);
                updateValue(originalContent, ZOMPRequestContent::getApprovals, newContent::setApprovals);
                updateValue(originalContent, ZOMPRequestContent::getNote, newContent::setNote);

                processRecordEe(newContent, originalContent, completionView);
                processRecordZp(newContent, originalContent, completionView);
                break;

            case COMPLETION:
                updateValue(originalContent, ZOMPRequestContent::getBusinessPartner, newContent::setBusinessPartner);
                updateValue(originalContent, ZOMPRequestContent::getLastInvoice, newContent::setLastInvoice);
                updateValue(originalContent, ZOMPRequestContent::getOverpaymentSettlement, newContent::setOverpaymentSettlement);
                break;
        }
    }

    private void processRecordEe(ZOMPRequestContent newContent, ZOMPRequestContent originalContent, CompletionView completionView) {

        if (newContent == null || newContent.getDeliveryPointsEe() == null) {
            return;
        }

        switch (completionView) {

            case OWNER:

                final Map<String, DeliveryPointRecordEE> originalContentRecordMap = Optional.ofNullable(originalContent)
                        .map(ZOMPRequestContent::getDeliveryPointsEe)
                        .orElse(List.of()).stream()
                        .collect(Collectors.toMap(DeliveryPointRecordEE::getContractId, e -> e));

                for (DeliveryPointRecordEE newContentRecord : newContent.getDeliveryPointsEe()) {
                    final DeliveryPointRecordEE originalContentRecord = originalContentRecordMap.get(newContentRecord.getContractId());

                    updateValue(originalContentRecord, DeliveryPointRecordEE::getTariffEe, newContentRecord::setTariffEe);
                    updateValue(originalContentRecord, DeliveryPointRecordEE::getAssumedConsumptionEe, newContentRecord::setAssumedConsumptionEe);
                    updateValue(originalContentRecord, DeliveryPointRecordEE::getAdvancePaymentPeriodEe, newContentRecord::setAdvancePaymentPeriodEe);
                }
                break;
        }
    }

    private void processRecordZp(ZOMPRequestContent newContent, ZOMPRequestContent originalContent, CompletionView completionView) {

        if (newContent == null || newContent.getDeliveryPointsZp() == null) {
            return;
        }

        switch (completionView) {

            case OWNER:

                final Map<String, DeliveryPointRecordZP> originalContentRecordMap = Optional.ofNullable(originalContent)
                        .map(ZOMPRequestContent::getDeliveryPointsZp)
                        .orElse(List.of()).stream()
                        .collect(Collectors.toMap(DeliveryPointRecordZP::getContractId, e -> e));

                for (DeliveryPointRecordZP newContentRecord : newContent.getDeliveryPointsZp()) {
                    final DeliveryPointRecordZP originalContentRecord = originalContentRecordMap.get(newContentRecord.getContractId());

                    updateValue(originalContentRecord, DeliveryPointRecordZP::getTariffZp, newContentRecord::setTariffZp);
                    updateValue(originalContentRecord, DeliveryPointRecordZP::getAssumedConsumptionZp, newContentRecord::setAssumedConsumptionZp);
                    updateValue(originalContentRecord, DeliveryPointRecordZP::getAdvancePaymentPeriodZp, newContentRecord::setAdvancePaymentPeriodZp);
                }
                break;
        }
    }

    private <T, V> void updateValue(T currentObject, Function<T, V> currentValueExtractor, Consumer<V> valueSetter) {
        final V newValue = Optional.ofNullable(currentObject)
                .map(currentValueExtractor)
                .orElse(null);

        valueSetter.accept(newValue);
    }

}