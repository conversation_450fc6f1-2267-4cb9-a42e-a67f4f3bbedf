package sk.spp.nzp.be.reporting.impl;

import sk.spp.nzp.be.reporting.IteratorWrapper;
import sk.spp.nzp.be.reporting.entitymappers.AbstractDtoMapper;

import java.util.Collection;
import java.util.Iterator;

public class CollectionIteratorWrapper<T> implements IteratorWrapper {

    private final Iterator<T> iterator;
    private final AbstractDtoMapper<T> mapper;
    private final boolean empty;

    public CollectionIteratorWrapper(Collection<T> collection, AbstractDtoMapper<T> mapper) {
        this.iterator = collection.iterator();
        this.mapper = mapper;
        this.empty = !hasNext();
    }

    @Override
    public boolean hasNext() {
        return iterator.hasNext();
    }

    @Override
    public Object[] next() {
        if(empty){
            return new Object[0];
        }

        return mapper.map(iterator.next());
    }

    @Override
    public void close() {}

}