package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerprofile.Product;
import sk.spp.nzp.be.assembler.customerprofile.ProductAssembler;
import sk.spp.nzp.commons.model.customerprofile.ProductEntity;
import sk.spp.nzp.commons.service.common.LocaleResolver;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class ProductAssemblerImpl implements ProductAssembler {

    private LocaleResolver localeResolver;

    public ProductAssemblerImpl(LocaleResolver localeResolver) {
        this.localeResolver = localeResolver;
    }

    @Override
    public Product map(ProductEntity input, Product output) {
        output.setUuid(input.getId().toString());
        output.setCode(input.getCode());
        output.setStatus(input.getStatus());
        output.setEe(input.getEe());
        output.setZp(input.getZp());
        output.setNotCommodity(input.getNotCommodity());

        localeResolver.resolve(
                Optional.ofNullable(input.getTranslations()).orElse(Collections.emptyList()))
                .ifPresent(v -> {
                    output.setName(v.getName());
                    output.setDescription(v.getDescription());
                });

        return output;
    }

    @Override
    public List<Product> map(Collection<ProductEntity> input) {
        if (input == null) {
            return Collections.emptyList();
        }

        return input.stream().map(p -> map(p, new Product())).collect(Collectors.toList());
    }

}
