package sk.spp.nzp.be.repository.customerrequest;

import org.springframework.stereotype.Repository;
import sk.spp.nzp.commons.api.customerrequest.enums.CustomerRequestCode;
import sk.spp.nzp.commons.api.customerrequest.enums.CustomerRequestTemplateStatus;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestTemplateEntity;
import sk.spp.nzp.commons.repository.customerrequest.CustomerRequestTemplateBaseRepository;

import java.util.Optional;

@Repository
public interface CustomerRequestTemplateRepository extends CustomerRequestTemplateBaseRepository {

    Optional<CustomerRequestTemplateEntity> findByCodeAndStatus(CustomerRequestCode code, CustomerRequestTemplateStatus status);

}
