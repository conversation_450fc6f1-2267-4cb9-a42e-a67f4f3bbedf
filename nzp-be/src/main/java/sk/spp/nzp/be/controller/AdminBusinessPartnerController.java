package sk.spp.nzp.be.controller;

import org.springframework.http.MediaType;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.customerprofile.BusinessPartner;
import sk.spp.nzp.be.service.customerprofile.BusinessPartnerService;

import java.util.UUID;

@RestController
@RequestMapping("admin/business-partners")
public class AdminBusinessPartnerController {

    private BusinessPartnerService businessPartnerService;

    public AdminBusinessPartnerController(BusinessPartnerService businessPartnerService) {
        this.businessPartnerService = businessPartnerService;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW')")
    @PostMapping(value = "/{bpId}/franchFixation/activate", produces = {MediaType.APPLICATION_JSON_VALUE})
    public BusinessPartner activateFranchFixation(@LogParam("bpId") @PathVariable(value = "bpId") String uuid) {

        return businessPartnerService.activateFranchFixation(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW')")
    @PostMapping(value = "/{bpId}/franchFixation/deactivate", produces = {MediaType.APPLICATION_JSON_VALUE})
    public BusinessPartner deactivateFranchFixation(@LogParam("bpId") @PathVariable(value = "bpId") String uuid) {

        return businessPartnerService.deactivateFranchFixation(uuid);
    }
}
