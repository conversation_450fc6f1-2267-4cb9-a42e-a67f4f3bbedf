package sk.spp.nzp.be.repository.customerprofile.query;

import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.api.customerprofile.TariffAndRatesSearch;
import sk.spp.nzp.commons.model.customerprofile.TariffEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;
import sk.spp.nzp.commons.repository.customerprofile.query.TariffFindAllQueryProvider;

import javax.persistence.EntityManager;

public class TariffSearchQueryProvider implements QueryDslProvider<TariffEntity> {

    private TariffAndRatesSearch query;

    public TariffSearchQueryProvider(TariffAndRatesSearch query) {
        this.query = query;
    }

    @Override
    public JPAQuery<TariffEntity> getQuery(EntityManager em) {
        return new TariffFindAllQueryProvider()
                .setStatus(query.getStatus())
                .setType(query.getType())
                .setCategory(query.getCategory())
                .setDistributionArea(query.getDistributionArea())
                .getQuery(em);
    }

}
