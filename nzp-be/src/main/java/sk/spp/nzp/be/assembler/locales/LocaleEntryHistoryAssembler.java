package sk.spp.nzp.be.assembler.locales;

import sk.spp.nzp.commons.api.locales.LocaleHistorySearchResponse;
import sk.spp.nzp.commons.model.locales.LocaleEntryHistoryEntity;

import java.util.List;

public interface LocaleEntryHistoryAssembler {

    LocaleHistorySearchResponse map(LocaleEntryHistoryEntity input, LocaleHistorySearchResponse output);

    List<LocaleHistorySearchResponse> map(List<LocaleEntryHistoryEntity> input, List<LocaleHistorySearchResponse> output);

}