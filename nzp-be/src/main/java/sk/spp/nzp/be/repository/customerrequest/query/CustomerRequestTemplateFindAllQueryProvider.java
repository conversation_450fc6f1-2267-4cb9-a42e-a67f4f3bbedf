package sk.spp.nzp.be.repository.customerrequest.query;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.commons.api.customerrequest.enums.CustomerRequestCode;
import sk.spp.nzp.commons.api.customerrequest.enums.CustomerRequestTemplateStatus;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestTemplateEntity;
import sk.spp.nzp.commons.model.customerrequest.QCustomerRequestTemplateEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

import javax.persistence.EntityManager;
import java.util.Set;

public class CustomerRequestTemplateFindAllQueryProvider implements QueryDslProvider<CustomerRequestTemplateEntity> {

    private Set<CustomerRequestTemplateStatus> statuses;
    private Set<CustomerRequestCode> codes;

    public CustomerRequestTemplateFindAllQueryProvider filterStatuses(Set<CustomerRequestTemplateStatus> statuses) {
        this.statuses = statuses;
        return this;
    }

    public CustomerRequestTemplateFindAllQueryProvider filterCodes(Set<CustomerRequestCode> codes) {
        this.codes = codes;
        return this;
    }

    @Override
    public JPAQuery<CustomerRequestTemplateEntity> getQuery(EntityManager em) {
        QCustomerRequestTemplateEntity customerRequestTemplate = QCustomerRequestTemplateEntity.customerRequestTemplateEntity;

        BooleanExpression statusFilter = statuses == null || statuses.isEmpty() ? null : customerRequestTemplate.status.in(statuses);
        BooleanExpression codesFilter = codes == null || codes.isEmpty() ? null : customerRequestTemplate.code.in(codes);

        return new JPAQuery<CustomerRequestTemplateEntity>(em)
                .from(customerRequestTemplate)
                .where(statusFilter, codesFilter);
    }
}
