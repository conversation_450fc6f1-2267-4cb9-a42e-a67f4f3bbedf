package sk.spp.nzp.be.controller;


import org.springframework.web.bind.annotation.*;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.annotation.paging.PagingAsQueryParams;
import sk.spp.nzp.be.api.common.PagedResponse;
import sk.spp.nzp.be.api.common.QueryStringPaging;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestSearchQuery;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestTemplate;
import sk.spp.nzp.be.service.customerrequest.CustomerRequestTemplateService;

import javax.validation.Valid;

@RestController
@RequestMapping("/customer-request-templates")
public class CustomerRequestTemplateController {

    private final CustomerRequestTemplateService customerRequestTemplateService;

    public CustomerRequestTemplateController(CustomerRequestTemplateService customerRequestTemplateService) {
        this.customerRequestTemplateService = customerRequestTemplateService;
    }

    @Log
    @LogParam
    @GetMapping("/")
    @PagingAsQueryParams
    public PagedResponse<CustomerRequestTemplate> getTemplates(
            @LogParam("paging")  QueryStringPaging paging
    ) {
        CustomerRequestSearchQuery query = new CustomerRequestSearchQuery();
        query.setPaging(paging.toPaging());

        return customerRequestTemplateService.getTemplates(query);
    }

    @Log
    @LogParam
    @PostMapping("/search")
    public PagedResponse<CustomerRequestTemplate> searchTemplates(
            @LogParam("query") @Valid @RequestBody CustomerRequestSearchQuery query
    ) {
        return customerRequestTemplateService.getTemplates(query);
    }

    @Log
    @LogParam
    @GetMapping("/{uuid}")
    public CustomerRequestTemplate getTemplate(
        @LogParam("uuid") @PathVariable String uuid
    ) {
        return customerRequestTemplateService.getTemplateForUuid(uuid);
    }

}
