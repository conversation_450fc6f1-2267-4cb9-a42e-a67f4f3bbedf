package sk.spp.nzp.be.assembler.customerprofile;

import java.util.Collection;
import java.util.List;

import sk.spp.nzp.be.api.customerprofile.Invoice;
import sk.spp.nzp.commons.model.customerprofile.InvoiceEntity;

public interface InvoiceAssembler {

    Invoice map(InvoiceEntity input, Invoice output);

    List<Invoice> map(Collection<InvoiceEntity> input);

    Invoice mapFull(InvoiceEntity input, Invoice output);

}
