package sk.spp.nzp.be.assembler.customerrequest;

import sk.spp.nzp.be.api.customerrequest.CustomerRequestTemplate;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestTemplateEntity;

import java.util.List;

public interface CustomerRequestTemplateAssembler {

    CustomerRequestTemplate map(CustomerRequestTemplateEntity input, CustomerRequestTemplate output);

    List<CustomerRequestTemplate> map(List<CustomerRequestTemplateEntity> input, List<CustomerRequestTemplate> output);

}
