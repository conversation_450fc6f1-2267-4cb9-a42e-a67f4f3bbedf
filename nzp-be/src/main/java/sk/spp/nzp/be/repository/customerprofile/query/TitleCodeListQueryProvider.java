package sk.spp.nzp.be.repository.customerprofile.query;

import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.service.codelist.model.CodeListQuery;
import sk.spp.nzp.commons.model.codelist.TitleEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;
import sk.spp.nzp.commons.repository.customerprofile.query.TitleFindAllQueryProvider;

import javax.persistence.EntityManager;

public class TitleCodeListQueryProvider implements QueryDslProvider<TitleEntity> {

    private CodeListQuery query;
    private String type;

    public TitleCodeListQueryProvider(CodeListQuery query, String type) {
        this.query = query;
        this.type = type;
    }

    @Override
    public JPAQuery<TitleEntity> getQuery(EntityManager em) {
        return new TitleFindAllQueryProvider()
                .setId(query.getCode())
                .setType(type)
                .getQuery(em);
    }
}
