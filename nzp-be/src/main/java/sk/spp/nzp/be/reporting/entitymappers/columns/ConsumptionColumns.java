package sk.spp.nzp.be.reporting.entitymappers.columns;

import java.util.Arrays;

public enum ConsumptionColumns {

    VALUE,
    DATE,
    TIME_FROM,
    EIC_OOM,
    FROM,
    TO,
    POWER,
    UPDATED;

    public static final String GENERIC_CODE_LIST_TYPE = "CONSUMPTION_EXPORT_COLUMN";

    public static ConsumptionColumns getByName(String stringValue) {
        return Arrays.stream(values())
                .filter(val -> val.name().equals(stringValue))
                .findFirst()
                .orElse(null);
    }

}