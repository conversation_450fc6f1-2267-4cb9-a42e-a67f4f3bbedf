package sk.spp.nzp.be.assembler.identitymanagement;

import sk.spp.nzp.be.api.identitymanagement.AccountInfo;
import sk.spp.nzp.commons.api.employeeaccess.EmployeeAccount;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;
public interface AccountInfoAssembler {

    AccountInfo map(CustomerAccountEntity inputCustomerAccount, EmployeeAccount inputEmployeeAccount, AccountInfo output);
}
