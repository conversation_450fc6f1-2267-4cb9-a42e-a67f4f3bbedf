package sk.spp.nzp.be.assembler.customerrequest.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customeraccess.CustomerAccount;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerSummary;
import sk.spp.nzp.be.api.customerrequest.CustomerRequest;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestSummary;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestSyncedResolver;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestTemplate;
import sk.spp.nzp.be.assembler.customeraccess.CustomerAccountAssembler;
import sk.spp.nzp.be.assembler.customerprofile.BusinessPartnerAssembler;
import sk.spp.nzp.be.assembler.customerrequest.*;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.api.customerrequest.enums.CustomerRequestStatus;
import sk.spp.nzp.commons.assembler.codelist.CodeListItemAssembler;
import sk.spp.nzp.commons.context.holder.SecurityContextHolder;
import sk.spp.nzp.commons.model.customerprofile.enums.CompletionView;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestEntity;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestTemplateEntity;
import sk.spp.nzp.commons.service.common.CustomerResolverService;

import java.time.Clock;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static sk.spp.nzp.commons.enums.CodeListType.CUSTOMER_REQUEST_STATUS;

@Component
public class CustomerRequestAssemblerImpl implements CustomerRequestAssembler {

    private final Clock clock;
    private final CustomerRequestNoteAssembler customerRequestNoteAssembler;
    private final CustomerRequestAttachmentAssembler customerRequestAttachmentAssembler;
    private final BusinessPartnerAssembler businessPartnerAssembler;
    private final CodeListItemAssembler codeListItemAssembler;
    private final CustomerRequestContractAssembler customerRequestContractAssembler;
    private final CustomerRequestContentAssembler contentAssembler;
    private final CustomerAccountAssembler customerAccountAssembler;
    private final CustomerResolverService customerResolverService;
    private final SecurityContextHolder securityContextHolder;
    private final CustomerRequestSyncedResolver customerRequestSyncedResolver;


    public CustomerRequestAssemblerImpl(
            Clock clock,
            CustomerRequestNoteAssembler customerRequestNoteAssembler,
            CustomerRequestAttachmentAssembler customerRequestAttachmentAssembler,
            BusinessPartnerAssembler businessPartnerAssembler,
            CodeListItemAssembler codeListItemAssembler,
            CustomerRequestContractAssembler customerRequestContractAssembler,
            CustomerRequestContentAssembler contentAssembler,
            CustomerAccountAssembler customerAccountAssembler,
            CustomerResolverService customerResolverService,
            SecurityContextHolder securityContextHolder,
            CustomerRequestSyncedResolver customerRequestSyncedResolver) {

        this.clock = clock;
        this.customerRequestNoteAssembler = customerRequestNoteAssembler;
        this.customerRequestAttachmentAssembler = customerRequestAttachmentAssembler;
        this.businessPartnerAssembler = businessPartnerAssembler;
        this.codeListItemAssembler = codeListItemAssembler;
        this.customerRequestContractAssembler = customerRequestContractAssembler;
        this.contentAssembler = contentAssembler;
        this.customerAccountAssembler = customerAccountAssembler;
        this.customerResolverService = customerResolverService;
        this.securityContextHolder = securityContextHolder;
        this.customerRequestSyncedResolver = customerRequestSyncedResolver;
    }

    @Override
    public CustomerRequestEntity map(CustomerRequest input, CustomerRequestEntity output, CompletionView completionView) {
        final LocalDateTime now = LocalDateTime.now(clock);

        output.setStatus(CustomerRequestStatus.PRE_CREATED);
        output.setRequestCancel(false);
        output.setIssuedAt(now);
        output.setStatusUpdatedAt(now);
        output.setCompletionCustomerEmail(input.getCompletionCustomerEmail());
        output.setDraftMetadata(input.getDraftMetadata());

        output.setContent(contentAssembler
                .filterUpdateContent(input.getContent(), output.getContent(), completionView));

        return output;
    }

    @Override
    public CustomerRequest map(CustomerRequestEntity input, CustomerRequest output) {
        return map(input, output, customerResolverService.determineCustomerType(input));
    }

    @Override
    public <T extends CustomerRequest> T map(CustomerRequestEntity input, T output, CompletionView completionView) {

        output.setUuid(input.getId().toString());
        output.setExternalId(input.getExternalId());
        output.setStatus(codeListItemAssembler.mapSummary(CUSTOMER_REQUEST_STATUS, input.getStatus().name(), new CodeListItem()));
        output.setStatusSource(input.getStatus().getSource());
        output.setRegistered(input.getRegisteredAt() != null);
        output.setRegisteredAt(input.getRegisteredAt());
        output.setFinnished(input.getFinnishedAt() != null);
        output.setFinnishedAt(input.getFinnishedAt());
        output.setPriceCollective(input.getPriceCollective());
        output.setPriceIndividual(input.getPriceIndividual());
        output.setAttachmentsCropped(input.getAttachmentsCropped());
        output.setAnonymous(input.getAnonymous());
        output.setConfirmedAt(input.getConfirmedAt());
        output.setRequestCancel(input.getRequestCancel());
        output.setIssuedAt(input.getIssuedAt());
        output.setStatusUpdatedAt(input.getStatusUpdatedAt());
        output.setNotificationStatusUpdatedAt(input.getNotificationStatusUpdatedAt());
        output.setCustomerRequestTemplate(getTemplate(input));

        output.setCompletionView(completionView);
        output.setCompletionCustomerEmail(input.getCompletionCustomerEmail());
        output.setCompletionSentAt(input.getCompletionSentAt());
        output.setCompletionResult(input.getCompletionResult());
        output.setCompletionRejectReason(input.getCompletionRejectReason());
        output.setCompletionValidTo(input.getCompletionValidTo());
        output.setCompletionNotificationReminderAt(input.getCompletionNotificationReminderAt());
        output.setDraftMetadata(input.getDraftMetadata());
        output.setSynced(customerRequestSyncedResolver.isSynced(input));

        if (input.getCompletionSentAt() != null) {
            Optional.ofNullable(input.getCustomerRequestTemplate())
                    .map(CustomerRequestTemplateEntity::getCompletionValidDays)
                    .map(d -> input.getCompletionSentAt().plusDays(d))
                    .ifPresent(output::setCompletionNotificationExpiredAt);
        }

        output.setContent(contentAssembler
                .getFilteredContent(input.getContent(), completionView));

        CustomerRequestTemplate template = new CustomerRequestTemplate();
        template.setUuid(input.getCustomerRequestTemplate().getId().toString());
        template.setAutomated(input.getCustomerRequestTemplate().getAutomated());
        output.setCustomerRequestTemplate(template);

        output.setNotes(customerRequestNoteAssembler.map(input.getNotes(), new ArrayList<>()));
        output.setAttachments(customerRequestAttachmentAssembler.map(input.getAttachments(), new ArrayList<>()));

        output.setCustomerRequestContracts(customerRequestContractAssembler.map(input.getCustomerRequestContracts()));

        if (input.getBusinessPartner() != null) {
            BusinessPartnerSummary businessPartnerSummary = new BusinessPartnerSummary();
            output.setBusinessPartner(businessPartnerAssembler.map(input.getBusinessPartner(), businessPartnerSummary));
        }

        if (input.getRequestCancelByCustomer() != null) {
            output.setRequestCancelBy(customerAccountAssembler.map(input.getRequestCancelByCustomer(), new CustomerAccount()));
        }

        return output;
    }

    private CustomerRequestTemplate getTemplate(CustomerRequestEntity entity) {
        CustomerRequestTemplate template = new CustomerRequestTemplate();
        template.setUuid(entity.getCustomerRequestTemplate().getId().toString());
        template.setCode(entity.getCustomerRequestTemplate().getCode());
        template.setAutomated(entity.getCustomerRequestTemplate().getAutomated());

        return template;
    }

    @Override
    public CustomerRequestSummary map(CustomerRequestEntity input, CustomerRequestSummary output) {
        output.setUuid(input.getId().toString());
        output.setExternalId(input.getExternalId());
        output.setStatus(codeListItemAssembler.mapSummary(CUSTOMER_REQUEST_STATUS, input.getStatus().name(), new CodeListItem()));
        output.setRequestCancel(input.getRequestCancel());
        output.setIssuedAt(input.getIssuedAt());
        output.setStatusUpdatedAt(input.getStatusUpdatedAt());
        output.setCustomerRequestTemplate(getTemplate(input));
        output.setStatusSource(input.getStatus().getSource());
        output.setRegistered(input.getRegisteredAt() != null);
        output.setRegisteredAt(input.getRegisteredAt());
        output.setFinnished(input.getFinnishedAt() != null);
        output.setFinnishedAt(input.getFinnishedAt());
        output.setCustomerRequestContracts(customerRequestContractAssembler.mapBrief(input.getCustomerRequestContracts()));
        output.setCompletionView(customerResolverService.determineCustomerType(input));
        output.setSynced(customerRequestSyncedResolver.isSynced(input));

        if (input.getBusinessPartner() != null) {
            BusinessPartnerSummary businessPartnerSummary = new BusinessPartnerSummary();
            businessPartnerSummary.setId(input.getBusinessPartner().getId());
            businessPartnerSummary.setName(input.getBusinessPartner().getName());
            businessPartnerSummary.setFirstName(input.getBusinessPartner().getFirstName());
            businessPartnerSummary.setLastName(input.getBusinessPartner().getLastName());
            businessPartnerSummary.setExternalId(input.getBusinessPartner().getExternalId());
            output.setBusinessPartner(businessPartnerSummary);
        }

        if (input.getConfirmedAt() != null) {
            output.setConfirmedAt(input.getConfirmedAt());
        }

        return output;
    }

    @Override
    public List<CustomerRequestSummary> map(List<CustomerRequestEntity> input, List<CustomerRequestSummary> output) {
        List<CustomerRequestSummary> summaries = input.stream().map(e -> map(e, new CustomerRequestSummary())).collect(Collectors.toList());
        output.addAll(summaries);

        return output;
    }

}
