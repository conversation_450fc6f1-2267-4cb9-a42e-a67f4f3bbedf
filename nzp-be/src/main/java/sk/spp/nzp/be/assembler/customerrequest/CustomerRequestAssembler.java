package sk.spp.nzp.be.assembler.customerrequest;

import sk.spp.nzp.commons.model.customerprofile.enums.CompletionView;
import sk.spp.nzp.be.api.customerrequest.CustomerRequest;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestSummary;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestEntity;

import java.util.List;

public interface CustomerRequestAssembler {

    CustomerRequestEntity map(CustomerRequest input, CustomerRequestEntity output, CompletionView completionView);

    CustomerRequest map(CustomerRequestEntity input, CustomerRequest output);

    <T extends CustomerRequest> T map(CustomerRequestEntity input, T output, CompletionView completionView);

    CustomerRequestSummary map(CustomerRequestEntity input, CustomerRequestSummary output);

    List<CustomerRequestSummary> map(List<CustomerRequestEntity> input, List<CustomerRequestSummary> output);

}
