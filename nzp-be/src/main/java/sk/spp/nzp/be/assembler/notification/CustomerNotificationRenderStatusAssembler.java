package sk.spp.nzp.be.assembler.notification;

import sk.spp.nzp.be.api.notification.CustomerNotificationRenderStatus;
import sk.spp.nzp.commons.model.notification.CustomerNotificationRenderStatusEntity;

import java.util.Collection;
import java.util.List;

public interface CustomerNotificationRenderStatusAssembler {

    CustomerNotificationRenderStatusEntity map(CustomerNotificationRenderStatus input, CustomerNotificationRenderStatusEntity output);

    CustomerNotificationRenderStatus map(CustomerNotificationRenderStatusEntity input, CustomerNotificationRenderStatus output);

    List<CustomerNotificationRenderStatus> map(Collection<CustomerNotificationRenderStatusEntity> input);

}