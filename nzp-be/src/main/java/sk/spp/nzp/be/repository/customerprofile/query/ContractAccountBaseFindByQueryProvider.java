package sk.spp.nzp.be.repository.customerprofile.query;

import java.time.LocalDate;
import java.util.UUID;

import javax.persistence.EntityManager;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;

import sk.spp.nzp.be.models.customerprofile.ContractAccountSearch;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.api.customersharing.enums.OwnershipType;
import sk.spp.nzp.commons.model.customerprofile.ContractAccountEntity;
import sk.spp.nzp.commons.model.customerprofile.QContractAccountEntity;
import sk.spp.nzp.commons.model.customerprofile.QContractEntity;
import sk.spp.nzp.commons.model.customersharing.QContractAccountOwnershipEntity;

public class ContractAccountBaseFindByQueryProvider {

    protected JPAQuery<ContractAccountEntity> findByBaseQuery(
            EntityManager entityManager,
            String businessPartnerId,
            ContractAccountSearch contractAccountSearch,
            UUID sharedCustomerUuid,
            BusinessPartnerQueue businessPartnerQueueSecurity,
            boolean withActiveContractsOnly
    ) {
        QContractAccountEntity contractAccountEntity = QContractAccountEntity.contractAccountEntity;
        QContractAccountOwnershipEntity contractAccountOwnershipEntity = QContractAccountOwnershipEntity.contractAccountOwnershipEntity;
        QContractEntity contractEntity = QContractEntity.contractEntity;
        
        BooleanExpression statusesExp = null;
        
        if (contractAccountSearch.getStatuses() != null && !contractAccountSearch.getStatuses().isEmpty()) {
            statusesExp = contractAccountEntity.status.in(contractAccountSearch.getStatuses());
        }
        
        BooleanExpression bpExp = null;
        
        if (businessPartnerId != null) {
            bpExp = contractAccountEntity.businessPartner().id.eq(businessPartnerId);
        }

        BooleanExpression bpQueueExp = null;
        if (businessPartnerQueueSecurity != null) {
            bpQueueExp = contractAccountEntity.businessPartner().queue.eq(businessPartnerQueueSecurity);
        }

        BooleanExpression filterActiveContracts = null;

        if (withActiveContractsOnly) {
            LocalDate now = LocalDate.now();
            BooleanExpression effectiveFromExp = contractEntity.effectiveFrom.isNull().or(contractEntity.effectiveFrom.loe(now));
            BooleanExpression effectiveToExp = contractEntity.effectiveTo.isNull().or(contractEntity.effectiveTo.goe(now));
            BooleanExpression bpMatch = contractEntity.businessPartner().eq(contractAccountEntity.businessPartner());
            filterActiveContracts = effectiveFromExp.and(effectiveToExp).and(bpMatch);
        }
        
        if (sharedCustomerUuid == null) {
            // temporary hack because signed customer (sharedCustomerId) can not be obtain from principal.

            JPAQuery<ContractAccountEntity> query = new JPAQuery<ContractAccountEntity>(entityManager)
                    .from(contractAccountEntity);

            if (withActiveContractsOnly) {
                query.innerJoin(contractEntity).on(contractEntity.contractAccount().eq(contractAccountEntity));
            }

            query.where(statusesExp, bpExp, filterActiveContracts);

            return query.distinct();
        } 
        
        BooleanExpression ownerExp = null;
        
        if (contractAccountSearch.getShared() == null || !contractAccountSearch.getShared()) {
            ownerExp = contractAccountOwnershipEntity.type.eq(OwnershipType.OWNER);
        }

        JPAQuery<ContractAccountEntity> query = new JPAQuery<ContractAccountEntity>(entityManager)
                .from(contractAccountEntity)
                .distinct()
                .join(contractAccountOwnershipEntity).on(contractAccountOwnershipEntity.customerAccount().id.eq(sharedCustomerUuid), contractAccountOwnershipEntity.contractAccount().eq(contractAccountEntity));

        if (withActiveContractsOnly) {
            query.innerJoin(contractEntity).on(contractEntity.contractAccount().eq(contractAccountEntity));
        }

        query.where(statusesExp, bpExp, ownerExp, bpQueueExp, filterActiveContracts);

        return query.distinct();
    }

    protected void order(JPAQuery<ContractAccountEntity> query) {
        
        // not specified now
    }
}
