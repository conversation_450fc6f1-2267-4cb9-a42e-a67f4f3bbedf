package sk.spp.nzp.be.assembler.customerrequest;

import sk.spp.nzp.be.api.customerrequest.CustomerRequestContract;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestContractEntity;

import java.util.List;

public interface CustomerRequestContractAssembler {

    CustomerRequestContract map(CustomerRequestContractEntity input, CustomerRequestContract output);

    List<CustomerRequestContract> map(List<CustomerRequestContractEntity> input);

    CustomerRequestContract mapBrief(CustomerRequestContractEntity input, CustomerRequestContract output);

    List<CustomerRequestContract> mapBrief(List<CustomerRequestContractEntity> input);

}
