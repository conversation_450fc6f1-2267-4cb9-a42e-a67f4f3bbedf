package sk.spp.nzp.be.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import sk.spp.nzp.commons.api.customeraccess.enums.ReCaptchaTokenType;

import java.time.Duration;
import java.util.List;
import java.util.Map;

@Component
@ConfigurationProperties(prefix = "google.recaptcha.token")
public class ReCaptchaTokenConfigurationHolder {

    private Duration validity;

    private Map<ReCaptchaTokenType, List<String>> allowedUses;

    public Duration getValidity() {
        return validity;
    }

    public ReCaptchaTokenConfigurationHolder setValidity(Duration validity) {
        this.validity = validity;
        return this;
    }

    public Map<ReCaptchaTokenType, List<String>> getAllowedUses() {
        return allowedUses;
    }

    public ReCaptchaTokenConfigurationHolder setAllowedUses(Map<ReCaptchaTokenType, List<String>> allowedUses) {
        this.allowedUses = allowedUses;
        return this;
    }

}