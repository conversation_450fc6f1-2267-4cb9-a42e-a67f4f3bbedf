package sk.spp.nzp.be.assembler.customeraccess;

import sk.spp.nzp.be.api.customeraccess.CustomerAccount;
import sk.spp.nzp.be.api.customeraccess.Registration;
import sk.spp.nzp.be.api.customerprofile.CustomerSummary;
import sk.spp.nzp.be.api.employeeprofile.CustomerPreRegistration;
import sk.spp.nzp.be.api.sap.SAPCustomerAccount;
import sk.spp.nzp.be.api.sap.SAPCustomerAccountRegistration;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;

import java.util.Collection;
import java.util.List;

public interface CustomerAccountAssembler {

    CustomerAccountEntity map(CustomerAccount input, CustomerAccountEntity output);

    CustomerAccount mapFull(CustomerAccountEntity input, CustomerAccount output);

    CustomerAccount map(CustomerAccountEntity input, CustomerAccount output);
    
    List<CustomerAccount> map(Collection<CustomerAccountEntity> input);

    CustomerAccountEntity map(CustomerPreRegistration input, CustomerAccountEntity output);

    CustomerAccountEntity map(Registration input, CustomerAccountEntity output);

    List<CustomerAccount> mapToList(Iterable<CustomerAccountEntity> input, List<CustomerAccount> output);

    CustomerSummary map(CustomerAccountEntity input, CustomerSummary output);

    SAPCustomerAccount map(CustomerAccountEntity input, SAPCustomerAccount output);
    CustomerAccountEntity map(SAPCustomerAccountRegistration input, CustomerAccountEntity output);

}
