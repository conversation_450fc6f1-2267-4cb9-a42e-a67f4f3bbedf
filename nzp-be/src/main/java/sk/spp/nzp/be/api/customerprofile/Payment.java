package sk.spp.nzp.be.api.customerprofile;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.api.customerprofile.enums.SettlementType;
import sk.spp.nzp.commons.api.customerprofile.enums.PaymentType;

public class Payment implements Serializable {

    private static final long serialVersionUID = 1L;

    private String externalId;
    private PaymentType type;
    private BigDecimal amount;
    private LocalDate executeAt;
    private String vs;
    private CodeListItem paymentType;
    private Boolean overpaid;
    private SettlementType settlementType;
    private List<String> relatedInvoiceId;

    public Boolean getOverpaid() {
        return overpaid;
    }

    public void setOverpaid(Boolean overpaid) {
        this.overpaid = overpaid;
    }

    public LocalDate getExecuteAt() {
        return executeAt;
    }

    public void setExecuteAt(LocalDate executeAt) {
        this.executeAt = executeAt;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public PaymentType getType() {
        return type;
    }

    public void setType(PaymentType type) {
        this.type = type;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getVs() {
        return vs;
    }

    public void setVs(String vs) {
        this.vs = vs;
    }

    public CodeListItem getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(CodeListItem paymentType) {
        this.paymentType = paymentType;
    }

    public SettlementType getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(SettlementType settlementType) {
        this.settlementType = settlementType;
    }

    public List<String> getRelatedInvoiceId() {
        return relatedInvoiceId;
    }

    public void setRelatedInvoiceId(List<String> relatedInvoiceId) {
        this.relatedInvoiceId = relatedInvoiceId;
    }


    @Override
    public String toString() {
        return "Payment{" +
                "externalId='" + externalId + '\'' +
                ", type=" + type +
                ", amount=" + amount +
                ", executeAt=" + executeAt +
                ", vs='" + vs + '\'' +
                ", paymentType=" + paymentType +
                ", overpaid=" + overpaid +
                ", settlementType=" + settlementType +
                ", relatedInvoiceId='" + relatedInvoiceId + '\'' +
                '}';
    }
}
