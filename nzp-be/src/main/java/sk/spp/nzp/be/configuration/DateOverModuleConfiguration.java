package sk.spp.nzp.be.configuration;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import sk.spp.nzp.commons.jackson.DateOverSerializer;
import sk.spp.nzp.commons.jackson.DateTimeOverSerializer;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Configuration
public class DateOverModuleConfiguration {

    @Bean
    public SimpleModule simpleModule() {

        return new SimpleModule() {

            @Override
            public void setupModule(SetupContext context) {
                super.setupModule(context);

                context.addBeanSerializerModifier(new BeanSerializerModifier() {

                    @Override
                    public JsonSerializer<?> modifySerializer(SerializationConfig config, BeanDescription desc, JsonSerializer<?> serializer) {

                        if (LocalDateTime.class.isAssignableFrom(desc.getBeanClass())) {
                            return new DateTimeOverSerializer((JsonSerializer<LocalDateTime>) serializer);
                        }

                        if (LocalDate.class.isAssignableFrom(desc.getBeanClass())) {
                            return new DateOverSerializer((JsonSerializer<LocalDate>) serializer);
                        }

                        return serializer;
                    }
                });
            }
        };
    }
}
