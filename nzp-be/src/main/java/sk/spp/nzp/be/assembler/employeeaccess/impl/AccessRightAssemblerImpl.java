package sk.spp.nzp.be.assembler.employeeaccess.impl;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import sk.spp.nzp.commons.api.employeeaccess.AccessRight;
import sk.spp.nzp.be.assembler.employeeaccess.AccessRightAssembler;
import sk.spp.nzp.commons.model.employeeaccess.AccessRightEntity;

@Component
public class AccessRightAssemblerImpl implements AccessRightAssembler {

    @Override
    public AccessRight map(AccessRightEntity input, AccessRight output) {
        
        output.setAdmin(input.getAdmin());
        output.setCode(input.getCode());
        output.setName(input.getName());
        output.setDescription(input.getDescription());
        output.setOptionQueue(input.getOptionQueue());
        
        return output;
    }
    
    @Override
    public List<AccessRight> map(Collection<AccessRightEntity> input) {

        if(input != null && !input.isEmpty()) {
            
            return input.stream().map(i -> map(i, new AccessRight())).collect(Collectors.toList());
        }
        
        return Collections.emptyList();
    }
}
