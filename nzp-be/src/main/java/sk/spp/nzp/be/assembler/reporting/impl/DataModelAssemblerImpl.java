package sk.spp.nzp.be.assembler.reporting.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.commons.api.reporting.DataEntity;
import sk.spp.nzp.commons.api.reporting.DataModel;
import sk.spp.nzp.commons.api.reporting.PropertyDefinition;
import sk.spp.nzp.be.assembler.reporting.DataModelAssembler;

import java.util.stream.Collectors;

@Component
public class DataModelAssemblerImpl implements DataModelAssembler {

    @Override
    public DataModel map(DataModel input, DataModel output) {
        output.setVersion(input.getVersion());

        if (input.getDataEntities() != null) {
            output.setDataEntities(
                    input.getDataEntities()
                            .stream()
                            .map(this::map)
                            .collect(Collectors.toList())
            );
        }

        return output;
    }

    private DataEntity map(DataEntity input) {
        DataEntity output = new DataEntity();
        output.setName(input.getName());
        output.setTableName(input.getTableName());

        if (input.getProperties() != null) {
            output.setProperties(
                    input.getProperties()
                            .stream()
                            .map(this::map)
                            .collect(Collectors.toList())
            );
        }

        return output;
    }

    private PropertyDefinition map(PropertyDefinition input) {
        PropertyDefinition output = new PropertyDefinition();
        output.setName(input.getName());
        output.setType(input.getType());
        output.setOptions(input.getOptions());
        output.setRefEntity(input.getRefEntity());

        return output;
    }
}
