package sk.spp.nzp.be.repository.customerprofile.query;

import java.util.UUID;

import javax.persistence.EntityManager;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;

import sk.spp.nzp.be.models.customerprofile.ContractSearch;
import sk.spp.nzp.commons.model.customerprofile.ContractEntity;
import sk.spp.nzp.commons.model.customerprofile.QContractEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

public class ContractFindByContractAccountUuidQueryProvider extends ContractBaseFindByQueryProvider implements QueryDslProvider<ContractEntity> {

    private String contractAccountId;
    private ContractSearch contractSearch;
    private UUID sharedCustomerUuid;

    // TODO: delete unnecessary code
    public ContractFindByContractAccountUuidQueryProvider(String contractAccountId, ContractSearch contractSearch,
                                                          UUID sharedCustomerUuid) {
        
        this.contractAccountId = contractAccountId;
        this.contractSearch = contractSearch;
        this.sharedCustomerUuid = sharedCustomerUuid;
    }

    @Override
    public JPAQuery<ContractEntity> getQuery(EntityManager em) {
        
        QContractEntity contractEntity = QContractEntity.contractEntity;
        
        BooleanExpression exp = contractEntity.contractAccount().id.eq(contractAccountId);
        
        return findByBaseQuery(em, exp, contractSearch, sharedCustomerUuid);
    }
}
