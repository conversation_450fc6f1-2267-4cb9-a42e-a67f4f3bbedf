package sk.spp.nzp.be.assembler.customerrequest.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestNote;
import sk.spp.nzp.be.assembler.customerrequest.CustomerRequestNoteAssembler;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestNoteEntity;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class CustomerRequestNoteAssemblerImpl implements CustomerRequestNoteAssembler {

    @Override
    public CustomerRequestNote map(CustomerRequestNoteEntity input, CustomerRequestNote output) {
        output.setUuid(input.getId().toString());
        output.setCreatedAt(input.getCreatedAt());
        output.setText(input.getText());
        output.setNotificationCreatedAt(input.getNotificationCreatedAt());

        return output;
    }

    @Override
    public List<CustomerRequestNote> map(List<CustomerRequestNoteEntity> input, List<CustomerRequestNote> output) {
        List<CustomerRequestNote> notes = input.stream().map(e -> map(e, new CustomerRequestNote())).collect(Collectors.toList());
        output.addAll(notes);

        return output;
    }

}
