package sk.spp.nzp.be.controller;

import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.common.PagedResponse;
import sk.spp.nzp.be.api.common.QueryStringPaging;
import sk.spp.nzp.be.api.employeeprofile.NotificationTemplateSearch;
import sk.spp.nzp.be.api.notification.*;
import sk.spp.nzp.be.api.reporting.ReportSummary;
import sk.spp.nzp.be.service.notification.NotificationScheduleService;
import sk.spp.nzp.be.service.notification.NotificationTemplateAttachmentService;
import sk.spp.nzp.be.service.notification.NotificationTemplateService;
import sk.spp.nzp.be.service.reporting.ReportService;
import sk.spp.nzp.commons.api.notification.enums.NotificationTemplateStatus;
import sk.spp.nzp.commons.api.notification.enums.NotificationTemplateType;
import sk.spp.nzp.commons.api.reporting.Report;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@RestController
@RequestMapping("/admin/notification-templates")
public class AdminNotificationTemplateController {

    private NotificationTemplateService notificationTemplateService;

    private NotificationScheduleService notificationScheduleService;

    private NotificationTemplateAttachmentService notificationTemplateAttachmentService;

    private ReportService reportService;

    public AdminNotificationTemplateController(NotificationTemplateService notificationTemplateService,
                                               NotificationScheduleService notificationScheduleService,
                                               NotificationTemplateAttachmentService notificationTemplateAttachmentService,
                                               ReportService reportService) {
        this.notificationTemplateService = notificationTemplateService;
        this.notificationScheduleService = notificationScheduleService;
        this.notificationTemplateAttachmentService = notificationTemplateAttachmentService;
        this.reportService = reportService;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_EDIT')")
    @PutMapping(value = "/{uuid}")
    public NotificationTemplate updateNotificationTemplate(
            @LogParam("uuid") @PathVariable UUID uuid,
            @LogParam("notificationTemplate") @RequestBody NotificationTemplate notificationTemplate) {

        return notificationTemplateService.updateNotificationTemplate(uuid, notificationTemplate);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_EDIT')")
    @DeleteMapping(value = "/{uuid}")
    public void deleteNotificationTemplate(@LogParam("uuid") @PathVariable UUID uuid) {

        notificationTemplateService.deleteNotificationTemplate(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_VIEW')")
    @PostMapping(value = "/search")
    public PagedResponse<NotificationTemplateSummary> notificationTemplateSearch(
            @LogParam("notificationTemplateSearch ") @RequestBody NotificationTemplateSearch notificationTemplateSearch) {

        return notificationTemplateService.getNotificationTemplates(notificationTemplateSearch);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE')")
    @PostMapping(value = "/{uuid}/schedule")
    public NotificationSchedule adminScheduleNotification(
            @LogParam("notificationSchedule ") @RequestBody NotificationSchedule notificationSchedule,
            @LogParam("uuid") @PathVariable UUID uuid) {

        return notificationScheduleService.scheduleNotification(uuid, notificationSchedule);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE')")
    @GetMapping(value = "/{uuid}/schedules")
    public List<NotificationScheduleSummary> adminGetSchedulesNotification(@LogParam("uuid") @PathVariable UUID uuid) {

        return notificationScheduleService.getNotificationSchedules(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_EDIT')")
    @PostMapping(value = "")
    public NotificationTemplate createNotificationTemplate(
            @LogParam("notificationTemplate") @RequestBody NotificationTemplate notificationTemplate) {

        return notificationTemplateService.createNotificationTemplate(notificationTemplate);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_EDIT')")
    @PostMapping(value = "/test")
    public NotificationTemplateTestResponse testNotificationTemplate(
            @LogParam("notificationTemplateTest") @RequestBody NotificationTemplateTest notificationTemplateTest) {

        return notificationTemplateService.testNotificationTemplate(notificationTemplateTest);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_VIEW')")
    @GetMapping(value = "/{uuid}/variables")
    public TemplateVariables notificationTemplateVariables(@LogParam("uuid") @PathVariable UUID uuid) {

        return notificationTemplateService.getNotificationTemplatesVariables(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_VIEW')")
    @GetMapping(value = "/reports/{uuid}/variables")
    public TemplateVariables notificationTemplateReportVariables(@LogParam("uuid") @PathVariable UUID uuid) {

        return notificationTemplateService.getNotificationTemplatesReportsVariables(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_VIEW')")
    @GetMapping(value = "", produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<NotificationTemplate> adminGetTemplates(
            @LogParam("queryStringPaging ") QueryStringPaging queryStringPaging,
            @LogParam("statuses") @RequestParam(required = false) Set<NotificationTemplateStatus> statuses,
            @LogParam("types") @RequestParam(required = false) Set<NotificationTemplateType> types) {

        return notificationTemplateService.getList(statuses, types, queryStringPaging.toPaging());
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_VIEW')")
    @GetMapping(value = "/{uuid}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public NotificationTemplate adminGetByUuid(@LogParam("uuid") @PathVariable UUID uuid) {

        return notificationTemplateService.getByUuid(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_VIEW')")
    @GetMapping(value = "/attachments/{uuid}/content")
    public void adminGetAttachmentContent(@LogParam("uuid") @PathVariable UUID uuid, HttpServletResponse response) throws IOException {

        NotificationTemplateAttachment attachment = notificationTemplateAttachmentService.getByUuidWithContent(uuid);

        ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                .filename(attachment.getFileName())
                .build();

        response.setContentType(attachment.getMimeType());
        response.setContentLengthLong(attachment.getLength());
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());

        StreamUtils.copy(attachment.getContent(), response.getOutputStream());
    }


    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_EDIT')")
    @PostMapping(value = "/{uuid}/attachments", produces = {MediaType.APPLICATION_JSON_VALUE})
    public NotificationTemplateAttachment createAttachment(@LogParam("uuid") @PathVariable UUID uuid,
                                                           @LogParam("attachment") @RequestBody NotificationTemplateAttachment notificationTemplate) {

        return notificationTemplateAttachmentService.createAttachment(uuid, notificationTemplate);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_EDIT')")
    @PutMapping(value = "/attachments/{attachmentUuid}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public NotificationTemplateAttachment uploadAttachmentContent(@LogParam("attachmentUuid") @PathVariable(value = "attachmentUuid") UUID uuid,
                                                                  @RequestBody MultipartFile content) throws IOException {

        return notificationTemplateAttachmentService.createAttachmentContent(
                uuid,
                content.getBytes(),
                content.getContentType(),
                content.getOriginalFilename(),
                content.getSize()
        );
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_EDIT')")
    @DeleteMapping("/attachments/{attachmentUuid}")
    @ResponseStatus(HttpStatus.ACCEPTED)
    public void adminDeleteAttachment(@LogParam("attachmentUuid") @PathVariable(name = "attachmentUuid") UUID uuid) {
        notificationTemplateAttachmentService.deleteAttachment(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_VIEW')")
    @GetMapping(value = "/report/search", produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<Report> getReports(@LogParam("queryStringPaging ") QueryStringPaging queryStringPaging,
                             @LogParam("queryStringPaging ") @RequestParam(required = false) String name){

        return reportService.getReportAllOnlyOutputProperties(queryStringPaging.toPaging(), name);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('NOTIFICATION_TEMPLATES_VIEW')")
    @GetMapping(value = "/reports/{uuid}/run/summary", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ReportSummary runCountById(@LogParam("uuid") @PathVariable UUID uuid) {
        return reportService.runSummary(uuid);
    }
}