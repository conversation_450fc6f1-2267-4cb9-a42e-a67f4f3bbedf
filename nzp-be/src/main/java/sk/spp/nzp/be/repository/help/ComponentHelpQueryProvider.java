package sk.spp.nzp.be.repository.help;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.repository.common.AbstractQueryProvider;
import sk.spp.nzp.commons.model.help.ComponentHelpEntity;
import sk.spp.nzp.commons.model.help.QComponentHelpEntity;
import sk.spp.nzp.commons.model.help.QComponentHelpI18NEntity;

import javax.persistence.EntityManager;
import java.util.List;

public class ComponentHelpQueryProvider extends AbstractQueryProvider<ComponentHelpEntity> {

    private QComponentHelpEntity qComponentHelpEntity = QComponentHelpEntity.componentHelpEntity;
    private QComponentHelpI18NEntity qComponentHelpI18NEntity = QComponentHelpI18NEntity.componentHelpI18NEntity;

    private List<String> screens;
    private List<String> fields;

    public ComponentHelpQueryProvider(List<String> screens, List<String> fields) {
        this.screens = screens;
        this.fields = fields;
    }

    @Override
    public JPAQuery<ComponentHelpEntity> getQuery(EntityManager em) {
        JPAQuery<ComponentHelpEntity> query = new JPAQuery<ComponentHelpEntity>(em).from(qComponentHelpEntity);

        query.where(screens(), fields()).distinct();

        return query;
    }

    private BooleanExpression screens() {
        if (screens != null && !screens.isEmpty()) {
            return qComponentHelpEntity.screen.in(screens);
        }
        return null;
    }

    private BooleanExpression fields() {
        if (fields != null && !fields.isEmpty()) {
            return qComponentHelpEntity.field.in(fields);
        }
        return null;
    }
}
