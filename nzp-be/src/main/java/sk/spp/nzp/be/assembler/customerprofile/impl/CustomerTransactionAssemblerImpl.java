package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerprofile.invoicesummary.CustomerTransactionSummary;
import sk.spp.nzp.be.assembler.customerprofile.CustomerTransactionAssembler;
import sk.spp.nzp.commons.model.customerprofile.CustomerTransactionEntity;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class CustomerTransactionAssemblerImpl implements CustomerTransactionAssembler {

    @Override
    public CustomerTransactionSummary map(CustomerTransactionEntity input, CustomerTransactionSummary output) {
        if (input == null) {
            return output;
        }

        return output
                .setTransactionId(input.getId())
                .setStartedAt(input.getStartedAt())
                .setFinishedAt(input.getFinishedAt())
                .setFinished(input.getFinished())
                .setVs(input.getVs())
                .setAmount(input.getAmount())
                .setStatus(input.getStatus())
                .setBulk(input.getBulk());
    }

    @Override
    public List<CustomerTransactionSummary> map(Collection<CustomerTransactionEntity> input) {

        if(input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new CustomerTransactionSummary())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}