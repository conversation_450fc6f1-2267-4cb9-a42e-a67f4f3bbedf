package sk.spp.nzp.be.controller;

import org.springframework.http.MediaType;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.annotation.paging.PagingAsQueryParams;
import sk.spp.nzp.be.api.common.FilterCondition;
import sk.spp.nzp.be.api.common.PagedResponse;
import sk.spp.nzp.be.api.common.QueryStringPaging;
import sk.spp.nzp.be.api.customerprofile.*;
import sk.spp.nzp.be.api.customerrequest.BusinessPartnerDataChange;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestSummary;
import sk.spp.nzp.be.service.customerprofile.*;
import sk.spp.nzp.commons.api.customerrequest.component.BankConnection;
import sk.spp.nzp.commons.validator.CustomerRequestChecks;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/business-partners")
public class BusinessPartnerController {

    private BusinessPartnerService businessPartnerService;
    private DeliveryPointService deliveryPointService;
    private UnitedDeliveryPointService unitedDeliveryPointService;
    private ContractAccountService contractAccountService;
    private InvoiceService invoiceService;

    public BusinessPartnerController(
            BusinessPartnerService businessPartnerService,
            DeliveryPointService deliveryPointService,
            UnitedDeliveryPointService unitedDeliveryPointService,
            ContractAccountService contractAccountService,
            InvoiceService invoiceService
    ) {
        this.businessPartnerService = businessPartnerService;
        this.deliveryPointService = deliveryPointService;
        this.unitedDeliveryPointService = unitedDeliveryPointService;
        this.contractAccountService = contractAccountService;
        this.invoiceService = invoiceService;
    }

    
    @Log
    @LogParam
    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and not hasRole('ROLE_EMPLOYEE')) or (hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW'))")
    @GetMapping(value = "/{bpId}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public BusinessPartner getBusinessPartnerById(@LogParam("bpId") @PathVariable String bpId) {

        return businessPartnerService.getById(bpId);
    }

// TODO: delete unnecessary code
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and not hasRole('ROLE_EMPLOYEE')) or (hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW'))")
//    @GetMapping(value = "/{uuid}/contract-accounts", produces = {MediaType.APPLICATION_JSON_VALUE})
//    @PagingAsQueryParams
//    public PagedResponse<ContractAccount> getContractAccounts(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("queryStringPaging ")  QueryStringPaging  queryStringPaging) {
//
//        return contractAccountService.getByBusinessPartner(uuid, new ContractAccountSearch().setPaging(queryStringPaging.toPaging()));
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and not hasRole('ROLE_EMPLOYEE')) or (hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW'))")
//    @PostMapping(value = "/{uuid}/contract-accounts/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public PagedResponse<ContractAccount> contractAccountSearch(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("contractAccountSearch ") @RequestBody ContractAccountSearch contractAccountSearch) {
//
//        return contractAccountService.getByBusinessPartner(uuid, contractAccountSearch);
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and not hasRole('ROLE_EMPLOYEE')) or (hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW'))")
//    @GetMapping(value = "/{uuid}/delivery-points", produces = {MediaType.APPLICATION_JSON_VALUE})
//    @PagingAsQueryParams
//    public PagedResponse<DeliveryPoint> getDeliveryPoints(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("queryStringPaging ")  QueryStringPaging  queryStringPaging) {
//
//        return deliveryPointService.getByBusinessPartner(uuid, new DeliveryPointSearch().setPaging(queryStringPaging.toPaging()));
//    }
//
//    @Log
//    @LogParam
//    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
//    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and not hasRole('ROLE_EMPLOYEE')) or (hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW'))")
//    @PostMapping(value = "/{uuid}/delivery-points/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public PagedResponse<DeliveryPoint> deliveryPointSearch(@LogParam("uuid") @PathVariable(value = "uuid") UUID uuid,
//            @LogParam("deliveryPointSearch ") @RequestBody DeliveryPointSearch deliveryPointSearch) {
//
//        return deliveryPointService.getByBusinessPartner(uuid, deliveryPointSearch);
//    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @GetMapping(value = "/{bpId}/united-delivery-points", produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<UnitedDeliveryPointSummary> getUnitedDeliveryPointsByBp(@LogParam("bpId") @PathVariable String bpId,
                                                                                 @LogParam("queryStringPaging ") QueryStringPaging  queryStringPaging,
                                                                                 @LogParam("fetch") @RequestParam(name = "fetch", required = false) List<DeliveryPointSummary.Fetch> fetches) {

        return unitedDeliveryPointService.getUnitedPointsByBusinessPartner(bpId, new UnitedDeliveryPointSearch(queryStringPaging.toPaging()), fetches);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping(value = "/{bpId}/united-delivery-points/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<UnitedDeliveryPointSummary> unitedDeliveryPointSearch(@LogParam("bpId") @PathVariable String bpId,
                                                                               @LogParam("unitedDeliveryPointSearch ") @RequestBody UnitedDeliveryPointSearch unitedDeliveryPointSearch) {

        return unitedDeliveryPointService.getUnitedPointsByBusinessPartner(bpId, unitedDeliveryPointSearch);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @GetMapping(value = "/{bpId}/delivery-points/filter")
    public List<DeliveryPointSummary> unitedDeliveryPointFilter(@LogParam("bpId") @PathVariable String bpId,
                                                                               @LogParam("filterCondition ") @RequestParam FilterCondition condition) {

        return unitedDeliveryPointService.getUnitedPointsByBusinessPartnerFiltered(bpId, condition);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))")
    @GetMapping(value = "/{bpId}/invoices", produces = {MediaType.APPLICATION_JSON_VALUE})
    @PagingAsQueryParams
    public PagedResponse<InvoiceSummary> getInvoices(@LogParam("bpId") @PathVariable String bpId,
                                                     @LogParam("queryStringPaging ") QueryStringPaging  queryStringPaging) {
        
        return invoiceService.getInvoices(
                new InvoiceSearch()
                        .setPaging(queryStringPaging.toPaging())
                        .withBusinessPartnerId(bpId)
        );
    }
    
    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))")
    @PostMapping(value = "/{bpId}/invoices/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public PagedResponse<InvoiceSummary> invoiceSearchByBp(@LogParam("bpId") @PathVariable String bpId,
            @LogParam("invoiceSearch ") @RequestBody InvoiceSearch invoiceSearch) {

        return invoiceService.getInvoices(invoiceSearch.withBusinessPartnerId(bpId));
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping(value = "/{bpId}/e-invoices/email")
    public List<CustomerRequestSummary> updateEInvoiceEmail(@LogParam("bpId") @PathVariable String bpId,
                                                            @LogParam("emailRequest") @Valid @RequestBody EInvoiceRequest eInvoiceRequest) {

        return contractAccountService.updateEInvoiceEmailByBusinessPartner(bpId, eInvoiceRequest.getEmail());
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
    @PostMapping(value = "/{bpId}/e-invoices/activation")
    public List<CustomerRequestSummary> activateEInvoice(@LogParam("bpId") @PathVariable String bpId,
                               @LogParam("emailRequest") @Valid @RequestBody EInvoiceRequest EInvoiceRequest) {

        return contractAccountService.activateEInvoiceByBusinessPartner(bpId, EInvoiceRequest.getEmail());
    }

    @Log
    @LogParam
    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and not hasRole('ROLE_EMPLOYEE')) or (hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW'))")
    @PostMapping(value = "/{bpId}/customer-requests/bank-connection", produces = {MediaType.APPLICATION_JSON_VALUE})
    public CustomerRequestSummary changeBankConnection(
            @LogParam("bpId") @PathVariable String bpId,
            @LogParam("bankConnection") @Validated(CustomerRequestChecks.class) @RequestBody BankConnection bankConnection
    ) {
        return businessPartnerService.changeBankConnection(bpId, bankConnection);
    }

    @Log
    @LogParam
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and not hasRole('ROLE_EMPLOYEE')) or (hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW'))")
    @PostMapping(value = "/{bpId}/consent-banner-visibility", produces = {MediaType.APPLICATION_JSON_VALUE})
    public BusinessPartnerSummary changeConsentBannerVisibility(
            @LogParam("bpId") @PathVariable String bpId
    ) {
        return businessPartnerService.changeConsentBannerVisibility(bpId);
    }

    @Log
    @LogParam
    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and not hasRole('ROLE_EMPLOYEE')) or (hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW'))")
    @PostMapping(value = "/{bpId}/customer-requests/personal-data", produces = {MediaType.APPLICATION_JSON_VALUE})
    public CustomerRequestSummary changePersonalData(
            @LogParam("bpId") @PathVariable String bpId,
            @LogParam("data") @Validated(CustomerRequestChecks.class) @RequestBody BusinessPartnerDataChange data
    ) {
        return businessPartnerService.changePersonalData(bpId, data);
    }

    @Log
    @LogParam
    @Secured({"ROLE_CUSTOMER", "ROLE_EMPLOYEE"})
    @PreAuthorize("(hasRole('ROLE_CUSTOMER') and not hasRole('ROLE_EMPLOYEE')) or (hasRole('ROLE_EMPLOYEE') and hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_BUSINESS_PARTNERS_VIEW'))")
    @PostMapping(value = "/{bpId}/sync", produces = {MediaType.APPLICATION_JSON_VALUE})
    public BusinessPartnerSummary changeEmail(
            @LogParam("bpId") @PathVariable String bpId,
            @LogParam("businessPartnerSync") @Validated(CustomerRequestChecks.class) @RequestBody BusinessPartnerSync businessPartnerSync
    ){
        return businessPartnerService.changeEmail(bpId,businessPartnerSync);
    }

}
