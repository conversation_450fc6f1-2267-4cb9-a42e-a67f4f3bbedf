package sk.spp.nzp.be.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.customeraccess.IbanCheck;
import sk.spp.nzp.be.api.customeraccess.PhoneFormatCheck;

import javax.validation.Valid;

@RestController
@RequestMapping("/validations")
public class ValidationsController {

    @Log
    @LogParam
    @PostMapping("/phone")
    public void checkPasswordComplexityWithPhone(@LogParam("req") @Valid @RequestBody PhoneFormatCheck req) {
        return;
    }

    @Log
    @LogParam
    @PostMapping("/iban")
    public void checkIban(@LogParam("req") @Valid @RequestBody IbanCheck req) {
        return;
    }

}
