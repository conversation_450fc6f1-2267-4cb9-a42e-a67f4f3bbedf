package sk.spp.nzp.be.assembler.auditlog.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.auditlog.AuditLog;
import sk.spp.nzp.be.api.auditlog.AuditLogSummary;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerSummary;
import sk.spp.nzp.be.assembler.auditlog.AuditLogAssembler;
import sk.spp.nzp.be.assembler.customerprofile.BusinessPartnerAssembler;
import sk.spp.nzp.be.service.codelist.model.CodeListQuery;
import sk.spp.nzp.be.service.codelist.provider.CustomerRequestTemplateCodeListProvider;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.assembler.AbstractAssembler;
import sk.spp.nzp.commons.assembler.codelist.CodeListItemAssembler;
import sk.spp.nzp.commons.enums.CodeListType;
import sk.spp.nzp.commons.model.audit.AuditLogEntity;
import sk.spp.nzp.commons.utils.Expressions;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class AuditLogAssemblerImpl extends AbstractAssembler implements AuditLogAssembler {

    private BusinessPartnerAssembler businessPartnerAssembler;
    private CodeListItemAssembler codeListItemAssembler;
    private CustomerRequestTemplateCodeListProvider customerRequestTemplateCodeListProvider;

    public AuditLogAssemblerImpl(
            BusinessPartnerAssembler businessPartnerAssembler,
            CodeListItemAssembler codeListItemAssembler,
            CustomerRequestTemplateCodeListProvider customerRequestTemplateCodeListProvider
    ) {
        this.businessPartnerAssembler = businessPartnerAssembler;
        this.codeListItemAssembler = codeListItemAssembler;
        this.customerRequestTemplateCodeListProvider = customerRequestTemplateCodeListProvider;
    }


    @Override
    public AuditLogSummary assemble(AuditLogEntity input, AuditLogSummary output, boolean canFillEmployee, String employeeLogin) {

        valueNotNull(input.getId().toString(), output::setUuid);

        valueNotNull(input.getCode(), value -> output.setCode(codeListItemAssembler.mapSummary(CodeListType.AUDIT_LOG_CODE, value.name(), new CodeListItem())));

        valueNotNull(input.getCreatedAt(), output::setCreatedAt);
        valueNotNull(input.getEntityType(), output::setEntityType);
        valueNotNull(input.getEntityReference(), output::setEntityReference);
        valueNotNull(input.getEntityId(), output::setEntityId);
        valueNotNull(filterAttributes(input.getAttributes()), output::setAttributes);

        valueNotNull(input.getLoggedCustomerAccountEmail(), output::setLoggedCustomerAccountEmail);
        valueNotNull(input.getLoggedCustomerAccountName(), output::setLoggedCustomerAccountName);

        valueNotNull(input.getRelatedCustomerAccountEmail(), output::setRelatedCustomerAccountEmail);
        valueNotNull(input.getRelatedCustomerAccountName(), output::setRelatedCustomerAccountName);
        if (canFillEmployee || (employeeLogin != null && employeeLogin.equals(input.getEmployeeLogin()))) {
            valueNotNull(input.getEmployeeEmail(), output::setEmployeeEmail);
            valueNotNull(input.getEmployeeLogin(), output::setEmployeeLogin);
            valueNotNull(input.getEmployeeName(), output::setEmployeeName);
        }
        if (input.getEmployeeLogin()!=null){
            output.setEmployeePresent(true);
        }else{
            output.setEmployeePresent(false);
        }
        valueNotNull(input.getBusinessPartnerName(), output::setBusinessPartnerName);
        valueNotNull(input.getBusinessPartnerExternalId(), output::setBusinessPartnerExternalId);
        valueNotNull(input.getFailover(), output::setFailover);

        if( Expressions.notEmpty(input.getEntityItem()) && input.getEntityType() != null) {
            switch (input.getEntityType()){
                case CUSTOMER_REQUEST:
                    CodeListItem codeListItem = customerRequestTemplateCodeListProvider.get(
                            CodeListType.CUSTOMER_REQUEST_TEMPLATE.name(),
                            new CodeListQuery().withCode(input.getEntityItem())
                    );
                    valueNotNull(codeListItem.getName(), output::setEntityText);
                    break;
                case ACCESS_GROUP_ENTITY:
                    valueNotNull(input.getEntityItem(), output::setEntityText);
                    break;
            }
        }

        return output;
    }


    @Override
    public AuditLog assemble(AuditLogEntity input, AuditLog output, boolean canFillEmployee, String employeeLogin) {

        valueNotNull(input.getId().toString(), output::setUuid);

        valueNotNull(input.getCode(), value -> output.setCode(codeListItemAssembler.mapSummary(CodeListType.AUDIT_LOG_CODE, value.name(), new CodeListItem())));

        valueNotNull(input.getCreatedAt(), output::setCreatedAt);
        valueNotEmpty(input.getSessionId(), output::setSessionId);
        valueNotEmpty(input.getRequestId(), output::setRequestId);
        valueNotEmpty(input.getRemoteAddress(), output::setRemoteAddress);
        valueNotNull(input.getEntityReference(), output::setEntityReference);
        valueNotNull(input.getEntityType(), output::setEntityType);
        valueNotNull(filterAttributes(input.getAttributes()), output::setAttributes);
        valueNotNull(input.getEntityId(), output::setEntityId);

        if(input.getLoggedCustomerAccount() != null) {
            output.setLoggedCustomerId(input.getLoggedCustomerAccount().getId());
        }

        valueNotNull(input.getLoggedCustomerAccountEmail(), output::setLoggedCustomerAccountEmail);
        valueNotNull(input.getLoggedCustomerAccountName(), output::setLoggedCustomerAccountName);

        if(input.getRelatedCustomerAccount() != null) {
            output.setRelatedCustomerId(input.getRelatedCustomerAccount().getId());
        }

        valueNotNull(input.getRelatedCustomerAccountEmail(), output::setRelatedCustomerAccountEmail);
        valueNotNull(input.getRelatedCustomerAccountName(), output::setRelatedCustomerAccountName);

        if (canFillEmployee || (employeeLogin != null && employeeLogin.equals(input.getEmployeeLogin()))) {
            valueNotNull(input.getEmployeeEmail(), output::setEmployeeEmail);
            valueNotNull(input.getEmployeeLogin(), output::setEmployeeLogin);
            valueNotNull(input.getEmployeeName(), output::setEmployeeName);
        }
        if(input.getEmployeeLogin()!=null){
            output.setEmployeePresent(true);
        }else{
            output.setEmployeePresent(false);
        }

        valueNotNull(input.getUserAgent(), output::setUserAgent);

        valueNotNull(input.getBusinessPartnerName(), output::setBusinessPartnerName);
        valueNotNull(input.getUserAgent(), output::setUserAgent);
        valueNotNull(input.getFailover(), output::setFailover);

        if( Expressions.notEmpty(input.getEntityItem()) && input.getEntityType() != null) {
            switch (input.getEntityType()){
                case CUSTOMER_REQUEST:
                    CodeListItem codeListItem = customerRequestTemplateCodeListProvider.get(
                            CodeListType.CUSTOMER_REQUEST_TEMPLATE.name(),
                            new CodeListQuery().withCode(input.getEntityItem())
                    );
                    valueNotNull(codeListItem.getName(), output::setEntityText);
                    break;
                case ACCESS_GROUP_ENTITY:
                    valueNotNull(input.getEntityItem(), output::setEntityText);
                    break;
            }
        }

        return output;
    }
    
    @Override
    public AuditLog mapFull(AuditLogEntity input, AuditLog output, boolean canFillEmployee, String employeeLogin) {
        
        assemble(input, output, canFillEmployee, employeeLogin);
        
        if(input.getBusinessPartner() != null) {
            output.setBusinessPartnerSummary(businessPartnerAssembler.map(input.getBusinessPartner(), new BusinessPartnerSummary()));
        }

        return output;
    }

    @Override
    public List<AuditLog> map(List<AuditLogEntity> input, boolean canFillEmployee, String employeeLogin) {
        if (input != null) {
            return input.stream()
                    .map(auditLogEntity -> assemble(auditLogEntity, new AuditLog(), canFillEmployee, employeeLogin))
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public List<AuditLogSummary> mapSummary(List<AuditLogEntity> input, boolean canFillEmployee, String employeeLogin) {
        if (input != null) {
            return input.stream()
                    .map(auditLogEntity -> assemble(auditLogEntity, new AuditLogSummary(), canFillEmployee, employeeLogin))
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public List<AuditLog> mapFull(List<AuditLogEntity> input, boolean canFillEmployee, String employeeLogin) {
        if (input != null) {
            return input.stream()
                    .map(auditLogEntity -> mapFull(auditLogEntity, new AuditLog(), canFillEmployee, employeeLogin))
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    private Map<String, String> filterAttributes(Map<String, String> attributes) {
        if (attributes == null) {
            return null;
        }

        Map<String, String> filteredAttributes = new HashMap<>();

        attributes.forEach((key, value) -> {
            if (!key.startsWith("secured_")) {
                filteredAttributes.put(key, value);
            }
        });

        return filteredAttributes;
    }
}
