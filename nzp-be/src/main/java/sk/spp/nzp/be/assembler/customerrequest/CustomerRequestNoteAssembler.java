package sk.spp.nzp.be.assembler.customerrequest;

import sk.spp.nzp.be.api.customerrequest.CustomerRequestNote;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestNoteEntity;

import java.util.List;

public interface CustomerRequestNoteAssembler {

    CustomerRequestNote map(CustomerRequestNoteEntity input, CustomerRequestNote output);

    List<CustomerRequestNote> map(List<CustomerRequestNoteEntity> input, List<CustomerRequestNote> output);

}
