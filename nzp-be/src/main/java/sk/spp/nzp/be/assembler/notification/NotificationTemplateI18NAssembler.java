package sk.spp.nzp.be.assembler.notification;

import sk.spp.nzp.be.api.notification.NotificationTemplateI18N;
import sk.spp.nzp.commons.model.notification.NotificationTemplateI18NEntity;

import java.util.Collection;
import java.util.List;

public interface NotificationTemplateI18NAssembler {

    NotificationTemplateI18NEntity map(NotificationTemplateI18N input, NotificationTemplateI18NEntity output);

    NotificationTemplateI18N map(NotificationTemplateI18NEntity input, NotificationTemplateI18N output);

    List<NotificationTemplateI18N> map(Collection<NotificationTemplateI18NEntity> input);

}
