package sk.spp.nzp.be.controller.notification;

import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.notification.CustomerNotification;
import sk.spp.nzp.be.service.notification.CustomerNotificationService;

import java.util.Collection;
import java.util.UUID;

@RestController
@RequestMapping("/notifications")
public class CustomerNotificationController {

    private CustomerNotificationService customerNotificationService;

    public CustomerNotificationController(CustomerNotificationService customerNotificationService) {

        this.customerNotificationService = customerNotificationService;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE'))")
    @GetMapping(value = "/{uuid}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public CustomerNotification getNotificationByUuid(@LogParam("uuid") @PathVariable UUID uuid) {

        return customerNotificationService.getByUuid(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE'))")
    @DeleteMapping(value = "/{uuid}")
    public void deleteByUuid(@LogParam("uuid") @PathVariable UUID uuid) {

        customerNotificationService.deleteByUuid(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE'))")
    @PutMapping(path = "read", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void putNotificationsRead(@LogParam("uuids") @RequestBody Collection<UUID> uuids) {
        customerNotificationService.markAsRead(uuids);
    }
}