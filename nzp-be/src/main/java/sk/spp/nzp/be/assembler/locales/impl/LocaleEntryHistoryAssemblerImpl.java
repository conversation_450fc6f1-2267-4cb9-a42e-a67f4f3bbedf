package sk.spp.nzp.be.assembler.locales.impl;

import org.springframework.stereotype.Component;
import sk.spp.nzp.be.assembler.locales.LocaleEntryHistoryAssembler;
import sk.spp.nzp.commons.api.locales.LocaleHistorySearchResponse;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;
import sk.spp.nzp.commons.model.locales.LocaleEntryHistoryEntity;

import java.util.List;

@Component
public class LocaleEntryHistoryAssemblerImpl implements LocaleEntryHistoryAssembler {

    @Override
    public LocaleHistorySearchResponse map(LocaleEntryHistoryEntity input, LocaleHistorySearchResponse output) {
        if (input == null) {
            return output;
        }
        output.setEditedEntryKey(input.getPath());
        output.setPreviousValue(input.getPreviousValue());
        output.setEditTime(input.getCreatedAt());
        output.setLoggedInEmployeeId(input.getLoggedInEmployeeId());

        return output;
    }

    @Override
    public List<LocaleHistorySearchResponse> map(
            List<LocaleEntryHistoryEntity> input, List<LocaleHistorySearchResponse> output) {

        input.stream()
                .map(e -> map(e, new LocaleHistorySearchResponse()))
                .forEach(output::add);

        return output;
    }

}