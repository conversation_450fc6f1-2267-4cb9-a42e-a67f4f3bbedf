package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerprofile.*;
import sk.spp.nzp.be.assembler.customerprofile.AccountManagerAssembler;
import sk.spp.nzp.be.assembler.customerprofile.AddressEmbedableAssembler;
import sk.spp.nzp.be.assembler.customerprofile.BusinessPartnerAssembler;
import sk.spp.nzp.be.assembler.customerprofile.BusinessPartnerConsentAssembler;
import sk.spp.nzp.be.service.customerrequest.FieldGroupUpdateService;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.api.customerrequest.enums.CustomerRequestCode;
import sk.spp.nzp.commons.assembler.codelist.CodeListItemAssembler;
import sk.spp.nzp.commons.enums.CodeListType;
import sk.spp.nzp.commons.model.codelist.TitleEntity;
import sk.spp.nzp.commons.model.common.VersionedEntity;
import sk.spp.nzp.commons.model.customeraccess.enums.QueueAccessEnum;
import sk.spp.nzp.commons.model.customerprofile.BusinessPartnerEntity;
import sk.spp.nzp.commons.model.customerprofile.BusinessPartnerExtendedOwnershipViewEntity;
import sk.spp.nzp.commons.model.customerprofile.BusinessPartnerPairingRequestEntity;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestEntity;
import sk.spp.nzp.commons.repository.customerrequest.CustomerRequestRepository;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class BusinessPartnerAssemblerImpl implements BusinessPartnerAssembler {

    private static final Logger LOG = LoggerFactory.getLogger(BusinessPartnerAssemblerImpl.class);

    private final BusinessPartnerConsentAssembler businessPartnerConsentAssembler;
    private final CodeListItemAssembler genericCodeListAssembler;
    private final AddressEmbedableAssembler addressEmbedableAssembler;
    private final AccountManagerAssembler accountManagerAssembler;
    private final FieldGroupUpdateService fieldGroupUpdateService;
    private final CustomerRequestRepository customerRequestRepository;

    public BusinessPartnerAssemblerImpl(BusinessPartnerConsentAssembler businessPartnerConsentAssembler,
                                        CodeListItemAssembler genericCodeListAssembler,
                                        AddressEmbedableAssembler addressEmbedableAssembler,
                                        AccountManagerAssembler accountManagerAssembler,
                                        FieldGroupUpdateService fieldGroupUpdateService,
                                        CustomerRequestRepository customerRequestRepository) {
        this.businessPartnerConsentAssembler = businessPartnerConsentAssembler;
        this.genericCodeListAssembler = genericCodeListAssembler;
        this.addressEmbedableAssembler = addressEmbedableAssembler;
        this.accountManagerAssembler = accountManagerAssembler;
        this.fieldGroupUpdateService = fieldGroupUpdateService;
        this.customerRequestRepository = customerRequestRepository;
    }

    @Override
    public BusinessPartner mapRef(BusinessPartnerEntity input, BusinessPartner output) {

        if (input == null) // external entities may not be imported during assembling
            return null;

        output.setId(input.getId());

        return output;
    }

    @Override
    public BusinessPartner map(BusinessPartnerEntity input, BusinessPartner output) {

        if (input == null) // external entities may not be imported during assembling
            return null;

        output.setId(input.getId());
        output.setExternalId(input.getExternalId());

        if (input.getType() != null) {
            output.setType(genericCodeListAssembler.mapSummary(CodeListType.BUSINESS_PARTNER_TYPE, input.getType(), new CodeListItem()));
        }
        if (input.getKind() != null) {
            output.setKind(genericCodeListAssembler.mapSummary(CodeListType.BUSINESS_PARTNER_KIND, input.getKind(), new CodeListItem()));
        }
        if (input.getCategory() != null) {
            output.setCategory(genericCodeListAssembler.mapSummary(CodeListType.BUSINESS_PARTNER_CATEGORY, input.getCategory(), new CodeListItem()));
        }
        output.setQueue(input.getQueue());
        output.setTaxIdNumber(input.getTaxIdNumber());
        output.setVatRegistrationNumber(input.getVatRegistrationNumber());
        output.setCompanyRegistrationNumber(input.getCompanyRegistrationNumber());
        output.setName(input.getName());
        output.setFirstName(input.getFirstName());
        output.setLastName(input.getLastName());
        output.setEmail(input.getEmail());
        output.setPhone(input.getPhone());
        output.setFrancheFixation(input.getFrancheFixation());
        output.setConsentBannerInvisibleUntil(input.getConsentBannerInvisibleUntil());
        output.setCardPaymentEnabled(input.getCardPaymentEnabled() != null ? input.getCardPaymentEnabled() : Boolean.FALSE);
        if (input.getTitleFront() != null) {
            output.setTitleFront(map(input.getTitleFront()));
        }

        if (input.getTitleBehind() != null) {
            output.setTitleBehind(map(input.getTitleBehind()));
        }
        if (input.getPrimaryAddress() != null) {
            output.setPrimaryAddress(addressEmbedableAssembler.map(input.getPrimaryAddress(), new Address()));
        }
        if (input.getAccountManager() != null) {
            output.setAccountManager(accountManagerAssembler.map(input.getAccountManager(), new AccountManager()));
        }

        output.setFieldUpdates(fieldGroupUpdateService.getFieldUpdates(input));

        List<CustomerRequestEntity> customerRequestEntities = fieldGroupUpdateService.getLastCustomerRequestsForEntity(input, Set.of(CustomerRequestCode.X_MS));
        if (Objects.nonNull(customerRequestEntities) && !customerRequestEntities.isEmpty()) {
            customerRequestEntities.sort(Collections.reverseOrder(Comparator.comparing(CustomerRequestEntity::getCreatedAt)));
            output.setConsentRequestId(customerRequestEntities.get(0).getId());
        }
        if (Objects.nonNull(input.getConsents()) && !input.getConsents().isEmpty()) {
            output.setConsents(new ArrayList<>(businessPartnerConsentAssembler.map(input.getConsents())));
        }

        return output;
    }

    @Override
    public BusinessPartner mapFull(BusinessPartnerEntity input, BusinessPartner output) {

        if (input == null) // external entities may not be imported during assembling
            return null;

        map(input, output);

        output.setConsents(new ArrayList<>(businessPartnerConsentAssembler.map(input.getConsents())));

        return output;
    }

    public Title map(TitleEntity entity){
        Title title = new Title();
        title.setExternalId(entity.getExternalId());
        title.setName(entity.getName());
        title.setType(entity.getType());
        title.setId(entity.getId());
        return title;
    }

    @Override
    public List<BusinessPartner> map(Collection<BusinessPartnerEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new BusinessPartner())).collect(Collectors.toList());
        }

        return new ArrayList<>();
    }

    @Override
    public List<BusinessPartnerSummary> mapSummaries(Collection<BusinessPartnerEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new BusinessPartnerSummary())).collect(Collectors.toList());
        }

        return new ArrayList<>();
    }

    @Override
    public List<BusinessPartnerUnitedDeliveryPointsSummary> mapUnitedSummaries(Collection<BusinessPartnerEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new BusinessPartnerUnitedDeliveryPointsSummary())).collect(Collectors.toList());
        }

        return new ArrayList<>();
    }

    @Override
    public List<BusinessPartner> mapFull(Collection<BusinessPartnerEntity> input) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> mapFull(i, new BusinessPartner())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public BusinessPartnerUnitedDeliveryPointsSummary map(BusinessPartnerEntity input, BusinessPartnerUnitedDeliveryPointsSummary output) {
        if (input == null) // external entities may not be imported during assembling
            return null;

        output.setId(input.getId());
        output.setExternalId(input.getExternalId());
        output.setFirstName(input.getFirstName());
        output.setLastName(input.getLastName());
        output.setName(input.getName());
        output.setEmail(input.getEmail());
        output.setQueue(input.getQueue());
        output.setConsentBannerInvisibleUntil(input.getConsentBannerInvisibleUntil());

        if (input.getKind() != null) {
            output.setKind(genericCodeListAssembler.mapSummary(CodeListType.BUSINESS_PARTNER_KIND, input.getKind(), new CodeListItem()));
        }

        if (BusinessPartnerQueue.INDIVIDUAL.equals(output.getQueue())) {
            output.setFrancheFixation(input.getFrancheFixation());
        }

        if (input.getAccountManager() != null) {
            output.setAccountManager(accountManagerAssembler.map(input.getAccountManager(), new AccountManager()));
        }

        List<CustomerRequestEntity> customerRequestEntities = fieldGroupUpdateService.getLastCustomerRequestsForEntity(input, Set.of(CustomerRequestCode.X_MS));
        if (Objects.nonNull(customerRequestEntities) && !customerRequestEntities.isEmpty()) {
            customerRequestEntities.sort(Collections.reverseOrder(Comparator.comparing(CustomerRequestEntity::getCreatedAt)));
            output.setConsentRequestId(customerRequestEntities.get(0).getId());
        }
        if (Objects.nonNull(input.getConsents()) && !input.getConsents().isEmpty()) {
            output.setBusinessPartnerConsents(new ArrayList<>(businessPartnerConsentAssembler.map(input.getConsents())));
        }

        return output;
    }

    @Override
    public BusinessPartnerSummary map(BusinessPartnerEntity input, BusinessPartnerSummary output) {
        if (input == null) // external entities may not be imported during assembling
            return null;

        output.setId(input.getId());
        output.setExternalId(input.getExternalId());
        output.setFirstName(input.getFirstName());
        output.setLastName(input.getLastName());
        output.setName(input.getName());
        output.setEmail(input.getEmail());
        output.setQueue(input.getQueue());
        output.setConsentBannerInvisibleUntil(input.getConsentBannerInvisibleUntil());
        output.setCardPaymentEnabled(input.getCardPaymentEnabled() != null ? input.getCardPaymentEnabled() : Boolean.FALSE);

        if (input.getKind() != null) {
            output.setKind(genericCodeListAssembler.mapSummary(CodeListType.BUSINESS_PARTNER_KIND, input.getKind(), new CodeListItem()));
        }

        if (BusinessPartnerQueue.INDIVIDUAL.equals(output.getQueue())) {
            output.setFrancheFixation(input.getFrancheFixation());
        }

        if (input.getAccountManager() != null) {
            output.setAccountManager(accountManagerAssembler.map(input.getAccountManager(), new AccountManager()));
        }

        if (Objects.nonNull(input.getConsents()) && !input.getConsents().isEmpty()) {
            output.setBusinessPartnerConsents(new ArrayList<>(businessPartnerConsentAssembler.map(input.getConsents())));
        }

        return output;
    }

    @Override
    public BusinessPartnerPairing map(BusinessPartnerExtendedOwnershipViewEntity input, BusinessPartnerPairing output, UUID customerAccountUuid, BusinessPartnerQueue bpQueueSecurity) {
        if (input.getBusinessPartner() == null) // external entities may not be imported during assembling
            return null;

        map(input.getBusinessPartner(), output);

        output.setPhone(input.getPhone());

        switch (input.getSource()) {
            case VERIFY:
                output.setShareByUnitedDeliveryPoint(false);
                output.setBusinessPartnerPairType(BusinessPartnerPairType.PAIRING_VERIFICATION);
                break;

            case OWNER:
                output.setShareByUnitedDeliveryPoint(false);
                output.setBusinessPartnerPairType(getPairingType(input, customerAccountUuid));
                break;

            case SHARING:
                output.setShareByUnitedDeliveryPoint(true);
                output.setBusinessPartnerPairType(getPairingType(input, customerAccountUuid));
                break;
        }

        if (bpQueueSecurity != null && bpQueueSecurity != input.getBusinessPartner().getQueue())
            output.setAccess(QueueAccessEnum.NO_QUEUE_ACCESS);

        return output;
    }

    @Override
    public List<BusinessPartnerPairing> map(Collection<BusinessPartnerExtendedOwnershipViewEntity> input, UUID customerAccountUuid, BusinessPartnerQueue bpQueueSecurity) {

        if (input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new BusinessPartnerPairing(), customerAccountUuid, bpQueueSecurity)).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    private BusinessPartnerPairType getPairingType(BusinessPartnerExtendedOwnershipViewEntity businessPartnerOwnershipView, UUID customerAccountUuid) {

        BusinessPartnerPairingRequestEntity lastPairingRequest = businessPartnerOwnershipView.getBusinessPartner().getPairingRequests().stream()
                .filter(r -> r.getCustomerAccount().getId().equals(customerAccountUuid))
                .max(Comparator.comparing(VersionedEntity::getCreatedAt))
                .orElse(null);

        if (lastPairingRequest != null) {
            switch (lastPairingRequest.getStatus()) {
                case DONE:
                    return BusinessPartnerPairType.PAIRED;
                case CREATED:
                case IN_PROGRESS:
                    switch (lastPairingRequest.getAction()) {
                        case PAIRING:
                            return BusinessPartnerPairType.PAIRING_CREATED;
                        case UNPAIRING:
                            return BusinessPartnerPairType.UNPAIRING_CREATED;
                    }
            }
            LOG.warn("Pairing type was not identified [BusinessPartnerPairingRequestEntity={}]", lastPairingRequest);
        }

        if (businessPartnerOwnershipView.getBusinessPartner().getOwnerships().stream() // Was already paired
                .anyMatch(o -> o.getCustomerAccount().getId().equals(customerAccountUuid))) {
            return BusinessPartnerPairType.PAIRED;
        }

        return null;
    }
}

