package sk.spp.nzp.be.assembler.notification.impl;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import sk.spp.nzp.be.api.customeraccess.CustomerAccount;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerSummary;
import sk.spp.nzp.be.api.customerprofile.DeliveryPointSummary;
import sk.spp.nzp.be.api.notification.CustomerNotificationSummary;
import sk.spp.nzp.be.assembler.notification.CustomerNotificationSummaryAssembler;
import sk.spp.nzp.commons.model.notification.CustomerNotificationEntity;

@Component
public class CustomerNotificationSummaryAssemblerImpl implements CustomerNotificationSummaryAssembler {

    @Override
    public CustomerNotificationSummary map(CustomerNotificationEntity input, CustomerNotificationSummary output) {
        output.setUuid(input.getId());
        output.setEmail(input.getEmail());
        output.setCreatedAt(input.getCreatedAt());
        output.setStatus(input.getStatus());
        output.setHeader(input.getHeader());
        output.setHeaderUrl(input.getHeaderUrl());
        output.setEntityType(input.getEntityType());
        output.setEntityId(input.getEntityId());
        output.setReadAt(input.getReadAt());

        if (input.getShareFrom() != null) {
            CustomerAccount customerAccount = new CustomerAccount();
            customerAccount.setId(input.getShareFrom().getId());
            customerAccount.setEmail(input.getShareFrom().getEmail());
            output.setSharedFrom(customerAccount);
        }

        if (input.getBusinessPartner() != null) {
            BusinessPartnerSummary businessPartnerSummary = new BusinessPartnerSummary();
            businessPartnerSummary.setId(input.getBusinessPartner().getId());
            output.setBusinessPartner(businessPartnerSummary);
        }

        if (input.getDeliveryPoint() != null) {
            DeliveryPointSummary deliveryPointSummary = new DeliveryPointSummary();
            deliveryPointSummary.setId(input.getDeliveryPoint().getId());
            output.setDeliveryPoint(deliveryPointSummary);

            if (input.getDeliveryPoint().getUnitedDeliveryPoint() != null) {
                output.setUnitedDeliveryPointUuid(input.getDeliveryPoint().getUnitedDeliveryPoint().getId());
            }
        }

        if (input.getNotificationTemplate() != null) {
            output.setGroup(input.getNotificationTemplate().getGroup());
            output.setExecutionType(input.getNotificationTemplate().getExecutionType());
            output.setTemplateCode(input.getNotificationTemplate().getCode());
            output.setCategory(input.getNotificationTemplate().getCategory());
        }

        return output;
    }

    @Override
    public List<CustomerNotificationSummary> map(Collection<CustomerNotificationEntity> input) {
        if (input != null && !input.isEmpty()) {
            return input.stream().map(i -> map(i, new CustomerNotificationSummary())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}
