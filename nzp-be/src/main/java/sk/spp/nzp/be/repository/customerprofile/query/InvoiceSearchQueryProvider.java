package sk.spp.nzp.be.repository.customerprofile.query;

import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQuery;
import org.springframework.util.CollectionUtils;
import sk.spp.nzp.be.api.common.Sorting;
import sk.spp.nzp.be.api.common.SortingAttribute;
import sk.spp.nzp.be.api.customerprofile.InvoiceSearch;
import sk.spp.nzp.be.repository.common.AbstractQueryProvider;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.api.customerprofile.enums.DeliveryPointType;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceStatus;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceType;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceTypeGroup;
import sk.spp.nzp.commons.api.customersharing.enums.OwnershipType;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.model.customerprofile.InvoiceEntity;
import sk.spp.nzp.commons.model.customerprofile.QInvoiceEntity;
import sk.spp.nzp.commons.model.customersharing.QInvoiceOwnershipEntity;
import sk.spp.nzp.commons.utils.FTSearchUtils;

import javax.persistence.EntityManager;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static sk.spp.nzp.commons.model.customerprofile.QBusinessPartnerEntity.businessPartnerEntity;
import static sk.spp.nzp.commons.model.customerprofile.QInvoiceEntity.invoiceEntity;

public class InvoiceSearchQueryProvider extends AbstractQueryProvider<InvoiceEntity> {

    private static final List<String> nonCommodityTypes = InvoiceType.getNonCommodity().stream()
            .map(Enum::name)
            .collect(Collectors.toList());

    private final InvoiceSearch invoiceSearch;
    private final BusinessPartnerQueue businessPartnerQueue;

    public InvoiceSearchQueryProvider(InvoiceSearch invoiceSearch, BusinessPartnerQueue businessPartnerQueue) {

        this.invoiceSearch = invoiceSearch;
        this.businessPartnerQueue = businessPartnerQueue;
    }

    @Override
    public JPAQuery<InvoiceEntity> getQuery(EntityManager entityManager) {
        validate();
        QInvoiceEntity invoiceEntity = QInvoiceEntity.invoiceEntity;

        JPAQuery<InvoiceEntity> baseQuery = new JPAQuery<InvoiceEntity>(entityManager).from(invoiceEntity);
        BooleanExpression invoiceIssueAtFilter = invoiceEntity.issueAt.after(LocalDate.now()).not();
        BooleanExpression ignoredInvoiceTypesExp = getIgnoredInvoiceTypesExp();

        BooleanExpression invoiceTypeExp = getInvoiceTypeExp();
        BooleanExpression invoiceTypeGroupExp = getInvoiceTypeGroupExp();
        BooleanExpression statusesExp = getStatusExp();
        BooleanExpression unitedDeliveryPointsExp = getUnitedDeliveryPointsExp();
        BooleanExpression productTypeExp = getProductTypeExp();
        BooleanExpression issuedFromExp = getIssueFromExp();
        BooleanExpression issuedToExp = getIssueToExp();
        BooleanExpression businessPartnerExp = getBusinessPartnerExp(baseQuery);
        BooleanExpression businessPartnerQueueExp = getBusinessPartnerQueueExp();
        BooleanExpression contractExp = getContractExp();
        BooleanExpression contractAccountExp = getContractAccountExp(baseQuery);
        BooleanExpression showCancelledExp = getShowCancelledExp();
        BooleanExpression businessPartnerFtExp = getBusinessPartnerFtExp();
        BooleanExpression expiredExp = getExpiredExp();
        BooleanExpression customerUnpaidExp = getCustomerUnpaidExp();
        BooleanExpression ownerExp = getCustomerOwnershipExp();
        //baseQuery = addCustomerOwnership(baseQuery);
        baseQuery = joinBusinessPartner(baseQuery);

        return baseQuery.where(
                invoiceTypeExp,
                statusesExp,
                unitedDeliveryPointsExp,
                productTypeExp,
                issuedFromExp,
                issuedToExp,
                businessPartnerExp,
                businessPartnerQueueExp,
                contractExp,
                contractAccountExp,
                invoiceTypeGroupExp,
                showCancelledExp,
                businessPartnerFtExp,
                invoiceIssueAtFilter,
                ignoredInvoiceTypesExp,
                expiredExp,
                ownerExp,
                customerUnpaidExp,
                QInvoiceEntity.invoiceEntity.status.ne(InvoiceStatus.INVALID)
        ).orderBy(getOrderBy());
    }

    private BooleanExpression getCustomerUnpaidExp(){
        if(Boolean.TRUE.equals(invoiceSearch.getCustomerUnpaid())){
            return invoiceEntity.status.in(InvoiceStatus.UNPAID, InvoiceStatus.PARTIALLY_PAID).and(
                  invoiceEntity.unpaid.isNotNull().and(invoiceEntity.unpaid.gt(BigDecimal.ZERO))
            );
        }
        return null;
    }

    private BooleanExpression getIgnoredInvoiceTypesExp() {
        return invoiceEntity.type.ne("UH").and(invoiceEntity.type.ne("VF"));
    }

    private BooleanExpression getShowCancelledExp() {
        if(!Boolean.TRUE.equals(invoiceSearch.getShowCancelled())){
            return invoiceEntity.status.ne(InvoiceStatus.CANCELLED);
        }
        return null;
    }

    private BooleanExpression getExpiredExp() {
        if(Boolean.TRUE.equals(invoiceSearch.getExpired())){
            return invoiceEntity.dueAt.before(LocalDate.now());
        }
        if(Boolean.FALSE.equals(invoiceSearch.getExpired())){
            return invoiceEntity.dueAt.before(LocalDate.now()).not();
        }
        return null;
    }

    private void validate() {
        timePeriodValidator();
        baseFilterValidator();
    }

    private BooleanExpression getCustomerOwnershipExp(){

        if (invoiceSearch.getCustomerAccountId() == null)
            return null;
        QInvoiceOwnershipEntity invoiceOwnershipEntity = QInvoiceOwnershipEntity.invoiceOwnershipEntity;
        BooleanExpression invoiceExp = invoiceOwnershipEntity.invoice().eq(invoiceEntity);
        BooleanExpression customerExp = invoiceOwnershipEntity.customerAccount().id.eq(UUID.fromString(invoiceSearch.getCustomerAccountId()));
        BooleanExpression typeExp = null;
        if (!Boolean.TRUE.equals(invoiceSearch.getShared())) {
            typeExp = invoiceOwnershipEntity.type.eq(OwnershipType.OWNER);
        }

        return JPAExpressions.selectOne().from(invoiceOwnershipEntity).where(invoiceExp, customerExp, typeExp).exists();
    }
/*
    private JPAQuery<InvoiceEntity> addCustomerOwnership(JPAQuery<InvoiceEntity> query) {
        if (invoiceSearch.getCustomerAccountId() == null)
            return query;
        QInvoiceOwnershipEntity invoiceOwnershipEntity = QInvoiceOwnershipEntity.invoiceOwnershipEntity;
        BooleanExpression invoiceExp = invoiceOwnershipEntity.invoice().eq(invoiceEntity);
        BooleanExpression customerExp = invoiceOwnershipEntity.customerAccount().id.eq(UUID.fromString(invoiceSearch.getCustomerAccountId()));
        BooleanExpression typeExp = null;
        if (!Boolean.TRUE.equals(invoiceSearch.getShared())) {
            typeExp = invoiceOwnershipEntity.type.eq(OwnershipType.OWNER);
        }

        JPAExpressions.selectOne().from(invoiceOwnershipEntity).where(invoiceExp, customerExp, typeExp).exists()
        List<Predicate> invoiceOwnershipOnClause = new ArrayList<>();
        invoiceOwnershipOnClause.add(invoiceOwnershipEntity.invoice().eq(invoiceEntity));
        invoiceOwnershipOnClause.add(invoiceOwnershipEntity.customerAccount().id.eq(UUID.fromString(invoiceSearch.getCustomerAccountId())));

        if (!Boolean.TRUE.equals(invoiceSearch.getShared())) {
            invoiceOwnershipOnClause.add(invoiceOwnershipEntity.type.eq(OwnershipType.OWNER));
        }

        return query.join(invoiceOwnershipEntity).on(invoiceOwnershipOnClause.toArray(new Predicate[0]));

    }*/

    private JPAQuery<InvoiceEntity> joinBusinessPartner(JPAQuery<InvoiceEntity> query) {

        List<Sorting> sortDefinition = sk.spp.nzp.commons.utils.Expressions.tryGet(() -> invoiceSearch.getPaging().getSort());

        if (sortDefinition == null || sortDefinition.stream().noneMatch(x -> SortingAttribute.INVOICE_BP_NAME.name().equals(x.getAttribute()) || SortingAttribute.INVOICE_BP_NAME.getCamelCaseName().equals(x.getAttribute()))) {
            return query.select(invoiceEntity);
        } else {
            query.select(invoiceEntity);
        }

        List<Predicate> businessPartnerOnClause = new ArrayList<>();
        businessPartnerOnClause.add(businessPartnerEntity.id.eq(invoiceEntity.contractAccount().businessPartnerId));

        return query.join(businessPartnerEntity).on(businessPartnerOnClause.toArray(new Predicate[0]));

    }

    private StringExpression getBpNameStringExpression() {
        return Expressions.asString(Expressions.asString(businessPartnerEntity.name).coalesce(""))
                .concat(Expressions.asString(businessPartnerEntity.firstName).coalesce(""))
                .concat(Expressions.asString(businessPartnerEntity.lastName).coalesce(""));
    }


    private BooleanExpression getBusinessPartnerExp(JPAQuery<InvoiceEntity> query) {
        if (invoiceSearch.getBusinessPartnerId() != null) {
            return invoiceEntity.contractAccount().businessPartnerId.eq(invoiceSearch.getBusinessPartnerId());
        }
        return null;
    }

    private BooleanExpression getContractAccountExp(JPAQuery<InvoiceEntity> query) {
        if (invoiceSearch.getContractAccountId() != null) {
            return invoiceEntity.contractAccountId.eq(invoiceSearch.getContractAccountId());
        }
        return null;
    }

    private BooleanExpression getContractExp() {
        if (invoiceSearch.getContractIds() != null) {
            return invoiceEntity.invoiceContractList.any().contract().id.in(invoiceSearch.getContractIds());
        }
        return null;
    }

    private BooleanExpression getBusinessPartnerQueueExp() {
        if (businessPartnerQueue != null) {
            return invoiceEntity.contractAccount().businessPartner().queue.eq(businessPartnerQueue);
        }
        return null;
    }

    private BooleanExpression getBusinessPartnerFtExp() {
        if (invoiceSearch.getBusinessPartnerFt() != null) {
            return FTSearchUtils.fulltextSearch(invoiceSearch.getBusinessPartnerFt(), invoiceEntity.contractAccount().businessPartner().nameSearchable);
        }
        return null;
    }

    private BooleanExpression getInvoiceTypeExp() {
        if (invoiceSearch.getInvoiceType() != null) {
            return invoiceEntity.type.eq(invoiceSearch.getInvoiceType().getCode());
        }
        return null;
    }
    private BooleanExpression getInvoiceTypeGroupExp() {
        if (invoiceSearch.getInvoiceGroup() != null) {

            if(invoiceSearch.getInvoiceGroup().equals(InvoiceTypeGroup.INVOICE)){
                return invoiceEntity.typeGroup.in(InvoiceTypeGroup.INVOICE, InvoiceTypeGroup.CREDIT);
            }

            return invoiceEntity.typeGroup.eq(invoiceSearch.getInvoiceGroup());
        }
        return null;
    }

    private BooleanExpression getStatusExp() {
        if (invoiceSearch.getStatuses() != null && !invoiceSearch.getStatuses().isEmpty()) {
            return QInvoiceEntity.invoiceEntity.status.in(invoiceSearch.getStatuses());
        }
        return null;
    }

    private BooleanExpression getUnitedDeliveryPointsExp() {
        if (invoiceSearch.getUnitedDeliveryPointIds() != null && !invoiceSearch.getUnitedDeliveryPointIds().isEmpty()) {
            return QInvoiceEntity.invoiceEntity.invoiceContractList.any().contract().unitedDeliveryPoint().id.in(invoiceSearch.getUnitedDeliveryPointIds().stream().map(UUID::fromString).collect(Collectors.toSet()));
        }
        return null;
    }

    private BooleanExpression getProductTypeExp() {
        if (invoiceSearch.getProductType() != null) {
            switch (invoiceSearch.getProductType()) {
                case ZP:
                    return invoiceEntity.invoiceContractList.any().contract().deliveryPoint().type.eq(DeliveryPointType.ZP);
                case EE:
                    return invoiceEntity.invoiceContractList.any().contract().deliveryPoint().type.eq(DeliveryPointType.EE);
                case N:
                    return invoiceEntity.type.in(nonCommodityTypes);
            }
        }
        return null;
    }

    private BooleanExpression getIssueFromExp() {
        if (invoiceSearch.getIssueAt() != null && invoiceSearch.getIssueAt().getFrom() != null) {
            return QInvoiceEntity.invoiceEntity.issueAt.goe(invoiceSearch.getIssueAt().getFrom().toLocalDate());
        }
        return null;
    }

    private BooleanExpression getIssueToExp() {
        if (invoiceSearch.getIssueAt() != null && invoiceSearch.getIssueAt().getTo() != null) {
            return QInvoiceEntity.invoiceEntity.issueAt.loe(invoiceSearch.getIssueAt().getTo().toLocalDate());
        }
        return null;
    }


    private void timePeriodValidator() {
        if (invoiceSearch.getIssueAt() != null) {
            if (invoiceSearch.getIssueAt().getFrom() != null && invoiceSearch.getIssueAt().getTo() != null) {
                // date From must not be after date To
                if (invoiceSearch.getIssueAt().getFrom().isAfter(invoiceSearch.getIssueAt().getTo())) {
                    throw new ApiException(ErrorCode.INVOICE_ISSUE_DATE_PERIOD_ERROR, "Date From must not be after the date To.");
                }
            }
        }
    }

    private void baseFilterValidator() {
        if (
                invoiceSearch.getBusinessPartnerId() == null &&
                        invoiceSearch.getContractAccountId() == null &&
                        CollectionUtils.isEmpty(invoiceSearch.getContractIds()) &&
                        invoiceSearch.getCustomerAccountId() == null &&
                        CollectionUtils.isEmpty(invoiceSearch.getUnitedDeliveryPointIds())
        )
            throw new ApiException(ErrorCode.GENERIC_VALIDATION_ERROR);
    }
    
    private OrderSpecifier<?>[] getOrderBy() {
        
        if(invoiceSearch.getPaging() == null || invoiceSearch.getPaging().getSort() == null || invoiceSearch.getPaging().getSort().isEmpty()) {

            return getDefaultOrderBy();
        }

        List<OrderSpecifier<?>> sortingList = new ArrayList<>();
        
        for(Sorting sorting: invoiceSearch.getPaging().getSort()) {
            
            if(SortingAttribute.INVOICE_ISSUE_AT.name().contentEquals(sorting.getAttribute()) || SortingAttribute.INVOICE_ISSUE_AT.getCamelCaseName().contentEquals(sorting.getAttribute())) {
                
                sortingList.add(getOrderSpecifier(sorting, invoiceEntity.issueAt));
                
            } else if(SortingAttribute.INVOICE_DUE_AT.name().contentEquals(sorting.getAttribute()) || SortingAttribute.INVOICE_DUE_AT.getCamelCaseName().contentEquals(sorting.getAttribute())) {
                
                sortingList.add(getOrderSpecifier(sorting, invoiceEntity.dueAt));
                
            } else if(SortingAttribute.INVOICE_EXTERNAL_ID.name().contentEquals(sorting.getAttribute()) || SortingAttribute.INVOICE_EXTERNAL_ID.getCamelCaseName().contentEquals(sorting.getAttribute())) {
                
                sortingList.add(getOrderSpecifier(sorting, invoiceEntity.externalId));
                
            } else if(SortingAttribute.INVOICE_STATUS.name().contentEquals(sorting.getAttribute()) || SortingAttribute.INVOICE_STATUS.getCamelCaseName().contentEquals(sorting.getAttribute())) {
                
                sortingList.add(getOrderSpecifier(sorting, invoiceEntity.status));
                
            } else if(SortingAttribute.INVOICE_AMOUNT.name().contentEquals(sorting.getAttribute()) || SortingAttribute.INVOICE_AMOUNT.getCamelCaseName().contentEquals(sorting.getAttribute())) {
                
                sortingList.add(getOrderSpecifier(sorting, invoiceEntity.amount));

            } else if(SortingAttribute.INVOICE_BP_NAME.name().contentEquals(sorting.getAttribute()) || SortingAttribute.INVOICE_BP_NAME.getCamelCaseName().contentEquals(sorting.getAttribute())) {

                sortingList.add(getOrderSpecifier(sorting, getBpNameStringExpression()));

            } else {
                
                throw new ApiException(ErrorCode.GENERIC_VALIDATION_ERROR);
            }
        }
        
        OrderSpecifier<?>[] output = new OrderSpecifier<?>[sortingList.size()];
        
        return sortingList.toArray(output);
    }
    
    private OrderSpecifier<?>[] getDefaultOrderBy() {
        
        OrderSpecifier<?>[] output = new OrderSpecifier<?>[1];
        output[0] = invoiceEntity.issueAt.desc();
        
        return output;
    }
}
