package sk.spp.nzp.be.reporting.entitymappers.columns;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public enum CustomerAccountColumns {

    EMAIL,
    TYPE,
    STATUS,
    ACTIVATION_AT,
    REGISTRATION_AT,
    LOCK_UNTIL,
    LOGIN_SUCCESS_AT,
    LOGIN_UNSUCCESS_AT,
    PASSWORD_UPDATED_AT,
    DEACTIVATION_REASON,
    LOCALE,
    PHONE,
    FIRST_NAME,
    LAST_NAME,
    CREATED_AT,

    BP_QUEUE,
    BP_COMPANY_REGISTRATION_NUMBER,
    BP_TAX_ID_NUMBER,
    BP_VAT_REGISTRATION_NUMBER,
    BP_NAME,
    BP_FIRST_NAME,
    BP_LAST_NAME,
    BP_EMAIL,
    BP_PHONE,
    BP_SYNCHRONIZATION_AT,
    BP_PRIMARY_STREET,
    BP_PRIMARY_STREET_NUMBER,
    BP_PRIMARY_CITY,
    BP_PRIMARY_ZIP_CODE,
    BP_PRIMARY_COUNTRY,
    BP_ACCOUNT_MANAGER,

    BP_CONSENT,
    BP_CONSENT_COMMUNICATION_CHANNEL;

    public static final String GENERIC_CODE_LIST_TYPE = "CUSTOMER_ACCOUNT_EXPORT_COLUMN";

    private static Map<String, CustomerAccountColumns> enumMap;

    static {
        enumMap = Arrays.stream(CustomerAccountColumns.values())
                .collect(Collectors.toMap(CustomerAccountColumns::name, Function.identity()));
    }

    public static CustomerAccountColumns valueOfOrNull(String stringValue) {
        return enumMap.getOrDefault(stringValue, null);
    }
}
