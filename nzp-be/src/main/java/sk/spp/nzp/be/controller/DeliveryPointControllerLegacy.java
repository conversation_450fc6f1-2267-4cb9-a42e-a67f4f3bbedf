package sk.spp.nzp.be.controller;

//
//@RestController
//@RequestMapping("/delivery-points")
//@Deprecated
public class DeliveryPointControllerLegacy {
//
//    @Value("${reporting.export.filename}")
//    private String fileName;
//
//    private DeliveryPointService deliveryPointService;
//    private InvoiceService invoiceService;
//    private MeterReadingService meterReadingService;
//    private CustomerRequestService customerRequestService;
//    private ConsumptionService consumptionService;
//
//    @Autowired
//    private DeliveryPointToContractProvider deliveryPointToContractProvider;
//
//    private ContractAccountService contractAccountService;
//
//    @Autowired
//    public DeliveryPointControllerLegacy(
//            DeliveryPointService deliveryPointService,
//            InvoiceService invoiceService,
//            MeterReadingService meterReadingService,
//            CustomerRequestService customerRequestService,
//            ConsumptionService consumptionService,
//            ContractAccountService contractAccountService
//    ) {
//        this.deliveryPointService = deliveryPointService;
//        this.invoiceService = invoiceService;
//        this.meterReadingService = meterReadingService;
//        this.customerRequestService = customerRequestService;
//        this.consumptionService = consumptionService;
//        this.contractAccountService = contractAccountService;
//    }
//
//    @Component
//    public class DeliveryPointToContractProvider {
//
//        @Autowired
//        private HistoryContextProvider historyContextProvider;
//
//        @Autowired
//        private DeliveryPointEntityRepository deliveryPointEntityRepository;
//
//        @Transactional
//        public String getContractId(String deliveryPointId, String businessPartnerId) {
//            return historyContextProvider.createDeliveryPointHistoryContext(
//                    deliveryPointEntityRepository.findById(deliveryPointId).orElseThrow(NotFoundException::new),
//                    businessPartnerId).getContract().getId();
//        }
//    }

//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
//    @GetMapping(value = "/{deliveryPointId}", produces = {MediaType.APPLICATION_JSON_VALUE})
//    public DeliveryPoint getByUuidLegacy(@LogParam("deliveryPointId") @PathVariable(value = "deliveryPointId") String deliveryPointId,
//                                   @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId) {
//
//        return deliveryPointService.getByContractId(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId));
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))")
//    @GetMapping(value = "/{deliveryPointId}/invoices", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    @PagingAsQueryParams
//    public PagedResponse<InvoiceSummary> getInvoicesByDpLegacy(@LogParam("deliveryPointId") @PathVariable(value = "deliveryPointId") String deliveryPointId,
//                                                     @LogParam("queryStringPaging ")  QueryStringPaging  queryStringPaging,
//                                                     @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId) {
//
//        return invoiceService.getInvoices(
//                new InvoiceSearch()
//                        .setPaging(queryStringPaging.toPaging())
//                        .withContractId(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId))
//        );
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))")
//    @GetMapping(value = "/{deliveryPointId}/invoices/summary", produces = {MediaType.APPLICATION_JSON_VALUE})
//    public InvoicePaymentSummary getInvoicesSummaryLegacy(@LogParam("deliveryPointId") @PathVariable(value = "deliveryPointId") String deliveryPointId,
//                                                    @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId) {
//
//        return invoiceService.getInvoiceSummaryByContractId(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId),false);
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))")
//    @PostMapping(value = "/{deliveryPointId}/invoices/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public PagedResponse<InvoiceSummary> invoiceSearchByContractLegacy(@LogParam("deliveryPointId") @PathVariable(value = "deliveryPointId") String deliveryPointId,
//                                                       @LogParam("invoiceSearch ") @RequestBody InvoiceSearch invoiceSearch,
//                                                       @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId) {
//
//        return invoiceService.getInvoices(invoiceSearch.withContractId(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId)));
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
//    @GetMapping(value = "/{deliveryPointId}/meter-readings", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public PagedResponse<MeterReading> getMeterReadingsLegacy(@LogParam("deliveryPointId") @PathVariable(value = "deliveryPointId") String deliveryPointId,
//                                                        @LogParam("queryStringPaging ") QueryStringPaging  queryStringPaging,
//                                                        @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId) {
//
//        return meterReadingService.getByContractId(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId), new MeterReadingSearch().setPaging(queryStringPaging.toPaging()));
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
//    @PostMapping(value = "/{deliveryPointId}/meter-readings/search", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public PagedResponse<MeterReading> meterReadingSearchLegacy(@LogParam("deliveryPointId") @PathVariable(value = "deliveryPointId") String deliveryPointId,
//                                                          @LogParam("meterReadingSearch ") @RequestBody MeterReadingSearch meterReadingSearch,
//                                                          @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId) {
//
//        return meterReadingService.getByContractId(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId), meterReadingSearch);
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
//    @PostMapping(value = "/{deliveryPointId}/meter-readings/search/download-export", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public void meterReadingSearchExportLegacy(@LogParam("deliveryPointId") @PathVariable(value = "deliveryPointId") String deliveryPointId,
//                                         @LogParam("meterReadingSearch ") @RequestBody MeterReadingSearch meterReadingSearch,
//                                         @LogParam("format") @RequestParam("format") DataReportFormat format,
//                                         @LogParam("columns") @RequestParam(value = "columns", required = false) List<String> columns,
//                                         @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId,
//                                         HttpServletResponse response) throws IOException {
//
//        ContentDisposition contentDisposition;
//        if (DataReportFormat.CSV.equals(format)) {
//            contentDisposition = ContentDisposition.builder("attachment")
//                    .filename(fileName + ".csv")
//                    .build();
//
//            response.setContentType("text/csv");
//            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
//        } else {
//            contentDisposition = ContentDisposition.builder("attachment")
//                    .filename(fileName + ".xlsx")
//                    .build();
//
//            response.setContentType("application/vnd.ms-excels");
//            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
//        }
//
//        meterReadingService.getByContractIdExport(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId), meterReadingSearch, format, columns, response.getOutputStream());
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
//    @PostMapping(value = "/{deliveryPointId}/meter-readings/info", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public ResponseEntity<List<MeterReading>> createMeterReadingInfoLegacy(@LogParam("deliveryPointId") @PathVariable(value = "deliveryPointId") String deliveryPointId,
//                                                                     @LogParam("meterReadingInfoRequest ") @RequestBody MeterReadingInfoRequest meterReadingInfoRequest,
//                                                                     @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId) {
//
//        List<MeterReading> output = meterReadingService.createMeterReadingInfo(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId), meterReadingInfoRequest);
//        return ResponseEntity.created(null).body(output);
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
//    @PostMapping(value = "/{deliveryPointId}/meter-readings/customer", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
//    public ResponseEntity<CustomerRequestSummary> createMeterReadingCustomerLegacy(@LogParam("deliveryPointId") @PathVariable(value = "deliveryPointId") String deliveryPointId,
//                                                                             @LogParam("meterReadingInfoRequest ") @RequestBody MeterReadingInfoRequest meterReadingInfoRequest,
//                                                                             @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId) {
//
//        CustomerRequestSummary output = meterReadingService.createMeterReadingCustomer(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId), meterReadingInfoRequest);
//
//        return ResponseEntity.created(null).body(output);
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))")
//    @PagingAsQueryParams
//    @GetMapping("/{deliveryPointId}/customer-requests")
//    public PagedResponse<CustomerRequestSummary> getCustomerRequestsByDpLegacy(
//            @LogParam("deliveryPointId") @PathVariable(name = "deliveryPointId") String deliveryPointId,
//            @LogParam("paging")  QueryStringPaging paging,
//            @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId) {
//        CustomerRequestSearchQuery query = new CustomerRequestSearchQuery();
//        query.setPaging(paging.toPaging());
//        query.setContractId(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId));
//
//        return customerRequestService.fetchCustomerRequests(query);
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_REQUESTS_VIEW')))")
//    @PostMapping("/{deliveryPointId}/customer-requests/search")
//    public PagedResponse<CustomerRequestSummary> deliveryPointSearchCustomerRequestsLegacy(
//            @LogParam("deliveryPointId") @PathVariable(name = "deliveryPointId") String deliveryPointId,
//            @LogParam("query") @RequestBody CustomerRequestSearchQuery query,
//            @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId) {
//        query.setContractId(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId));
//
//        return customerRequestService.fetchCustomerRequests(query);
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
//    @PostMapping("/{deliveryPointId}/consumptions/search")
//    public ConsumptionSearchResponse searchConsumptionsLegacy(
//            @LogParam("deliveryPointId") @PathVariable(name = "deliveryPointId") String deliveryPointId,
//            @LogParam("consumptionSearch") @RequestBody ConsumptionSearch consumptionSearch,
//            @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId
//    ) {
//        return consumptionService.getByContractId(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId), consumptionSearch);
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
//    @PostMapping("/{deliveryPointId}/customer-requests/tariff-rate")
//    public CustomerRequestSummary changeTariffLegacy(
//            @LogParam("deliveryPointId") @PathVariable(name = "deliveryPointId") String deliveryPointId,
//            @LogParam("tariffChange") @RequestBody TariffChange tariffChange,
//            @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId
//    ) {
//        return deliveryPointService.changeTariff(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId), tariffChange);
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
//    @PostMapping("/{deliveryPointId}/customer-requests/advance-payment")
//    public CustomerRequestSummary changeAdvancePaymentLegacy(
//            @LogParam("deliveryPointId") @PathVariable(name = "deliveryPointId") String deliveryPointId,
//            @LogParam("tariffChange") @Validated(CustomerRequestChecks.class) @RequestBody AdvancePaymentAndPeriod advancePayment,
//            @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId
//    ) {
//        return deliveryPointService.changeAdvancePayment(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId), advancePayment);
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
//    @PostMapping("/{deliveryPointId}/customer-requests/payment-method")
//    public CustomerRequestSummary changePaymentMethodLegacy(
//            @LogParam("deliveryPointId") @PathVariable(name = "deliveryPointId") String deliveryPointId,
//            @LogParam("paymentMethod") @Validated(CustomerRequestChecks.class) @RequestBody PaymentMethod paymentMethod,
//            @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId
//    ) {
//        return deliveryPointService.changePaymentMethod(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId), paymentMethod);
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
//    @PostMapping("/{deliveryPointId}/customer-requests/sipo-number")
//    public CustomerRequestSummary changeSipoNumberLegacy(
//            @LogParam("deliveryPointId") @PathVariable(name = "deliveryPointId") String deliveryPointId,
//            @LogParam("sipo") @Validated(CustomerRequestChecks.class) @RequestBody SipoNumberChange sipo,
//            @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId
//    ) {
//        return deliveryPointService.changeSipoNumber(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId), sipo);
//    }
//
//    @Log
//    @LogParam
//    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_DELIVERY_POINTS_VIEW')))")
//    @PostMapping(value = "/{deliveryPointId}/e-invoices/email")
//    public List<CustomerRequestSummary> updateEInvoiceEmailByDpLegacy(@LogParam("deliveryPointId") @PathVariable(value = "deliveryPointId") String deliveryPointId,
//                                                            @LogParam("emailRequest") @Valid @RequestBody EInvoiceRequest eInvoiceRequest,
//                                                            @LogParam("businessPartnerId") @RequestParam(value = "bpId", required = false) String businessPartnerId) {
//
//        return contractAccountService.updateEInvoiceEmailByContractId(deliveryPointToContractProvider.getContractId(deliveryPointId, businessPartnerId), eInvoiceRequest.getEmail());
//    }
}
