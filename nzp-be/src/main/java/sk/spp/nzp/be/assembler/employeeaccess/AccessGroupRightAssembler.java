package sk.spp.nzp.be.assembler.employeeaccess;

import java.util.Collection;
import java.util.List;

import sk.spp.nzp.commons.api.employeeaccess.AccessRight;
import sk.spp.nzp.commons.model.employeeaccess.AccessGroupRightEntity;

public interface AccessGroupRightAssembler {

    AccessGroupRightEntity map(AccessRight input, AccessGroupRightEntity output);

    AccessRight map(AccessGroupRightEntity input, AccessRight output);

    List<AccessRight> map(Collection<AccessGroupRightEntity> input);

}
