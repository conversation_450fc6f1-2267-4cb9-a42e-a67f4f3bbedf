package sk.spp.nzp.be.repository.employeeprofile.query;

import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import org.apache.commons.lang3.StringUtils;
import sk.spp.nzp.be.api.common.Sorting;
import sk.spp.nzp.be.api.common.SortingAttribute;
import sk.spp.nzp.be.api.employeeprofile.NotificationTemplateSearch;
import sk.spp.nzp.be.repository.common.AbstractQueryProvider;
import sk.spp.nzp.commons.model.notification.NotificationTemplateEntity;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.List;

import static sk.spp.nzp.commons.model.notification.QNotificationTemplateEntity.notificationTemplateEntity;

public class NotificationTemplateSearchProvider extends AbstractQueryProvider<NotificationTemplateEntity> {

    NotificationTemplateSearch notificationTemplateSearch;

    public NotificationTemplateSearchProvider(NotificationTemplateSearch notificationTemplateSearch) {

        this.notificationTemplateSearch = notificationTemplateSearch;
    }


    @Override
    public JPAQuery<NotificationTemplateEntity> getQuery(EntityManager em) {

        JPAQuery<NotificationTemplateEntity> query = new JPAQuery<NotificationTemplateEntity>(em).from(notificationTemplateEntity);


        BooleanExpression nameExp = null;

        if (!StringUtils.isBlank(notificationTemplateSearch.getName())) {
            nameExp = notificationTemplateEntity.name.containsIgnoreCase(notificationTemplateSearch.getName());
        }


        BooleanExpression statusesExp = null;

        if (notificationTemplateSearch.getStatuses() != null) {
            statusesExp = notificationTemplateEntity.status.in(notificationTemplateSearch.getStatuses());
        }

        BooleanExpression groupsExp = null;

        if (notificationTemplateSearch.getGroups() != null) {
            groupsExp = notificationTemplateEntity.group.in(notificationTemplateSearch.getGroups());
        }
        BooleanExpression typesExp = null;

        if (notificationTemplateSearch.getTypes() != null) {
            typesExp = notificationTemplateEntity.type.in(notificationTemplateSearch.getTypes());
        }
        BooleanExpression prioritiesExp = null;

        if (notificationTemplateSearch.getPriorities() != null) {
            prioritiesExp = notificationTemplateEntity.priority.in(notificationTemplateSearch.getPriorities());
        }

        BooleanExpression executionTypeExp = null;

        if (notificationTemplateSearch.getDataSet() != null) {
            executionTypeExp = notificationTemplateEntity.executionType.in(notificationTemplateSearch.getDataSet());
        }

        BooleanExpression deletedExp = null;
        if (notificationTemplateSearch.getDeleted() == null) {
            deletedExp = notificationTemplateEntity.deleted.eq(Boolean.FALSE);
        } else {
            deletedExp = notificationTemplateEntity.deleted.eq(notificationTemplateSearch.getDeleted());
        }
        
        query = query.where(
                nameExp,
                statusesExp,
                groupsExp,
                typesExp,
                executionTypeExp,
                prioritiesExp,
                deletedExp
        ).orderBy(getOrderBy());

        return query;
    }


    private OrderSpecifier<?>[] getOrderBy() {

        if (notificationTemplateSearch.getPaging() == null || notificationTemplateSearch.getPaging().getSort() == null || notificationTemplateSearch.getPaging().getSort().isEmpty()) {

            return getDefaultOrderBy();
        }

        List<OrderSpecifier<?>> sortingList = new ArrayList<>();

        for (Sorting sorting : notificationTemplateSearch.getPaging().getSort()) {

            if (SortingAttribute.NOTIFICATION_TEMPLATE_NAME.name().contentEquals(sorting.getAttribute()) || SortingAttribute.NOTIFICATION_TEMPLATE_NAME.getCamelCaseName().contentEquals(sorting.getAttribute())) {

                sortingList.add(getOrderSpecifier(sorting, notificationTemplateEntity.name));

            } else if (SortingAttribute.NOTIFICATION_TEMPLATE_STATUS.name().contentEquals(sorting.getAttribute()) || SortingAttribute.NOTIFICATION_TEMPLATE_NAME.getCamelCaseName().contentEquals(sorting.getAttribute())) {

                sortingList.add(getOrderSpecifier(sorting, notificationTemplateEntity.status));

            } else if (SortingAttribute.NOTIFICATION_TEMPLATE_TYPE.name().contentEquals(sorting.getAttribute()) || SortingAttribute.NOTIFICATION_TEMPLATE_NAME.getCamelCaseName().contentEquals(sorting.getAttribute())) {

                sortingList.add(getOrderSpecifier(sorting, notificationTemplateEntity.type));

            }
        }

        if (sortingList.isEmpty()) {

            return getDefaultOrderBy();
        }

        OrderSpecifier<?>[] output = new OrderSpecifier<?>[sortingList.size()];

        return sortingList.toArray(output);
    }

    private OrderSpecifier<?>[] getDefaultOrderBy() {

        OrderSpecifier<?>[] output = new OrderSpecifier<?>[1];
        output[0] = notificationTemplateEntity.name.asc();

        return output;
    }

}