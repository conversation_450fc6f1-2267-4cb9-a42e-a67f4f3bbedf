package sk.spp.nzp.be.repository.customerrequest.query;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestSearchQuery;
import sk.spp.nzp.be.api.customerrequest.CustomerRequestSyncedResolver;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.api.customerrequest.enums.CustomerRequestStatus;
import sk.spp.nzp.commons.model.customerprofile.QBusinessPartnerEntity;
import sk.spp.nzp.commons.model.customerprofile.QContractEntity;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestEntity;
import sk.spp.nzp.commons.model.customerrequest.QCustomerRequestContractEntity;
import sk.spp.nzp.commons.model.customerrequest.QCustomerRequestEntity;
import sk.spp.nzp.commons.repository.common.QueryDslProvider;

import javax.persistence.EntityManager;
import java.time.LocalTime;

public class CustomerRequestFindAllQueryProvider implements QueryDslProvider<CustomerRequestEntity> {

    private final boolean appendNotSynced;
    private final LocalTime syncTime;

    private final CustomerRequestSearchQuery searchQuery;
    private final BusinessPartnerQueue businessPartnerQueue;

    private final QCustomerRequestEntity customerRequestEntity = QCustomerRequestEntity.customerRequestEntity;
    private final QContractEntity contractEntity = QContractEntity.contractEntity;
    private final QBusinessPartnerEntity businessPartnerEntity = QBusinessPartnerEntity.businessPartnerEntity;
    private final QCustomerRequestContractEntity customerRequestContractEntity = QCustomerRequestContractEntity.customerRequestContractEntity;

    public CustomerRequestFindAllQueryProvider(CustomerRequestSearchQuery searchQuery, BusinessPartnerQueue businessPartnerQueue) {
        this(searchQuery, businessPartnerQueue, false, null);
    }

    // SNVI-945 Add not synced customer requests if asked
    public CustomerRequestFindAllQueryProvider(CustomerRequestSearchQuery searchQuery, BusinessPartnerQueue businessPartnerQueue,
                                               boolean appendNotSynced, LocalTime syncTime) {
        this.searchQuery = searchQuery;
        this.businessPartnerQueue = businessPartnerQueue;
        this.appendNotSynced = appendNotSynced;
        this.syncTime = syncTime;
    }

    @Override
    public JPAQuery<CustomerRequestEntity> getQuery(EntityManager em) {

        JPAQuery<CustomerRequestEntity> query = new JPAQuery<CustomerRequestEntity>(em).from(customerRequestEntity);

        // join with customer request contracts
        query.leftJoin(customerRequestEntity.customerRequestContracts, customerRequestContractEntity);

        // add contract join only if needed
        if (searchQuery.hasUnitedDeliveryPointUuids() || searchQuery.hasProduct() || searchQuery.hasDeliveryPointId() || searchQuery.hasContractId()) {
            BooleanExpression contractJoinExp =
                    contractEntity.id.eq(customerRequestContractEntity.contract().id).or(
                            customerRequestContractEntity.contract().isNull().and(contractEntity.contractAccountId.eq(customerRequestContractEntity.contractAccount().id))
                    );
            query.leftJoin(contractEntity).on(contractJoinExp);
        }

        // add business partner join only if needed
        if (searchQuery.hasBusinessPartnerId() || businessPartnerQueue != null) {
            query.leftJoin(businessPartnerEntity).on(customerRequestEntity.businessPartner().id.eq(businessPartnerEntity.id));
        }

        query.where(
                getUnitedDeliveryPointFilter(),
                getExternalIdFilter(),
                getCustomerFilter(),
                getDeliveryPointFilter(),
                getBusinessPartnerFilter(),
                getContractAccountFilter(),
                getContractFilter(),
                getCreatedFromFilter(),
                getCreatedToFilter(),
                getCodesFilter(),
                getStatusesFilter(),
                getProductCondition(),
                getBusinessPartnerQueueFilter()
        );

        query.orderBy(customerRequestEntity.createdAt.desc());

        return query.distinct();
    }

    private BooleanExpression getStatusesFilter() {
        BooleanExpression statusFilter = searchQuery.hasStatuses() ? customerRequestEntity.status.in(searchQuery.getStatuses()) : null;
        BooleanExpression filterOutPreCreated = customerRequestEntity.status.ne(CustomerRequestStatus.PRE_CREATED);

        if (searchQuery.hasStatuses() && searchQuery.getStatuses().contains(CustomerRequestStatus.PRE_CREATED)) {
            return Expressions.anyOf(statusFilter, getNotSyncedFilter());
        } else {
            return Expressions.anyOf(filterOutPreCreated.and(statusFilter), getNotSyncedFilter());
        }
    }


    private BooleanExpression getNotSyncedFilter() {
        if(this.appendNotSynced) {
            return CustomerRequestSyncedResolver.getNotSyncedBooleanExpression(syncTime);
        } else {
            return null;
        }
    }

    private BooleanExpression getCodesFilter() {
        return searchQuery.hasCodes() ? customerRequestEntity.customerRequestTemplate().code.in(searchQuery.getCodes()) : null;
    }

    private BooleanExpression getCreatedToFilter() {
        return searchQuery.hasCreatedAt() && searchQuery.getCreatedAt().getTo() != null ?
                customerRequestEntity.createdAt.loe(searchQuery.getCreatedAt().getTo()) : null;
    }

    private BooleanExpression getCreatedFromFilter() {
        return searchQuery.hasCreatedAt() && searchQuery.getCreatedAt().getFrom() != null ?
                customerRequestEntity.createdAt.goe(searchQuery.getCreatedAt().getFrom()) : null;
    }

    private BooleanExpression getBusinessPartnerFilter() {
        return searchQuery.hasBusinessPartnerId() ?
                businessPartnerEntity.id.eq(searchQuery.getBusinessPartnerId()) : null;
    }

    private BooleanExpression getContractAccountFilter() {
        return searchQuery.hasContractAccountId() ?
                customerRequestContractEntity.contractAccount().id.eq(searchQuery.getContractAccountId())
                : null;
    }

    private BooleanExpression getContractFilter() {
        return searchQuery.hasContractId() ?
                contractEntity.id.eq(searchQuery.getContractId())
                : null;
    }

    private BooleanExpression getCustomerFilter() {
        return searchQuery.hasCustomerUuid() ? (customerRequestEntity.customer().id.eq(searchQuery.getCustomerUuid())
                .or(customerRequestEntity.completionCustomer().id.eq(searchQuery.getCustomerUuid()))) : null;
    }

    private BooleanExpression getBusinessPartnerQueueFilter() {
        return businessPartnerQueue != null ? (customerRequestEntity.businessPartner().isNull().or(businessPartnerEntity.queue.eq(businessPartnerQueue))) : null;
    }

    private BooleanExpression getUnitedDeliveryPointFilter() {
        return searchQuery.hasUnitedDeliveryPointUuids() ? contractEntity.unitedDeliveryPoint().id.in(searchQuery.getUnitedDeliveryPointUuids()) : null;
    }

    private BooleanExpression getExternalIdFilter() {
        return searchQuery.hasExternalIds() ? customerRequestEntity.externalId.in(searchQuery.getExternalIds()) : null;
    }

    private BooleanExpression getDeliveryPointFilter() {
        if (searchQuery.getDeliveryPointId() == null) {
            return null;
        }

        BooleanExpression dpPresent = customerRequestContractEntity.deliveryPoint().id.eq(searchQuery.getDeliveryPointId());
        BooleanExpression dpByCa = contractEntity.deliveryPointId.eq(searchQuery.getDeliveryPointId());
        BooleanExpression dpNotPresent = customerRequestContractEntity.deliveryPoint().isNull().and(dpByCa);

        return dpPresent.or(dpNotPresent);
    }

    private BooleanExpression getProductCondition() {
        return searchQuery.hasProduct() ? contractEntity.deliveryPoint().type.eq(searchQuery.getProduct()) : null;
    }

}
