package sk.spp.nzp.be.controller;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.customerprofile.Invoice;
import sk.spp.nzp.be.api.customerprofile.InvoicePaymentInfo;
import sk.spp.nzp.be.api.customerprofile.InvoicePaymentInfoRequest;
import sk.spp.nzp.be.api.ibmcm.IBMCMDocument;
import sk.spp.nzp.be.service.customerprofile.InvoiceService;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/invoices")
public class InvoiceController {

    private final InvoiceService invoiceService;
    
    public InvoiceController(InvoiceService invoiceService) {
        this.invoiceService = invoiceService;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))")
    @GetMapping(value = "/{invoiceId}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public Invoice getInvoiceById(@LogParam("invoiceId") @PathVariable String invoiceId) {

        return invoiceService.getById(invoiceId);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))")
    @PostMapping(value = "/{invoiceId}/payment-info", produces = {MediaType.APPLICATION_JSON_VALUE})
    public InvoicePaymentInfo getInvoicePaymentInfo(@LogParam("invoiceId") @PathVariable String invoiceId,
                                                    @LogParam("invoicePaymentInfoRequest ") @RequestBody InvoicePaymentInfoRequest invoicePaymentInfoRequest) {

        return invoiceService.getInvoicePaymentInfo(invoiceId, invoicePaymentInfoRequest.getContractIds(), true);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_CUSTOMER') and (not hasRole('ROLE_EMPLOYEE') or (hasAuthority('CUSTOMERS_ACTOR_EMPLOYEE') and hasAuthority('ENTITY_INVOICES_VIEW')))")
    @GetMapping(value = "/{invoiceId}/content", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE})
    public void getInvoiceFile(
            @LogParam("invoiceId") @PathVariable String invoiceId,
            HttpServletResponse response) throws IOException {

        IBMCMDocument document = invoiceService.getInvoiceFile(invoiceId);

        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + document.getName());
        response.setContentType(document.getMimeType());

        document.writeTo(response.getOutputStream());
    }
}
