package sk.spp.nzp.be.assembler.customerprofile.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import sk.spp.nzp.be.api.customerprofile.Address;
import sk.spp.nzp.be.api.customerprofile.BusinessPartner;
import sk.spp.nzp.be.api.customerprofile.ContractAccount;
import sk.spp.nzp.be.api.customerprofile.ContractAccountSummary;
import sk.spp.nzp.be.assembler.customerprofile.AddressEmbedableAssembler;
import sk.spp.nzp.be.assembler.customerprofile.BusinessPartnerAssembler;
import sk.spp.nzp.be.assembler.customerprofile.ContractAccountAssembler;
import sk.spp.nzp.be.service.customerrequest.FieldGroupUpdateService;
import sk.spp.nzp.commons.api.codelist.CodeListItem;
import sk.spp.nzp.commons.api.customerprofile.enums.PaymentType;
import sk.spp.nzp.commons.assembler.codelist.CodeListItemAssembler;
import sk.spp.nzp.commons.enums.CodeListType;
import sk.spp.nzp.commons.model.customerprofile.ContractAccountEntity;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class ContractAccountAssemblerImpl implements ContractAccountAssembler {

    private BusinessPartnerAssembler businessPartnerAssembler;
    private AddressEmbedableAssembler addressEmbedableAssembler;
    private CodeListItemAssembler codeListItemAssembler;
    private FieldGroupUpdateService fieldGroupUpdateService;

    public ContractAccountAssemblerImpl(
            BusinessPartnerAssembler businessPartnerAssembler,
            AddressEmbedableAssembler addressEmbedableAssembler,
            CodeListItemAssembler codeListItemAssembler,
            FieldGroupUpdateService fieldGroupUpdateService
    ) {
        this.businessPartnerAssembler = businessPartnerAssembler;
        this.addressEmbedableAssembler = addressEmbedableAssembler;
        this.codeListItemAssembler = codeListItemAssembler;
        this.fieldGroupUpdateService = fieldGroupUpdateService;
    }

    @Override
    public ContractAccount map(ContractAccountEntity input, ContractAccount output) {

        if (input == null) // external entities may not be imported during assembling
            return null;

        output.setId(input.getId());
        output.setExternalId(StringUtils.stripStart(input.getExternalId(), "0"));
        output.setStatus(input.getStatus());
        output.setEInvoice(input.geteInvoice());
        output.setEmail(input.getEmail());
        output.setPhone(input.getPhone());

        output.setFirstName(input.getFirstName());
        output.setLastName(input.getLastName());
        output.setName(input.getName());
        output.setLastPaymentMethodChange(input.getLastPaymentMethodChange());

        output.setMandateReference(input.getMandateReference());

        if (input.getPaymentType() != null) {
            String inputPaymentType = input.getPaymentType();
            boolean paymentTypeUseIban = PaymentType.BANK_INKASO.getExternalName().equals(inputPaymentType) ||
                    PaymentType.BANK_TRANSFER.getExternalName().equals(inputPaymentType) ||
                    PaymentType.FOREIGN_BANK_INKASO.getExternalName().equals(inputPaymentType);

            output.setPaymentType(codeListItemAssembler.mapSummary(CodeListType.PAYMENT_TYPE, inputPaymentType, new CodeListItem()));

            if (PaymentType.SIPO.getExternalName().equals(inputPaymentType)){
                output.setSipo(input.getSipo());
            }

            if (paymentTypeUseIban){
                output.setIban(input.getIban());
            }

            if (paymentTypeUseIban){
                output.setBillingIban(input.getBillingIban());
            }
        }

        if(input.getBillingAddress() != null) {
            output.setBillingAddress(addressEmbedableAssembler.map(input.getBillingAddress(), new Address()));
        }

        if(input.getPostAddress() != null) {
            output.setPostAddress(addressEmbedableAssembler.map(input.getPostAddress(), new Address()));
        }

        output.setBusinessPartner(businessPartnerAssembler.map(input.getBusinessPartner(), new BusinessPartner()));

        output.setFieldUpdates(fieldGroupUpdateService.getFieldUpdates(input));

        return output;
    }
    
    @Override
    public ContractAccount mapRef(ContractAccountEntity input, ContractAccount output) {

        if (input == null) // external entities may not be imported during assembling
            return null;

        output.setId(input.getId());

        return output;
    }

    @Override
    public ContractAccountSummary mapSummary(ContractAccountEntity input, ContractAccountSummary output, boolean includeFieldUpdates) {
        output.setId(input.getId());
        output.setExternalId(StringUtils.stripStart(input.getExternalId(), "0"));
        output.setEInvoice(input.geteInvoice());
        output.setEmail(input.getEmail());
        output.setPhone(input.getPhone());
        output.setLastPaymentMethodChange(input.getLastPaymentMethodChange());

        if (input.getPaymentType() != null) {
            output.setPaymentType(codeListItemAssembler.mapSummary(CodeListType.PAYMENT_TYPE, input.getPaymentType(), new CodeListItem()));
        }

        if(includeFieldUpdates) {
            output.setFieldUpdates(fieldGroupUpdateService.getFieldUpdates(input));
        }

        return output;
    }

    @Override
    public List<ContractAccount> map(Collection<ContractAccountEntity> input) {

        if(input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new ContractAccount())).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public ContractAccountSummary mapBrief(ContractAccountEntity input, ContractAccountSummary output) {
        output.setId(input.getId());
        output.setExternalId(input.getExternalId());
        output.setEInvoice(input.geteInvoice());

        return output;
    }

}
