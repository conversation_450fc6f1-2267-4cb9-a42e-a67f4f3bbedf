package sk.spp.nzp.be.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.annotation.paging.PagingAsQueryParams;
import sk.spp.nzp.be.api.common.PagedResponse;
import sk.spp.nzp.be.api.common.QueryStringPaging;
import sk.spp.nzp.be.service.reporting.ReportService;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.api.reporting.DataModel;
import sk.spp.nzp.commons.api.reporting.DataReportFormat;
import sk.spp.nzp.commons.api.reporting.Report;
import sk.spp.nzp.commons.api.reporting.ReportDefinition;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.utils.Expressions;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/admin/reports")
public class AdminReportController {

    @Value("${reporting.export.filename}")
    private String fileName;

    private ReportService reportService;

    public AdminReportController(ReportService reportService) {
        this.reportService = reportService;
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('REPORTS_VIEW')")
    @GetMapping(value = "/dataModel", produces = {MediaType.APPLICATION_JSON_VALUE})
    public DataModel getDataModel() {
        return reportService.getDataModel();
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('REPORTS_VIEW')")
    @GetMapping(value = "/{uuid}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public Report getReportById(@LogParam("uuid") @PathVariable UUID uuid) {
        return reportService.getReportById(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('REPORTS_VIEW')")
    @GetMapping(value = "", produces = {MediaType.APPLICATION_JSON_VALUE})
    @PagingAsQueryParams
    public PagedResponse<Report> adminGetReports(@LogParam("queryStringPaging ") QueryStringPaging queryStringPaging,
                                            @LogParam("queryStringPaging ") @RequestParam(required = false) String name) {
        return reportService.getReportAll(queryStringPaging.toPaging(), name);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('REPORTS_EDIT')")
    @PostMapping(consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Report postReport(@LogParam("dataReport") @RequestBody Report report) {
        return reportService.saveReport(report);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('REPORTS_EDIT')")
    @PutMapping(consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Report putReport(@LogParam("dataReport") @RequestBody Report report) {
        return reportService.updateReport(report);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('REPORTS_EDIT')")
    @DeleteMapping(value = "/{uuid}")
    public void deleteReport(@LogParam("uuid") @PathVariable UUID uuid) {
        reportService.removeReport(uuid);
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('REPORTS_VIEW')")
    @GetMapping(value = "/{uuid}/run")
    public void adminRunReport(@LogParam("uuid") @PathVariable UUID uuid,
                          @LogParam("format") @RequestParam DataReportFormat format,
                          HttpServletResponse response) throws IOException {

        ContentDisposition contentDisposition;
        if (DataReportFormat.CSV.equals(format)) {
            contentDisposition = ContentDisposition.builder("attachment")
                    .filename(fileName + ".csv")
                    .build();

            response.setContentType("text/csv");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
        } else {
            contentDisposition = ContentDisposition.builder("attachment")
                    .filename(fileName + ".xlsx")
                    .build();

            response.setContentType("application/vnd.ms-excels");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
        }
        List<String> errors = reportService.runById(uuid, response.getOutputStream(), format);
        if (!errors.isEmpty()) {
            throw new ApiException(ErrorCode.VALIDATION_ERROR, errors);
        }
    }

    @Log
    @LogParam
    @PostMapping(value = "/run", consumes = {MediaType.APPLICATION_JSON_VALUE})
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('REPORTS_EDIT')")
    public void runReport(@LogParam("reportDefinition") @RequestBody ReportDefinition reportDefinition,
                          HttpServletResponse response) throws IOException {
        DataReportFormat format = reportDefinition.getFormat();
        ContentDisposition contentDisposition;
        if (DataReportFormat.CSV.equals(format)) {
            contentDisposition = ContentDisposition.builder("attachment")
                    .filename(fileName + ".csv")
                    .build();

            response.setContentType("text/csv");
            response.setHeader(HttpHeaders.CONTENT_TYPE, "text/csv");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
        } else {
            contentDisposition = ContentDisposition.builder("attachment")
                    .filename(fileName + ".xlsx")
                    .build();

            response.setContentType("application/vnd.ms-excels");
            response.setHeader(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excels");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
        }
        List<String> strings = reportService.runByDefinition(reportDefinition, response.getOutputStream());
        if (!strings.isEmpty()) {
            throw new ApiException(ErrorCode.VALIDATION_ERROR, strings);
        }
    }

    @Log
    @LogParam
    @PreAuthorize("hasRole('ROLE_EMPLOYEE') and hasAuthority('REPORTS_VIEW')")
    @PostMapping(value = "/validate", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public void validateReport(@LogParam("reportDefinition") @RequestBody ReportDefinition reportDefinition) {
        List<String> errors = reportService.validateReport(reportDefinition);
        if(Expressions.notEmpty(errors)){
            throw new ApiException(ErrorCode.VALIDATION_ERROR, errors);
        }
    }
}
