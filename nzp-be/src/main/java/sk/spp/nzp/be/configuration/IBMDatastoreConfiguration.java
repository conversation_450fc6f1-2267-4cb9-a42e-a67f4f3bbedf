package sk.spp.nzp.be.configuration;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import sk.spp.nzp.be.utils.ibmcm.IBMDatastore;
import sk.spp.nzp.be.utils.ibmcm.IBMDatastoreProperties;

import javax.annotation.PreDestroy;

@Configuration
public class IBMDatastoreConfiguration {

    private IBMDatastore datastore;

    @Value("${ibmcm.datastore.serverConfigPath}")
    private String serverConfigPath;

    @Value("${ibmcm.datastore.serverEnvironmentConfigPath}")
    private String serverEnvironmentConfigPath;

    @Bean(name = "ibmDatastoreProperties")
    @ConfigurationProperties(prefix = "ibmcm.datastore")
    public IBMDatastoreProperties ibmDatastoreProperties() {
        return new IBMDatastoreProperties();
    }

    @Bean(name = "ibmDatastorePoolProperties")
    @ConfigurationProperties(prefix = "ibmcm.datastore.pool")
    public GenericObjectPoolConfig<?> ibmDatastorePoolProperties() {
        return new GenericObjectPoolConfig<>();
    }

    @Bean(name = "ibmDatastore")
    public IBMDatastore ibmDatastore(
            @Qualifier("ibmDatastoreProperties") IBMDatastoreProperties properties,
            @Qualifier("ibmDatastorePoolProperties") GenericObjectPoolConfig<?> poolProperties) {
        datastore = new IBMDatastore(properties, poolProperties, serverConfigPath, serverEnvironmentConfigPath);
        return datastore;
    }

    @PreDestroy
    public void destroy() {
        datastore.destroy();
    }
}