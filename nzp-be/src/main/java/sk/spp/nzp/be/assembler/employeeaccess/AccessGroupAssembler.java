package sk.spp.nzp.be.assembler.employeeaccess;

import java.util.Collection;
import java.util.List;

import sk.spp.nzp.commons.api.employeeaccess.AccessGroup;
import sk.spp.nzp.commons.model.employeeaccess.AccessGroupEntity;

public interface AccessGroupAssembler {

    AccessGroupEntity map(AccessGroup input, AccessGroupEntity output);

    AccessGroup map(AccessGroupEntity input, AccessGroup output);

    AccessGroup mapFull(AccessGroupEntity input, AccessGroup output);

    List<AccessGroup> map(Collection<AccessGroupEntity> input);

}
