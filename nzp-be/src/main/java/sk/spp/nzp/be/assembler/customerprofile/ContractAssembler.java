package sk.spp.nzp.be.assembler.customerprofile;

import sk.spp.nzp.be.api.customerprofile.Contract;
import sk.spp.nzp.be.api.customerprofile.ContractSummary;
import sk.spp.nzp.commons.model.customerprofile.ContractEntity;

public interface ContractAssembler {

    Contract map(ContractEntity input, Contract output);

    ContractSummary mapWithContractAccount(ContractEntity input, ContractSummary output, boolean includeFieldUpdates);

    ContractSummary map(ContractEntity input, ContractSummary output);


}
