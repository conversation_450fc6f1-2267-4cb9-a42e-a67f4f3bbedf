package sk.spp.nzp.be.assembler.customerprofile;

import sk.spp.nzp.be.api.customerprofile.DeliveryPoint;
import sk.spp.nzp.be.api.customerprofile.DeliveryPointSummary;
import sk.spp.nzp.commons.api.customerconsumption.ConsumptionReservedValues;
import sk.spp.nzp.commons.context.DeliveryPointHistoryContext;
import sk.spp.nzp.commons.model.customerprofile.DeliveryPointEntity;
import sk.spp.nzp.commons.service.customerconsumption.DeliveryPointInfo;
import sk.spp.nzp.commons.service.customerconsumption.DeliveryPointReservedValueIntervalsInfo;

import java.util.List;

public interface DeliveryPointAssembler {

    DeliveryPoint map(DeliveryPointEntity input, DeliveryPoint output, List<DeliveryPoint.Fetch> fetches, DeliveryPointHistoryContext historyContext);

    DeliveryPoint map(DeliveryPointEntity input, DeliveryPoint output, DeliveryPointHistoryContext historyContext);

    DeliveryPoint map(DeliveryPointEntity input, DeliveryPoint output, DeliveryPointHistoryContext historyContext, List<DeliveryPoint.Fetch> fetches);

    DeliveryPoint mapFull(DeliveryPointEntity input, DeliveryPoint output, DeliveryPointHistoryContext historyContext);

    DeliveryPointSummary map(DeliveryPointEntity input, DeliveryPointSummary output, DeliveryPointHistoryContext historyContext);

    DeliveryPointSummary map(DeliveryPointEntity input, DeliveryPointSummary output, List<DeliveryPointSummary.Fetch> dpsFetches, DeliveryPointHistoryContext historyContext);

    DeliveryPointSummary map(DeliveryPointEntity input, DeliveryPointSummary output, List<DeliveryPointSummary.Fetch> dpsFetches, List<DeliveryPoint.Fetch> udpFetches,DeliveryPointHistoryContext historyContext);

    ConsumptionReservedValues map(DeliveryPointEntity input, ConsumptionReservedValues output, DeliveryPointHistoryContext historyContext);

    DeliveryPoint map(DeliveryPointInfo input, DeliveryPoint output);

    ConsumptionReservedValues map(DeliveryPointReservedValueIntervalsInfo input, ConsumptionReservedValues output);
}