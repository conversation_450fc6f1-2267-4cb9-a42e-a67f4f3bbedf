package sk.spp.nzp.be.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.customeraccess.PasswordCheck;
import sk.spp.nzp.be.service.common.PasswordPolicyService;

import javax.validation.Valid;

@RestController
@RequestMapping("/password-policy")
public class PasswordPolicyController {

    private PasswordPolicyService passwordPolicyService;

    public PasswordPolicyController(PasswordPolicyService passwordPolicyService) {
        this.passwordPolicyService = passwordPolicyService;
    }

    @Log
    @LogParam
    @PostMapping
    public void checkPasswordComplexity(@LogParam("passwordComplexityCheck") @Valid @RequestBody PasswordCheck req) {
        passwordPolicyService.checkPasswordComplexity(req.getPassword());
    }

}

