package sk.spp.nzp.be.controller;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sk.isdd.common.logging.Log;
import sk.isdd.common.logging.LogParam;
import sk.spp.nzp.be.api.codelist.ConfigParameter;
import sk.spp.nzp.be.service.codelist.ConfigurationService;
import sk.spp.nzp.commons.model.codelist.enums.ConfigParameterTarget;

import java.util.List;

@RestController
@RequestMapping("/config")
public class ConfigController {

    private final ConfigurationService configurationService;

    public ConfigController(ConfigurationService configurationService) {
        this.configurationService = configurationService;
    }

    @Log
    @LogParam
    @GetMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
    public List<ConfigParameter> getConfig() {
        return configurationService.getConfigParameters(ConfigParameterTarget.UI);
    }
}
