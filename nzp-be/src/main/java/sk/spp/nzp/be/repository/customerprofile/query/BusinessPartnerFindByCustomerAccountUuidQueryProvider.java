package sk.spp.nzp.be.repository.customerprofile.query;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.DateTimeExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import sk.spp.nzp.be.api.common.Sorting;
import sk.spp.nzp.be.api.customerprofile.BusinessPartnerSearch;
import sk.spp.nzp.be.repository.common.AbstractQueryProvider;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.api.customersharing.enums.OwnershipType;
import sk.spp.nzp.commons.api.enums.ErrorCode;
import sk.spp.nzp.commons.exception.ApiException;
import sk.spp.nzp.commons.model.customerprofile.BusinessPartnerEntity;
import sk.spp.nzp.commons.model.customerprofile.QBusinessPartnerEntity;
import sk.spp.nzp.commons.model.customerprofile.QContractEntity;
import sk.spp.nzp.commons.model.customerprofile.QUnitedDeliveryPointEntity;
import sk.spp.nzp.commons.model.customersharing.QBusinessPartnerOwnershipEntity;
import sk.spp.nzp.commons.model.customersharing.QUnitedDeliveryPointOwnershipEntity;

import javax.persistence.EntityManager;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static sk.spp.nzp.commons.model.customerprofile.QBusinessPartnerEntity.businessPartnerEntity;

public class BusinessPartnerFindByCustomerAccountUuidQueryProvider extends AbstractQueryProvider<BusinessPartnerEntity> {

    private UUID customerAccountUuid;
    private BusinessPartnerSearch businessPartnerSearch;
    private BusinessPartnerQueue businessPartnerQueue;
    private Boolean excludeHiddenDp;
    
    public BusinessPartnerFindByCustomerAccountUuidQueryProvider(UUID customerAccountUuid, BusinessPartnerSearch businessPartnerSearch, BusinessPartnerQueue businessPartnerQueue,Boolean excludeHiddenDp) {
        
        this.customerAccountUuid = customerAccountUuid;
        this.businessPartnerSearch = businessPartnerSearch;
        this.businessPartnerQueue = businessPartnerQueue;
        this.excludeHiddenDp= excludeHiddenDp;
    }

    @Override
    public JPAQuery<BusinessPartnerEntity> getQuery(EntityManager em) {
        
        return findByCustomerAccountUuidQuery(em);
    }
    public JPAQuery<BusinessPartnerEntity> findByCustomerAccountUuidQuery(EntityManager em) {
        
        QBusinessPartnerEntity businessPartnerEntity = QBusinessPartnerEntity.businessPartnerEntity;
        QBusinessPartnerOwnershipEntity businessPartnerOwnershipEntity = QBusinessPartnerOwnershipEntity.businessPartnerOwnershipEntity;
        QUnitedDeliveryPointOwnershipEntity unitedDeliveryPointOwnershipEntity = QUnitedDeliveryPointOwnershipEntity.unitedDeliveryPointOwnershipEntity;
        QUnitedDeliveryPointEntity unitedDeliveryPointEntity = QUnitedDeliveryPointEntity.unitedDeliveryPointEntity;
        QContractEntity contractEntity = QContractEntity.contractEntity;

        BooleanExpression ownerExp = null;
        
        if(businessPartnerSearch.getShared() == null || !businessPartnerSearch.getShared().booleanValue()) {
            ownerExp = businessPartnerOwnershipEntity.type.eq(OwnershipType.OWNER);
        }
        BooleanExpression bpIdExp = null;
        if(businessPartnerSearch.getId() != null ) {
            bpIdExp = businessPartnerEntity.id.eq(businessPartnerSearch.getId());
        }
        BooleanBuilder bpAllDeliveryPointsHidden = null;

        if(Boolean.TRUE.equals(excludeHiddenDp)) {
            bpAllDeliveryPointsHidden = new BooleanBuilder();
            JPQLQuery<Tuple> select = JPAExpressions.select().from(unitedDeliveryPointOwnershipEntity)
                    .join(unitedDeliveryPointEntity)
                    .on(unitedDeliveryPointOwnershipEntity.unitedDeliveryPoint()
                                    .eq(unitedDeliveryPointEntity),
                            unitedDeliveryPointEntity.businessPartner().eq(businessPartnerEntity),
                            unitedDeliveryPointOwnershipEntity.hidden.eq(Boolean.FALSE).or(unitedDeliveryPointOwnershipEntity.hidden.isNull()));
            BooleanExpression activeContract = DateTimeExpression.currentDate(LocalDate.class).between(contractEntity.effectiveFrom, contractEntity.effectiveTo);


            if(businessPartnerSearch.getShared() == null || !businessPartnerSearch.getShared()) {

                bpAllDeliveryPointsHidden = bpAllDeliveryPointsHidden.and(select
                        .where(unitedDeliveryPointOwnershipEntity.type.eq(OwnershipType.OWNER)).exists()
                ).and(JPAExpressions.select().from(contractEntity).where(contractEntity.businessPartner().eq(businessPartnerEntity)
                        .and(activeContract)).exists());
            }else{
                bpAllDeliveryPointsHidden = bpAllDeliveryPointsHidden.and(
                        select.exists()).and(JPAExpressions.select().from(contractEntity).where(contractEntity.businessPartner().eq(businessPartnerEntity)
                        .and(activeContract)).exists());

            }
        }

        BooleanExpression queueExp = null;
        if(businessPartnerQueue != null) {
            queueExp = businessPartnerEntity.queue.eq(businessPartnerQueue);
        }

        if(businessPartnerSearch.getQueue() != null) {
            queueExp = businessPartnerEntity.queue.eq(businessPartnerSearch.getQueue());
        }

        return new JPAQuery<BusinessPartnerEntity>(em).from(businessPartnerEntity)
                .join(businessPartnerOwnershipEntity).on(businessPartnerOwnershipEntity.customerAccount().id.eq(customerAccountUuid), businessPartnerOwnershipEntity.businessPartner().eq(businessPartnerEntity))
                .where(ownerExp, queueExp, bpAllDeliveryPointsHidden, bpIdExp)
                .orderBy(getOrderBy());
    }

    private OrderSpecifier<?>[] getOrderBy() {

        if(businessPartnerSearch.getPaging() == null || businessPartnerSearch.getPaging().getSort() == null || businessPartnerSearch.getPaging().getSort().isEmpty()) {
            return getDefaultOrderBy();
        }

        List<OrderSpecifier<?>> sortingList = new ArrayList<>();
        for(Sorting sorting: businessPartnerSearch.getPaging().getSort()) {

            switch (sorting.getAttribute()){
                case "id":
                    sortingList.add(getOrderSpecifier(sorting, businessPartnerEntity.id));
                    break;
                case "name":
                    sortingList.add(getOrderSpecifier(sorting, businessPartnerEntity.name));
                    break;
                case "firstName":
                    sortingList.add(getOrderSpecifier(sorting, businessPartnerEntity.firstName));
                    break;
                case "lastName":
                    sortingList.add(getOrderSpecifier(sorting, businessPartnerEntity.lastName));
                    break;
                case "category":
                    sortingList.add(getOrderSpecifier(sorting, businessPartnerEntity.category));
                    break;
                case "email":
                    sortingList.add(getOrderSpecifier(sorting, businessPartnerEntity.email));
                    break;
                case "nameSearchable":
                    sortingList.add(getOrderSpecifier(sorting, businessPartnerEntity.nameSearchable));
                    break;
                default:
                    throw new ApiException(ErrorCode.GENERIC_VALIDATION_ERROR);
            }
        }

        OrderSpecifier<?>[] output = new OrderSpecifier<?>[sortingList.size()];
        return sortingList.toArray(output);
    }

    private OrderSpecifier<?>[] getDefaultOrderBy() {
        OrderSpecifier<?>[] output = new OrderSpecifier<?>[1];
        output[0] = businessPartnerEntity.name.desc();
        return output;
    }
}
