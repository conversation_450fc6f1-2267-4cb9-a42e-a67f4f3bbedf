package sk.spp.nzp.be.configuration;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class DatasourceConfiguration {

    @Bean
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSourceProperties getPrimaryDatasourceProperties(){
        return new DataSourceProperties();
    }

    @Bean(name = "reportingDatasourceProperties")
    @ConfigurationProperties(prefix = "reporting.datasource")
    public DataSourceProperties getReportingDatasourceProperties(){
        return new DataSourceProperties();
    }

    @Bean(name = "mssqlDatasourceProperties")
    @ConfigurationProperties(prefix = "mssql.datasource")
    public DataSourceProperties getMssqlDatasourceProperties(){
        return new DataSourceProperties();
    }

    @Bean
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.hikari")
    public HikariDataSource primaryDataSource(DataSourceProperties properties) {
        return properties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }

    @Bean(name = "reportingDatasource")
    @ConfigurationProperties(prefix = "reporting.datasource.hikari")
    public HikariDataSource reportingDataSource(@Qualifier("reportingDatasourceProperties") DataSourceProperties properties) {
        return properties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }

    @Bean(name = "mssqlDataSource")
    @ConfigurationProperties(prefix = "mssql.datasource.hikari")
    public HikariDataSource msqsqlDataSource(@Qualifier("mssqlDatasourceProperties") DataSourceProperties properties) {
        return properties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }
}
