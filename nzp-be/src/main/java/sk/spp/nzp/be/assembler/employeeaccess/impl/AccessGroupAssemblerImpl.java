package sk.spp.nzp.be.assembler.employeeaccess.impl;

import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import sk.spp.nzp.commons.api.employeeaccess.AccessGroup;
import sk.spp.nzp.be.assembler.employeeaccess.AccessGroupAssembler;
import sk.spp.nzp.be.assembler.employeeaccess.AccessGroupRightAssembler;
import sk.spp.nzp.commons.api.employeeaccess.AccessRight;
import sk.spp.nzp.commons.model.employeeaccess.AccessGroupEntity;

@Component
public class AccessGroupAssemblerImpl implements AccessGroupAssembler {

    private AccessGroupRightAssembler accessGroupRightAssembler;

    public AccessGroupAssemblerImpl(AccessGroupRightAssembler accessGroupRightAssembler) {
        
        this.accessGroupRightAssembler = accessGroupRightAssembler;
    }

    @Override
    public AccessGroupEntity map(AccessGroup input, AccessGroupEntity output) {
        
        output.setCode(input.getCode());
        output.setName(input.getName());
        output.setDescription(input.getDescription());
        
        return output;
    }
    
    @Override
    public AccessGroup map(AccessGroupEntity input, AccessGroup output) {
        
        output.setUuid(input.getId().toString());
        output.setCode(input.getCode());
        output.setName(input.getName());
        output.setDescription(input.getDescription());
        
        return output;
    }
    
    @Override
    public AccessGroup mapFull(AccessGroupEntity input, AccessGroup output) {
        
        map(input, output);
        
        if(input.getAccesses() != null) {
            output.setAccessRights(accessGroupRightAssembler.map(input.getAccesses()));
        }
        
        return output;
    }
    
    @Override
    public List<AccessGroup> map(Collection<AccessGroupEntity> input) {

        if(input != null && !input.isEmpty()) {

            return input.stream().map(i -> map(i, new AccessGroup())).collect(Collectors.toList());
        }
        
        return Collections.emptyList();
    }
}
