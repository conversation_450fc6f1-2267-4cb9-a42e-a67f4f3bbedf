package sk.spp.nzp.be.customerprofile;

import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.transaction.PlatformTransactionManager;
import sk.isdd.paybysquare.exception.InputValidationException;
import sk.spp.nzp.AbstractTestBase;
import sk.spp.nzp.be.api.common.PagedResponse;
import sk.spp.nzp.be.api.customerprofile.*;
import sk.spp.nzp.be.api.customerprofile.invoicesummary.InvoicePaymentSummary;
import sk.spp.nzp.be.assembler.common.SharingAssembler;
import sk.spp.nzp.be.assembler.customerprofile.*;
import sk.spp.nzp.be.codelist.builder.GenericCodeListEntityBuilder;
import sk.spp.nzp.be.customeraccess.builder.CustomerAccountEntityBuilder;
import sk.spp.nzp.be.customerprofile.builder.*;
import sk.spp.nzp.be.customersharing.builder.InvoiceOwnershipEntityBuilder;
import sk.spp.nzp.be.customersharing.builder.UnitedDeliveryPointOwnershipEntityBuilder;
import sk.spp.nzp.be.repository.customerprofile.query.InvoiceSearchQueryProvider;
import sk.spp.nzp.be.service.customerprofile.BankAccountResolver;
import sk.spp.nzp.be.service.customerprofile.InvoiceEntityService;
import sk.spp.nzp.be.service.customerprofile.InvoiceService;
import sk.spp.nzp.be.service.customerprofile.PayBySquareService;
import sk.spp.nzp.be.service.customerprofile.impl.InvoiceServiceImpl;
import sk.spp.nzp.be.service.ibmcm.IBMCMService;
import sk.spp.nzp.commons.api.customeraccess.enums.CustomerAccountStatus;
import sk.spp.nzp.commons.api.customeraccess.enums.CustomerAccountType;
import sk.spp.nzp.commons.api.customerprofile.enums.BalancingReason;
import sk.spp.nzp.commons.api.customerprofile.enums.BusinessPartnerQueue;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceStatus;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceTypeGroup;
import sk.spp.nzp.commons.api.customerprofile.enums.SettlementType;
import sk.spp.nzp.commons.api.customersharing.enums.OwnershipType;
import sk.spp.nzp.commons.context.holder.SecurityContextHolder;
import sk.spp.nzp.commons.enums.BusinessPartnerKind;
import sk.spp.nzp.commons.exception.NotFoundException;
import sk.spp.nzp.commons.model.codelist.GenericCodeListEntity;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;
import sk.spp.nzp.commons.model.customerprofile.*;
import sk.spp.nzp.commons.model.customersharing.UnitedDeliveryPointOwnershipEntity;
import sk.spp.nzp.commons.repository.common.QueryDslRepository;
import sk.spp.nzp.commons.repository.customerprofile.CustomerTransactionEntityRepository;
import sk.spp.nzp.commons.repository.customerprofile.InvoiceEntityRepository;
import sk.spp.nzp.commons.repository.customerprofile.InvoiceRawEntityRepository;
import sk.spp.nzp.commons.repository.customerprofile.UnitedDeliveryPointEntityRepository;
import sk.spp.nzp.commons.service.common.HistoryContextProvider;
import sk.spp.nzp.commons.service.security.SecurityChecker;
import sk.spp.nzp.commons.utils.ExternalIdHash;
import sk.spp.nzp.commons.utils.FTSearchUtils;
import sk.spp.nzp.securitymock.WithNzpUserMock;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

public class InvoiceServiceTest extends AbstractTestBase {

    private static final DateTimeFormatter DTF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss,SSS");
    public static final String SINGLE_INVOICE_TEST_NOTE = "Uhrada z MojeSPP PayBySquare - SINGLE";
    public static final String MULTI_INVOICE_TEST_NOTE = "Uhrada z MojeSPP PayBySquare - MULTI";
    public static final String BENEFICIARY_NAME = "MojeSPP";
    public static final String TEST_PAY_BY_SQUARE_STRING = "TESTPayBySquareString";

    @Autowired
    private InvoiceService invoiceService;
    @Autowired
    private InvoiceAssembler invoiceAssembler;
    @Autowired
    private InvoiceEntityRepository invoiceEntityRepository;
    @Autowired
    private InvoiceRawEntityRepository invoiceRawEntityRepository;
    @Autowired
    private CustomerTransactionEntityRepository customerTransactionEntityRepository;
    @Autowired
    private UnitedDeliveryPointEntityRepository unitedDeliveryPointEntityRepository;
    @Autowired
    private SecurityChecker securityChecker;
    @Autowired
    private SecurityContextHolder securityContextHolder;
    @Autowired
    PaymentEmbedableAssembler paymentEmbedableAssembler;
    @Autowired
    SharingAssembler sharingAssembler;
    @Autowired
    InvoiceSummaryAssembler invoiceSummaryAssembler;
    @Autowired
    DeliveryPointAssembler deliveryPointAssembler;
    @Autowired
    HistoryContextProvider historyContextProvider;
    @Autowired
    CustomerTransactionAssembler customerTransactionAssembler;
    @Mock
    private QueryDslRepository queryDslRepositoryMock;
    @Mock
    private IBMCMService documentServiceMock;

    private InvoiceService invoiceServiceMock;
    @Autowired
     BusinessPartnerAssembler businessPartnerAssembler;

    @Autowired
    private InvoiceEntityService invoiceEntityService;

    @MockBean
    private PayBySquareService payBySquareService;

    @Autowired
    private BankAccountResolver bankAccountResolver;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @BeforeEach
    public void setUp() {
        invoiceServiceMock = new InvoiceServiceImpl(documentServiceMock, invoiceAssembler, invoiceEntityRepository, invoiceRawEntityRepository,
                queryDslRepositoryMock, customerTransactionEntityRepository, unitedDeliveryPointEntityRepository, securityChecker,
                securityContextHolder, paymentEmbedableAssembler, sharingAssembler, invoiceSummaryAssembler,
                deliveryPointAssembler, customerTransactionAssembler,businessPartnerAssembler, historyContextProvider, invoiceEntityService,
                bankAccountResolver, payBySquareService, platformTransactionManager);
        ReflectionTestUtils.setField(invoiceServiceMock, "singleInvoiceReferrerNote", SINGLE_INVOICE_TEST_NOTE);
        ReflectionTestUtils.setField(invoiceServiceMock, "multipleInvoiceReferrerNote", MULTI_INVOICE_TEST_NOTE);
        ReflectionTestUtils.setField(invoiceServiceMock, "beneficiaryName", BENEFICIARY_NAME);
    }

    @BeforeEach
    public void clearMocks() {
        Mockito.reset(payBySquareService);
    }

    @Test
    @WithNzpUserMock(customerUuid = "123e4567-e89b-12d3-a456-************")
    public void getInvoiceSearchSummary(){
        LocalDate actualDate = LocalDate.now();

        CustomerAccountEntity logged = new CustomerAccountEntityBuilder(context).getLogged();
        InvoiceEntity ie1 = new InvoiceEntityBuilder(context).amount(BigDecimal.valueOf(1)).persist().get();
        InvoiceEntity ie2 = new InvoiceEntityBuilder(context).amount(BigDecimal.valueOf(2)).dueAt(actualDate.plusDays(1)).unpaid(BigDecimal.TEN).persist().get();
        InvoiceEntity ie3 = new InvoiceEntityBuilder(context).amount(BigDecimal.valueOf(3)).dueAt(actualDate.minusDays(1)).unpaid(BigDecimal.TEN).persist().get();
        new InvoiceOwnershipEntityBuilder(context).customerAccount(logged).grantorCustomerAccount(logged).invoice(ie1).persist();
        new InvoiceOwnershipEntityBuilder(context).customerAccount(logged).grantorCustomerAccount(logged).invoice(ie2).persist();
        new InvoiceOwnershipEntityBuilder(context).customerAccount(logged).grantorCustomerAccount(logged).invoice(ie3).persist();
        clean();
        InvoiceSearch invoiceSearch = new InvoiceSearch().withCustomerAccountId(logged.getId());
        InvoicePaymentSummary invoiceSearchSummary = invoiceService.getInvoiceSearchSummary(invoiceSearch);
        Assertions.assertEquals(2, invoiceSearchSummary.getUnpaid().getCount());
        Assertions.assertEquals(1, invoiceSearchSummary.getUnpaidExpired().getCount());
        Assertions.assertEquals(Integer.valueOf(3), invoiceSearchSummary.getCount());
    }

    @Test
    @WithNzpUserMock(customerUuid = "123e4567-e89b-12d3-a456-************")
    public void getInvoicesCountAsEmployee(){
        LocalDate actualDate = LocalDate.now();

        CustomerAccountEntity logged = new CustomerAccountEntityBuilder(context).getLogged();
        InvoiceEntity ie1 = new InvoiceEntityBuilder(context).amount(BigDecimal.valueOf(1)).dueAt(actualDate.minusDays(2)).persist().get();
        InvoiceEntity ie2 = new InvoiceEntityBuilder(context).amount(BigDecimal.valueOf(2)).dueAt(actualDate.plusDays(1)).unpaid(BigDecimal.TEN).persist().get();
        InvoiceEntity ie3 = new InvoiceEntityBuilder(context).amount(BigDecimal.valueOf(3)).dueAt(actualDate.minusDays(1)).unpaid(BigDecimal.TEN).persist().get();
        new InvoiceOwnershipEntityBuilder(context).customerAccount(logged).grantorCustomerAccount(logged).invoice(ie1).persist();
        new InvoiceOwnershipEntityBuilder(context).customerAccount(logged).grantorCustomerAccount(logged).invoice(ie2).persist();
        new InvoiceOwnershipEntityBuilder(context).customerAccount(logged).grantorCustomerAccount(logged).invoice(ie3).persist();
        clean();
        InvoiceSearch invoiceSearch = new InvoiceSearch().withCustomerAccountId(logged.getId()).withExpired(true);
        Assertions.assertEquals(2L, (long) invoiceService.getInvoicesCountAsEmployee(invoiceSearch).getCount());

        invoiceSearch = new InvoiceSearch().withCustomerAccountId(logged.getId()).withExpired(false);
        Assertions.assertEquals(1L, (long) invoiceService.getInvoicesCountAsEmployee(invoiceSearch).getCount());
    }

    @Test
    @WithNzpUserMock(customerUuid = "123e4567-e89b-12d3-a456-************")
    public void getCount(){
        CustomerAccountEntity logged = new CustomerAccountEntityBuilder(context).getLogged();
        InvoiceEntity ie1 = new InvoiceEntityBuilder(context).status(InvoiceStatus.PAID).persist().get();
        InvoiceEntity ie2 = new InvoiceEntityBuilder(context).status(InvoiceStatus.UNPAID).persist().get();
        InvoiceEntity ie3 = new InvoiceEntityBuilder(context).status(InvoiceStatus.UNPAID).persist().get();
        new InvoiceOwnershipEntityBuilder(context).customerAccount(logged).grantorCustomerAccount(logged).invoice(ie1).persist();
        new InvoiceOwnershipEntityBuilder(context).customerAccount(logged).grantorCustomerAccount(logged).invoice(ie2).persist();
        new InvoiceOwnershipEntityBuilder(context).customerAccount(logged).grantorCustomerAccount(logged).invoice(ie3).persist();
        clean();
        InvoiceSearch invoiceSearch = new InvoiceSearch().withCustomerAccountId(logged.getId());
        Assertions.assertEquals(3L, (long) invoiceService.getCount(invoiceSearch).getCount());
        Assertions.assertEquals(1L, (long) invoiceService.getCount(invoiceSearch.withStatus(InvoiceStatus.PAID)).getCount());
    }


    @ParameterizedTest
    @EnumSource(BusinessPartnerKind.class)
    @WithNzpUserMock(customerUuid = "123e4567-e89b-12d3-a456-************")
    public void getByUuid(BusinessPartnerKind bpKind) {
        BusinessPartnerEntity bpe1 = new BusinessPartnerEntityBuilder(context)
                .externalId("EXT1")
                .queue(BusinessPartnerQueue.byKind(bpKind.getValue()))
                .kind(bpKind)
                .persist()
                .get();

        GenericCodeListEntity gcl = new GenericCodeListEntityBuilder(context)
                .code("ZD")
                .type("INVOICE_TYPE")
                .persist().get();

        GenericCodeListEntity gc2 = new GenericCodeListEntityBuilder(context)
                .code("U")
                .type("PAYMENT_TYPE")
                .persist().get();

        ContractAccountEntity cae = new ContractAccountEntityBuilder(context)
                .businessPartner(bpe1)
                .paymentType("U")
                .persist()
                .get();

        UnitedDeliveryPointEntity udpe = new UnitedDeliveryPointEntityBuilder(context).businessPartner(bpe1).persist().get();

        DeliveryPointEntity dpe = new DeliveryPointEntityBuilder(context)
                .persist()
                .get();

        ContractEntity contract = new ContractEntityBuilder(context)
                .deliveryPointEntity(dpe)
                .unitedDeliveryPointEntity(udpe)
                .businessPartner(bpe1)
                .persist().get();

        DeliveryPointEntity dpe2 = new DeliveryPointEntityBuilder(context)
                .persist()
                .get();

        ContractEntity contract2 = new ContractEntityBuilder(context)
                .deliveryPointEntity(dpe2)
                .unitedDeliveryPointEntity(udpe)
                .businessPartner(bpe1)
                .persist().get();

        InvoiceEntity ie = new InvoiceEntityBuilder(context)
                .externalId("InvoiceExt")
                .status(InvoiceStatus.PAID)
                .type(InvoiceTypeGroup.ADVANCE_INVOICE)
                .type(gcl.getCode())
                .contractAccount(cae)
                .amount(BigDecimal.TEN)
                .dueAt(localDateTime("2020-01-01 00:00:00,000", DTF).toLocalDate())
                .issueAt(localDateTime("2020-01-02 00:00:00,000", DTF).toLocalDate())
                .vs("vs")
                .persist()
                .get();

        PaymentEmbeddable paymentEmbeddable = new PaymentEmbeddableBuilder(context).get();
        InvoiceRawEntity ire = new InvoiceRawEntityBuilder(context)
                .invoice(ie)
                .item2(1)
                .contractAccountId(ExternalIdHash.encodeExternalId("CA_ID"))
                .deliveryPoint(dpe)
                .contract(contract)
                .payment(paymentEmbeddable)
                .persist()
                .get();
        InvoiceRawEntity ire2 = new InvoiceRawEntityBuilder(context)
                .invoice(ie)
                .item2(2)
                .contract(contract2)
                .contractAccountId(ExternalIdHash.encodeExternalId("CA_ID"))
                .deliveryPoint(dpe2)
                .payment(paymentEmbeddable)
                .persist()
                .get();

        InvoiceRawEntity ire3 = new InvoiceRawEntityBuilder(context)
                .invoice(ie)
                .item2(3)
                .contractAccountId(ExternalIdHash.encodeExternalId("CA_ID"))
                .contract(contract2)
                .deliveryPoint(dpe2)
                .payment(paymentEmbeddable)
                .persist()
                .get();
        new InvoiceContractEntityBuilder(context)
                .invoice(ie.getExternalId())
                .contract(contract.getExternalId())
                .persist();
        new InvoiceContractEntityBuilder(context)
                .invoice(ie.getExternalId())
                .contract(contract2.getExternalId())
                .persist();
        CustomerAccountEntity cuae1 = new CustomerAccountEntityBuilder(context).getLogged();

        CustomerAccountEntity cuae2 = new CustomerAccountEntityBuilder(context)
                .status(CustomerAccountStatus.ACTIVE)
                .email("zzzzzzz")
                .type(CustomerAccountType.CUSTOMER)
                .persist()
                .get();

        UnitedDeliveryPointOwnershipEntity dpoe1 = new UnitedDeliveryPointOwnershipEntityBuilder(context, cuae1, udpe)
                .persistWithInvoiceOwnership(ie)
                .get();

        UnitedDeliveryPointOwnershipEntity dpoe2 = new UnitedDeliveryPointOwnershipEntityBuilder(context)
                .inherited(false)
                .type(OwnershipType.SHARING)
                .unitedDeliveryPoint(udpe)
                .customerAccount(cuae2)
                .persistWithInvoiceOwnership(ie)
                .get();

        // flush/clear
        clean();

        Invoice result = invoiceService.getById(ie.getId());

        // ZP14 (PMERU-1998, SPPNZP-209, PMERU-2033)
        String expectedVs = BusinessPartnerKind.HOME.equals(bpKind) ? cae.getExternalId() : ie.getReference();

        Assertions.assertEquals(ie.getExternalId(), result.getExternalId());
        Assertions.assertEquals(ie.getStatus(), result.getStatus());
        Assertions.assertEquals(ie.getType(), result.getType().getCode());
        Assertions.assertEquals(ie.getAmount().doubleValue(), result.getAmount().doubleValue(), 0.01);
        Assertions.assertEquals(ie.getDueAt(), result.getDueAt());
        Assertions.assertEquals(ie.getIssueAt(), result.getIssueAt());
        Assertions.assertEquals(deleteLeadingZeros(expectedVs, 10), result.getVs());
        Assertions.assertEquals(ie.getContainsPaymentPlan(), result.getContainsPaymentPlan());
        Assertions.assertEquals(ie.getContainsPaymentRequest(), result.getContainsPaymentRequest());

        Assertions.assertEquals(3, result.getInvoiceItems().size());
        Assertions.assertEquals(2, result.getDeliveryPoints().size());

        Assertions.assertEquals(1, result.getPayments().size());
        Assertions.assertEquals(paymentEmbeddable.getExternalId(), result.getPayments().get(0).getExternalId());
        Assertions.assertEquals(paymentEmbeddable.getVs(), result.getPayments().get(0).getVs());
        Assertions.assertEquals(0, BigDecimal.valueOf(30).compareTo(result.getPayments().get(0).getAmount()));
        Assertions.assertEquals(paymentEmbeddable.getType(), result.getPayments().get(0).getType());


        Assertions.assertEquals(2, result.getDeliveryPointItems().size());
        DeliveryPointItem dpi2 = result.getDeliveryPointItems().stream().filter(dpi -> dpi.getDeliveryPoint().getExternalId().equals(dpe2.getExternalId())).findFirst().get();
        Assertions.assertEquals(0, ire2.getAmount().add(ire3.getAmount()).compareTo(dpi2.getAmount()));

        Assertions.assertEquals(2, result.getDeliveryPoints().size());
        Assertions.assertEquals(3, result.getInvoiceItems().size());
        InvoiceItem item = result.getInvoiceItems().stream().filter(ii -> ii.getId().equals(ire.getId().toString())).findFirst().get();

        Assertions.assertEquals(0, ire.getAmount().compareTo(item.getAmount()));

        Assertions.assertEquals(bpe1.getExternalId(), result.getBusinessPartner().getExternalId());
        Assertions.assertEquals(cae.getExternalId(), result.getContractAccount().getExternalId());
        Assertions.assertEquals(cae.getPaymentType(), result.getContractAccount().getPaymentType().getCode());


        String randomUuid = UUID.randomUUID().toString();

        try {

            invoiceService.getById(randomUuid);

            Assertions.fail("Exception was expected but no one was thrown.");

        } catch (NotFoundException e) {

            // this is expected
        }
    }

    @Test
    @WithNzpUserMock(customerUuid = "123e4567-e89b-12d3-a456-************")
    public void getByUuid_resolveOverpaymentsWithoutPairInvoiceRaw() {
        GenericCodeListEntity gcl = new GenericCodeListEntityBuilder(context)
                .code("ZD")
                .type("INVOICE_TYPE")
                .persist().get();

        new GenericCodeListEntityBuilder(context)
                .code("U")
                .type("PAYMENT_TYPE")
                .persist().get();

        clean();

        BusinessPartnerEntity bpe = new BusinessPartnerEntityBuilder(context)
                .externalId("EXT1")
                .queue(BusinessPartnerQueue.COLLECTIVE)
                .persist()
                .get();

        ContractAccountEntity cae = new ContractAccountEntityBuilder(context)
                .businessPartner(bpe)
                .paymentType("U")
                .persist()
                .get();

        UnitedDeliveryPointEntity udpe = new UnitedDeliveryPointEntityBuilder(context)
                .businessPartner(bpe)
                .persist()
                .get();

        InvoiceEntity invoice = new InvoiceEntityBuilder(context)
                .externalId("INVOICE_1")
                .type(gcl.getCode())
                .contractAccount(cae)
                .persist()
                .get();

        PaymentEmbeddable payment = new PaymentEmbeddableBuilder(context)
                .externalId("PAYMENT_123")
                .amount(BigDecimal.valueOf(100))
                .get();

        InvoiceRawEntity overpaymentRaw = new InvoiceRawEntityBuilder(context)
                .invoice(invoice)
                .payment(payment)
                .type("FA")
                .amount(BigDecimal.valueOf(100))
                .persist()
                .get();

        overpaymentRaw.setBalancingReason(BalancingReason.TV_08);
        overpaymentRaw.setInvoiceFa(invoice);

        invoice.addInvoiceRawList(overpaymentRaw);

        CustomerAccountEntity cuae = new CustomerAccountEntityBuilder(context).getLogged();
        new UnitedDeliveryPointOwnershipEntityBuilder(context, cuae, udpe)
                .persistWithInvoiceOwnership(invoice)
                .get();

        clean();

        Invoice result = invoiceService.getById(invoice.getId());

        Assertions.assertNotNull(result, "Invoice should not be null");
        Assertions.assertNotNull(result.getPayments(), "Payments should not be null");
        Assertions.assertTrue(result.getPayments().isEmpty(), "Should return empty payments list");
    }

    @Test
    @WithNzpUserMock(customerUuid = "123e4567-e89b-12d3-a456-************")
    public void getByUuid_resolveOverpaymentsWithPairInvoiceRaw() {
        GenericCodeListEntity gcl = new GenericCodeListEntityBuilder(context)
                .code("ZD2")
                .type("INVOICE_TYPE")
                .persist().get();

        new GenericCodeListEntityBuilder(context)
                .code("U")
                .type("PAYMENT_TYPE")
                .persist().get();

        clean();

        BusinessPartnerEntity bpe = new BusinessPartnerEntityBuilder(context)
                .externalId("EXT1")
                .queue(BusinessPartnerQueue.COLLECTIVE)
                .persist()
                .get();

        ContractAccountEntity cae = new ContractAccountEntityBuilder(context)
                .businessPartner(bpe)
                .paymentType("U")
                .persist()
                .get();

        InvoiceEntity invoice1 = new InvoiceEntityBuilder(context)
                .externalId("INVOICE_1")
                .type(gcl.getCode())
                .contractAccount(cae)
                .persist()
                .get();

        InvoiceEntity invoice2 = new InvoiceEntityBuilder(context)
                .externalId("INVOICE_2")
                .type(gcl.getCode())
                .contractAccount(cae)
                .persist()
                .get();

        String paymentExternalId = "PAYMENT_456";
        PaymentEmbeddable payment = new PaymentEmbeddableBuilder(context)
                .externalId(paymentExternalId)
                .amount(BigDecimal.valueOf(100))
                .get();

        PaymentEmbeddable pairPayment = new PaymentEmbeddableBuilder(context)
                .externalId(paymentExternalId)  // same external ID
                .amount(BigDecimal.valueOf(-100))
                .get();

        InvoiceRawEntity overpaymentRaw = new InvoiceRawEntityBuilder(context)
                .invoice(invoice1)
                .payment(payment)
                .type("FA")
                .amount(BigDecimal.valueOf(100))
                .persist()
                .get();
        overpaymentRaw.setBalancingReason(BalancingReason.TV_15);
        overpaymentRaw.setInvoiceFa(invoice1);

        InvoiceRawEntity pairRaw = new InvoiceRawEntityBuilder(context)
                .invoice(invoice2)
                .payment(pairPayment)
                .type("FA")
                .amount(BigDecimal.valueOf(-100))
                .persist()
                .get();
        pairRaw.setInvoiceFa(invoice2);

        invoice1.addInvoiceRawList(overpaymentRaw);
        invoice2.addInvoiceRawList(pairRaw);

        CustomerAccountEntity cuae = new CustomerAccountEntityBuilder(context).getLogged();

        new InvoiceOwnershipEntityBuilder(context)
                .customerAccount(cuae)
                .grantorCustomerAccount(cuae)
                .invoice(invoice1)
                .persist();
        new InvoiceOwnershipEntityBuilder(context)
                .customerAccount(cuae)
                .grantorCustomerAccount(cuae)
                .invoice(invoice2)
                .persist();

        clean();

        Invoice result = invoiceService.getById(invoice1.getId());

        Assertions.assertNotNull(result, "Invoice should not be null");
        Assertions.assertNotNull(result.getPayments(), "Payments should not be null");
        Assertions.assertEquals(1, result.getPayments().size(), "Should return one payment when pair invoice row exists");

        Payment payment1 = result.getPayments().get(0);
        Assertions.assertEquals(SettlementType.PAYED_FROM_OVERPAYMENT, payment1.getSettlementType(), "Payment should have PAYED_FROM_OVERPAYMENT settlement type");
        Assertions.assertEquals(paymentExternalId, payment1.getExternalId(), "Payment external ID should match");
        Assertions.assertEquals(0, BigDecimal.valueOf(100).compareTo(payment1.getAmount()), "Payment amount should match");
        Assertions.assertNotNull(payment1.getRelatedInvoiceId(), "Related invoice IDs should not be null");
        Assertions.assertEquals(1, payment1.getRelatedInvoiceId().size(), "Should have one related invoice ID");
        Assertions.assertEquals(invoice2.getId(), payment1.getRelatedInvoiceId().get(0), "Related invoice ID should match pair invoice");
    }

    @Test
    @WithNzpUserMock(customerUuid = "5c9e828b-5fb4-444e-afb4-860bfdd911b1")
    public void findInvoiceByBusinessPartner() {
        BusinessPartnerEntity bpe1 = new BusinessPartnerEntityBuilder(context)
                .externalId("EXT1")
                .queue(BusinessPartnerQueue.COLLECTIVE)
                .persist()
                .get();

        FTSearchUtils.createSearchable(bpe1)
                .ifPresent(bpe1::setNameSearchable);

        BusinessPartnerEntity bpe2 = new BusinessPartnerEntityBuilder(context)
                .externalId("EXT2")
                .queue(BusinessPartnerQueue.COLLECTIVE)
                .persist()
                .get();

        GenericCodeListEntity gcl = new GenericCodeListEntityBuilder(context)
                .code("ZD")
                .type("INVOICE_TYPE")
                .persist().get();

        GenericCodeListEntity gc2 = new GenericCodeListEntityBuilder(context)
                .code("U")
                .type("PAYMENT_TYPE")
                .persist().get();

        ContractAccountEntity cae = new ContractAccountEntityBuilder(context)
                .businessPartner(bpe1)
                .paymentType("U")
                .persist()
                .get();

        ContractAccountEntity cae2 = new ContractAccountEntityBuilder(context)
                .businessPartner(bpe2)
                .persist()
                .get();

        UnitedDeliveryPointEntity udpe = new UnitedDeliveryPointEntityBuilder(context).businessPartner(bpe1).persist().get();
        UnitedDeliveryPointEntity udpe2 = new UnitedDeliveryPointEntityBuilder(context).businessPartner(bpe2).persist().get();

        DeliveryPointEntity dpe = new DeliveryPointEntityBuilder(context)
                .persist()
                .get();

        DeliveryPointEntity dpe2 = new DeliveryPointEntityBuilder(context)
                .persist()
                .get();

        ContractEntity contract = new ContractEntityBuilder(context)
                .deliveryPointEntity(dpe)
                .unitedDeliveryPointEntity(udpe)
                .businessPartner(bpe1)
                .persist().get();

        ContractEntity contract2 = new ContractEntityBuilder(context)
                .deliveryPointEntity(dpe2)
                .unitedDeliveryPointEntity(udpe2)
                .businessPartner(bpe2)
                .persist().get();

        InvoiceEntity ie = new InvoiceEntityBuilder(context)
                .externalId("InvoiceExt")
                .status(InvoiceStatus.PAID)
                .type(InvoiceTypeGroup.ADVANCE_INVOICE)
                .type(gcl.getCode())
                .contractAccount(cae)
                .amount(BigDecimal.TEN)
                .dueAt(localDateTime("2020-01-01 00:00:00,000", DTF).toLocalDate())
                .issueAt(localDateTime("2020-01-02 00:00:00,000", DTF).toLocalDate())
                .vs("vs")
                .persist()
                .get();

        InvoiceEntity ie2 = new InvoiceEntityBuilder(context)
                .externalId("InvoiceExt2")
                .status(InvoiceStatus.PAID)
                .type(InvoiceTypeGroup.ADVANCE_INVOICE)
                .type(gcl.getCode())
                .contractAccount(cae2)
                .amount(BigDecimal.TEN)
                .dueAt(localDateTime("2020-01-01 00:00:00,000", DTF).toLocalDate())
                .issueAt(localDateTime("2020-01-02 00:00:00,000", DTF).toLocalDate())
                .vs("vs")
                .persist()
                .get();

        CustomerAccountEntity cuae1 = new CustomerAccountEntityBuilder(context).getLogged();

        UnitedDeliveryPointOwnershipEntity dpoe1 = new UnitedDeliveryPointOwnershipEntityBuilder(context, cuae1, udpe)
                .persistWithInvoiceOwnership(ie)
                .get();

        UnitedDeliveryPointOwnershipEntity dpoe2 = new UnitedDeliveryPointOwnershipEntityBuilder(context, cuae1, udpe2)
                .persistWithInvoiceOwnership(ie2)
                .get();

        // flush/clear
        clean();

        InvoiceSearch invoiceSearch = new InvoiceSearch();
        invoiceSearch.setBusinessPartnerFt("XT1");

        PagedResponse<InvoiceSummary> result = invoiceService.getInvoices(invoiceSearch);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.getResult().size());

        InvoiceSummary invoiceSummary = result.getResult().stream().findFirst().get();

        Assertions.assertEquals(ie.getId(), invoiceSummary.getId());
        Assertions.assertEquals(ie.getBusinessPartner().getExternalId(), invoiceSummary.getBusinessPartner().getExternalId());
        Assertions.assertEquals(ie.getContractAccount().getExternalId(), invoiceSummary.getContractAccount().getExternalId());
        Assertions.assertEquals(ie.getContractAccount().getPaymentType(), invoiceSummary.getContractAccount().getPaymentType().getCode());


    }

    @WithNzpUserMock(customerUuid = "5c9e828b-5fb4-444e-afb4-860bfdd911b1")
    @ParameterizedTest
    @EnumSource(BusinessPartnerKind.class)
    public void getPaymentInfoTest_whenSingleContractAccount_thenContractAccountExternalIdAsVS(BusinessPartnerKind bpKind) throws InputValidationException {
        BusinessPartnerEntity bpe1 = new BusinessPartnerEntityBuilder(context)
                .externalId("EXT1")
                .queue(BusinessPartnerQueue.COLLECTIVE)
                .kind(bpKind)
                .persist()
                .get();

        UnitedDeliveryPointEntity udpe = new UnitedDeliveryPointEntityBuilder(context).businessPartner(bpe1).persist().get();

        DeliveryPointEntity dpe = new DeliveryPointEntityBuilder(context)
                .persist()
                .get();

        ContractEntity contractEntity = new ContractEntityBuilder(context)
                .deliveryPointEntity(dpe)
                .unitedDeliveryPointEntity(udpe)
                .businessPartner(bpe1)
                .persist()
                .get();

        ContractAccountEntity cae = new ContractAccountEntityBuilder(context)
                .businessPartner(bpe1)
                .externalId("************")
                .persist()
                .get();

        InvoiceEntity ie = new InvoiceEntityBuilder(context)
                .externalId("InvoiceExt")
                .status(InvoiceStatus.PAID)
                .type(InvoiceTypeGroup.ADVANCE_INVOICE)
                .contractAccount(cae)
                .amount(BigDecimal.TEN)
                .unpaid(BigDecimal.ZERO)
                .dueAt(localDateTime("2020-01-01 00:00:00,000", DTF).toLocalDate())
                .issueAt(localDateTime("2020-01-02 00:00:00,000", DTF).toLocalDate())
                .vs("************")
                .persist()
                .get();

        new InvoiceContractEntityBuilder(context)
                .invoice(ie.getExternalId())
                .contract(contractEntity.getExternalId())
                .persist();

        CustomerAccountEntity cuae1 = new CustomerAccountEntityBuilder(context).getLogged();

        UnitedDeliveryPointOwnershipEntity dpoe1 = new UnitedDeliveryPointOwnershipEntityBuilder(context, cuae1, udpe).persistWithInvoiceOwnership(ie).get();
        // flush/clear
        clean();

        LocalDate now = LocalDate.now();
        List<InvoiceEntity> invoices = new ArrayList();

        // 3 overdue invoices, 90 SKK total, 12 paid
        invoices.add(getInvoice(now.minusDays(1), 9, null,cae));
        invoices.add(getInvoice(now.minusDays(1), 70, 12F, cae));
        invoices.add(getInvoice(now.minusDays(1), 11, null, cae));

        // 2 invoices, 63 SKK total, 10 paid
        invoices.add(getInvoice(now.plusDays(1), 50, 10F, cae));
        invoices.add(getInvoice(now.plusDays(1), 13, null, cae));

        Mockito.doReturn(invoices).when(queryDslRepositoryMock).findByQuery(Mockito.any(InvoiceSearchQueryProvider.class));
        Mockito.doReturn(TEST_PAY_BY_SQUARE_STRING).when(payBySquareService).generate(anyString(), anyString(), any(BigDecimal.class), anyString(), anyString());

        // queryDslRepository.findByQuery
        InvoicePaymentInfo paymentInfo = invoiceServiceMock.getInvoicePaymentInfo(ie.getId(), Set.of(contractEntity.getId()), true);

        Assertions.assertNotNull(paymentInfo);
        Assertions.assertEquals(5, paymentInfo.getInvoicePaymentSummary().getUnpaid().getCount());
        Assertions.assertEquals(0, BigDecimal.valueOf(131).compareTo(paymentInfo.getInvoicePaymentSummary().getUnpaid().getAmount()));
        Assertions.assertEquals(3, paymentInfo.getInvoicePaymentSummary().getUnpaidExpired().getCount());
        Assertions.assertEquals(0, BigDecimal.valueOf(78).compareTo(paymentInfo.getInvoicePaymentSummary().getUnpaidExpired().getAmount()));
        Assertions.assertEquals(paymentInfo.getQueue(), bpe1.getQueue());
        // Assert single invoice
        Assertions.assertNotNull(paymentInfo.getSingleInvoice());
        Assertions.assertEquals(0, ie.getUnpaid().compareTo(paymentInfo.getSingleInvoice().getAmount()));
        Assertions.assertEquals(deleteLeadingZeros(!BusinessPartnerKind.HOME.equals(bpKind) ? ie.getReference() : cae.getExternalId(), 10), paymentInfo.getSingleInvoice().getVs());   // VS has max 10 digits, leading zeros are deleted if more than 10 digits
        Assertions.assertEquals(SINGLE_INVOICE_TEST_NOTE, paymentInfo.getSingleInvoice().getRefererNote());
        Assertions.assertNotNull(paymentInfo.getSingleInvoice().getBanks());
        Assertions.assertTrue(paymentInfo.getSingleInvoice().getBanks().size() > 0);
        assertAllBankAttributesAreFilled(paymentInfo.getSingleInvoice().getBanks());
        verify(payBySquareService, times(paymentInfo.getSingleInvoice().getBanks().size())).generate(eq(paymentInfo.getSingleInvoice().getVs()), eq(paymentInfo.getSingleInvoice().getRefererNote()), eq(paymentInfo.getSingleInvoice().getAmount()), anyString(), anyString());
        // Assert multi invoice
        Assertions.assertNotNull(paymentInfo.getMultipleInvoices());
        Assertions.assertEquals(0, paymentInfo.getInvoicePaymentSummary().getUnpaid().getAmount().compareTo(paymentInfo.getMultipleInvoices().getAmount()));
        Assertions.assertEquals(deleteLeadingZeros(cae.getExternalId(), 10), paymentInfo.getMultipleInvoices().getVs());    // VS has max 10 digits, leading zeros are deleted if more than 10 digits
        Assertions.assertEquals(MULTI_INVOICE_TEST_NOTE, paymentInfo.getMultipleInvoices().getRefererNote());
        Assertions.assertNotNull(paymentInfo.getMultipleInvoices().getBanks());
        Assertions.assertTrue(paymentInfo.getMultipleInvoices().getBanks().size() > 0);
        assertAllBankAttributesAreFilled(paymentInfo.getMultipleInvoices().getBanks());
        verify(payBySquareService, times(paymentInfo.getMultipleInvoices().getBanks().size())).generate(eq(paymentInfo.getMultipleInvoices().getVs()), eq(paymentInfo.getMultipleInvoices().getRefererNote()), eq(paymentInfo.getMultipleInvoices().getAmount()), anyString(), anyString());
    }

    @WithNzpUserMock(customerUuid = "5c9e828b-5fb4-444e-afb4-860bfdd911b1")
    @ParameterizedTest
    @EnumSource(BusinessPartnerKind.class)
    public void getPaymentInfoTest_multipleContractAccount(BusinessPartnerKind bpKind) throws InputValidationException {
        BusinessPartnerEntity bpe1 = new BusinessPartnerEntityBuilder(context)
                .externalId("************")
                .queue(BusinessPartnerQueue.COLLECTIVE)
                .kind(bpKind)
                .persist()
                .get();

        UnitedDeliveryPointEntity udpe = new UnitedDeliveryPointEntityBuilder(context).businessPartner(bpe1).persist().get();

        DeliveryPointEntity dpe = new DeliveryPointEntityBuilder(context)
                .persist()
                .get();

        ContractEntity contractEntity = new ContractEntityBuilder(context)
                .deliveryPointEntity(dpe)
                .unitedDeliveryPointEntity(udpe)
                .businessPartner(bpe1)
                .persist()
                .get();

        ContractEntity contractEntity2 = new ContractEntityBuilder(context)
                .deliveryPointEntity(dpe)
                .unitedDeliveryPointEntity(udpe)
                .businessPartner(bpe1)
                .persist()
                .get();

        ContractAccountEntity cae = new ContractAccountEntityBuilder(context)
                .businessPartner(bpe1)
                .externalId("************")
                .persist()
                .get();

        ContractAccountEntity cae2 = new ContractAccountEntityBuilder(context)
                .businessPartner(bpe1)
                .externalId("************")
                .persist()
                .get();

        InvoiceEntity ie = new InvoiceEntityBuilder(context)
                .externalId("InvoiceExt")
                .status(InvoiceStatus.PAID)
                .type(InvoiceTypeGroup.ADVANCE_INVOICE)
                .contractAccount(cae)
                .amount(BigDecimal.TEN)
                .unpaid(BigDecimal.ZERO)
                .dueAt(localDateTime("2020-01-01 00:00:00,000", DTF).toLocalDate())
                .issueAt(localDateTime("2020-01-02 00:00:00,000", DTF).toLocalDate())
                .vs("************")
                .persist()
                .get();

        CustomerAccountEntity cuae1 = new CustomerAccountEntityBuilder(context).getLogged();

        UnitedDeliveryPointOwnershipEntity dpoe1 = new UnitedDeliveryPointOwnershipEntityBuilder(context, cuae1, udpe).persistWithInvoiceOwnership(ie).get();
        // flush/clear
        clean();

        LocalDate now = LocalDate.now();
        List<InvoiceEntity> invoices = new ArrayList();

        // 3 overdue invoices, 90 SKK total, 12 paid
        invoices.add(getInvoice(now.minusDays(1), 9, null,cae));
        invoices.add(getInvoice(now.minusDays(1), 70, 12F, cae));
        invoices.add(getInvoice(now.minusDays(1), 11, null, cae));

        // 2 invoices, 63 SKK total, 10 paid
        invoices.add(getInvoice(now.plusDays(1), 50, 10F, cae));
        invoices.add(getInvoice(now.plusDays(1), 13, null, cae));

        // 1 invoice. 50 SKK total, 10 paid - this is related to different contract account
        invoices.add(getInvoice(now.plusDays(1), 50, 10F, cae2));

        Mockito.doReturn(invoices).when(queryDslRepositoryMock).findByQuery(Mockito.any(InvoiceSearchQueryProvider.class));
        Mockito.doReturn(TEST_PAY_BY_SQUARE_STRING).when(payBySquareService).generate(anyString(), anyString(), any(BigDecimal.class), anyString(), anyString());

        // queryDslRepository.findByQuery
        InvoicePaymentInfo paymentInfo = invoiceServiceMock.getInvoicePaymentInfo(ie.getId(), Set.of(contractEntity.getId(), contractEntity2.getId()), true);

        Assertions.assertNotNull(paymentInfo);
        Assertions.assertEquals(6, paymentInfo.getInvoicePaymentSummary().getUnpaid().getCount());
        Assertions.assertEquals(0, BigDecimal.valueOf(171).compareTo(paymentInfo.getInvoicePaymentSummary().getUnpaid().getAmount()));
        Assertions.assertEquals(3, paymentInfo.getInvoicePaymentSummary().getUnpaidExpired().getCount());
        Assertions.assertEquals(0, BigDecimal.valueOf(78).compareTo(paymentInfo.getInvoicePaymentSummary().getUnpaidExpired().getAmount()));
        Assertions.assertEquals(paymentInfo.getQueue(), bpe1.getQueue());
        // Assert single invoice
        Assertions.assertNotNull(paymentInfo.getSingleInvoice());
        Assertions.assertEquals(0, ie.getUnpaid().compareTo(paymentInfo.getSingleInvoice().getAmount()));
        Assertions.assertEquals(deleteLeadingZeros(!BusinessPartnerKind.HOME.equals(bpKind) ? ie.getReference() : cae.getExternalId(), 10), paymentInfo.getSingleInvoice().getVs());
        Assertions.assertEquals(SINGLE_INVOICE_TEST_NOTE, paymentInfo.getSingleInvoice().getRefererNote());
        Assertions.assertNotNull(paymentInfo.getSingleInvoice().getBanks());
        Assertions.assertFalse(paymentInfo.getSingleInvoice().getBanks().isEmpty());
        assertAllBankAttributesAreFilled(paymentInfo.getSingleInvoice().getBanks());
        verify(payBySquareService, times(paymentInfo.getSingleInvoice().getBanks().size())).generate(eq(paymentInfo.getSingleInvoice().getVs()), eq(paymentInfo.getSingleInvoice().getRefererNote()), eq(paymentInfo.getSingleInvoice().getAmount()), anyString(), anyString());
        // Assert multi invoice
        Assertions.assertNotNull(paymentInfo.getMultipleInvoices());
        Assertions.assertEquals(0, paymentInfo.getInvoicePaymentSummary().getUnpaid().getAmount().compareTo(paymentInfo.getMultipleInvoices().getAmount()));
        Assertions.assertEquals(deleteLeadingZeros(bpe1.getExternalId(), 10), paymentInfo.getMultipleInvoices().getVs());
        Assertions.assertEquals(MULTI_INVOICE_TEST_NOTE, paymentInfo.getMultipleInvoices().getRefererNote());
        Assertions.assertNotNull(paymentInfo.getMultipleInvoices().getBanks());
        Assertions.assertFalse(paymentInfo.getMultipleInvoices().getBanks().isEmpty());
        assertAllBankAttributesAreFilled(paymentInfo.getMultipleInvoices().getBanks());
        verify(payBySquareService, times(paymentInfo.getMultipleInvoices().getBanks().size())).generate(eq(paymentInfo.getMultipleInvoices().getVs()), eq(paymentInfo.getMultipleInvoices().getRefererNote()), eq(paymentInfo.getMultipleInvoices().getAmount()), anyString(), anyString());
    }

    private void assertAllBankAttributesAreFilled(List<PaymentDetail.BankItem> banks) {
        assertTrue(banks.stream().allMatch(bankItem -> StringUtils.isNotEmpty(bankItem.getIban())));
        assertTrue(banks.stream().allMatch(bankItem -> StringUtils.isNotEmpty(bankItem.getName())));
        assertTrue(banks.stream().allMatch(bankItem -> bankItem.getPayBySquare() != null));
        assertTrue(banks.stream().map(x -> x.getPayBySquare()).allMatch(payBySquareItem ->  StringUtils.isNotEmpty(payBySquareItem.getQrCode())));
        // Check only first is preffered
        assertTrue(banks.get(0).isPreffered());
        assertTrue(banks.stream().skip(1).allMatch(bankItem -> !bankItem.isPreffered()));
    }

    @Test
    @WithNzpUserMock(customerUuid = "5c9e828b-5fb4-444e-afb4-860bfdd911b1")
    public void transactionSummaryTest(){
        DeliveryPointEntity dpe = new DeliveryPointEntityBuilder(context).persist().get();
        ContractEntity ce = new ContractEntityBuilder(context).deliveryPointEntity(dpe).persist().get();
        CustomerAccountEntity cae = new CustomerAccountEntityBuilder(context).getLogged();
        InvoiceEntity ie = new InvoiceEntityBuilder(context).persist().get();
        InvoiceEntity ie2 = new InvoiceEntityBuilder(context).persist().get();
        InvoiceEntity ie3 = new InvoiceEntityBuilder(context).persist().get();
        new InvoiceContractEntityBuilder(context).contract(ce.getExternalId()).invoice(ie.getExternalId()).persist();
        new InvoiceContractEntityBuilder(context).contract(ce.getExternalId()).invoice(ie2.getExternalId()).persist();
        new InvoiceContractEntityBuilder(context).contract(ce.getExternalId()).invoice(ie3.getExternalId()).persist();
        new InvoiceOwnershipEntityBuilder(context).customerAccount(cae).grantorCustomerAccount(cae).invoice(ie).persist();
        new InvoiceOwnershipEntityBuilder(context).customerAccount(cae).grantorCustomerAccount(cae).invoice(ie2).persist();
        new InvoiceOwnershipEntityBuilder(context).customerAccount(cae).grantorCustomerAccount(cae).invoice(ie3).persist();



        CustomerTransactionEntity cte = new CustomerTransactionEntityBuilder(context)
                .customer(new CustomerAccountEntityBuilder(context).getLogged())
                .amount(BigDecimal.valueOf(15))
                .persist().get();
        new CustomerTransactionInvoiceEntityBuilder(context)
                .customerTransaction(cte)
                .invoice(ie)
                .persist();
        new CustomerTransactionInvoiceEntityBuilder(context)
                .customerTransaction(cte)
                .invoice(ie2)
                .persist();

        CustomerTransactionEntity cte2 = new CustomerTransactionEntityBuilder(context)
                .customer(new CustomerAccountEntityBuilder(context).getLogged())
                .amount(BigDecimal.TEN)
                .persist().get();
        new CustomerTransactionInvoiceEntityBuilder(context)
                .customerTransaction(cte2)
                .invoice(ie2)
                .persist();
        new CustomerTransactionInvoiceEntityBuilder(context)
                .customerTransaction(cte2)
                .invoice(ie3)
                .persist();

        clean();
        InvoicePaymentSummary summary = invoiceService.getInvoiceSummaryByContractIds(Set.of(ce.getId()),false);
        Assertions.assertNull(summary.getTransactionSummary());
        }

    @Test
    @WithNzpUserMock(customerUuid = "5c9e828b-5fb4-444e-afb4-860bfdd911b1")
    public void getInvoiceSummaryTest() {

        BusinessPartnerEntity bpe1 = new BusinessPartnerEntityBuilder(context)
                .externalId("EXT1")
                .queue(BusinessPartnerQueue.COLLECTIVE)
                .persist()
                .get();

        ContractAccountEntity cae = new ContractAccountEntityBuilder(context)
                .businessPartner(bpe1)
                .persist()
                .get();

        LocalDate now = LocalDate.now();
        List<InvoiceEntity> invoices = new ArrayList();

        // 3 overdue invoices, 90 SKK total, 12 paid
        invoices.add(getInvoice(now.minusDays(1), 9, null, cae));
        invoices.add(getInvoice(now.minusDays(1), 70, 12F, cae));
        invoices.add(getInvoice(now.minusDays(1), 11, null, cae));

        // 2 invoices, 63 SKK total, 10 paid
        invoices.add(getInvoice(now.plusDays(1), 50, 10F, cae));
        invoices.add(getInvoice(now.plusDays(1), 13, null, cae));

        Mockito.doReturn(invoices).when(queryDslRepositoryMock).findByQuery(Mockito.any(InvoiceSearchQueryProvider.class));

        // queryDslRepository.findByQuery
        InvoicePaymentSummary summary = invoiceServiceMock.getInvoiceSummaryByContractIds(Set.of(UUID.randomUUID().toString()),false);

        Assertions.assertNotNull(summary);
        Assertions.assertEquals(5, summary.getUnpaid().getCount());
        Assertions.assertEquals(0, BigDecimal.valueOf(131).compareTo(summary.getUnpaid().getAmount()));
        Assertions.assertEquals(3, summary.getUnpaidExpired().getCount());
        Assertions.assertEquals(0, BigDecimal.valueOf(78).compareTo(summary.getUnpaidExpired().getAmount()));
    }

    private InvoiceEntity getInvoice(LocalDate dueAt, float amount, Float paid, ContractAccountEntity cae) {
        InvoiceEntity ie = new InvoiceEntityBuilder(context)
                .externalId("InvoiceExt")
                .status(InvoiceStatus.UNPAID)
                .type(InvoiceTypeGroup.ADVANCE_INVOICE)
                .amount(BigDecimal.valueOf(amount))
                .unpaid(BigDecimal.valueOf(amount))
                .contractAccount(cae)
                .dueAt(dueAt)
                .issueAt(localDateTime("2020-01-02 00:00:00,000", DTF).toLocalDate())
                .vs("vs").get();
        if(paid != null) {
            PaymentEmbeddable paymentEmbeddable = new PaymentEmbeddableBuilder(context).amount(BigDecimal.valueOf(paid)).get();
            InvoiceRawEntity ire1 = new InvoiceRawEntityBuilder(context)
                    .invoice(ie)
                    .contractAccountId(ExternalIdHash.encodeExternalId("CA_ID"))
                    .payment(paymentEmbeddable)
                    .status(InvoiceStatus.PAID)
                    .amount(BigDecimal.valueOf(paid))
                    .get();
            ie.addInvoiceRawList(ire1);
            if (amount > paid) {
                InvoiceRawEntity ire2 = new InvoiceRawEntityBuilder(context)
                        .invoice(ie)
                        .contractAccountId(ExternalIdHash.encodeExternalId("CA_ID"))
                        .amount(BigDecimal.valueOf(amount - paid))
                        .status(InvoiceStatus.UNPAID)
                        .get();
                ie.addInvoiceRawList(ire2);
                ie.setUnpaid(ire2.getAmount());
            }
        }
        return ie;
    }
}
