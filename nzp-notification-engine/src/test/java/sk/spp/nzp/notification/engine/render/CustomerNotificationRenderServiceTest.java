package sk.spp.nzp.notification.engine.render;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import sk.spp.nzp.AbstractTest;
import sk.spp.nzp.be.customeraccess.builder.CustomerAccountEntityBuilder;
import sk.spp.nzp.be.notification.builder.CustomerNotificationEntityBuilder;
import sk.spp.nzp.be.notification.builder.NotificationTemplateEntityBuilder;
import sk.spp.nzp.be.notification.builder.NotificationTemplateI18NEntityBuilder;
import sk.spp.nzp.commons.api.customeraccess.enums.CustomerAccountStatus;
import sk.spp.nzp.commons.api.customeraccess.enums.CustomerAccountType;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceTypeGroup;
import sk.spp.nzp.commons.api.notification.NotificationContextAttributes;
import sk.spp.nzp.commons.api.notification.NotificationTemplateI18NDto;
import sk.spp.nzp.commons.api.notification.enums.*;
import sk.spp.nzp.commons.mapper.notification.render.CustomerNotificationRenderMapper;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;
import sk.spp.nzp.commons.model.notification.CustomerNotificationEntity;
import sk.spp.nzp.commons.model.notification.NotificationTemplateEntity;
import sk.spp.nzp.commons.model.notification.NotificationTemplateI18NEntity;
import sk.spp.nzp.commons.model.notification.enums.NotificationCode;
import sk.spp.nzp.commons.notification.context.Customer;
import sk.spp.nzp.commons.notification.context.Invoice;
import sk.spp.nzp.commons.notification.context.Logged;
import sk.spp.nzp.commons.repository.notification.CustomerNotificationEntityRepository;
import sk.spp.nzp.commons.service.notification.CustomerNotificationRenderRequest;
import sk.spp.nzp.commons.service.notification.CustomerNotificationRenderService;
import sk.spp.nzp.commons.service.notification.NotificationRenderJobData;
import sk.spp.nzp.notification.engine.builder.CustomerNotificationRenderStatusEntityBuilder;

import javax.persistence.EntityManager;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class CustomerNotificationRenderServiceTest extends AbstractTest {

    @Autowired
    protected EntityManager em;

    @Autowired
    private CustomerNotificationRenderService customerNotificationRenderService;
    @Autowired
    private CustomerNotificationRenderMapper customerNotificationRenderMapper;
    @Autowired
    private CustomerNotificationEntityRepository customerNotificationEntityRepository;

    private NotificationContextAttributes attributes;
    private NotificationTemplateEntity notificationTemplateEntity;
    private CustomerAccountEntity customer;

    @BeforeEach
    public void before() {

        attributes = new NotificationContextAttributes();
        Map<String, Object> inner = new HashMap<>();
        inner.put("testBody", "body");
        inner.put("testSubject", "subject");
        inner.put("testSms", "sms");
        inner.put("testPushTitle", "pushTitle");
        inner.put("testPushBody", "pushBody");
        inner.put("testPushText", "pushText");
        inner.put("testPushRedirection", "pushRedirection");
        inner.put("testHeader", "header");
        inner.put("testHeaderUrl", "headerUrl");
        inner.put("nullAttribute", null);

        attributes.setAttributes(inner);

        Logged logged = new Logged();
        Customer loggedCustomer = new Customer();
        loggedCustomer.setEmail("loggedCustomerEmail");
        logged.setCustomer(loggedCustomer);
        attributes.setLogged(logged);

        attributes.setLocale("SK");

        Invoice invoice = new Invoice();
        invoice.setTypeGroup(InvoiceTypeGroup.CREDIT);
        attributes.setInvoice(invoice);

        customer = new CustomerAccountEntityBuilder(context)
                .status(CustomerAccountStatus.ACTIVE)
                .email("sasasas")
                .externalId("EXT_CAE1")
                .type(CustomerAccountType.CUSTOMER)
                .persist()
                .get();

        notificationTemplateEntity = new NotificationTemplateEntityBuilder(context)
                .code(NotificationCode.BUSINESS_PARTNER_PAIRING_REQUEST)
                .status(NotificationTemplateStatus.ACTIVE)
                .type(NotificationTemplateType.CUSTOMER_NOTICE)
                .name("name1")
                .enableEmail(true)
                .enableSms(true)
                .enablePush(true)
                .enablePortal(true)
                .priority(NotificationTemplatePriority.HIGH)
                .description("description1")
                .attributes(Map.of("key", "value"))
                .persist()
                .get();

        new NotificationTemplateI18NEntityBuilder(context)
                .emailBody("${attributes.testBody}_${logged.customer.email}_${locale}_${invoice.typeGroup}_${notificationUuid}_${attributes.nullAttribute!\"\"}")
                .emailSubject("${attributes.testSubject}")
                .smsBody("${attributes.testSms}")
                .pushTitle("${attributes.testPushTitle}")
                .pushBody("${attributes.testPushBody}")
                .pushText("${attributes.testPushText}")
                .pushRedirection("${attributes.testPushRedirection}")
                .header("${attributes.testHeader}")
                .headerUrl("${attributes.testHeaderUrl}")
                .notificationTemplate(notificationTemplateEntity)
                .locale("SK")
                .persist();


        clean();
    }


    @Test
    public void testService() {
        CustomerNotificationEntity customerNotificationEntity = new CustomerNotificationEntityBuilder(context)
                .attributes(attributes)
                .source(CustomerNotificationSource.NZP)
                .status(CustomerNotificationStatus.CREATED)
                .header("H")
                .headerUrl("HU")
                .locale("SK")
                .notificationTemplate(notificationTemplateEntity)
                .persist()
                .get();

        new CustomerNotificationRenderStatusEntityBuilder(context)
                .customerNotification(customerNotificationEntity)
                .priority(CustomerNotificationRenderStatusPriority.HIGH)
                .persist();

        clean();

        CustomerNotificationEntity notification = customerNotificationEntityRepository.findById(customerNotificationEntity.getId()).get();

        NotificationRenderJobData notificationRenderJobData = customerNotificationRenderService.renderCustomerNotification(preProcess(notification));

        customerNotificationRenderMapper.mapJobData(notificationRenderJobData, notification);

        Assertions.assertEquals(notification.getEmailBody(), "body_loggedCustomerEmail_SK_CREDIT_" + customerNotificationEntity.getId().toString() + "_");
        Assertions.assertEquals(notification.getEmailSubject(), "subject");
        Assertions.assertEquals(notification.getSmsBody(), "sms");
        Assertions.assertEquals(notification.getPushTitle(), "pushTitle");
        Assertions.assertEquals(notification.getPushBody(), "pushBody");
        Assertions.assertEquals(notification.getPushText(), "pushText");
        Assertions.assertEquals(notification.getPushRedirection(), "pushRedirection");
        Assertions.assertEquals(notification.getHeader(), "header");
        Assertions.assertEquals(notification.getHeaderUrl(), "headerUrl");
    }

    private CustomerNotificationRenderRequest preProcess(CustomerNotificationEntity entity) {
        Optional<NotificationTemplateEntity> template = Optional.ofNullable(entity.getNotificationTemplate());

        CustomerNotificationRenderRequest dto = new CustomerNotificationRenderRequest()
                .setId(entity.getId().toString())
                .setAttributes(entity.getAttributes() == null ? new NotificationContextAttributes() : entity.getAttributes());

        template.ifPresent(nt -> {
            dto.setNotificationTemplateCode(nt.getCode());
            dto.setEnableEmail(nt.isEnableEmail());
            dto.setEnablePortal(nt.isEnablePortal());
            dto.setEnableSms(nt.isEnableSms());
            dto.setEnablePush(nt.isEnablePush());
        });
        if (entity.getLocale() != null) {
            String locale = entity.getLocale().trim().toLowerCase();

            List<NotificationTemplateI18NEntity> i18ns = template
                    .map(NotificationTemplateEntity::getI18ns)
                    .orElse(List.of());

            for (NotificationTemplateI18NEntity i18n : i18ns) {
                if (localeEquals(i18n, locale)) {

                    dto.setNotificationTemplateI18n(map(i18n));
                    break;
                }
            }
        }

        return dto;
    }

    private boolean localeEquals(NotificationTemplateI18NEntity i18n, String locale) {
        if (i18n.getLocale() != null) {
            String entityLocale = i18n.getLocale().trim().toLowerCase();

            if (entityLocale.equals(locale)) {
                return true;
            }
            if (entityLocale.length() > locale.length() && entityLocale.startsWith(locale)) {
                return true;
            }
            if (entityLocale.length() < locale.length() && locale.startsWith(entityLocale)) {
                return true;
            }
        }

        return false;
    }

    private NotificationTemplateI18NDto map(NotificationTemplateI18NEntity entity) {
        return new NotificationTemplateI18NDto()
                .setHeader(entity.getHeader())
                .setHeaderUrl(entity.getHeaderUrl())
                .setEmailSubject(entity.getEmailSubject())
                .setEmailBody(entity.getEmailBody())
                .setSmsBody(entity.getSmsBody())
                .setPushTitle(entity.getPushTitle())
                .setPushBody(entity.getPushBody())
                .setPushText(entity.getPushText())
                .setPushRedirection(entity.getPushRedirection())
                .setLocale(entity.getLocale());
    }
}
