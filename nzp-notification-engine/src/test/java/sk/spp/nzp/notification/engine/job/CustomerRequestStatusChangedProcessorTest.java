package sk.spp.nzp.notification.engine.job;

import org.assertj.core.api.AbstractOptionalAssert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import sk.spp.nzp.AbstractTest;
import sk.spp.nzp.be.customeraccess.builder.CustomerAccountEntityBuilder;
import sk.spp.nzp.be.customerrequest.builder.CustomerRequestEntityBuilder;
import sk.spp.nzp.be.customerrequest.builder.CustomerRequestTemplateEntityBuilder;
import sk.spp.nzp.be.notification.builder.NotificationTemplateEntityBuilder;
import sk.spp.nzp.commons.api.customerrequest.enums.CustomerRequestStatus;
import sk.spp.nzp.commons.batch.Job;
import sk.spp.nzp.commons.context.CustomerNotificationContext;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestEntity;
import sk.spp.nzp.commons.model.customerrequest.CustomerRequestTemplateEntity;
import sk.spp.nzp.commons.model.notification.NotificationTemplateEntity;
import sk.spp.nzp.commons.model.notification.enums.NotificationCode;
import sk.spp.nzp.commons.repository.customerrequest.CustomerRequestRepository;
import sk.spp.nzp.commons.service.notification.NotifyService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class CustomerRequestStatusChangedProcessorTest extends AbstractTest {

    @Autowired
    private CustomerRequestRepository customerRequestRepository;
    @MockBean
    private NotifyService notifyService;

    @InjectMocks
    @Autowired
    private CustomerRequestStatusChangedProcessor customerRequestStatusChangedProcessor;

    private NotificationTemplateEntity notificationTemplate;
    private CustomerRequestTemplateEntity requestTemplate;

    @BeforeEach
    public void init() {
        notificationTemplate = new NotificationTemplateEntityBuilder(context)
                .code(NotificationCode.CUSTOMER_REQUEST_STATUS_CHANGE)
                .attributes(Map.of("test-key", "test-value"))
                .persist()
                .get();

        requestTemplate = new CustomerRequestTemplateEntityBuilder(context)
                .persist()
                .get();
    }

    @Test
    public void processSuccess() {
        List<JobDescription> successfulJobs = createDescriptions(2, true);

        List<Job<UUID>> failedJobs = customerRequestStatusChangedProcessor.process(createJobs(successfulJobs), false, "locked-by-id");
        clean();

        verifyNotifyServiceCalls(successfulJobs);
        assertThat(failedJobs).hasSize(0);
        assertRequestsStatus(successfulJobs, true);
    }

    private void verifyNotifyServiceCalls(List<JobDescription> jobDescriptions) {

        ArgumentCaptor<CustomerNotificationContext> contextCaptor = ArgumentCaptor.forClass(CustomerNotificationContext.class);
        Class<Map<String, Object>> mapClass =
                (Class<Map<String, Object>>)(Class)ArrayList.class;
        ArgumentCaptor<Map<String, Object>> attributesCaptor = ArgumentCaptor.forClass(mapClass);

        verify(notifyService, times(jobDescriptions.size()))
                .notify(contextCaptor.capture());

        List<UUID> expectedRequestIds = jobDescriptions.stream()
                .map(JobDescription::getCustomerRequestId)
                .collect(Collectors.toList());

        assertThat(NotificationCode.CUSTOMER_REQUEST_STATUS_CHANGE.name()).isEqualTo(contextCaptor.getValue().getCode());
    }

    private void assertRequestsStatus(List<JobDescription> jobDescriptions, boolean createdAtFilled) {
        jobDescriptions.stream()
                .forEach(job -> {

                    AbstractOptionalAssert<?, LocalDateTime> assertion = assertThat(customerRequestRepository.findById(job.getCustomerRequestId()))
                            .isPresent()
                            .map(CustomerRequestEntity::getNotificationStatusUpdatedAt);

                    if (createdAtFilled) {
                        assertion.isPresent();
                    } else {
                        assertion.isEmpty();
                    }
                });
    }

    private List<Job<UUID>> createJobs(List<JobDescription> jobDescriptions) {
        return jobDescriptions.stream()
                .map(job -> new Job<UUID>().setId(job.getCustomerRequestId()))
                .collect(Collectors.toCollection(ArrayList::new));
    }

    private List<JobDescription> createDescriptions(int count, boolean createCustomer) {
        List<JobDescription> jobDescriptions = new ArrayList();

        for (int i = 0; i < count; i++) {

            CustomerAccountEntity customer = null;
            if (createCustomer) {
                 customer = new CustomerAccountEntityBuilder(context)
                        .persist()
                        .get();
            }

            CustomerRequestEntity customerRequest = new CustomerRequestEntityBuilder(context)
                    .status(CustomerRequestStatus.CREATED)
                    .customerRequestTemplate(requestTemplate)
                    .customer(customer)
                    .persist()
                    .get();

            jobDescriptions.add(new JobDescription(customerRequest.getId(), customer == null ? null : customer.getId()));
        }
        clean();
        return jobDescriptions;
    }

    private class JobDescription {

        private final UUID customerRequestId;
        private final UUID customerId;

        public JobDescription(
                UUID customerRequestId,
                UUID customerId) {

            this.customerRequestId = customerRequestId;
            this.customerId = customerId;
        }

        public UUID getCustomerRequestId() {
            return customerRequestId;
        }

        public UUID getCustomerId() {
            return customerId;
        }
    }
}
