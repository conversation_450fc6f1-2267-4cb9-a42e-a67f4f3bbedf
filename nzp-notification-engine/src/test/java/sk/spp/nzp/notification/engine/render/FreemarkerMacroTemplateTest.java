package sk.spp.nzp.notification.engine.render;

import freemarker.template.Configuration;
import freemarker.template.Template;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import sk.spp.nzp.AbstractTest;
import sk.spp.nzp.be.codelist.builder.ConfigParameterEntityBuilder;
import sk.spp.nzp.commons.api.notification.NotificationContextAttributes;
import sk.spp.nzp.commons.model.codelist.ConfigParameterEntity;
import sk.spp.nzp.commons.model.codelist.enums.ConfigParameterTarget;
import sk.spp.nzp.commons.model.codelist.enums.ConfigParameterType;
import sk.spp.nzp.commons.notification.context.Customer;

import java.io.StringReader;
import java.io.StringWriter;
import java.io.Writer;

public class FreemarkerMacroTemplateTest extends AbstractTest {

    @Autowired
    @Qualifier("notificationFreemarkerConfiguration")
    private Configuration notificationFreemarkerConfiguration;

    @Test
    public void test_freemarker_macro_test_render() throws Exception {
        new ConfigParameterEntityBuilder(context)
                .id("freemarker.template.test.ftl")
                .value("<#macro test_macro param>HEADER ${param} BODY <#nested> FOOTER</#macro>")
                .target(ConfigParameterTarget.BE)
                .type(ConfigParameterType.STRING)
                .persist();
        flush();

        String template = "<#import \"test.ftl\" as test><@test.test_macro param=\"PARAM\">CONTENT</@test.test_macro>";
        Template emailTemplate = new Template(null, new StringReader(template), notificationFreemarkerConfiguration);
        emailTemplate.setLogTemplateExceptions(false);
        Writer emailWriter = new StringWriter();
        NotificationContextAttributes attributes = new NotificationContextAttributes();
        emailTemplate.process(attributes, emailWriter);
        String content = emailWriter.toString();


        //
        // compare content and expected
        Assertions.assertEquals("HEADER PARAM BODY CONTENT FOOTER", content);

    }
}
