package sk.spp.nzp.notification.engine.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import sk.spp.nzp.AbstractTest;
import sk.spp.nzp.be.customeraccess.builder.CustomerAccountEntityBuilder;
import sk.spp.nzp.be.notification.builder.NotificationScheduleEntityBuilder;
import sk.spp.nzp.be.notification.builder.NotificationTemplateEntityBuilder;
import sk.spp.nzp.be.reporting.builder.ReportEntityBuilder;
import sk.spp.nzp.commons.api.reporting.FilterCondition;
import sk.spp.nzp.commons.api.reporting.FilterOperator;
import sk.spp.nzp.commons.api.reporting.Join;
import sk.spp.nzp.commons.api.reporting.JoinOperator;
import sk.spp.nzp.commons.api.reporting.ReportDefinition;
import sk.spp.nzp.commons.batch.Job;
import sk.spp.nzp.commons.model.customeraccess.CustomerAccountEntity;
import sk.spp.nzp.commons.model.notification.CustomerNotificationEntity;
import sk.spp.nzp.commons.model.notification.NotificationScheduleEntity;
import sk.spp.nzp.commons.model.notification.NotificationTemplateEntity;
import sk.spp.nzp.commons.model.reporting.ReportEntity;
import sk.spp.nzp.commons.repository.notification.CustomerNotificationEntityRepository;

import javax.sql.DataSource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Disabled // jdbcTemplate doesn't work properly in tests
@ExtendWith(MockitoExtension.class)
public class NotificationScheduleProcessorTest extends AbstractTest {

    @Autowired
    private CustomerNotificationEntityRepository notificationRepository;

    @InjectMocks
    @Autowired
    private NotificationScheduleProcessor notificationScheduleProcessor;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private ObjectMapper mapper;

    @Test
    public void processSuccess() throws JsonProcessingException {
        ReportDefinition reportDefinition = buildReportDefinition();

        ReportEntity reportEntity = new ReportEntityBuilder(context)
                .definition(mapper.writeValueAsString(reportDefinition))
                .name("TEST_PROCESS")
                .persist()
                .get();

        NotificationTemplateEntity template = new NotificationTemplateEntityBuilder(context)
                .code("TESTcode")
                .reportCustomerColumn("c_id")
                .report(reportEntity)
                .persist()
                .get();

        NotificationScheduleEntity scheduleEntity = new NotificationScheduleEntityBuilder(context)
                .notificationTemplate(template)
                .persist()
                .get();
        CustomerAccountEntity customerAccountEntity = new CustomerAccountEntityBuilder(context).persist().get();


        Job<UUID> job = new Job<>();
        job.setId(scheduleEntity.getId());

        clean();
        List<Job<UUID>> failedJobs = notificationScheduleProcessor.process(List.of(job), false, "locked-by-id");
        clean();

        assertThat(failedJobs).hasSize(0);
        List<CustomerNotificationEntity> notifications = notificationRepository.findAll();
        assertEquals(notifications.size(), 1);
        CustomerNotificationEntity notification = notifications.get(0);
        assertEquals(notification.getCustomerAccount().getId(), customerAccountEntity.getId());
        assertNotNull(notification.getAttributes().getAttributes());
        Map<String, Object> attributes = notification.getAttributes().getAttributes();
        assertEquals(attributes.get("CUSTOMER_ID"), customerAccountEntity.getId().toString());
        assertEquals(attributes.get("CUSTOMER_EMAIL"), customerAccountEntity.getEmail());
    }

    private ReportDefinition buildReportDefinition() {

        ReportDefinition reportDefinition = new ReportDefinition();
        reportDefinition.setAlias("c");
        reportDefinition.setDataEntity("CustomerAccount");
        reportDefinition.setOutputProps(Arrays.asList("c.id", "c.version", "c.createdAt", "c.updatedAt"));
        FilterCondition filterCondition = new FilterCondition();
        filterCondition.setProperty("c.createdAt");
        filterCondition.setOperator(FilterOperator.IS_GREATER_THAN);
        filterCondition.setValue("2020-01-01T10:20:30");
        FilterCondition filterCondition2 = new FilterCondition();
        filterCondition2.setProperty("c.createdAt");
        filterCondition2.setOperator(FilterOperator.IS_NOT_NULL);
        reportDefinition.setFilterConditions(Arrays.asList(filterCondition, filterCondition2));
        Join join = new Join();
        join.setAlias("a");
        join.setProperty("approvals");
        join.setJoinOp(JoinOperator.OUTER_JOIN);
        reportDefinition.setJoins(Collections.singletonList(join));

        return reportDefinition;
    }

}