package sk.spp.nzp.commons.utils;

import sk.spp.nzp.commons.model.customerprofile.InvoiceRawEntity;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class InvoiceUtil {

    public static List<String> getPaymentExternalIds (Set<InvoiceRawEntity> invoiceRawSet) {
        return invoiceRawSet.stream()
                .filter(irs -> irs!= null && irs.getPayment() != null && irs.getPayment().getExternalId() != null)
                .map(irs -> irs.getPayment().getExternalId())
                .collect(Collectors.toList());
    }
}
