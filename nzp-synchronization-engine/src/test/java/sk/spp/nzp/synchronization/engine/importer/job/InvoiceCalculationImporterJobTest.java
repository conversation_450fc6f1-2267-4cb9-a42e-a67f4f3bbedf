package sk.spp.nzp.synchronization.engine.importer.job;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import sk.spp.nzp.AbstractTest;
import sk.spp.nzp.be.customerprofile.builder.InvoiceEntityBuilder;
import sk.spp.nzp.be.customerprofile.builder.InvoiceRawEntityBuilder;
import sk.spp.nzp.be.customerprofile.builder.PaymentEmbeddableBuilder;
import sk.spp.nzp.commons.api.customerprofile.enums.BalancingReason;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceStatus;
import sk.spp.nzp.commons.model.customerprofile.InvoiceEntity;
import sk.spp.nzp.commons.model.customerprofile.InvoiceRawEntity;
import sk.spp.nzp.commons.model.customerprofile.PaymentEmbeddable;
import sk.spp.nzp.synchronization.engine.importer.BatchImporterJob;
import sk.spp.nzp.synchronization.engine.repository.customerprofile.InvoiceEntityRepository;
import sk.spp.nzp.synchronization.engine.repository.customerprofile.InvoiceRawEntityRepository;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.UUID;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class InvoiceCalculationImporterJobTest extends AbstractTest {

    @Autowired
    private InvoiceCalculationImporterJob invoiceCalculationImporterJob;

    @Qualifier("SynchronizationInvoiceEntityRepository")
    @Autowired
    private InvoiceEntityRepository invoiceRepository;

    @Qualifier("SynchronizationInvoiceRawEntityRepository")
    @Autowired
    private InvoiceRawEntityRepository invoiceRawRepository;


    @Test
    public void processFailure() {
        // create import context with non-existent invoice ID
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportContext<InvoiceCalculationImporterJob.Job> context = invoiceCalculationImporterJob.new ImportContext<>();
        context.setId(UUID.randomUUID());
        InvoiceCalculationImporterJob.Job job = invoiceCalculationImporterJob.new Job().setInvoiceId("NON_EXISTENT_ID");
        context.setBatch(Arrays.asList(job));
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportResult<InvoiceCalculationImporterJob.Job> result = invoiceCalculationImporterJob.new ImportResult<>();

        invoiceCalculationImporterJob.process(context, result);

        assertEquals(0, result.getSuccessJobs().size(), "Should have no successful jobs");
        assertEquals(1, result.getFailedJobs().size(), "Should have one failed job");
    }


    @Test
    public void processPaidStatus() {
        InvoiceEntity invoice = new InvoiceEntityBuilder(context)
                .status(InvoiceStatus.INVALID)
                .type("ZD")
                .persist()
                .get();
        invoice.setInvalidated(true);

        PaymentEmbeddable payment1 = new PaymentEmbeddableBuilder(context)
                .amount(BigDecimal.valueOf(50.0))
                .externalId("PAYMENT_PAID_001")
                .get();
        PaymentEmbeddable payment2 = new PaymentEmbeddableBuilder(context)
                .amount(BigDecimal.valueOf(75.0))
                .externalId("PAYMENT_PAID_002")
                .get();

        // invoice raw entity with PAID status
        InvoiceRawEntity invoiceRawPaid1 = new InvoiceRawEntityBuilder(context)
                .invoice(invoice)
                .item1(1)
                .item2(1)
                .item3(1)
                .amount(BigDecimal.valueOf(50.0))
                .status(InvoiceStatus.PAID)
                .payment(payment1)
                .get();
        invoiceRawPaid1.setExternalId("RAW_PAID_001");
        invoiceRawRepository.save(invoiceRawPaid1);

        // another invoice raw entity with PAID status
        InvoiceRawEntity invoiceRawPaid2 = new InvoiceRawEntityBuilder(context)
                .invoice(invoice)
                .item1(1)
                .item2(2)
                .item3(1)
                .amount(BigDecimal.valueOf(75.0))
                .status(InvoiceStatus.PAID)
                .payment(payment2)
                .get();
        invoiceRawPaid2.setExternalId("RAW_PAID_002");
        invoiceRawRepository.save(invoiceRawPaid2);

        clean();

        // create import context and batch
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportContext<InvoiceCalculationImporterJob.Job> context = invoiceCalculationImporterJob.new ImportContext<>();
        context.setId(UUID.randomUUID());
        InvoiceCalculationImporterJob.Job job = invoiceCalculationImporterJob.new Job().setInvoiceId(invoice.getId());
        context.setBatch(Arrays.asList(job));
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportResult<InvoiceCalculationImporterJob.Job> result = invoiceCalculationImporterJob.new ImportResult<>();

        invoiceCalculationImporterJob.process(context, result);

        clean();

        assertEquals(1, result.getSuccessJobs().size(), "Should have one successful job");
        assertEquals(0, result.getFailedJobs().size(), "Should have no failed jobs");

        InvoiceEntity updatedInvoice = invoiceRepository.findById(invoice.getId()).orElse(null);
        assertNotNull(updatedInvoice, "Invoice should exist");
        assertEquals(InvoiceStatus.PAID, updatedInvoice.getStatus(), "Invoice status should be PAID");
        assertEquals(0, BigDecimal.valueOf(125.0).compareTo(updatedInvoice.getAmount()), "Total amount should be calculated");
        assertEquals(0, BigDecimal.ZERO.compareTo(updatedInvoice.getUnpaid()), "Unpaid amount should be zero");
    }

    @Test
    public void processUnpaidStatus() {
        InvoiceEntity invoice = new InvoiceEntityBuilder(context)
                .status(InvoiceStatus.INVALID)
                .type("ZD")
                .persist()
                .get();
        invoice.setInvalidated(true);

        // invoice raw entity with UNPAID status
        InvoiceRawEntity invoiceRawUnpaid1 = new InvoiceRawEntityBuilder(context)
                .invoice(invoice)
                .item1(1)
                .item2(1)
                .item3(1)
                .amount(BigDecimal.valueOf(60.0))
                .status(InvoiceStatus.UNPAID)
                .get();
        invoiceRawUnpaid1.setExternalId("RAW_UNPAID_001");
        invoiceRawRepository.save(invoiceRawUnpaid1);

        // another invoice raw entity with UNPAID status
        InvoiceRawEntity invoiceRawUnpaid2 = new InvoiceRawEntityBuilder(context)
                .invoice(invoice)
                .item1(1)
                .item2(2)
                .item3(1)
                .amount(BigDecimal.valueOf(40.0))
                .status(InvoiceStatus.UNPAID)
                .get();
        invoiceRawUnpaid2.setExternalId("RAW_UNPAID_002");
        invoiceRawRepository.save(invoiceRawUnpaid2);

        clean();

        // create import context and batch
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportContext<InvoiceCalculationImporterJob.Job> context = invoiceCalculationImporterJob.new ImportContext<>();
        context.setId(UUID.randomUUID());
        InvoiceCalculationImporterJob.Job job = invoiceCalculationImporterJob.new Job().setInvoiceId(invoice.getId());
        context.setBatch(Arrays.asList(job));
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportResult<InvoiceCalculationImporterJob.Job> result = invoiceCalculationImporterJob.new ImportResult<>();

        invoiceCalculationImporterJob.process(context, result);

        clean();

        assertEquals(1, result.getSuccessJobs().size(), "Should have one successful job");
        assertEquals(0, result.getFailedJobs().size(), "Should have no failed jobs");

        InvoiceEntity updatedInvoice = invoiceRepository.findById(invoice.getId()).orElse(null);
        assertNotNull(updatedInvoice, "Invoice should exist");
        assertEquals(InvoiceStatus.UNPAID, updatedInvoice.getStatus(), "Invoice status should be UNPAID");
        assertEquals(0, BigDecimal.valueOf(100.0).compareTo(updatedInvoice.getAmount()), "Total amount should be calculated");
        assertEquals(0, BigDecimal.valueOf(100.0).compareTo(updatedInvoice.getUnpaid()), "Unpaid amount should be calculated");
    }

    @Test
    public void processPartiallyPaidStatus() {
        InvoiceEntity invoice = new InvoiceEntityBuilder(context)
                .status(InvoiceStatus.INVALID)
                .type("ZD")
                .persist()
                .get();
        invoice.setInvalidated(true);

        PaymentEmbeddable payment = new PaymentEmbeddableBuilder(context)
                .amount(BigDecimal.valueOf(80.0))
                .externalId("PAYMENT_PARTIAL_001")
                .get();

        // invoice raw entity with PAID status
        InvoiceRawEntity invoiceRawPaid = new InvoiceRawEntityBuilder(context)
                .invoice(invoice)
                .item1(1)
                .item2(1)
                .item3(1)
                .amount(BigDecimal.valueOf(80.0))
                .status(InvoiceStatus.PAID)
                .payment(payment)
                .get();
        invoiceRawPaid.setExternalId("RAW_PARTIAL_PAID");
        invoiceRawRepository.save(invoiceRawPaid);

        // invoice raw entity with UNPAID status
        InvoiceRawEntity invoiceRawUnpaid = new InvoiceRawEntityBuilder(context)
                .invoice(invoice)
                .item1(1)
                .item2(2)
                .item3(1)
                .amount(BigDecimal.valueOf(45.0))
                .status(InvoiceStatus.UNPAID)
                .get();
        invoiceRawUnpaid.setExternalId("RAW_PARTIAL_UNPAID");
        invoiceRawRepository.save(invoiceRawUnpaid);

        clean();

        // create import context and batch
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportContext<InvoiceCalculationImporterJob.Job> context = invoiceCalculationImporterJob.new ImportContext<>();
        context.setId(UUID.randomUUID());
        InvoiceCalculationImporterJob.Job job = invoiceCalculationImporterJob.new Job().setInvoiceId(invoice.getId());
        context.setBatch(Arrays.asList(job));
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportResult<InvoiceCalculationImporterJob.Job> result = invoiceCalculationImporterJob.new ImportResult<>();

        invoiceCalculationImporterJob.process(context, result);

        clean();

        assertEquals(1, result.getSuccessJobs().size(), "Should have one successful job");
        assertEquals(0, result.getFailedJobs().size(), "Should have no failed jobs");

        InvoiceEntity updatedInvoice = invoiceRepository.findById(invoice.getId()).orElse(null);
        assertNotNull(updatedInvoice, "Invoice should exist");
        assertEquals(InvoiceStatus.PARTIALLY_PAID, updatedInvoice.getStatus(), "Invoice status should be PARTIALLY_PAID");
        assertEquals(0, BigDecimal.valueOf(125.0).compareTo(updatedInvoice.getAmount()), "Total amount should be calculated");
        assertEquals(0, BigDecimal.valueOf(45.0).compareTo(updatedInvoice.getUnpaid()), "Unpaid amount should be calculated");
    }


    @Test
    public void processItemMovedStatus() {
        InvoiceEntity invoice = new InvoiceEntityBuilder(context)
                .status(InvoiceStatus.INVALID)
                .type("ZD")
                .persist()
                .get();
        invoice.setInvalidated(true);

        // payment with unique external ID
        PaymentEmbeddable payment = new PaymentEmbeddableBuilder(context)
                .amount(BigDecimal.valueOf(100.0))
                .externalId("UNIQUE_PAYMENT_001")
                .get();

        // invoice raw entity with TV_03 balancing reason and unique payment
        InvoiceRawEntity invoiceRawWithItemMoved = new InvoiceRawEntityBuilder(context)
                .invoice(invoice)
                .item1(1)
                .item2(1)
                .item3(1)
                .amount(BigDecimal.valueOf(100.0))
                .status(InvoiceStatus.PAID)
                .payment(payment)
                .get();

        invoiceRawWithItemMoved.setExternalId("RAW_ITEM_MOVED");
        invoiceRawWithItemMoved.setBalancingReason(BalancingReason.TV_03);
        invoiceRawRepository.save(invoiceRawWithItemMoved);

        clean();

        // create import context and batch
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportContext<InvoiceCalculationImporterJob.Job> context = invoiceCalculationImporterJob.new ImportContext<>();
        context.setId(UUID.randomUUID());
        InvoiceCalculationImporterJob.Job job = invoiceCalculationImporterJob.new Job().setInvoiceId(invoice.getId());
        context.setBatch(Arrays.asList(job));
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportResult<InvoiceCalculationImporterJob.Job> result = invoiceCalculationImporterJob.new ImportResult<>();
        invoiceCalculationImporterJob.process(context, result);

        clean();

        assertEquals(1, result.getSuccessJobs().size(), "Should have one successful job");
        assertEquals(0, result.getFailedJobs().size(), "Should have no failed jobs");

        InvoiceEntity updatedInvoice = invoiceRepository.findById(invoice.getId()).orElse(null);
        assertNotNull(updatedInvoice, "Invoice should exist");
        assertEquals(InvoiceStatus.ITEM_MOVED, updatedInvoice.getStatus(), "Invoice status should be ITEM_MOVED");
    }


    @Test
    public void processAsItemMovedStatusButWithTypeOtherThenZD() {
        InvoiceEntity invoice = new InvoiceEntityBuilder(context)
                .status(InvoiceStatus.INVALID)
                .type("FA")                             // type is FA, not ZD
                .amount(BigDecimal.valueOf(100.0))      // set amount to avoid NPE
                .persist()
                .get();
        invoice.setInvalidated(true);

        // payment with unique external ID
        PaymentEmbeddable payment = new PaymentEmbeddableBuilder(context)
                .amount(BigDecimal.valueOf(100.0))
                .externalId("UNIQUE_PAYMENT_001")
                .get();

        // invoice raw entity with TV_03 balancing reason and unique payment
        InvoiceRawEntity invoiceRawWithItemMoved = new InvoiceRawEntityBuilder(context)
                .invoice(invoice)
                .item1(1)
                .item2(1)
                .item3(1)
                .amount(BigDecimal.valueOf(100.0))
                .status(InvoiceStatus.PAID)
                .payment(payment)
                .get();

        invoiceRawWithItemMoved.setExternalId("RAW_ITEM_MOVED");
        invoiceRawWithItemMoved.setBalancingReason(BalancingReason.TV_03);
        invoiceRawRepository.save(invoiceRawWithItemMoved);

        clean();

        // create import context and batch
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportContext<InvoiceCalculationImporterJob.Job> context = invoiceCalculationImporterJob.new ImportContext<>();
        context.setId(UUID.randomUUID());
        InvoiceCalculationImporterJob.Job job = invoiceCalculationImporterJob.new Job().setInvoiceId(invoice.getId());
        context.setBatch(Arrays.asList(job));
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportResult<InvoiceCalculationImporterJob.Job> result = invoiceCalculationImporterJob.new ImportResult<>();
        invoiceCalculationImporterJob.process(context, result);

        clean();

        assertEquals(1, result.getSuccessJobs().size(), "Should have one successful job");
        assertEquals(0, result.getFailedJobs().size(), "Should have no failed jobs");

        InvoiceEntity updatedInvoice = invoiceRepository.findById(invoice.getId()).orElse(null);
        assertNotNull(updatedInvoice, "Invoice should exist");
        assertNotEquals(InvoiceStatus.ITEM_MOVED, updatedInvoice.getStatus(), "Invoice status NOT should be ITEM_MOVED");
        assertEquals(InvoiceStatus.PAID, updatedInvoice.getStatus(), "Invoice status should be PAID");
    }

    @ParameterizedTest
    @MethodSource("provideSecondBalancingReasonAndExpectedStatusForPreuctovanieUseCase")
    public void processItemMovedStatusWithOtherRecords(BalancingReason balancingReason, InvoiceStatus expectedStatus) {
        InvoiceEntity invoice = new InvoiceEntityBuilder(context)
                .status(InvoiceStatus.INVALID)
                .type("ZD")
                .persist()
                .get();
        invoice.setInvalidated(true);

        // another invoice for the other record
        InvoiceEntity otherInvoice = new InvoiceEntityBuilder(context)
                .status(InvoiceStatus.INVALID)
                .type("ZD")
                .persist()
                .get();

        // payment with shared external ID
        PaymentEmbeddable payment = new PaymentEmbeddableBuilder(context)
                .amount(BigDecimal.valueOf(100.0))
                .externalId("SHARED_PAYMENT_001")
                .get();

        // invoice raw entity with TV_03 balancing reason
        InvoiceRawEntity invoiceRawWithItemMoved = new InvoiceRawEntityBuilder(context)
                .invoice(invoice)
                .item1(1)
                .item2(1)
                .item3(1)
                .amount(BigDecimal.valueOf(100.0))
                .status(InvoiceStatus.PAID)
                .payment(payment)
                .get();

        invoiceRawWithItemMoved.setExternalId("RAW_ITEM_MOVED");
        invoiceRawWithItemMoved.setBalancingReason(BalancingReason.TV_03);
        invoiceRawRepository.save(invoiceRawWithItemMoved);

        // create another invoice raw entity with same payment external ID but different balancing reason
        InvoiceRawEntity otherInvoiceRaw = new InvoiceRawEntityBuilder(context)
                .invoice(otherInvoice)
                .item1(1)
                .item2(1)
                .item3(1)
                .amount(BigDecimal.valueOf(50.0))
                .status(InvoiceStatus.PAID)
                .payment(payment)
                .get();

        otherInvoiceRaw.setExternalId("RAW_OTHER_RECORD");
        otherInvoiceRaw.setBalancingReason(balancingReason); // Different balancing reason
        invoiceRawRepository.save(otherInvoiceRaw);

        clean();

        // create import context and batch
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportContext<InvoiceCalculationImporterJob.Job> context = invoiceCalculationImporterJob.new ImportContext<>();
        context.setId(UUID.randomUUID());
        InvoiceCalculationImporterJob.Job job = invoiceCalculationImporterJob.new Job().setInvoiceId(invoice.getId());
        context.setBatch(Arrays.asList(job));
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportResult<InvoiceCalculationImporterJob.Job> result = invoiceCalculationImporterJob.new ImportResult<>();
        invoiceCalculationImporterJob.process(context, result);

        clean();

        assertEquals(1, result.getSuccessJobs().size(), "Should have one successful job");
        assertEquals(0, result.getFailedJobs().size(), "Should have no failed jobs");

        InvoiceEntity updatedInvoice = invoiceRepository.findById(invoice.getId()).orElse(null);
        assertNotNull(updatedInvoice, "Invoice should exist");
        // should NOT be ITEM_MOVED because there are other non-TV_03 records with same payment external ID
        assertEquals(expectedStatus, updatedInvoice.getStatus(),"Invoice status should be " + expectedStatus.name());
    }

    private static Stream<Arguments> provideSecondBalancingReasonAndExpectedStatusForPreuctovanieUseCase() {
        return Stream.of(
                Arguments.of(BalancingReason.TV_01, InvoiceStatus.PAID),
                Arguments.of(BalancingReason.TV_03, InvoiceStatus.ITEM_MOVED)
        );
    }

    @Test
    public void processCancelledStatus() {
        InvoiceEntity invoice = new InvoiceEntityBuilder(context)
                .status(InvoiceStatus.INVALID)
                .type("ZD")
                .persist()
                .get();
        invoice.setInvalidated(true);

        // invoice raw entity with TV_05 balancing reason (triggers CANCELLED status)
        InvoiceRawEntity invoiceRawCancelled = new InvoiceRawEntityBuilder(context)
                .invoice(invoice)
                .item1(1)
                .item2(1)
                .item3(1)
                .amount(BigDecimal.valueOf(100.0))
                .status(InvoiceStatus.PAID)
                .get();
        invoiceRawCancelled.setExternalId("RAW_CANCELLED_001");
        invoiceRawCancelled.setBalancingReason(BalancingReason.TV_05);
        invoiceRawRepository.save(invoiceRawCancelled);

        clean();

        // create import context and batch
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportContext<InvoiceCalculationImporterJob.Job> context = invoiceCalculationImporterJob.new ImportContext<>();
        context.setId(UUID.randomUUID());
        InvoiceCalculationImporterJob.Job job = invoiceCalculationImporterJob.new Job().setInvoiceId(invoice.getId());
        context.setBatch(Arrays.asList(job));
        BatchImporterJob<InvoiceCalculationImporterJob.Job>.ImportResult<InvoiceCalculationImporterJob.Job> result = invoiceCalculationImporterJob.new ImportResult<>();

        invoiceCalculationImporterJob.process(context, result);

        clean();

        assertEquals(1, result.getSuccessJobs().size(), "Should have one successful job");
        assertEquals(0, result.getFailedJobs().size(), "Should have no failed jobs");

        InvoiceEntity updatedInvoice = invoiceRepository.findById(invoice.getId()).orElse(null);
        assertNotNull(updatedInvoice, "Invoice should exist");
        assertEquals(InvoiceStatus.CANCELLED, updatedInvoice.getStatus(), "Invoice status should be CANCELLED");
    }
}
