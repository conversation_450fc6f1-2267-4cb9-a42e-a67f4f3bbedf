package sk.spp.nzp.synchronization.engine.importer.job;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import sk.spp.nzp.commons.api.customerprofile.enums.BalancingReason;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceStatus;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceSubType;
import sk.spp.nzp.commons.api.customerprofile.enums.InvoiceTypeGroup;
import sk.spp.nzp.commons.api.customersharing.enums.OwnershipType;
import sk.spp.nzp.commons.enums.EntityType;
import sk.spp.nzp.commons.model.customerprofile.ContractAccountEntity;
import sk.spp.nzp.commons.model.customerprofile.InvoiceEntity;
import sk.spp.nzp.commons.model.customerprofile.InvoiceRawEntity;
import sk.spp.nzp.commons.model.customersharing.BaseOwnershipEntity;
import sk.spp.nzp.commons.model.customersharing.ContractAccountOwnershipEntity;
import sk.spp.nzp.commons.model.customersharing.InvoiceOwnershipEntity;
import sk.spp.nzp.commons.model.customersharing.UnitedDeliveryPointOwnershipEntity;
import sk.spp.nzp.commons.model.synchronization.enums.SynchronizationType;
import sk.spp.nzp.commons.utils.Expressions;
import sk.spp.nzp.synchronization.engine.importer.BatchImporterJob;
import sk.spp.nzp.synchronization.engine.repository.customerprofile.InvoiceEntityRepository;
import sk.spp.nzp.synchronization.engine.repository.customerprofile.InvoiceRawEntityRepository;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static sk.spp.nzp.commons.utils.InvoiceUtil.getPaymentExternalIds;

@Component
@ConfigurationProperties(prefix = "importer-job.invoice-calculation")
public class InvoiceCalculationImporterJob extends BatchImporterJob<InvoiceCalculationImporterJob.Job> {

    private InvoiceEntityRepository invoiceRepository;
    private InvoiceRawEntityRepository invoiceRawRepository;

    public InvoiceCalculationImporterJob(InvoiceEntityRepository invoiceRepository, InvoiceRawEntityRepository invoiceRawRepository) {
        this.invoiceRepository = invoiceRepository;
        this.invoiceRawRepository = invoiceRawRepository;
    }

    @Override
    protected String fetchJobsQuery(Map<String, Object> parameters, ImportContext<InvoiceCalculationImporterJob.Job> context) {
        parameters.put("limit", getBatchSize());
        parameters.put("ownership_type", OwnershipType.OWNER.toString());
        // udpate for which are invalidate and exist DeliveryPoint
        return String.format(
                "   update invoice " +
                        "   set invalidated = false " +
                        "   where id in" +
                        "   (" +
                        "      select inner_invoice.id from invoice inner_invoice where inner_invoice.invalidated = true limit :limit" +
                        "   ) " +
                        "   returning id"
        );
    }


    @Override
    protected RowMapper<Job> getJobRowMapper(ImportContext importContext) {
        return (rs, rowNum) -> new Job().setInvoiceId(rs.getString("id"));
    }

    @Override
    protected void process(ImportContext<InvoiceCalculationImporterJob.Job> context, ImportResult result) {
        Map<String, InvoiceEntity> invoiceMap;
        Set<String> invoiceIds = context.getBatch()
                .stream()
                .map(Job::getInvoiceId)
                .collect(Collectors.toSet());
        List<InvoiceEntity> invoiceEntityList = invoiceRepository.findWithItemsByIdIn(invoiceIds);
        invoiceMap = invoiceEntityList
                .stream()
                .collect(Collectors.toMap(InvoiceEntity::getId, v -> v));

        for (Job job : context.getBatch()) {
            try {
                MDC.put(requestIdKey, job.getInvoiceId());
                InvoiceEntity invoiceEntity = invoiceMap.get(job.getInvoiceId());

                Set<InvoiceRawEntity> invoiceRawSet = new HashSet<>();
                invoiceRawSet.addAll(Optional.ofNullable(invoiceEntity.getInvoiceRawFaList()).orElse(new ArrayList<>()));
                invoiceRawSet.addAll(Optional.ofNullable(invoiceEntity.getInvoiceRawList()).orElse(new ArrayList<>()));

                computeInvoiceFields(
                        invoiceEntity,
                        invoiceRawSet
                );
                processOwnerships(
                        invoiceEntity,
                        invoiceRawSet
                );
                result.getSuccessJobs().add(job);
            } catch (Exception e) {
                result.getFailedJobs().add(job);
                LOGGER.error(String.format("ImportJob; FAILED process; context=[%s], invoice.id=[%s], msg=[%s]",
                        context,
                        job.getInvoiceId(),
                        e.getMessage()), e);
            } finally {
                MDC.remove(requestIdKey);
            }
        }
    }

    private void processOwnerships(InvoiceEntity invoiceEntity, Set<InvoiceRawEntity> invoiceRawSet){
        List<BaseOwnershipEntity> ownerships = new ArrayList<>();
        List<ContractAccountOwnershipEntity> contractAccountOwnerships = Optional.ofNullable(invoiceEntity.getContractAccount()).map(ContractAccountEntity::getOwnerships).orElse(new ArrayList<>());
        Set<UnitedDeliveryPointOwnershipEntity> udpOwnerships = invoiceRawSet.stream()
                .filter(ir -> ir != null && ir.getContract()!=null && ir.getContract().getUnitedDeliveryPoint() != null && ir.getContract().getUnitedDeliveryPoint().getOwnerships() != null)
                .map(ir -> ir.getContract().getUnitedDeliveryPoint().getOwnerships())
                .flatMap(Collection::stream)
                .filter(udpo -> udpo != null && OwnershipType.SHARING.equals(udpo.getType()))
                .collect(Collectors.toSet());

        //clear all
        invoiceEntity.getOwnerships().clear();

        contractAccountOwnerships.forEach(caOwnership->addOwnership(invoiceEntity, caOwnership, null));
        udpOwnerships.forEach(udpOwnership->addOwnership(invoiceEntity, udpOwnership, udpOwnership.getUnitedDeliveryPoint().getId()));
    }

    private void addOwnership(InvoiceEntity invoiceEntity, BaseOwnershipEntity baseOwnershipEntity, UUID udpId){
        InvoiceOwnershipEntity ownership = new InvoiceOwnershipEntity();
        ownership.setTargetEntity(invoiceEntity);
        ownership.setInherited(baseOwnershipEntity.isInherited());
        ownership.setType(baseOwnershipEntity.getType());
        ownership.setCustomerAccount(baseOwnershipEntity.getCustomerAccount());
        ownership.setGrantorCustomerAccount(baseOwnershipEntity.getGrantorCustomerAccount());
        ownership.setUnitedDeliveryPointUuid(udpId);
        invoiceEntity.getOwnerships().add(ownership);
    }

    private void computeInvoiceFields(InvoiceEntity invoiceEntity, Set<InvoiceRawEntity> invoiceRawSet) {
        boolean paid = false;
        boolean unpaid = false;
        boolean cancelled = false;
        boolean itemMoved = false;
        boolean containsPaymentRequest = false;
        boolean containsPaymentPlan = false;
        InvoiceSubType subType = null;
        BigDecimal unpaidAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal overpaidAmount = BigDecimal.ZERO;
        invoiceEntity.setContainsPaymentPlan(false);
        invoiceEntity.setContainsPaymentRequest(false);
        String code = invoiceEntity.getType();

        List<String> paymentExternalIds = getPaymentExternalIds(invoiceRawSet);
        Map<String, Set<BalancingReason>> invoiceRawExtPaymentIdToReasonsMap = paymentExternalIds.isEmpty() ?
                Collections.emptyMap() :
                invoiceRawRepository.findByPaymentExternalIdIn(paymentExternalIds).stream()
                        .collect(Collectors.groupingBy(ir -> ir.getPayment().getExternalId(), Collectors.mapping(InvoiceRawEntity::getBalancingReason, Collectors.toSet())));

        for (InvoiceRawEntity invoiceRawEntity : invoiceRawSet) {
            BigDecimal amount = invoiceRawEntity.getAmount();

            totalAmount = totalAmount.add(amount);

            if (BalancingReason.TV_05.equals(invoiceRawEntity.getBalancingReason())) {
                cancelled = true;
            }

            if (InvoiceStatus.PAID.equals(invoiceRawEntity.getStatus())) {
                paid = true;
            }

            if (InvoiceStatus.UNPAID.equals(invoiceRawEntity.getStatus()) &&
                    Expressions.empty(invoiceRawEntity.getRepaymentPlanId())) {
                unpaid = true;
                unpaidAmount = unpaidAmount.add(amount);

                if(amount.compareTo(BigDecimal.ZERO) < 0){
                    overpaidAmount = overpaidAmount.add(amount.abs());
                }
            }

            if (isInvoiceTypeZd(code) &&                                                                                        // apply rule only on ADVANCE_INVOICE (type 'ZD')
                    BalancingReason.TV_03.equals(invoiceRawEntity.getBalancingReason()) &&
                    invoiceRawEntity.getPayment() != null &&
                    invoiceRawEntity.getPayment().getExternalId() != null &&
                    invoiceRawExtPaymentIdToReasonsMap.get(invoiceRawEntity.getPayment().getExternalId()).size() == 1) {        // itemMoved is false if there is more then one balancing reason for the same paymentExternalId
                    itemMoved = true;
            }

            if (Expressions.notEmpty(invoiceRawEntity.getPaymentRequestInvoiceId())) {
                containsPaymentRequest = true;
            }

            if (Expressions.notEmpty(invoiceRawEntity.getRepaymentPlanId())) {
                containsPaymentPlan = true;
            }

            if (subType == null) {
                subType = Optional.ofNullable(invoiceRawEntity.getMainOperation())
                        .map(InvoiceSubType::determineType)
                        .orElse(null);
            }
        }

        if(!"FA".equalsIgnoreCase(StringUtils.deleteWhitespace(code))) {
            invoiceEntity.setAmount(totalAmount);
        }

        invoiceEntity.setUnpaid(unpaidAmount);
        invoiceEntity.setOverpaid(overpaidAmount);
        invoiceEntity.setContainsPaymentRequest(containsPaymentRequest);
        invoiceEntity.setContainsPaymentPlan(containsPaymentPlan);
        invoiceEntity.setSubType(subType);

        // determine type group
        if (isInvoiceTypeZd(code)) {
            invoiceEntity.setTypeGroup(InvoiceTypeGroup.ADVANCE_INVOICE);
        } else if ("US".equalsIgnoreCase(StringUtils.deleteWhitespace(code))) {
            invoiceEntity.setTypeGroup(InvoiceTypeGroup.REPAYMENT_PLAN);
        } else if (invoiceEntity.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            invoiceEntity.setTypeGroup(InvoiceTypeGroup.CREDIT);
        } else if ("FA".equalsIgnoreCase(StringUtils.deleteWhitespace(code))) {
            invoiceEntity.setTypeGroup(InvoiceTypeGroup.INVOICE);
        } else {
            invoiceEntity.setTypeGroup(InvoiceTypeGroup.OTHERS);
        }

        if (cancelled) {
            invoiceEntity.setStatus(InvoiceStatus.CANCELLED);
        } else if (itemMoved) {
            invoiceEntity.setStatus(InvoiceStatus.ITEM_MOVED);
        } else if (paid && unpaid) {
            invoiceEntity.setStatus(InvoiceStatus.PARTIALLY_PAID);
        } else if (paid) {
            invoiceEntity.setStatus(InvoiceStatus.PAID);
        } else {
            invoiceEntity.setStatus(InvoiceStatus.UNPAID);
        }
    }

    private boolean isInvoiceTypeZd(String invoiceType) {
        return "ZD".equalsIgnoreCase(org.apache.commons.lang3.StringUtils.deleteWhitespace(invoiceType));
    }


    @Override
    protected EntityType getEntityType() {
        return EntityType.INVOICE;
    }

    @Override
    protected SynchronizationType getSynchronizationType() {
        return SynchronizationType.INVOICE_CALCULATION;
    }

    /**
     *
     */
    public class Job {

        /**
         * Contract
         */
        private String invoiceId;

        public String getInvoiceId() {
            return invoiceId;
        }

        public Job setInvoiceId(String invoiceId) {
            this.invoiceId = invoiceId;
            return this;
        }
    }
}
