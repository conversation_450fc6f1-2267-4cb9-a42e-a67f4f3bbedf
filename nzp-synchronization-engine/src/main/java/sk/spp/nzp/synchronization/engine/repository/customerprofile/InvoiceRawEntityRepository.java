package sk.spp.nzp.synchronization.engine.repository.customerprofile;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sk.spp.nzp.commons.model.customerprofile.InvoiceRawEntity;

import java.util.Collection;
import java.util.List;

@Repository("SynchronizationInvoiceRawEntityRepository")
public interface InvoiceRawEntityRepository extends JpaRepository<InvoiceRawEntity, String>, QuerydslPredicateExecutor<InvoiceRawEntity> {


    @EntityGraph(attributePaths = {"synchronizationLog"})
    List<InvoiceRawEntity> findWithSynchronizationByExternalIdIn(Collection<String> strings);

    @Query("SELECT ir FROM InvoiceRawEntity ir WHERE ir.payment.externalId IN :paymentExternalIds")
    List<InvoiceRawEntity> findByPaymentExternalIdIn(@Param("paymentExternalIds") List<String> paymentExternalIds);
}
